package com.bjcj.service.mobile;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.mobile.PdaSubconfigMapper;
import com.bjcj.model.dto.mobile.PdaSubconfigDto;
import com.bjcj.model.po.mobile.PdaSubconfig;
import com.bjcj.model.po.mobile.PdaSudoconfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Transactional
public class PdaSubconfigService extends ServiceImpl<PdaSubconfigMapper,PdaSubconfig> {

    @Autowired
    private PdaSubconfigMapper subconfigMapper;

    /**
     * 更新其父类
     * @param pdaSubconfig
     * @return
     */
    @Transactional
    public Boolean updateParent(PdaSubconfig pdaSubconfig,Boolean changeParent) {

        Boolean result = updateById(pdaSubconfig);


        if (!changeParent){
            return result;
        }

        //设置公共组件
        if (pdaSubconfig.getPubcomponent()==1){
            while (pdaSubconfig.getParentid()!=null){

                QueryWrapper<PdaSubconfig> queryWrapper = new QueryWrapper();
                queryWrapper.eq("subId",pdaSubconfig.getParentid());
                pdaSubconfig = getOne(queryWrapper);

                if (pdaSubconfig.getPubcomponent()==1){
                    pdaSubconfig.setNumdepend(pdaSubconfig.getNumdepend()+1);
                }else {
                    pdaSubconfig.setPubcomponent(1);
                    pdaSubconfig.setNumdepend(1);
                }

                updateById(pdaSubconfig);
            }
        }else {
            //设置非公共组件
            while (pdaSubconfig.getParentid()!=null){

                QueryWrapper<PdaSubconfig> queryWrapper = new QueryWrapper();
                queryWrapper.eq("subId",pdaSubconfig.getParentid());
                pdaSubconfig = getOne(queryWrapper);

                if (pdaSubconfig.getNumdepend()>1){
                    pdaSubconfig.setNumdepend(pdaSubconfig.getNumdepend()-1);
                }else {
                    pdaSubconfig.setPubcomponent(0);
                    pdaSubconfig.setNumdepend(0);
                }

                updateById(pdaSubconfig);
            }
        }

        return result;
    }

    public List<PdaSubconfig> list(Map<String,Object> map) {
        List<PdaSubconfig> softList= this.subconfigMapper.list(map);
        return softList;
    }

    public List<PdaSubconfig> getOption(PdaSubconfig pdaSubconfig) {

        QueryWrapper<PdaSubconfig> queryWrapper = new QueryWrapper();

        queryWrapper.eq("pid",pdaSubconfig.getPid());
        queryWrapper.eq("builtmodule","是");
        if (pdaSubconfig.getSubid() == null){
            queryWrapper.isNull("parentid");
        }else {
            queryWrapper.eq("parentid",pdaSubconfig.getSubid());
        }

        return list(queryWrapper);
    }

    public List<PdaSubconfig> getWholeOption(PdaSubconfig pdaSubconfig) {

        return subconfigMapper.getWholeOption(pdaSubconfig);
    }
    
    public List<PdaSubconfig> listByPage(PdaSubconfigDto PdaSubconfigDto) {
        LambdaQueryWrapper<PdaSubconfig> queryWrapper = new LambdaQueryWrapper();

        queryWrapper.eq(PdaSubconfig::getPid,PdaSubconfigDto.getPid());
        if (PdaSubconfigDto.getSubid()==null){
            queryWrapper.isNull(PdaSubconfig::getParentid);
        }else {
            queryWrapper.eq(PdaSubconfig::getParentid,PdaSubconfigDto.getSubid());
        }

        if (PdaSubconfigDto.getCurrentPage() != null && PdaSubconfigDto.getPageSize() != null){
            IPage<PdaSubconfig> ipage = new Page<>(PdaSubconfigDto.getCurrentPage(), PdaSubconfigDto.getPageSize());
            List<PdaSubconfig> list = page(ipage,queryWrapper).getRecords();
            return list;
        }

        return new ArrayList();
    }
    
    public List<PdaSubconfig> getParentSubconfig() {
        QueryWrapper<PdaSubconfig> queryWrapper = new QueryWrapper();
        queryWrapper.eq("builtmodule","是");
        return list(queryWrapper);
    }

    public Long count(PdaSubconfigDto PdaSubconfigDto) {

        QueryWrapper<PdaSubconfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pid",PdaSubconfigDto.getPid());
        if (PdaSubconfigDto.getSubid()==null){
            queryWrapper.isNull("parentid");
        }else {
            queryWrapper.eq("parentid",PdaSubconfigDto.getSubid());
        }

        return count(queryWrapper);
    }
    
    public Boolean add(PdaSubconfig pdaSubconfig) {
        return save(pdaSubconfig);
    }
    
    public Boolean delete(PdaSubconfig pdaSubconfig) {
        return removeById(pdaSubconfig);
    }
    
    public Boolean update(PdaSubconfig pdaSubconfig) {
        return updateById(pdaSubconfig);
    }
    
    public PdaSubconfig getOne(PdaSubconfig pdaSubconfig) {

        QueryWrapper queryWrapper = new QueryWrapper();

        if (pdaSubconfig.getSubid()!=null){
            queryWrapper.eq("subid",pdaSubconfig.getSubid());
        }

        return getOne(queryWrapper);
    }
    
    public PdaSubconfig getOne() {

        QueryWrapper queryWrapper = new QueryWrapper();

        queryWrapper.eq("pid",2170);
        queryWrapper.eq("sortid","1");

        return getOne(queryWrapper);
    }
    public JsonResult selectPage(Integer page, Integer pageSize, String pid) {

        Page<PdaSubconfig> pager = new Page<>(page, pageSize);
        LambdaQueryWrapper<PdaSubconfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PdaSubconfig::getPid, pid);
        Page<PdaSubconfig> pages = subconfigMapper.selectPage(pager, wrapper);
        return JsonResult.success(pages);

    }
}
