<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.naturalresources.categories.ResourceDataitemsMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.naturalresources.categories.ResourceDataitems">
    <!--@mbg.generated-->
    <!--@Table public.resource_dataitems-->
    <result column="id" jdbcType="CHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="displayname" jdbcType="VARCHAR" property="displayname" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="year" jdbcType="NUMERIC" property="year" />
    <result column="scale" jdbcType="NUMERIC" property="scale" />
    <result column="districtcode" jdbcType="VARCHAR" property="districtcode" />
    <result column="districtname" jdbcType="VARCHAR" property="districtname" />
    <result column="accesscount" jdbcType="NUMERIC" property="accesscount" />
    <result column="organizationid" jdbcType="VARCHAR" property="organizationid" />
    <result column="resourcecatalogid" jdbcType="CHAR" property="resourcecatalogid" />
    <result column="thumbnailurl" jdbcType="VARCHAR" property="thumbnailurl" />
    <result column="status" jdbcType="NUMERIC" property="status" />
    <result column="datatime" jdbcType="TIMESTAMP" property="datatime" />
    <result column="datatimeprecision" jdbcType="NUMERIC" property="datatimeprecision" />
    <result column="secretlevel" jdbcType="VARCHAR" property="secretlevel" />
    <result column="registerman" jdbcType="VARCHAR" property="registerman" />
    <result column="registerdate" jdbcType="TIMESTAMP" property="registerdate" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="isbigdataimport" jdbcType="BOOLEAN" property="isbigdataimport" />
    <result column="cs" jdbcType="VARCHAR" property="cs" />
    <result column="levelsign" jdbcType="VARCHAR" property="levelsign" />
    <result column="repairman" jdbcType="VARCHAR" property="repairman" />
    <result column="repairdate" jdbcType="TIMESTAMP" property="repairdate" />
    <result column="displayorder" jdbcType="NUMERIC" property="displayorder" />
    <result column="datatype" jdbcType="NUMERIC" property="datatype" />
    <result column="validationtype" jdbcType="NUMERIC" property="validationtype" />
    <result column="metadata" jdbcType="OTHER" property="metadata" />
    <result column="authoritytype" jdbcType="NUMERIC" property="authoritytype" />
    <result column="isvisiable" jdbcType="BOOLEAN" property="isvisiable" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", displayname, description, "year", "scale", districtcode, districtname, 
    accesscount, organizationid, resourcecatalogid, thumbnailurl, "status", datatime, 
    datatimeprecision, secretlevel, registerman, registerdate, code, isbigdataimport, 
    cs, levelsign, repairman, repairdate, displayorder, datatype, validationtype, metadata, 
    authoritytype, isvisiable
  </sql>

  <select id="selectPageList" resultType="com.bjcj.model.po.naturalresources.categories.ResourceServices">
    select cs.* from resource_services cs
    left join resource_dataitems cdi on cdi.id = cs.dataitemid
    where 1=1
    <if test="searchStr!= null and searchStr!= ''">
      and ( cs.name like concat('%', #{searchStr}, '%') or cs.displayname like concat('%', #{searchStr}, '%') )
    </if>
    <if test="resourceCatalogId!= null and resourceCatalogId!=''">
      and cdi.resourcecatalogid = #{resourceCatalogId}
    </if>
    and cdi.status=1
    order by cs.registerdate desc
    LIMIT #{size} OFFSET #{current}

  </select>

  <select id="selectListCount" resultType="java.lang.Long">
    select count(*) from (
    select cs.* from resource_services cs
    left join resource_dataitems cdi on cdi.id = cs.dataitemid
    where 1=1
    <if test="searchStr!= null and searchStr!= ''">
      and ( cs.name like concat('%', #{searchStr}, '%') or cs.displayname like concat('%', #{searchStr}, '%') )
    </if>
    <if test="resourceCatalogId!= null and resourceCatalogId!=''">
      and cdi.resourcecatalogid = #{resourceCatalogId}
    </if>
    and cdi.status=1
    ) c
  </select>

  <update id="updateReviewStatus">
    update resource_dataitems set status = #{status} where id = #{resId}
    </update>

  <update id="updateReviewStatus2">
    update resource_services set status = #{status} where id = #{resId}
  </update>
</mapper>