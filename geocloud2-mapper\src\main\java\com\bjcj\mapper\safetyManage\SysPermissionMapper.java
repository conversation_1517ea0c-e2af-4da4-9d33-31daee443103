package com.bjcj.mapper.safetyManage;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.safetyManage.SysPermission;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;


/**
 *@Author：qinyi
 *@Date：2023/12/5  9:36
*/
public interface SysPermissionMapper extends BaseMapper<SysPermission> {
    List<SysPermission> selectAll();

    /**
     * 获取用户的所有权限
     */
    Set<String> findByUserId(long userId);

    List<SysPermission> getPermissionByUserId(@Param("userId") Long userId);
}