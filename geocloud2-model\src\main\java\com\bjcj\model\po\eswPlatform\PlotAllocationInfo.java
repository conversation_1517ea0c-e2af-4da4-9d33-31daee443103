package com.bjcj.model.po.eswPlatform;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024-8-22  15:06
*/
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "plot_allocation_info")
public class PlotAllocationInfo implements Serializable {
    /**
     * 标识
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(description="标识")
    @Size(max = 40,message = "标识最大长度要小于 40")
    private String id;

    /**
     * 名称
     */
    @TableField(value = "name")
    @Schema(description="名称")
    @Size(max = 100,message = "名称最大长度要小于 100")
    private String name;

    /**
     * 三维地块配置信息，json字符串保存
     */
    @TableField(value = "content")
    @Schema(description="三维地块配置信息，json字符串保存")
    private String content;

    /**
     * 颜色参数
     */
    @TableField(value = "color_cause")
    @Schema(description="颜色参数")
    @Size(max = 255,message = "颜色参数最大长度要小于 255")
    private String colorCause;

    /**
     * 高度
     */
    @TableField(value = "altitude")
    @Schema(description="高度")
    @Size(max = 255,message = "高度最大长度要小于 255")
    private String altitude;

    /**
     * 类型
     */
    @TableField(value = "type")
    @Schema(description="类型")
    @Size(max = 60,message = "类型最大长度要小于 60")
    private String type;

    /**
     * 上级id
     */
    @TableField(value = "parentid")
    @Schema(description="上级id")
    @Size(max = 40,message = "上级id最大长度要小于 40")
    private String parentId;

    //special_plan_id
    @TableField(value = "special_plan_id")
    @Schema(description="专题id")
    @NotNull(message = "专题id不能为null")
    private Long specialPlanId;

    private static final long serialVersionUID = 1L;
}