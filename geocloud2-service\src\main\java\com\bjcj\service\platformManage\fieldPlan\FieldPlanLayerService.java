package com.bjcj.service.platformManage.fieldPlan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.platformManage.fieldPlan.FieldPlanLayerFieldMapper;
import com.bjcj.mapper.platformManage.fieldPlan.FieldPlanLayerMapper;
import com.bjcj.model.dto.fieldPlan.FieldLayerNameDto;
import com.bjcj.model.po.platformManage.fieldPlan.FieldPlanLayer;
import com.bjcj.model.po.platformManage.fieldPlan.FieldPlanLayerField;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 *@Author：qinyi
 *@Date：2024/2/19  17:21
*/
@Service
public class FieldPlanLayerService extends ServiceImpl<FieldPlanLayerMapper, FieldPlanLayer> {

    @Resource
    private FieldPlanLayerMapper fieldPlanLayerMapper;
    @Resource
    private FieldPlanLayerFieldMapper fieldPlanLayerFieldMapper;

    public JsonResult editFieldLayerName(FieldLayerNameDto dto) {
        this.fieldPlanLayerMapper.editFieldLayerName(dto);
        return JsonResult.success();
    }

    public JsonResult deleteById(String id, Long fieldPlanId) {
        this.fieldPlanLayerMapper.delete(new LambdaQueryWrapper<FieldPlanLayer>().eq(FieldPlanLayer::getId, id).eq(FieldPlanLayer::getFieldPlanId, fieldPlanId));
        this.fieldPlanLayerFieldMapper.delete(new LambdaQueryWrapper<FieldPlanLayerField>().eq(FieldPlanLayerField::getDataLayerId, id)
                .eq(FieldPlanLayerField::getFieldPlanId, fieldPlanId));
        return JsonResult.success();
    }
}
