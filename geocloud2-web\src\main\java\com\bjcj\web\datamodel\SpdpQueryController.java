package com.bjcj.web.datamodel;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.datamodel.ResourceEsDto;
import com.bjcj.model.dto.datamodel.ResourceEsDto2;
import com.bjcj.model.dto.datamodel.SpdpQueryDto;
import com.bjcj.model.po.datamodel.ResourceEs;
import com.bjcj.model.po.datamodel.SpdpQuery;
import com.bjcj.model.po.naturalresources.categories.MetadataTables;
import com.bjcj.service.datamodel.MetadataTablestructuresService;
import com.bjcj.service.datamodel.ResourceEsService;
import com.bjcj.service.datamodel.SpdpQueryService;
import com.bjcj.service.naturalresources.categories.MetadataTablesService;
import com.bjcj.service.naturalresources.categories.ResourceDataitemsService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
* 查询配置(spdp_query)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/dataQuery")
@Tag(name = "数据搜索")
@Validated
public class SpdpQueryController {
    /**
    * 服务对象
    */
    @Resource
    private SpdpQueryService spdpQueryService;

    @Resource
    private ResourceEsService resourceEsService;

    @Resource
    private MetadataTablesService metadataTablesService;

    @Resource
    private ResourceDataitemsService resourceDataitemsService;

    @Resource
    private MetadataTablestructuresService metadataTablestructuresService;

    @SaCheckPermission("sys:read")
    @GetMapping("/list")
    @Operation(summary = "分类空间搜索列表", description = "分类空间搜索列表")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "searchStr", description = "searchStr", required = false)
    })
    public JsonResult<List<SpdpQuery>> list(@RequestParam(value = "searchStr",required = false) String searchStr){
        if(StringUtils.isNotBlank(searchStr)){
            return JsonResult.success(this.spdpQueryService.list(new LambdaQueryWrapper<SpdpQuery>().and(i -> i.like(SpdpQuery::getName, searchStr).or().like(SpdpQuery::getDisplayname, searchStr))));
        }
        return JsonResult.success(this.spdpQueryService.list());
    }

    @OperaLog(operaModule = "分类空间搜索-删除",operaType = OperaLogConstant.DELETE,operaDesc = "分类空间搜索-删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del/{queryid}")
    @Operation(summary = "删除", description = "根据id删除")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "delflkjss")
    public JsonResult del(@PathVariable("queryid") String queryid){
        return JsonResult.success(this.spdpQueryService.removeById(queryid));
    }


    @OperaLog(operaModule = "分类空间搜索-批量删除",operaType = OperaLogConstant.DELETE,operaDesc = "分类空间搜索-批量删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delBatch")
    @Operation(summary = "批量删除", description = "根据id批量删除")
    @ApiOperationSupport(order = 3)
    @Parameters({
            @Parameter(name = "queryids", description = "ids逗号拼接", required = true)
    })
    @RequestLock(prefix = "delBatchflkjss")
    public JsonResult delBatch(@RequestParam("queryids") String queryids){
        if(queryids.contains(",")){
            //ids转list
            List<String> ids = List.of(queryids.split(","));
            return JsonResult.success(this.spdpQueryService.removeByIds(ids));
        }else{
            return JsonResult.error("ids格式错误,必须选择一条以上的数据");
        }
    }

    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "注册/编辑", description = "注册/编辑")
    @ApiOperationSupport(order = 4)
    @RequestLock(prefix = "addOrEditsjss")
    public JsonResult add(@Validated @RequestBody SpdpQueryDto dto){
        SpdpQuery spdpQuery = BeanUtil.copyProperties(dto, SpdpQuery.class);
        return this.spdpQueryService.saveOrUpdate(spdpQuery) ? JsonResult.success() : JsonResult.error();
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/allSearchlist")
    @Operation(summary = "全文搜索配置列表", description = "全文搜索配置列表")
    @ApiOperationSupport(order = 5)
    @Parameters({
            @Parameter(name = "searchStr", description = "数据集名称", required = false),
            @Parameter(name = "status", description = "入库状态", required = false)
    })
    public JsonResult<List<ResourceEs>> allSearchlist(@RequestParam(value = "searchStr",required = false) String searchStr,
                                                      @RequestParam(value = "status",required = false) Integer status){
        LambdaQueryWrapper<ResourceEs> queryWrapper = new LambdaQueryWrapper<ResourceEs>();
        queryWrapper.eq(Objects.nonNull(status),ResourceEs::getStatus,status);
        queryWrapper.like(StringUtils.isNotBlank(searchStr),ResourceEs::getTopicname,searchStr);
        List<ResourceEs> list = this.resourceEsService.list(queryWrapper);
        list.forEach(item->{
            MetadataTables metadataTables = this.metadataTablesService.getById(item.getTableid());
            String dataitemname = this.resourceDataitemsService.getById(metadataTables.getDataitemid()).getDisplayname();
            item.setDataItemName(dataitemname);
            item.setMetadatatablesName(metadataTables.getName());
            String name = this.metadataTablestructuresService.getById(metadataTables.getTablestructureid()).getName();
            item.setMetadatatablestructuresName(name);
        });
        return JsonResult.success(list);
    }

    @OperaLog(operaModule = "全文搜索配置-删除",operaType = OperaLogConstant.DELETE,operaDesc = "全文搜索配置-删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delResourceEs/{id}")
    @Operation(summary = "全文搜索配置-删除", description = "根据id删除")
    @ApiOperationSupport(order = 6)
    @RequestLock(prefix = "delResourceEs")
    public JsonResult delResourceEs(@PathVariable("id") String id){
        return JsonResult.success(this.resourceEsService.removeById(id));
    }


    @OperaLog(operaModule = "全文搜索配置-批量删除",operaType = OperaLogConstant.DELETE,operaDesc = "全文搜索配置-批量删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delBatchResourceEs")
    @Operation(summary = "全文搜索配置-批量删除", description = "根据id批量删除")
    @ApiOperationSupport(order = 7)
    @Parameters({
            @Parameter(name = "ids", description = "ids逗号拼接", required = true)
    })
    @RequestLock(prefix = "delBatchResourceEs")
    public JsonResult delBatchResourceEs(@RequestParam("ids") String ids){
        if(ids.contains(",")){
            //ids转list
            List<String> id2 = List.of(ids.split(","));
            return JsonResult.success(this.resourceEsService.removeByIds(id2));
        }else{
            return JsonResult.error("ids格式错误,必须选择一条以上的数据");
        }
    }


    @SaCheckPermission("sys:write")
    @PostMapping("/addResourceEs")
    @Operation(summary = "新增全文搜索配置", description = "新增全文搜索配置")
    @ApiOperationSupport(order = 8)
    @RequestLock(prefix = "addResourceEs")
    public JsonResult addResourceEs(@Validated @RequestBody ResourceEsDto2 dto){
        List<ResourceEsDto> dtos = dto.getDtos();
        if(dtos.isEmpty()){
            return JsonResult.error("请选择数据");
        }
        List<ResourceEs> esList = new ArrayList<>(dtos.size());
        for (ResourceEsDto resourceEsDto : dtos) {
            ResourceEs es = BeanUtil.copyProperties(resourceEsDto, ResourceEs.class);
            es.setIsdeleteexitindex(false);
            esList.add(es);
        }
        return this.resourceEsService.saveBatch(esList) ? JsonResult.success() : JsonResult.error();
    }

    @SaCheckPermission("sys:write")
    @PostMapping("/editResourceEs")
    @Operation(summary = "编辑全文搜索配置", description = "编辑全文搜索配置")
    @ApiOperationSupport(order = 9)
    @RequestLock(prefix = "editResourceEs")
    public JsonResult editResourceEs(ResourceEsDto dto){
        ResourceEs es = BeanUtil.copyProperties(dto, ResourceEs.class);
        return this.resourceEsService.updateById(es) ? JsonResult.success() : JsonResult.error();
    }
}
