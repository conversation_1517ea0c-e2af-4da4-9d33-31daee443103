package com.bjcj.web.datamodel;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.datamodel.SpdpOverlayanalysis;
import com.bjcj.model.po.naturalresources.categories.ResourceServices;
import com.bjcj.model.po.naturalresources.categories.ResourceServicesZcgnfw;
import com.bjcj.service.datamodel.SpdpOverlayanalysisService;
import com.bjcj.service.naturalresources.categories.ResourceServicesService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


/**
* 数据分析规则表(spdp_overlayanalysis)表控制层
* <AUTHOR>
*/
@RestController
@RequestMapping("/spdp_overlayanalysis")
@Tag(name = "12.重叠分析配置")
@Validated
public class OverlayanalysisController {
    /**
    * 服务对象
    */
    @Resource
    private SpdpOverlayanalysisService spdpOverlayanalysisService;

    @Resource
    private ResourceServicesService resourceServicesService;

    @OperaLog(operaModule = "根据规则id查询列表",operaType = OperaLogConstant.LOOK,operaDesc = "列表带模糊查询")
    @SaCheckPermission("sys:read")
    @GetMapping("/list")
    @Operation(summary = "根据规则id查询列表", description = "列表带模糊查询")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "catalogid", description = "分析规则id", required = true),
            @Parameter(name = "searchStr", description = "搜索名称(displayname)", required = false)
    })
    public JsonResult<List<SpdpOverlayanalysis>> list(@RequestParam("catalogid") String catalogid,
                                                      @RequestParam(value = "searchStr",required = false) String searchStr){
        LambdaQueryWrapper<SpdpOverlayanalysis> queryWrapper = new LambdaQueryWrapper<SpdpOverlayanalysis>();
        queryWrapper.eq(SpdpOverlayanalysis::getCatalogid, catalogid);
        queryWrapper.like(searchStr!=null,SpdpOverlayanalysis::getDisplayname,searchStr);
        return JsonResult.success(this.spdpOverlayanalysisService.list(queryWrapper));
    }


    @OperaLog(operaModule = "重叠分析-删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del/{overlayanalysisid}")
    @Operation(summary = "删除", description = "根据id删除")
    @ApiOperationSupport(order = 3)
    public JsonResult del(@PathVariable("overlayanalysisid") String overlayanalysisid){
        return JsonResult.success(this.spdpOverlayanalysisService.removeById(overlayanalysisid));
    }

    @OperaLog(operaModule = "重叠分析-批量删除",operaType = OperaLogConstant.DELETE,operaDesc = "批量删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/batchDel")
    @Operation(summary = "批量删除", description = "根据id批量删除")
    @ApiOperationSupport(order = 4)
    @Parameters({
            @Parameter(name = "catalogids", description = "分析规则ids逗号拼接", required = true)
    })
    public JsonResult batchDel(@RequestParam("catalogids") String catalogids){
        if(catalogids.contains(",")){
            //ids转list
            List<String> ids = List.of(catalogids.split(","));
            return JsonResult.success(this.spdpOverlayanalysisService.removeByIds(ids));
        }else{
            return JsonResult.error("ids格式错误,必须选择一条以上的数据");
        }
    }

    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "注册/编辑", description = "注册/编辑")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "addOrEdit")
    public JsonResult addOrEdit(@Validated @RequestBody SpdpOverlayanalysis dto){
        if(Objects.isNull(dto.getInputlayers())) dto.setInputlayers("[]");
        // if(Objects.nonNull(dto.getOverlaylayers())){
        //     //去除字符串中的空格
        //     dto.setOverlaylayers(dto.getOverlaylayers().replace(" ",""));
        // }
        return JsonResult.success(this.spdpOverlayanalysisService.saveOrUpdate(dto));
    }


    @OperaLog(operaModule = "注册分析服务(传统分析服务)",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "注册分析服务(传统分析服务)")
    @SaCheckPermission("sys:write")
    @PostMapping("/registTraditionalService")
    @Operation(summary = "注册分析服务(传统分析服务)", description = "注册分析服务(传统分析服务)")
    @ApiOperationSupport(order = 5)
    public JsonResult registTraditionalService(@Validated @RequestBody ResourceServicesZcgnfw resourceServicesZcgnfw){
        //用名称判断服务是否已被注册
        List<ResourceServices> list = this.resourceServicesService.list(new LambdaQueryWrapper<ResourceServices>().eq(ResourceServices::getName, resourceServicesZcgnfw.getName()));
        if(!list.isEmpty()){
            return JsonResult.error("服务名称已被注册");
        }
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        resourceServicesZcgnfw.setRegisterman(username);
        resourceServicesZcgnfw.setRegisterdate(LocalDateTime.now());
        resourceServicesZcgnfw.setIsvisiable(true);
        resourceServicesZcgnfw.setAccesscount(new BigDecimal("0"));
        resourceServicesZcgnfw.setResourcecategory(Short.valueOf("1"));
        ResourceServices resourceServices = BeanUtil.copyProperties(resourceServicesZcgnfw, ResourceServices.class);
        //todo 转发地址需要配置自动填写
        int sjs = (int) (Math.random()*9000) + 1000;
        resourceServices.setTransferurl("/rest/services/"+resourceServices.getName()+"/DPServer");
        return this.resourceServicesService.save(resourceServices) ? JsonResult.success("注册成功") : JsonResult.error("注册失败");
    }
}
