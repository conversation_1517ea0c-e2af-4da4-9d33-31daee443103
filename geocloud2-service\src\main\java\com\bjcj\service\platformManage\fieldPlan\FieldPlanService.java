package com.bjcj.service.platformManage.fieldPlan;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.datamodel.DataLayerFieldMapper;
import com.bjcj.mapper.datamodel.DataLayerMapper;
import com.bjcj.mapper.datamodel.MetadataTablestructurefieldsMapper;
import com.bjcj.mapper.datamodel.MetadataTablestructuresMapper;
import com.bjcj.mapper.platformManage.fieldPlan.FieldPlanLayerFieldMapper;
import com.bjcj.mapper.platformManage.fieldPlan.FieldPlanLayerMapper;
import com.bjcj.mapper.platformManage.fieldPlan.FieldPlanMapper;
import com.bjcj.model.dto.fieldPlan.DataLayerIdsDto;
import com.bjcj.model.po.datamodel.MetadataTablestructurefields;
import com.bjcj.model.po.datamodel.MetadataTablestructures;
import com.bjcj.model.po.platformManage.fieldPlan.FieldPlan;
import com.bjcj.model.po.platformManage.fieldPlan.FieldPlanLayer;
import com.bjcj.model.po.platformManage.fieldPlan.FieldPlanLayerField;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *@Author：qinyi
 *@Date：2024/2/19  17:19
*/
@Service
public class FieldPlanService extends ServiceImpl<FieldPlanMapper, FieldPlan> {

    @Resource
    private FieldPlanMapper fieldPlanMapper;

    @Resource
    private FieldPlanLayerMapper fieldPlanLayerMapper;

    @Resource
    private FieldPlanLayerFieldMapper fieldPlanLayerFieldMapper;

    @Resource
    private DataLayerMapper dataLayerMapper;

    @Resource
    private DataLayerFieldMapper dataLayerFieldMapper;

    @Resource
    private FieldPlanLayerService fieldPlanLayerService;

    @Resource
    private FieldPlanLayerFieldService fieldPlanLayerFieldService;

    @Resource
    private MetadataTablestructuresMapper metadataTablestructuresMapper;

    @Resource
    private MetadataTablestructurefieldsMapper metadataTablestructurefieldsMapper;



    public JsonResult deleteById(Long id) {
        FieldPlan fieldPlan = fieldPlanMapper.selectById(id);
        if(Objects.isNull(fieldPlan)) return JsonResult.error("方案不存在");
        this.fieldPlanLayerFieldMapper.delete(new LambdaQueryWrapper<FieldPlanLayerField>().eq(FieldPlanLayerField::getFieldPlanId,id));
        this.fieldPlanLayerMapper.delete(new LambdaQueryWrapper<FieldPlanLayer>().eq(FieldPlanLayer::getFieldPlanId,id));
        this.fieldPlanMapper.deleteById(id);
        return JsonResult.success("删除成功");
    }

    public JsonResult addLayer(DataLayerIdsDto dto) {
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        if(Objects.isNull(dto.getDataLayerIdList()) || dto.getDataLayerIdList().isEmpty()) return JsonResult.error("新增对象为空");
        List<MetadataTablestructures> dataLayerList = this.metadataTablestructuresMapper.selectList(new LambdaQueryWrapper<MetadataTablestructures>()
                .in(MetadataTablestructures::getTablestructureid, dto.getDataLayerIdList())
        );
        List<FieldPlanLayer> fieldPlanLayerList = new ArrayList<>(dataLayerList.size());
        dataLayerList.forEach(dataLayer -> {
            FieldPlanLayer fieldPlanLayer = new FieldPlanLayer();
            fieldPlanLayer.setId(dataLayer.getTablestructureid());
            fieldPlanLayer.setName(dataLayer.getName());
            fieldPlanLayer.setShowName(dataLayer.getDisplayname());
            fieldPlanLayer.setIsMust(dataLayer.getRequired());
            fieldPlanLayer.setLayerTypeId(Long.valueOf(dataLayer.getShapetype()));
            fieldPlanLayer.setFieldPlanId(dto.getFieldPlanId());
            fieldPlanLayer.setOperator(username);
            fieldPlanLayerList.add(fieldPlanLayer);
        });
        List<MetadataTablestructurefields> dataLayerFieldList = this.metadataTablestructurefieldsMapper.selectList(new LambdaQueryWrapper<MetadataTablestructurefields>()
                .in(MetadataTablestructurefields::getTablestructureid, dto.getDataLayerIdList())
        );
        List<FieldPlanLayerField> fieldPlanLayerFieldList = new ArrayList<>(dataLayerFieldList.size());
        dataLayerFieldList.forEach(dataLayerField -> {
            FieldPlanLayerField fieldPlanLayerField = new FieldPlanLayerField();
            fieldPlanLayerField.setFieldPlanId(dto.getFieldPlanId());
            fieldPlanLayerField.setFieldName(dataLayerField.getName());
            fieldPlanLayerField.setShowName(dataLayerField.getDisplayname());
            fieldPlanLayerField.setFieldTypeId(dataLayerField.getFieldtype());
            fieldPlanLayerField.setDataLayerId(dataLayerField.getTablestructureid());
            fieldPlanLayerField.setIsSort(dataLayerField.getIssort());
            fieldPlanLayerField.setIsShow(dataLayerField.getIsvisiable());
            fieldPlanLayerField.setShowSort(dataLayerField.getDisplayorder());
            fieldPlanLayerFieldList.add(fieldPlanLayerField);
        });
        boolean b = this.fieldPlanLayerService.saveBatch(fieldPlanLayerList);
        boolean f = this.fieldPlanLayerFieldService.saveBatch(fieldPlanLayerFieldList);
        if(b && f) return JsonResult.success("新增成功");
        else return JsonResult.error("新增失败");
    }

    public JsonResult exportTo(Long fromPlatformAppConfId, Long toPlatformAppConfId) {
        FieldPlan fieldPlan = this.fieldPlanMapper.selectOne(new LambdaQueryWrapper<FieldPlan>().eq(FieldPlan::getPlatformAppConfId,toPlatformAppConfId));
        if(Objects.isNull(fieldPlan)) JsonResult.error("目标平台应用不存在");
        FieldPlan fieldPlan1 = this.fieldPlanMapper.selectOne(new LambdaQueryWrapper<FieldPlan>().eq(FieldPlan::getPlatformAppConfId,fromPlatformAppConfId));
        //先删除目标原数据
        this.fieldPlanLayerMapper.delete(new LambdaQueryWrapper<FieldPlanLayer>().eq(FieldPlanLayer::getFieldPlanId,fieldPlan.getId()));
        this.fieldPlanLayerFieldMapper.delete(new LambdaQueryWrapper<FieldPlanLayerField>().eq(FieldPlanLayerField::getFieldPlanId,fieldPlan.getId()));

        //导入新的数据
        List<FieldPlanLayer> fieldPlanLayerList = this.fieldPlanLayerMapper.selectList(new LambdaQueryWrapper<FieldPlanLayer>().eq(FieldPlanLayer::getFieldPlanId,fieldPlan1.getId()));
        fieldPlanLayerList.forEach(fieldPlanLayer -> {
            fieldPlanLayer.setFieldPlanId(fieldPlan.getId());
            this.fieldPlanLayerMapper.insert(fieldPlanLayer);
        });
        List<FieldPlanLayerField> fieldPlanLayerFieldList = this.fieldPlanLayerFieldMapper.selectList(new LambdaQueryWrapper<FieldPlanLayerField>().eq(FieldPlanLayerField::getFieldPlanId,fieldPlan1.getId()));
        fieldPlanLayerFieldList.forEach(fieldPlanLayerField -> {
            fieldPlanLayerField.setFieldPlanId(fieldPlan.getId());
            this.fieldPlanLayerFieldMapper.insert(fieldPlanLayerField);
        });
        return JsonResult.success("导入成功!");
    }
}
