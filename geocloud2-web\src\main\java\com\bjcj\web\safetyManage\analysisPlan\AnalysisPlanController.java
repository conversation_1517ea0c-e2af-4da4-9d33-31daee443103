package com.bjcj.web.safetyManage.analysisPlan;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.analysisPlan.AnalysisPlanDto;
import com.bjcj.model.po.datamodel.DataLayer;
import com.bjcj.model.po.datamodel.DataLayerField;
import com.bjcj.model.po.datamodel.DataLayerFieldAnalysis;
import com.bjcj.model.po.platformManage.analysisPlan.AnalysisPlan;
import com.bjcj.model.po.platformManage.analysisPlan.AnalysisPlanFieldDetail;
import com.bjcj.model.po.platformManage.analysisPlan.AnalysisPlanFieldStatistics;
import com.bjcj.service.datamodel.DataLayerFieldService;
import com.bjcj.service.datamodel.DataLayerService;
import com.bjcj.service.platformManage.analysisPlan.AnalysisPlanFieldDetailService;
import com.bjcj.service.platformManage.analysisPlan.AnalysisPlanFieldStatisticsService;
import com.bjcj.service.platformManage.analysisPlan.AnalysisPlanService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
* 分析展示方案表(public.analysis_plan)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/analysis_plan")
@Tag(name = "5分析展示方案")
@Validated
public class AnalysisPlanController {
    /**
    * 服务对象
    */
    @Resource
    private AnalysisPlanService analysisPlanService;

    @Resource
    private DataLayerService dataLayerService;

    @Resource
    private DataLayerFieldService dataLayerFieldService;

    @Resource
    private AnalysisPlanFieldDetailService analysisPlanFieldDetailService;

    @Resource
    private AnalysisPlanFieldStatisticsService analysisPlanFieldStatisticsService;

    @OperaLog(operaModule = "新增/编辑方案",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "新增/编辑方案")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑方案", description = "新增/编辑方案")
    @ApiOperationSupport(order = 2)
    @Transactional(rollbackFor = Exception.class)
    @RequestLock(prefix = "addOrEdit")
    public JsonResult addOrEdit(@Validated @RequestBody AnalysisPlanDto dto) {
        return analysisPlanService.addOrEdit(dto);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "方案列表", description = "方案列表")
    @ApiOperationSupport(order = 1)
    @GetMapping("/list")
    @Parameters({
            @Parameter(name = "platformAppConfId", description = "平台应用配置id", required = true)
    })
    public JsonResult<List<AnalysisPlan>> list(@RequestParam(value = "platformAppConfId", required = true) Long platformAppConfId) {
        List<AnalysisPlan> analysisPlan = analysisPlanService.list(new LambdaQueryWrapper<AnalysisPlan>().eq(AnalysisPlan::getPlatformAppConfId, platformAppConfId).orderByAsc(AnalysisPlan::getCreateTime));
        analysisPlan.parallelStream().forEach(plan -> {
            if(Objects.nonNull(plan.getFxDataLayerId()))
                plan.setFxLayerName(this.dataLayerService.getOne(new LambdaQueryWrapper<DataLayer>().eq(DataLayer::getId, plan.getFxDataLayerId())).getName());
            if(Objects.nonNull(plan.getBfxDataLayerId()))
                plan.setBfxLayerName(this.dataLayerService.getOne(new LambdaQueryWrapper<DataLayer>().eq(DataLayer::getId, plan.getBfxDataLayerId())).getName());

            if(plan.getPlanType().equals("1"))
                plan.setAnalysisPlanFieldDetailDtos(this.analysisPlanFieldDetailService.list(new LambdaQueryWrapper<AnalysisPlanFieldDetail>().eq(AnalysisPlanFieldDetail::getAnalysisPlanId, plan.getId())));
            if(plan.getPlanType().equals("2") || plan.getPlanType().equals("3")){
                plan.setAnalysisPlanFieldDetailDtos(this.analysisPlanFieldDetailService.list(new LambdaQueryWrapper<AnalysisPlanFieldDetail>().eq(AnalysisPlanFieldDetail::getAnalysisPlanId, plan.getId())));
                plan.setAnalysisPlanFieldStatisticsDtos(this.analysisPlanFieldStatisticsService.list(new LambdaQueryWrapper<AnalysisPlanFieldStatistics>().eq(AnalysisPlanFieldStatistics::getAnalysisPlanId, plan.getId())));
            }
        });
        return JsonResult.success(analysisPlan);
    }

    @OperaLog(operaModule = "删除方案",operaType = OperaLogConstant.DELETE,operaDesc = "删除方案")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除方案", description = "删除方案")
    @ApiOperationSupport(order = 3)
    @Transactional(rollbackFor = Exception.class)
    public JsonResult delete(@PathVariable("id") Long id){
        return this.analysisPlanService.deleteById(id);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "图层字段列表", description = "图层字段列表")
    @ApiOperationSupport(order = 4)
    @GetMapping("/LayerFieldList")
    @Parameters({
            @Parameter(name = "fxDataLayerId", description = "分析图层id", required = true),
            @Parameter(name = "bfxDataLayerId", description = "被分析图层id", required = true)
    })
    public JsonResult<List<DataLayerFieldAnalysis>> LayerFieldList(@RequestParam(value = "fxDataLayerId" ,required = true) Long fxDataLayerId,
                                                                   @RequestParam(value = "bfxDataLayerId" ,required = true) Long bfxDataLayerId) {
        List<DataLayerField> list = this.dataLayerFieldService.list(new LambdaQueryWrapper<DataLayerField>().in(DataLayerField::getDataLayerId, fxDataLayerId, bfxDataLayerId));
        List<DataLayerFieldAnalysis> list1 = new ArrayList<DataLayerFieldAnalysis>(list.size());
        list.parallelStream().forEach(field -> {
            DataLayerFieldAnalysis fieldAnalysis = BeanUtil.copyProperties(field,DataLayerFieldAnalysis.class);
            if(fieldAnalysis.getDataLayerId().compareTo(fxDataLayerId) == 0)
                fieldAnalysis.setFieldSource("来自分析图层:"+dataLayerService.getById(fieldAnalysis.getDataLayerId()).getName());
            else
                fieldAnalysis.setFieldSource("来自被分析图层:"+dataLayerService.getById(fieldAnalysis.getDataLayerId()).getName());
            list1.add(fieldAnalysis);
        });
        return JsonResult.success(list1);
    }

    @OperaLog(operaModule = "导出到...",operaType = OperaLogConstant.CREATE,operaDesc = "传平台id")
    @SaCheckPermission("sys:write")
    @PostMapping("/export")
    @Operation(summary = "分析展示方案导出到...", description = "传平台id")
    @Parameters({
            @Parameter(name = "fromPlatformAppConfId", description = "导出的平台id", required = true),
            @Parameter(name = "toPlatformAppConfId", description = "导入的平台id", required = true)
    })
    @ApiOperationSupport(order = 5)
    @Transactional(rollbackFor = Exception.class)
    public JsonResult exportTo(@RequestParam("fromPlatformAppConfId") Long fromPlatformAppConfId,
                               @RequestParam("toPlatformAppConfId") Long toPlatformAppConfId){
        return this.analysisPlanService.exportTo(fromPlatformAppConfId,toPlatformAppConfId);
    }

}
