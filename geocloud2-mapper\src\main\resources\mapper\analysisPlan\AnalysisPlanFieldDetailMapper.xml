<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.platformManage.analysisPlan.AnalysisPlanFieldDetailMapper">
    <resultMap id="BaseResultMap" type="com.bjcj.model.po.platformManage.analysisPlan.AnalysisPlanFieldDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="analysis_plan_id" jdbcType="BIGINT" property="analysisPlanId"/>
        <result column="field_name" jdbcType="VARCHAR" property="fieldName"/>
        <result column="show_name" jdbcType="VARCHAR" property="showName"/>
        <result column="field_width" jdbcType="VARCHAR" property="fieldWidth"/>
        <result column="is_locate" jdbcType="CHAR" property="isLocate"/>
        <result column="mapping_method" jdbcType="VARCHAR" property="mappingMethod"/>
        <result column="mapping_param" jdbcType="VARCHAR" property="mappingParam"/>
        <result column="source" jdbcType="VARCHAR" property="source"/>
        <result column="detail_type" jdbcType="CHAR" property="detailType"/>
    </resultMap>

</mapper>