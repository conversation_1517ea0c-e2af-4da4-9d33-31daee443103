package com.bjcj.model.dto.datamodel;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/6/27  9:15
*/

/**
    * 查询配置
    */
@Schema(description="分类空间搜索表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "spdp_query")
public class SpdpQueryDto implements Serializable {
    @Schema(description="id")
    @Size(max = 36,message = "max length should less than 36")
    private String queryid;

    @Schema(description="")
    @Size(max = 60,message = "max length should less than 60")
    @NotBlank(message = "is not blank")
    private String name;

    @Schema(description="")
    @Size(max = 100,message = "max length should less than 100")
    @NotBlank(message = "is not blank")
    private String displayname;

    /**
     * 查询模型
     */
    @Schema(description="查询模型")
    @Size(max = 100,message = "查询模型max length should less than 100")
    private String querymodelid;

    /**
     * 参数
     */
    @Schema(description="参数")
    @Size(max = 4000,message = "参数max length should less than 4000")
    private String params;

    private static final long serialVersionUID = 1L;
}