<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.platformManage.fieldPlan.FieldPlanLayerMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.platformManage.fieldPlan.FieldPlanLayer">
    <!--@mbg.generated-->
    <!--@Table public.field_plan_layer-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="is_must" jdbcType="BOOLEAN" property="isMust" />
    <result column="layer_type_id" jdbcType="BIGINT" property="layerTypeId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="field_plan_id" jdbcType="BIGINT" property="fieldPlanId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", show_name, code, is_must, layer_type_id, remark, field_plan_id, create_time, 
    update_time, "operator"
  </sql>

  <update id="editFieldLayerName">
    update field_plan_layer
    <trim prefix="set" suffixOverrides=",">
      <if test="showName != null and showName!=''">
        show_name = #{showName},
      </if>
      <if test="name != null and name!=''">
        name = #{name},
      </if>
    </trim>
    where id = #{id}
  </update>
</mapper>