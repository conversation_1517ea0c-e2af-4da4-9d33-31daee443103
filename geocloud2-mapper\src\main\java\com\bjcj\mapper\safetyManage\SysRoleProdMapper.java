package com.bjcj.mapper.safetyManage;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.safetyManage.SysRoleProd;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 *@Author：qinyi
 *@Date：2023/12/13  15:06
*/
public interface SysRoleProdMapper extends BaseMapper<SysRoleProd> {
    Integer insertBatch(@Param("list") List<SysRoleProd> list);

    void deleteBatch(List<Long> delIdList, Long roleId);

    String selectRoleNameByProductId(@Param("id") Long id);
}