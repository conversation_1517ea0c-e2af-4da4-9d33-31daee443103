package com.bjcj.model.dto.fileResManage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/1/16  16:02
*/

/**
    * 文件资源与数据服务关联表
    */
@Schema(description="文件资源与数据服务关联表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "fileset_cataservice")
public class FilesetCataserviceDto implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    @TableField(value = "cata_service_id")
    @Schema(description="数据服务id  resource_services表的id")
    @NotBlank(message = "is not null")
    private String cataServiceId;

    @TableField(value = "file_set_info_id")
    @Schema(description="文件资源id集合")
    @NotNull(message = "is not null")
    private List<Long> fileSetInfoIdList;

    private static final long serialVersionUID = 1L;
}