package com.bjcj.model.dto.fieldPlan;

import com.baomidou.mybatisplus.annotation.*;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/2/19  17:19
*/

/**
    * 字段展示方案表
    */
@Schema(description="字段展示方案表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "field_plan")
public class FieldPlanDto implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 名称
     */
    @TableField(value = "field_name")
    @Schema(description="名称")
    @Size(max = 50,message = "名称max length should less than 50")
    @NotBlank(message = "名称is not blank")
    @RequestKeyParam(name = "fieldName")
    private String fieldName;

    /**
     * 描述
     */
    @TableField(value = "remark")
    @Schema(description="描述")
    @Size(max = 255,message = "描述max length should less than 255")
    private String remark;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_time",fill = FieldFill.UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    @TableField(value = "operater")
    @Schema(description="")
    @Size(max = 30,message = "max length should less than 30")
    private String operater;

    /**
     * 平台应用配置id
     */
    @TableField(value = "platform_app_conf_id")
    @Schema(description="平台应用配置id")
    @NotNull(message = "平台应用配置id is not null")
    private Long platformAppConfId;

    private static final long serialVersionUID = 1L;
}