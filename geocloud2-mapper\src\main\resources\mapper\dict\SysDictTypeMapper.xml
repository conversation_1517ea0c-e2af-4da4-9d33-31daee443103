<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.dict.SysDictTypeMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.dict.SysDictType">
    <!--@mbg.generated-->
    <!--@Table public.sys_dict_type-->
    <id column="dict_id" jdbcType="BIGINT" property="dictId" />
    <result column="dict_name" jdbcType="VARCHAR" property="dictName" />
    <result column="dict_type" jdbcType="VARCHAR" property="dictType" />
    <result column="status" jdbcType="CHAR" property="status" />
    <result column="operater" jdbcType="VARCHAR" property="operater" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="dict_data_type" jdbcType="CHAR" property="dictDataType" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    dict_id, dict_name, dict_type, "status", operater, create_time, update_time, remark, 
    dict_data_type, group_id
  </sql>
</mapper>