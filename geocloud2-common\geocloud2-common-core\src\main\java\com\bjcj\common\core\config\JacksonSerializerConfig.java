package com.bjcj.common.core.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

/**
 * jackson 序列化配置
 * @Author：qinyi
 * @Date：2023/12/6 15:25
 */
@Configuration
public class JacksonSerializerConfig {

    /**
     * JavaScript 中最大的安全整数 <code>2<sup>53</sup> - 1</code>
     *
     * @see <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Number/MAX_SAFE_INTEGER">MDN - MAX_SAFE_INTEGER</a>
     */
    public static final long MAX_SAFE_INTEGER = 0x1FFFFFFFFFFFFFL;
    /**
     * JavaScript 中最小的安全整数 <code>-(2<sup>53</sup> - 1)</code>
     *
     * @see <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Number/MIN_SAFE_INTEGER">MDN - MIN_SAFE_INTEGER</a>
     */
    public static final long MIN_SAFE_INTEGER = -0x1FFFFFFFFFFFFFL;

    private static final JsonSerializer<Long> SERIALIZER = new JsonSerializer<Long>() {
        @Override
        public void serialize(Long value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            if (value == null) {
                gen.writeNull();
                return;
            }
            // 是否是 JavaScript 安全整数
            boolean isSafeInteger = MIN_SAFE_INTEGER <= value && value <= MAX_SAFE_INTEGER;
            if (isSafeInteger) {
                gen.writeNumber(value);
            } else {
                gen.writeString(value.toString());
            }
        }
    };


    /**
     * 解决JavaScript在 json 反序列化时 long 类型缺失精度问题
     *
     * @return Jackson2ObjectMapperBuilderCustomizer
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {
        return jacksonObjectMapperBuilder -> {
            jacksonObjectMapperBuilder.serializerByType(Long.TYPE, SERIALIZER);
            jacksonObjectMapperBuilder.serializerByType(Long.class, SERIALIZER);
        };
    }
}
