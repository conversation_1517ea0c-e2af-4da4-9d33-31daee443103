package com.bjcj.service.eswPlatform;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.eswPlatform.PlotAllocationInfoMapper;
import com.bjcj.model.po.eswPlatform.PlotAllocationInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 *@Author：qinyi
 *@Date：2024-8-22  15:06
*/
@Service
public class PlotAllocationInfoService extends ServiceImpl<PlotAllocationInfoMapper, PlotAllocationInfo> {

    @Resource
    PlotAllocationInfoMapper plotAllocationInfoMapper;

    public JsonResult<Page<PlotAllocationInfo>> selectpage(Page<PlotAllocationInfo> pager, String type) {
        return JsonResult.success(this.plotAllocationInfoMapper.selectPage(pager, new LambdaQueryWrapper<PlotAllocationInfo>().eq(PlotAllocationInfo::getType, type)));
    }
}
