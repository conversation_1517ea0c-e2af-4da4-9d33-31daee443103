package com.bjcj.common.core.converter;

import org.springframework.core.convert.converter.Converter;
import org.springframework.lang.NonNull;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 日期时间转换器 LocalDateTime
 * <AUTHOR>
 * @date 2023/12/19 16:33 周二
 */
public class LocalDateTimeConverter implements Converter<String, LocalDateTime> {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public LocalDateTime convert(@NonNull String source) {
        return LocalDateTime.parse(source, FORMATTER);
    }
}
