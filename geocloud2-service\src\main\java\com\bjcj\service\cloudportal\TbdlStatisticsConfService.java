package com.bjcj.service.cloudportal;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.mapper.cloudportal.TbdlStatisticsConfMapper;
import com.bjcj.model.po.cloudportal.TbdlStatisticsConf;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 *@Author：qinyi
 *@Date：2024/7/16  9:51
*/
@Service
public class TbdlStatisticsConfService extends ServiceImpl<TbdlStatisticsConfMapper, TbdlStatisticsConf> {

    @Resource
    TbdlStatisticsConfMapper tbdlStatisticsConfMapper;

    public void removeAll() {
        this.tbdlStatisticsConfMapper.deleteAll();
    }
}
