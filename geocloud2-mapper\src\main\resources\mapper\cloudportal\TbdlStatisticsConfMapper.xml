<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.cloudportal.TbdlStatisticsConfMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.cloudportal.TbdlStatisticsConf">
    <!--@mbg.generated-->
    <!--@Table public.tbdl_statistics_conf-->
    <result column="workspaceid" jdbcType="VARCHAR" property="workspaceid" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="xzq_code" jdbcType="VARCHAR" property="xzqCode" />
    <result column="special_plan_id" jdbcType="BIGINT" property="specialPlanId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    workspaceid, "table_name",xzq_code,special_plan_id
  </sql>

  <delete id="deleteAll">
    TRUNCATE TABLE tbdl_statistics_conf;
    </delete>
</mapper>