package com.bjcj.web.naturalresources.function;

import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/12/4 15:27 周一
 */
@Tag(name = "功能服务信息返回参数(暂时无用)",description = "功能服务信息返回参数(暂时无用)")
@ApiSupport(order = 57)
@RestController
@Slf4j
@RequestMapping(value = "/request_result")
public class RequestResultController {

    /*@Resource
    private RequestResultService requestResultService;

    @Operation(summary = "功能服务信息返回参数注册")
    @PostMapping(value = "/save")
    public JsonResult save(@RequestBody @Validated RequestResult requestResult){
        requestResultService.save(requestResult);
        Long id = requestResult.getId();
        return JsonResult.success(id);
    }

    @Operation(summary = "功能服务信息返回参数修改")
    @PostMapping(value = "/update")
    public JsonResult update(@RequestBody @Validated RequestResult requestResult){

        return JsonResult.success(requestResultService.saveOrUpdate(requestResult));
    }

    @Operation(summary = "功能服务信息返回参数删除")
    @GetMapping(value = "/del/{id}")
    public JsonResult del(@PathVariable Long id){

        return requestResultService.del(id);
    }

    @Operation(summary = "功能服务信息返回参数id查询")
    @GetMapping(value = "/findById/{id}")
    public JsonResult<RequestResult> findById(@PathVariable Long id){

        return requestResultService.findById(id);
    }

    @Operation(summary = "功能服务信息返回参数parentid查询")
    @GetMapping(value = "/findByParentId/{parentid}")
    public JsonResult<List<RequestResult>> findByParentId(@PathVariable Long parentid){

        return requestResultService.findByParentId(parentid);
    }*/

}
