package com.bjcj.common.core.config;

import cn.dev33.satoken.stp.StpInterface;
import cn.hutool.core.util.ObjUtil;
import com.bjcj.mapper.safetyManage.SafetyRoleMapper;
import com.bjcj.mapper.safetyManage.SysPermissionMapper;
import com.bjcj.model.po.safetyManage.SafetyRole;
import com.bjcj.model.po.safetyManage.SysPermission;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Optional;

/**
 * 自定义权限加载接口实现类
 *
 * <AUTHOR>
 * @date 2024/8/20 16:35 周二
 */
@Configuration
public class SaTokenPermission implements StpInterface {

    @Resource
    SysPermissionMapper sysPermissionMapper;

    @Resource
    SafetyRoleMapper sysRoleMapper;

    /**
     * 返回一个账号所拥有的权限码集合
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {

        return Optional.ofNullable(sysPermissionMapper.getPermissionByUserId(Long.parseLong(loginId.toString())))
                .orElse(Lists.newArrayList())
                .stream()
                .filter(ObjUtil::isNotEmpty)
                .map(SysPermission::getCode)
                .toList();
    }

    /**
     * 返回一个账号所拥有的角色标识集合 (权限与角色可分开校验)
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        return sysRoleMapper.getRoleByUserId(Long.parseLong(loginId.toString()))
                .stream()
                .map(SafetyRole::getRoleCode)
                .toList();
    }


}
