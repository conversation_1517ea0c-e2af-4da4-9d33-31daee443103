package com.bjcj.model.dto.safetyManage;

import com.baomidou.mybatisplus.annotation.TableField;
import com.bjcj.common.utils.annotation.BlankOrPattern;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
*@Author：qinyi
*@Date：2023/11/27  10:31
*/

@Schema(description="用户表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserDto implements Serializable {

    @Schema(description="")
    private Long id;

    /**
     * 姓名
     */
    @Schema(description="姓名")
    @Size(max = 50,message = "姓名最大长度要小于 50")
    @NotBlank(message = "姓名不能为空")
    @RequestKeyParam(name = "nickName")
    private String nickName;

    /**
     * 用户名
     */
    @Schema(description="用户名")
    @Size(max = 100,message = "用户名最大长度要小于 100")
    @BlankOrPattern(regexp = "^[a-zA-Z0-9]*$",message = "用户名只能是字母或数字")
    private String username;

    /**
     * 密码
     */
    @Schema(description="密码")
    @Size(max = 100,message = "密码最大长度要小于 100")
//    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 注册人
     */
    @Schema(description="注册人")
    @Size(max = 100,message = "注册人最大长度要小于 100")
    private String registerUser;

    /**
     * 机构id
     */
    @Schema(description="机构id")
    @NotNull(message = "机构id不能为null")
    private Long institutionId;

    /**
     * 部门id
     */
    @Schema(description="部门id")
    @NotNull(message = "部门id不能为null")
    private Long deptId;

    /**
     * 显示顺序
     */
    @Schema(description="显示顺序")
    @NotNull(message = "显示顺序不能为null")
    private Integer showSort;

    /**
     * 注册时间
     */
    @Schema(description="注册时间")
    @Size(max = 30,message = "注册时间最大长度要小于 30")
    private String registerTime;

    /**
     * 激活状态0待提交1未激活2审核失败3激活
     */
    @Schema(description="激活状态0待提交1未激活2审核失败3激活")
    private Integer activeStatus;

    /**
     * 角色ids逗号分隔
     */
    @Schema(description="角色ids逗号分隔")
    @Size(max = 255,message = "角色ids逗号分隔最大长度要小于 255")
    private String roleIds;

    /**
     * 访问权限ids逗号分隔
     */
    @Schema(description="访问权限ids逗号分隔")
    @Size(max = 255,message = "访问权限ids逗号分隔最大长度要小于 255")
    private String accessPermissionsIds;

    /**
     * 创建时间
     */
    @Schema(description="创建时间")
    @Size(max = 30,message = "创建时间最大长度要小于 30")
    private String createTime;

    /**
     * 更新时间
     */
    @Schema(description="更新时间")
    @Size(max = 30,message = "更新时间最大长度要小于 30")
    private String updateTime;

    /**
     * 最后操作人
     */
    @Schema(description="最后操作人")
    @Size(max = 100,message = "最后操作人最大长度要小于 100")
    private String operater;

    /**
     * 手机号
     */
    @Schema(description="手机号")
    @Size(max = 20,message = "手机号最大长度要小于 20")
    @BlankOrPattern(regexp = "^1[3-9]\\d{9}$",message = "手机号格式不正确")
    private String telephone;

    /**
     * 办公电话
     */
    @Schema(description="办公电话")
    @Size(max = 20,message = "办公电话最大长度要小于 20")
    @BlankOrPattern(regexp = "^0[1-9]\\d{2}-\\d{7,8}$",message = "办公电话格式不正确")
    private String officePhone;

    /**
     * 邮箱
     */
    @Schema(description="邮箱")
    @Size(max = 50,message = "邮箱最大长度要小于 50")
    @BlankOrPattern(regexp = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$",message = "邮箱格式不正确")
    private String email;

    /**
     * 家庭电话
     */
    @Schema(description="家庭电话")
    @Size(max = 20,message = "家庭电话最大长度要小于 20")
    @BlankOrPattern(regexp = "^0[1-9]\\d{2}-\\d{7,8}$", message = "家庭电话格式不正确")
    private String familyPhone;

    @TableField(value = "is_lock")
    @Schema(description="是否锁定-0否1是")
    private Integer isLock;


    /**
     * 头像
     */
    @TableField(value = "avatar")
    @Schema(description="头像")
    @Size(max = 255,message = "头像最大长度要小于 255")
    private String avatar;

    /**
     * sex
     */
    @TableField(value = "sex")
    @Schema(description="性别")
    @Size(max = 5,message = "性别最大长度要小于 5")
    private String sex;

    /**
     * age
     */
    @TableField(value = "age")
    @Schema(description="年龄")
    @Size(max = 5,message = "年龄最大长度要小于 5")
    private String age;


    /**
     * birthday
     */
    @TableField(value = "birthday")
    @Schema(description="生日")
    @Size(max = 30,message = "生日最大长度要小于 30")
    private String birthday;

    private static final long serialVersionUID = 1L;
}