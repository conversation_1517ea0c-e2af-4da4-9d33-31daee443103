package com.bjcj.model.dto.datamodel;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/4/19  9:38
*/

/**
    * 数据标准
    */
@Schema(description="数据标准")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MetadataDatastandardsDto implements Serializable {
    /**
     * 标识
     */
    @Schema(description="标识")
    private String datastandardid;

    /**
     * 名称
     */
    @Schema(description="名称")
    @Size(max = 60,message = "名称max length should less than 60")
    @NotBlank(message = "名称is not blank")
    private String name;

    /**
     * 描述信息
     */
    @Schema(description="描述信息")
    @Size(max = 150,message = "描述信息max length should less than 150")
    private String description;

    /**
     * 编码
     */
    @Schema(description="编码")
    @Size(max = 20,message = "编码max length should less than 20")
    private String code;

    /**
     * 显示名称
     */
    @Schema(description="显示名称")
    @Size(max = 60,message = "显示名称max length should less than 60")
    @NotBlank(message = "显示名称is not blank")
    private String displayname;

    /**
     * 所属数据类别的标识
     */
    @Schema(description="所属数据类别的标识")
    @Size(max = 36,message = "所属数据类别的标识max length should less than 36")
    private String datacategoryid;

    /**
     * 默认展示方案
     */
    @Schema(description="默认展示方案")
    @Size(max = 36,message = "默认展示方案max length should less than 36")
    private String defaultdisplayschemaid;

    /**
     * 版本号
     */
    @Schema(description="版本号")
    @Size(max = 10,message = "版本号max length should less than 10")
    private String version;

    /**
     * 版准的订立时间
     */
    @Schema(description="版准的订立时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    /**
     * 显示顺序
     */
    @Schema(description="显示顺序")
    @NotNull(message = "显示顺序is not null")
    private Short displayorder;

    private static final long serialVersionUID = 1L;
}