package com.bjcj.model.po.naturalresources.products;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/12/5 9:09 周二
 */
@Schema(description="数据产品管理数据")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "products_data")
public class ProductsData {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="", hidden = true)
    private Long id;

    @NotNull
    @TableField(value = "name")
    @Schema(description="注册名称")
    private String name;

    @NotNull
    @TableField(value = "sname")
    @Schema(description="显示名称")
    private String sname;

    @TableField(value = "district")
    @Schema(description="行政区")
    private String district;

    @NotNull
    @TableField(value = "catalogue")
    @Schema(description="服务目录")
    private String catalogue;

    @TableField(value = "auth_itype")
    @Schema(description="权限类型")
    private int authItype;

    @NotNull
    @TableField(value = "reg_address")
    @Schema(description="注册图件")
    private String regAddress;

    @TableField(value = "access_address")
    @Schema(description="访问地址")
    private String accessAddress;

    @TableField(value = "fw_itype")
    @Schema(description="服务类型")
    private String fwItype;

    @TableField(value = "tag")
    @Schema(description="标签")
    private String tag;

    @TableField(value = "img_url")
    @Schema(description="缩略图")
    private String imgUrl;

    @TableField(value = "description")
    @Schema(description="描述")
    private String description;

    @TableField(value = "keywords")
    @Schema(description="关键字")
    private String keywords;

    @TableField(value = "coordinate")
    @Schema(description="坐标系")
    private String coordinate;

    @TableField(value = "reg_name")
    @Schema(description="注册人")
    private String regName;

    @TableField(value = "status")
    @Schema(description="状态")
    private Boolean status;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "del")
    @Schema(description="删除状态（0未删除，1已删除）")
    private int del;

    @TableField(value = "prov_name")
    @Schema(description="服务提供者名称")
    private String provName;

    @TableField(value = "prov_address")
    @Schema(description="服务提供者地址")
    private String provAddress;

    @TableField(value = "prov_phone")
    @Schema(description="服务提供者联系电话")
    private String provPhone;

    @TableField(value = "prov_email")
    @Schema(description="服务提供者邮箱")
    private String provEmail;

    @TableField(exist = false)
    @Schema(description="被授予的角色")
    private String authFromRoleName;

}
