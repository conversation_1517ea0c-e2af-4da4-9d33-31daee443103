package com.bjcj.model.po.naturalresources.categories;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/11/28 14:14 周二
 */
@Schema(description="物理数据")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "categories_physics")
public class CategoriesPhysics implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="", hidden = true)
    private Long id;

    @TableField(value = "parent_id")
    @Schema(description="父级id")
    private Long parentId;

    @NotBlank(message = "物理数据名称不能为空")
    @TableField(value = "name")
    @Schema(description="名称")
    private String name;

    @TableField(value = "sname")
    @Schema(description="显示名称")
    private String sname;

    @NotBlank(message = "数据类型不能为空")
    @TableField(value = "district")
    @Schema(description="行政区")
    private String district;

    @NotBlank(message = "数据类型不能为空")
    @TableField(value = "standard")
    @Schema(description="数据标准")
    private String standard;

    @NotBlank(message = "数据类型不能为空")
    @TableField(value = "structure")
    @Schema(description="表结构")
    private String structure;

    @NotBlank(message = "数据类型不能为空")
    @TableField(value = "workspace")
    @Schema(description="工作空间")
    private String workspace;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "del")
    @Schema(description="删除状态（0未删除，1已删除）")
    private int del;

}
