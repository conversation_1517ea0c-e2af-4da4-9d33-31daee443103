package com.bjcj.model.vo.naturalresources.categories;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/4/25  9:19
*/

/**
    * 资源项
    */
@Schema(description="地图服务")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResourceServicesVo implements Serializable {
    /**
     * 标识
     */
    @Schema(description="id标识")
    private String id;

    /**
     * 名称
     */
    @Schema(description="名称")
    private String name;

    /**
     * 别名
     */
    @Schema(description="别名")
    private String displayname;

    /**
     * 描述
     */
    @Schema(description="描述")
    private String description;

    /**
     * 原始地址
     */
    @Schema(description="原始地址")
    private String url;

    /**
     * 权限类型
     */
    @Schema(description="权限类型")
    private Short authoritytype;

    /**
     * 状态，0表示待审核，1表示运行中，2表示未通过，3维护中，4处理中，5表示处理失败
     */
    @Schema(description="状态，0表示待审核，1表示运行中，2表示未通过，3维护中，4处理中，5表示处理失败")
    private Short status;

    /**
     * 转发地址
     */
    @Schema(description="转发地址")
    private String transferurl;

    /**
     * 注册人登录名
     */
    @Schema(description="注册人登录名")
    private String registerman;

    /**
     * 注册日期
     */
    @Schema(description="注册日期")
    private LocalDateTime registerdate;

    /**
     * 修改人登录名
     */
    @Schema(description="修改人登录名")
    private String repairman;

    /**
     * 修改日期
     */
    @Schema(description="修改日期")
    private LocalDateTime repairdate;

    /**
     * 版本号
     */
    @Schema(description="版本号")
    private Integer version;

    /**
     * 资源目录id，resource_catalogs的id
     */
    @Schema(description="资源目录id，resource_catalogs的id")
    private String resourcecatalogid;

    /**
     * 服务引擎id
     */
    @Schema(description="服务引擎id")
    private String serviceclusterid;

    /**
     * 缩略图url
     */
    @Schema(description="缩略图url")
    private String thumbnailurl;

    /**
     * 注册类型 1 系统注册,2 门户注册
     */
    @Schema(description="注册类型 1 系统注册,2 门户注册")
    private Short registertype;

    /**
     * 服务缓存类型 1 Dynamic,2 Tiled
     */
    @Schema(description="服务缓存类型 1 Dynamic,2 Tiled")
    private Short cachetype;

    /**
     * 资源类型（小类）如应用服务和数据服务对应的小类有MapServer、WMSServer等；数据产品对应的小类有服务、应用、图件、文档等
     */
    @Schema(description="资源类型（小类）如应用服务和数据服务对应的小类有MapServer、WMSServer等；数据产品对应的小类有服务、应用、图件、文档等")
    private String resourcetype;

    /**
     * 请求类型 1 不需要token,2 需要token
     */
    @Schema(description="请求类型 1 不需要token,2 需要token")
    private Short requesttype;

    /**
     * 评价等级 1-5级
     */
    @Schema(description="评价等级 1-5级")
    private BigDecimal evaluationlevel;

    /**
     * 服务访问量
     */
    @Schema(description="服务访问量")
    private BigDecimal accesscount;

    /**
     * 资源类别。1应用服务；2应用；3文档；4数据；5数据产品；6数据服务；7数据库；8服务器
     */
    @Schema(description="资源类别。1应用服务；2应用；3文档；4数据；5数据产品；6数据服务；7数据库；8服务器")
    private Short resourcecategory;

    /**
     * 注册人所在的组织机构标识
     */
    @Schema(description="注册人所在的组织机构标识")
    private String organizationid;

    /**
     * 数据分组标识
     */
    @Schema(description="数据分组标识")
    private String dataitemid;

    /**
     * 浏览资源时候默认浏览的对象
     */
    @Schema(description="浏览资源时候默认浏览的对象")
    private Short isdefaultbrowser;

    /**
     * 其他参数
     */
    @Schema(description="其他参数")
    private String params;

    /**
     * 切片图层最大比例尺，这个值一般用作静态和动态图层动态切换来用，所以这个比例尺值一般需要比实际最大比例尺小
     */
    @Schema(description="切片图层最大比例尺，这个值一般用作静态和动态图层动态切换来用，所以这个比例尺值一般需要比实际最大比例尺小")
    private BigDecimal titlemaxscale;

    /**
     * 是否按行政区过滤 0 否,1 是
     */
    @Schema(description="是否按行政区过滤 0 否,1 是")
    private Boolean isfilter;

    /**
     * 显示顺序
     */
    @Schema(description="显示顺序")
    private Short displayorder;

    /**
     * 元数据字段
     */
    @Schema(description="元数据字段")
    private Object metadata;

    /**
     * 是否显示
     */
    @Schema(description="是否显示")
    private Boolean isvisiable;

    /**
     * 服务组标识
     */
    @Schema(description="服务组标识")
    private String servicegroupid;

    /**
     * 行政区代码
     */
    @Schema(description="行政区代码")
    private String districtcode;

    /**
     * 行政区名称
     */
    @Schema(description="行政区名称")
    private String districtname;

    /**
     * 服务标识
     */
    @Schema(description="服务标识")
    private String servicetag;

    @Schema(description="服务id")
    private String cateId;

    @Schema(description="角色id")
    private Long roleId;


    private static final long serialVersionUID = 1L;
}