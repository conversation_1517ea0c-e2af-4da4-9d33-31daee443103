package com.bjcj.model.po.naturalresources.categories;

import com.baomidou.mybatisplus.annotation.*;
import com.bjcj.common.utils.mapperType.JsonbTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/4/24  18:18
*/
/**
    * 表信息
    */
@Schema(description="物理数据")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "metadata_tables")
public class MetadataTables implements Serializable {
    /**
     * 标识
     */
    @TableId(value = "tableid", type = IdType.ASSIGN_UUID)
    @Schema(description="标识")
    @Size(max = 36,message = "标识max length should less than 36")
    private String tableid;

    /**
     * 名称
     */
    @TableField(value = "name")
    @Schema(description="名称")
    @Size(max = 60,message = "名称max length should less than 60")
    @NotBlank(message = "名称is not blank")
    private String name;

    /**
     * 显示名称
     */
    @TableField(value = "displayname")
    @Schema(description="显示名称")
    @Size(max = 60,message = "显示名称max length should less than 60")
    private String displayname;

    @TableField(value = "dataitemid")
    @Schema(description="")
    @Size(max = 36,message = "max length should less than 36")
    private String dataitemid;

    @TableField(exist = false)
    @Schema(description="数据项名称")
    private String dataitemName;

    /**
     * 表结构的ID
     */
    @TableField(value = "tablestructureid")
    @Schema(description="表结构的ID")
    @Size(max = 36,message = "表结构的IDmax length should less than 36")
    @NotBlank(message = "表结构的IDis not blank")
    private String tablestructureid;

    @TableField(exist = false)
    @Schema(description="表结构的名称")
    private String tablestructureName;

    /**
     * 记录数
     */
    @TableField(value = "recordcount")
    @Schema(description="记录数")
    private Integer recordcount;

    /**
     * 更新时间
     */
    @TableField(value = "updatetime", fill = FieldFill.UPDATE)
    @Schema(description="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;

    /**
     * 数据表所属的数据类别
     */
    @TableField(value = "datacategoryid")
    @Schema(description="数据表所属的数据类别")
    @Size(max = 36,message = "数据表所属的数据类别max length should less than 36")
    private String datacategoryid;

    /**
     * 数据表所属的数据标准
     */
    @TableField(value = "datastandardid")
    @Schema(description="数据表所属的数据标准")
    @Size(max = 36,message = "数据表所属的数据标准max length should less than 36")
    private String datastandardid;

    /**
     * 工作空间的标识
     */
    @TableField(value = "workspaceid")
    @Schema(description="工作空间的标识")
    @Size(max = 36,message = "工作空间的标识max length should less than 36")
    private String workspaceid;

    /**
     * 行政区代码
     */
    @TableField(value = "districtcode")
    @Schema(description="行政区代码")
    @Size(max = 20,message = "行政区代码max length should less than 20")
    private String districtcode;

    /**
     * 行政区名称
     */
    @TableField(value = "districtname")
    @Schema(description="行政区名称")
    @Size(max = 100,message = "行政区名称max length should less than 100")
    private String districtname;

    /**
     * 带号
     */
    @TableField(value = "zone")
    @Schema(description="带号")
    private Short zone;

    /**
     * 服务器标识
     */
    @TableField(value = "serverid")
    @Schema(description="服务器标识")
    @Size(max = 36,message = "服务器标识max length should less than 36")
    private String serverid;

    /**
     * 元数据字段
     */
    @TableField(value = "metadata", typeHandler = JsonbTypeHandler.class)
    @Schema(description="元数据字段")
    private Object metadata;

    /**
     * 数据分类；0表示外部数据，1表示统计数据，2表示业务数据
     */
    @TableField(value = "datacategory")
    @Schema(description="数据分类；0表示外部数据，1表示统计数据，2表示业务数据")
    private Short datacategory;

    /**
     * 业务分类 ;  0表示土地，1表示地质，2表示矿产，3表示测绘，4表示海洋
     */
    @TableField(value = "businesscategory")
    @Schema(description="业务分类 ;  0表示土地，1表示地质，2表示矿产，3表示测绘，4表示海洋")
    private Short businesscategory;

    /**
     * 显示顺序
     */
    @TableField(value = "displayorder")
    @Schema(description="显示顺序")
    private Short displayorder;

    /**
     * 注册人
     */
    @TableField(value = "registerman")
    @Schema(description="注册人")
    @Size(max = 100,message = "注册人max length should less than 100")
    private String registerman;

    /**
     * 注册时间
     */
    @TableField(value = "registerdate", fill = FieldFill.INSERT)
    @Schema(description="注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registerdate;

    private static final long serialVersionUID = 1L;
}