package com.bjcj.common.utils.annotation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.constraints.Pattern;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Matcher;
import java.util.regex.PatternSyntaxException;

/**
 * @Author：qinyi
 * @Date：2024/3/22 15:50
 * 验证器  当字段不为null且长度>0  才执行正则校验
 */
public class BlankOrPatternValidator implements ConstraintValidator<BlankOrPattern, String> {

    private static final Logger log = LoggerFactory.getLogger(BlankOrPatternValidator.class);
    private java.util.regex.Pattern pattern;

    public BlankOrPatternValidator() {
    }

    @Override
    public void initialize(BlankOrPattern parameters) {
        Pattern.Flag[] flags = parameters.flags();
        int intFlag = 0;
        Pattern.Flag[] arr$ = flags;
        int len$ = flags.length;

        for(int i$ = 0; i$ < len$; ++i$) {
            Pattern.Flag flag = arr$[i$];
            intFlag |= flag.getValue();
        }

        try {
            this.pattern = java.util.regex.Pattern.compile(parameters.regexp(), intFlag);
        } catch (PatternSyntaxException var8) {
            throw var8;
        }
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        if (value == null || value.length() == 0) {
            return true;
        } else {
            Matcher m = this.pattern.matcher(value);
            return m.matches();
        }
    }
}
