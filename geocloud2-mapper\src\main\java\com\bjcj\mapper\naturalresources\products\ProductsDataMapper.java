package com.bjcj.mapper.naturalresources.products;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.model.po.naturalresources.products.ProductsData;
import com.bjcj.model.vo.naturalresources.products.ProductDataVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/5 9:21 周二
 */
public interface ProductsDataMapper extends BaseMapper<ProductsData> {

    @Select("<script>"+
            "select c.id,c.name,c.sname,c.fw_itype,s.product_id,s.role_id from products_data c "+
            " left join sys_role_prod s on s.product_id = c.id and s.role_id = #{roleId} "+
            " where c.del = 0 and c.auth_itype = 1 "+
            "<if test=\"name!=null and name!=''\">"+
            " and c.name like CONCAT('%', #{name}, '%') or c.sname like CONCAT('%', #{name}, '%') "+
            "</if>"+
            "<if test=\"id!=null and id!=''\">"+
            " and c.catalogue like CONCAT('%', #{id}, '%')"+
            "</if>"+
            " <if test=\"auth==1\">"+
            " and s.product_id is not null "+
            " </if>"+
            "</script>")
    IPage<ProductDataVo> listSAuth(@Param("name") String name,@Param("id") String id,@Param("auth") int auth,@Param("roleId") Long roleId, @Param("page") Page<ProductDataVo> page);

    List<ProductsData> selectPageList(@Param("current") Long current, @Param("size") Long size, @Param("searchStr") String searchStr,@Param("productsId") String productsId);

    Long selectListCount(@Param("searchStr") String searchStr,@Param("productsId") String productsId);
}
