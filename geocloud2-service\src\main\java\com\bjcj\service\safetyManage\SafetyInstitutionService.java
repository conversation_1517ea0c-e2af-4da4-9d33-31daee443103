package com.bjcj.service.safetyManage;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.db.sql.Wrapper;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.safetyManage.SafetyDeptMapper;
import com.bjcj.mapper.safetyManage.SafetyInstitutionMapper;
import com.bjcj.mapper.safetyManage.UserMapper;
import com.bjcj.model.dto.safetyManage.SafetyInstitutionDto;
import com.bjcj.model.po.safetyManage.SafetyDept;
import com.bjcj.model.po.safetyManage.SafetyInstitution;
import com.bjcj.model.po.safetyManage.User;
import com.bjcj.model.vo.safetyManage.InstitutionVo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：qinyi
 * @Package：com.bjcj.service.safe
 * @Project：geocloud2
 * @name：SafeService
 * @Date：2023/11/23 10:45
 * @Filename：SafeService
 */
@Service
public class SafetyInstitutionService extends ServiceImpl<SafetyInstitutionMapper, SafetyInstitution> {

    @Resource
    SafetyInstitutionMapper safetyInstitutionMapper;

    @Resource
    SafetyDeptMapper safetyDeptMapper;

    @Resource
    UserMapper userMapper;

    public JsonResult<Page<SafetyInstitution>> pageDataSort(Page<SafetyInstitution> pager,String name,String level,String institutionId) {
        LambdaQueryWrapper<SafetyInstitution> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(level),SafetyInstitution::getLevel,level);
        if(StringUtils.isNotEmpty(name)){
            queryWrapper.and(wrapper -> wrapper.like(SafetyInstitution::getInstitutionName,name)).or().like(SafetyInstitution::getInstitutionShortName,name);
        }
        if(StringUtils.isNotEmpty(institutionId)){
            queryWrapper.eq(SafetyInstitution::getParentId,Long.valueOf(institutionId));
        }else {
            queryWrapper.eq(SafetyInstitution::getParentId,0);
        }
        queryWrapper.orderByAsc(SafetyInstitution::getShowSort);
        return JsonResult.success(safetyInstitutionMapper.selectPage(pager,queryWrapper));
    }

    public JsonResult saveData(SafetyInstitutionDto dto) {
        SafetyInstitution si = BeanUtil.copyProperties(dto,SafetyInstitution.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        si.setOperater(username);
        if(Objects.isNull(dto.getId())){
            si.setCreateTime(sdf.format(new Date()));
            return safetyInstitutionMapper.insert(si) > 0 ? JsonResult.success() : JsonResult.error();
        }else{
            si.setUpdateTime(sdf.format(new Date()));
            return safetyInstitutionMapper.updateById(si) > 0 ? JsonResult.success() : JsonResult.error();
        }
    }

    public JsonResult delData(Long id) {
        LambdaQueryWrapper<SafetyDept> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SafetyDept::getParentInstitutionId,id);
        List<SafetyDept> safetyDeptList = this.safetyDeptMapper.selectList(queryWrapper);
        if(!safetyDeptList.isEmpty()){
            return JsonResult.error("已分配部门,不允许删除");
        }
        List<User> userList = userMapper.selectList(new LambdaQueryWrapper<User>().eq(User::getInstitutionId,id));
        if(!userList.isEmpty()){
            return JsonResult.error("机构下存在用户,不允许删除");
        }
        return safetyInstitutionMapper.deleteById(id) > 0 ? JsonResult.success() : JsonResult.error();
    }

    public JsonResult getInstitution() {

       List<InstitutionVo> institutionVo = new ArrayList<>(List.of());
        List<InstitutionVo> vo = safetyInstitutionMapper.selectList(null).stream().map(
                v-> InstitutionVo.builder()
                        .id(String.valueOf(v.getId()))
                        .name(v.getInstitutionName())
                        .parentId(String.valueOf(v.getParentId()))
                        .children(new ArrayList<>())
                        .build()
        ).collect(Collectors.toList());
        // 2. 构建ID到部门的映射
        Map<String, InstitutionVo> institutionMap = new HashMap<>();
        for (InstitutionVo dept : vo) {
            institutionMap.put(dept.getId(), dept);
        }
        for (InstitutionVo dept : vo) {
            String parentId = dept.getParentId();
            if("0".equals(parentId)){
                institutionVo.add(dept);
            }else {
                InstitutionVo parentDept = institutionMap.get(parentId);
                if (parentDept != null) {
                    parentDept.addChild(dept);
                }
            }
        }
       return JsonResult.success(institutionVo);
    }
}
