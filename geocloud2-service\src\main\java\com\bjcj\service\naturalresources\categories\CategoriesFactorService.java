package com.bjcj.service.naturalresources.categories;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.categories.CategoriesFactorMapper;
import com.bjcj.model.po.naturalresources.categories.CategoriesFactor;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/29 11:31 周三
 */
@Service
public class CategoriesFactorService extends ServiceImpl<CategoriesFactorMapper, CategoriesFactor> {

    @Resource
    private CategoriesFactorMapper categoriesFactorMapper;

    public JsonResult lists(int current, int size){
        Page<CategoriesFactor> page = new Page<>(current, size);
        LambdaUpdateWrapper<CategoriesFactor> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(CategoriesFactor::getDel, 0);
        IPage<CategoriesFactor> iPage = categoriesFactorMapper.selectPage(page, lambdaUpdateWrapper);

        return JsonResult.success(iPage);
    }

    public JsonResult del(Long id){
        LambdaUpdateWrapper<CategoriesFactor> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(CategoriesFactor::getDel, 1)
                .in(CategoriesFactor::getId, id);
        boolean result = this.update(lambdaUpdateWrapper);
        return JsonResult.success(result);
    }

    public JsonResult<List<CategoriesFactor>> findById(Long id){
        LambdaQueryWrapper<CategoriesFactor> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CategoriesFactor::getParentId, id)
                .eq(CategoriesFactor::getDel, 0)
                .orderByDesc(CategoriesFactor::getCreateTime);
        List<CategoriesFactor> list = categoriesFactorMapper.selectList(wrapper);
        return JsonResult.success(list);
    }

}
