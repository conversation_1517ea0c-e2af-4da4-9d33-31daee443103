<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.eswPlatform.SchemeModelsMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.eswPlatform.SchemeModels">
    <!--@mbg.generated-->
    <!--@Table public.scheme_models-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="schemeid" jdbcType="VARCHAR" property="schemeid" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="modelurl" jdbcType="VARCHAR" property="modelurl" />
    <result column="type" jdbcType="VARCHAR" property="type" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", schemeid, content, modelurl, "type"
  </sql>
</mapper>