package com.bjcj.web.cloudportal;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.cloudportal.TbdlStatisticsConf;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlan;
import com.bjcj.service.cloudportal.TbdlStatisticsConfService;
import com.bjcj.service.platformManage.specialPlan.SpecialPlanService;
import com.bjcj.service.system.SysXzq14Service;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
* 图斑地类统计配置(public.tbdl_statistics_conf)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/tbdl_statistics_conf")
@Tag(name = "图斑地类统计配置")
@Validated
public class TbdlStatisticsConfController {
    /**
    * 服务对象
    */
    @Resource
    private TbdlStatisticsConfService tbdlStatisticsConfService;

    @Resource
    SpecialPlanService specialPlanService;

    @Resource
    SysXzq14Service sysXzq14Service;

    @OperaLog(operaModule = "保存",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "保存")
    @PostMapping("save")
    @SaCheckPermission("sys:write")
    @Operation(summary = "保存", description = "保存")
    public JsonResult save(@Validated @RequestBody TbdlStatisticsConf po){
        String code = this.sysXzq14Service.getOnes(po.getXzqId());
        po.setXzqCode(code);
        List<TbdlStatisticsConf> list = this.tbdlStatisticsConfService.list(new LambdaQueryWrapper<TbdlStatisticsConf>().eq(TbdlStatisticsConf::getSpecialPlanId, po.getSpecialPlanId()));
        if(!list.isEmpty()){
            this.tbdlStatisticsConfService.remove(new LambdaQueryWrapper<TbdlStatisticsConf>().eq(TbdlStatisticsConf::getSpecialPlanId, po.getSpecialPlanId()));
        }
        return this.tbdlStatisticsConfService.save(po) ? JsonResult.success() : JsonResult.error();
    }

    @GetMapping("queryOne")
    @SaCheckPermission("sys:read")
    @Operation(summary = "查询", description = "查询")
    @Parameters({
            @Parameter(name = "special_plan_id", description = "专题id", required = true)
    })
    public JsonResult queryOne(@RequestParam(value = "special_plan_id",required = true) String special_plan_id){
        return JsonResult.success(this.tbdlStatisticsConfService.list(new LambdaQueryWrapper<TbdlStatisticsConf>().eq(TbdlStatisticsConf::getSpecialPlanId, Long.parseLong(special_plan_id))));
    }

    @GetMapping("queryByAppName")
    @SaCheckPermission("sys:read")
    @Operation(summary = "根据name查询", description = "根据name查询")
    @Parameters({
            @Parameter(name = "appName", description = "appName", required = true)
    })
    public JsonResult queryByAppName(@RequestParam(value = "appName",required = true) String appName){
        //根据appName查询专题方案id
        Long special_plan_id = this.specialPlanService.getOne(new LambdaQueryWrapper<SpecialPlan>().eq(SpecialPlan::getPlanName, appName)).getId();
        return JsonResult.success(this.tbdlStatisticsConfService.list(new LambdaQueryWrapper<TbdlStatisticsConf>().eq(TbdlStatisticsConf::getSpecialPlanId, special_plan_id)));
    }
}
