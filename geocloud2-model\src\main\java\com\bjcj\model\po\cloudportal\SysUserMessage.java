package com.bjcj.model.po.cloudportal;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/7/29  9:50
*/
/**
    * 消息中心
    */
@Schema(description="消息中心")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_user_message")
public class SysUserMessage implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @TableField(value = "content")
    @Schema(description="")
    @Size(max = 1000,message = "max length should less than 1000")
    private String content;

    @TableField(value = "title")
    @Schema(description="")
    @Size(max = 255,message = "max length should less than 1000")
    private String title;

    /**
     * 发送用户id
     */
    @TableField(value = "send_user_id")
    @Schema(description="发送用户id")
    private Long sendUserId;

    /**
     * 接收用户id
     */
    @TableField(value = "receive_user_id")
    @Schema(description="接收用户id")
    private Long receiveUserId;

    /**
     * 是否阅读
     */
    @TableField(value = "is_read")
    @Schema(description="是否阅读")
    private Boolean isRead;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}