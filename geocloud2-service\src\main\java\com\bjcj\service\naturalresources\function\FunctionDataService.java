package com.bjcj.service.naturalresources.function;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.function.FunctionDataMapper;
import com.bjcj.model.po.naturalresources.function.*;
import com.bjcj.model.vo.naturalresources.function.FunctionDataVo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/30 9:46 周四
 */
@Service
public class FunctionDataService extends ServiceImpl<FunctionDataMapper, FunctionData> {

    @Resource
    private FunctionDataMapper functionDataMapper;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private HttpServletResponse response;

    public JsonResult treeList(){
        LambdaQueryWrapper<FunctionData> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(FunctionData::getParentId, 0)
                .eq(FunctionData::getDel, 0)
                .eq(FunctionData::getStatus, true)
                .orderByAsc(FunctionData::getSort);
        List<FunctionData> functionData = functionDataMapper.selectList(lambdaQueryWrapper);
        List<FunctionDataVo> list = functionData.stream().map(item -> {
            FunctionDataVo functionDataVo = new FunctionDataVo();
            BeanUtils.copyProperties(item, functionDataVo);
            return functionDataVo;
        }).collect(Collectors.toList());

        list.forEach(s -> {
            this.getChildren(s,1);
        });

        return JsonResult.success(list);
    }

    private void getChildren(FunctionDataVo item, int index){
        LambdaQueryWrapper<FunctionData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FunctionData::getParentId, item.getId());
        if(index == 1){
            wrapper.eq(FunctionData::getStatus, true);
        }
        wrapper.eq(FunctionData::getDel, 0);
        wrapper.orderByAsc(FunctionData::getSort);
        //根据parentId查询
        List<FunctionData> list = functionDataMapper.selectList(wrapper);

        List<FunctionDataVo> voList = list.stream().map(p -> {
            FunctionDataVo vo = new FunctionDataVo();
            BeanUtils.copyProperties(p, vo);
            return vo;
        }).collect(Collectors.toList());
        //写入到children
        item.setChildren(voList);

        //如果children不为空，继续往下找
        if (!CollectionUtils.isEmpty(voList)) {
            voList.forEach(s->{
                this.getChildren(s, index);
            });
        }
    }

    public JsonResult lists(){

        LambdaQueryWrapper<FunctionData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FunctionData::getParentId, 0)
                .eq(FunctionData::getDel, 0)
                .orderByAsc(FunctionData::getSort);
        List<FunctionData> functionData = functionDataMapper.selectList(wrapper);
        List<FunctionDataVo> list = functionData.stream().map(item -> {
            FunctionDataVo functionDataVo = new FunctionDataVo();
            BeanUtils.copyProperties(item, functionDataVo);
            return functionDataVo;
        }).collect(Collectors.toList());

        list.forEach(s -> {
            this.getChildren(s,2);
        });

        return JsonResult.success(list);
    }

    public JsonResult del(Long id){
        LambdaUpdateWrapper<FunctionData> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(FunctionData::getDel, 1)
                .eq(FunctionData::getId, id);
        int result = functionDataMapper.update(wrapper);

        return JsonResult.success(result);
    }

    public JsonResult uptStatus(Long id, Boolean status){
        LambdaUpdateWrapper<FunctionData> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(FunctionData::getStatus, status)
                .eq(FunctionData::getId, id);
        int result = functionDataMapper.update(wrapper);
        if(result < 1){
            return JsonResult.error();
        }
        return JsonResult.success();
    }

    public JsonResult exportJson(){

        LambdaQueryWrapper<FunctionData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FunctionData::getDel, 0);
        List<FunctionData> entities = functionDataMapper.selectList(wrapper);
        try {
            // 获取响应输出流
            OutputStream outputStream = response.getOutputStream();

            // 设置响应头
            response.setContentType("application/json");
            response.setHeader("Content-Disposition", "attachment; filename=\"dataServiceCategoriesData.json\"");
            // 将查询到的结果序列化为json格式
            String jsonString = JSON.toJSONString(entities);
            // 将序列化的json数据集写入到输出流中
            outputStream.write(jsonString.getBytes(StandardCharsets.UTF_8));
            // 推送输出流结果到浏览器
            outputStream.flush();
        }catch (Exception e){
            log.error("导出文件失败"+e);
        }

        return JsonResult.success();
    }

    public JsonResult importJson(MultipartFile file){
        List<FunctionData> list = new ArrayList<>();
        try {
            InputStream inputStream = file.getInputStream();
            list = objectMapper.readValue(inputStream, new TypeReference<List<FunctionData>>() {});
            Boolean result = this.saveBatch(list);
            if(!result){
                return JsonResult.error();
            }
        }catch (Exception e) {
            log.error("导入文件失败"+e);
            return JsonResult.error();
        }

        return JsonResult.success();
    }

}
