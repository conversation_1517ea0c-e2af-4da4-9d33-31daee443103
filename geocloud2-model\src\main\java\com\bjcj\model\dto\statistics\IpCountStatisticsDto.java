package com.bjcj.model.dto.statistics;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author：qinyi
 * @Date：2024-8-9 09:34
 */
@Schema(description="ip统计")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IpCountStatisticsDto implements Serializable {
    @Schema(description="总ip数量")
    private Long ipCount;

    @Schema(description="今日ip数量")
    private Long todayIpCount;

}
