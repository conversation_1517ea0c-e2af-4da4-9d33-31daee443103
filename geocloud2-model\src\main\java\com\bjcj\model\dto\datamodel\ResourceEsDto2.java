package com.bjcj.model.dto.datamodel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/6/27  15:13
*/

/**
    * 数据入ES数据库
    */
@Schema(description="数据入ES数据库集合")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResourceEsDto2 implements Serializable {

    private List<ResourceEsDto> dtos;

    private static final long serialVersionUID = 1L;
}