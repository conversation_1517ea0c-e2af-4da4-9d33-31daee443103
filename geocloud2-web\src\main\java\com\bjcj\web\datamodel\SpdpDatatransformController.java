package com.bjcj.web.datamodel;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.datamodel.SpdpDatatransformDto;
import com.bjcj.model.po.datamodel.SpdpDatatransform;
import com.bjcj.service.datamodel.SpdpDatatransformService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
* 数据转换配置(public.spdp_datatransform)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/spdp_datatransform")
@Tag(name = "数据转换")
@Validated
public class SpdpDatatransformController {
    /**
    * 服务对象
    */
    @Resource
    private SpdpDatatransformService spdpDatatransformService;

    @SaCheckPermission("sys:read")
    @GetMapping("/list")
    @Operation(summary = "列表", description = "列表")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "searchStr", description = "searchStr", required = false)
    })
    public JsonResult<List<SpdpDatatransform>> list(@RequestParam(value = "searchStr",required = false) String searchStr){
        if(StringUtils.isNotBlank(searchStr)){
            return JsonResult.success(this.spdpDatatransformService.list(new LambdaQueryWrapper<SpdpDatatransform>().and(i -> i.like(SpdpDatatransform::getName, searchStr).or().like(SpdpDatatransform::getDisplayname, searchStr))));
        }
        return JsonResult.success(this.spdpDatatransformService.list());
    }

    @OperaLog(operaModule = "删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del/{datatransformid}")
    @Operation(summary = "删除", description = "根据id删除")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "delsjzh")
    public JsonResult del(@PathVariable("datatransformid") String datatransformid){
        return this.spdpDatatransformService.removeById(datatransformid) ? JsonResult.success() : JsonResult.error();
    }

    @OperaLog(operaModule = "批量删除",operaType = OperaLogConstant.DELETE,operaDesc = "批量删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delBatch")
    @Operation(summary = "批量删除", description = "根据id批量删除")
    @ApiOperationSupport(order = 3)
    @Parameters({
            @Parameter(name = "datatransformids", description = "ids逗号拼接", required = true)
    })
    @RequestLock(prefix = "delBatchsjzh")
    public JsonResult delBatch(@RequestParam("datatransformids") String datatransformids){
        if(datatransformids.contains(",")){
            //ids转list
            List<String> ids = List.of(datatransformids.split(","));
            return JsonResult.success(this.spdpDatatransformService.removeByIds(ids));
        }else{
            return JsonResult.error("ids格式错误,必须选择一条以上的数据");
        }
    }

    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "注册/编辑", description = "注册/编辑")
    @ApiOperationSupport(order = 4)
    @RequestLock(prefix = "addOrEditsjzh")
    public JsonResult addOrEditsjzh(@Validated @RequestBody SpdpDatatransformDto dto){
        SpdpDatatransform spdpDatatransform = BeanUtil.copyProperties(dto, SpdpDatatransform.class);
        return this.spdpDatatransformService.saveOrUpdate(spdpDatatransform) ? JsonResult.success() : JsonResult.error();
    }

}
