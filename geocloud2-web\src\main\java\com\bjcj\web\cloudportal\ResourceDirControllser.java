package com.bjcj.web.cloudportal;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.jwt.JWTUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.common.utils.enums.FileTypeEnum;
import com.bjcj.model.po.naturalresources.categories.ResourceDataitems;
import com.bjcj.model.po.naturalresources.categories.ResourceServices;
import com.bjcj.model.po.naturalresources.categories.ResourceServices2;
import com.bjcj.model.po.naturalresources.categories.ResourceServices3;
import com.bjcj.model.po.naturalresources.resReview.UserServrReview2;
import com.bjcj.model.po.platformConf.PlatformAppConf;
import com.bjcj.model.po.platformManage.modulePlan.ModulePlan;
import com.bjcj.model.po.platformManage.searchConf.SpecialPlanSearchConf;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlan;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlanDir;
import com.bjcj.model.po.safetyManage.Menu;
import com.bjcj.model.po.safetyManage.SafetyDept;
import com.bjcj.model.po.safetyManage.SysRoleCate;
import com.bjcj.service.naturalresources.categories.*;
import com.bjcj.service.naturalresources.fileResManage.FileInfosService;
import com.bjcj.service.naturalresources.resReview.UserServrReviewService;
import com.bjcj.service.platformConf.PlatformAppConfService;
import com.bjcj.service.platformManage.modulePlan.ModulePlanService;
import com.bjcj.service.platformManage.searchConf.SpecialPlanSearchConfService;
import com.bjcj.service.platformManage.specialPlan.SpecialPlanService;
import com.bjcj.service.safetyManage.MenuService;
import com.bjcj.service.safetyManage.SafetyRoleService;
import com.bjcj.service.safetyManage.SysRoleCateService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * @Author：qinyi
 * @Date：2024/1/10 14:13
 */
@RestController
@RequestMapping("/resourceDir")
@Tag(name = "云门户/资源目录")
@Validated
@SaCheckLogin
public class ResourceDirControllser {
    private static final Logger log = LoggerFactory.getLogger(ResourceDirControllser.class);

    @Resource
    private CategoriesDataService categoriesDataService;

    @Resource
    private CategoriesDataInfoService categoriesDataInfoService;

    @Resource
    private CategoriesServiceService cataMapService;

    @Resource
    private SpecialPlanService specialPlanService;

    @Resource
    private SpecialPlanSearchConfService specialPlanSearchConfService;

    @Resource
    private MenuService menuService;

    @Resource
    private PlatformAppConfService platformAppConfService;

    @Resource
    private ModulePlanService modulePlanService;

    @Resource
    private FileInfosService fileInfosService;

    @Resource
    private SafetyRoleService safetyRoleService;

    @Resource
    private UserServrReviewService userServrReviewService;

    @Resource
    private SysRoleCateService sysRoleCateService;

    @Resource
    private ResourceDataitemsService resourceDataitemsService;

    @Resource
    private ResourceServicesService resourceServicesService;


    @SaCheckPermission("sys:read")
    @GetMapping("/cataData")
    @Operation(summary = "专题资源目录一级分类列表", description = "资源目录一级分类列表")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "appName", description = "专题名称", required = true)
    })
    public JsonResult<List<SpecialPlanDir>> cataData(@RequestParam(value = "appName",required = true) String appName) {
        return JsonResult.success(categoriesDataService.cataDataList(appName));
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/treeListByCataId")
    @Operation(summary = "根据专题一级资源目录获取子节点树", description = "根据一级资源目录获取子节点树")
    @ApiOperationSupport(order = 2)
    @Parameters({
            @Parameter(name = "cataId", description = "一级目录id", required = true),
            @Parameter(name = "searchStr", description = "搜索", required = false),
            @Parameter(name = "appName", description = "专题名称", required = true)
    })
    public JsonResult<List<SpecialPlanDir>> treeListByCataId(@RequestParam(value = "cataId") String cataId,
                                                             @RequestParam(value = "searchStr",required = false) String searchStr,
                                                             @RequestParam(value = "appName",required = true) String appName){
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        List<SpecialPlanDir> cataList = this.categoriesDataService.treeListByCataIdSpecial(cataId,userId,searchStr,appName);
        return JsonResult.success(cataList);
    }

    /**
     * 根据目录id获取资源
     * 暂时没在用
     */
    @SaCheckPermission("sys:read")
    @GetMapping("/cataDataInfoByCataId")
    @Operation(summary = "根据目录id获取资源", description = "根据目录id获取资源")
    @ApiOperationSupport(order = 3)
    @Parameters({
            @Parameter(name = "cataId", description = "一级目录id", required = true)
    })
    public JsonResult<List<ResourceDataitems>> cataDataInfoBycataId(@RequestParam(value = "catalogid") String catalogid){
        return this.resourceDataitemsService.cataDataInfoBycataId(catalogid);
    }

    /**
     * 根据目录id获取资源
     * 暂时没在用
     */
    @SaCheckPermission("sys:read")
    @GetMapping("/allinfoByCataInfoId")
    @Operation(summary = "根据资源id获取配置详情", description = "根据资源id获取配置详情(数据服务,物理数据,要素服务)")
    @ApiOperationSupport(order = 4)
    @Parameters({
            @Parameter(name = "cataInfoId", description = "资源id", required = true)
    })
    public JsonResult<HashMap<String,Object>> allinfoByCataInfoId(@RequestParam(value = "cataInfoId") String dataitemid){
        return this.resourceDataitemsService.allinfoByCataInfoId(dataitemid);
    }

    // todo 先不做,前端可处理
    /**
    @SaCheckPermission("sys:read")
    @GetMapping("/resourceDir")
    @Operation(summary = "鼠标悬停显示资源目录", description = "鼠标悬停显示资源目录(数据服务,功能服务,数据产品)")
    @ApiOperationSupport(order = 5)
    public JsonResult<HashMap<String,List<Object>>> resourceDir(){
        return this.categoriesDataService.resourceDir();
    }
    */


    @SaCheckPermission("sys:read")
    @GetMapping("/serviceList")
    @Operation(summary = "目录id查服务列表", description = "排序(名称,浏览量,时间);根据比例尺，发布单位，时间，列表筛选")
    @ApiOperationSupport(order = 5)
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "cataDataId", description = "资源目录id", required = true),
            @Parameter(name = "sort", description = "排序(sname,views_count,create_time)", required = false),
            @Parameter(name = "order", description = "顺序(asc,desc)", required = false),
            @Parameter(name = "startYear", description = "开始年份", required = false),
            @Parameter(name = "endYear", description = "结束年份", required = false),
            @Parameter(name = "scale", description = "比例尺", required = false),
            @Parameter(name = "publishInstitutionName", description = "发布单位", required = false)
    })
    public  JsonResult<Page<ResourceServices2>> serviceList(@RequestParam("current") Integer page,
                                                            @RequestParam("size") Integer pageSize,
                                                            @RequestParam("cataDataId") String cataDataId,
                                                            @RequestParam(value = "sort",required = false) String sort,
                                                            @RequestParam(value = "order",required = false) String order,
                                                            @RequestParam(value = "startYear",required = false) String startYear,
                                                            @RequestParam(value = "endYear",required = false) String endYear,
                                                            @RequestParam(value = "scale",required = false) String scale,
                                                            @RequestParam(value = "publishInstitutionName",required = false) String publishInstitutionName){
        Page<SafetyDept> pager = new Page<>(page, pageSize);
        return this.categoriesDataService.serviceList(cataDataId, pager, sort, order, startYear, endYear, scale,publishInstitutionName);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/queryServiceCountByCataId")
    @Operation(summary = "根据数据服务目录id查询其下服务数量", description = "有效的服务数量")
    @ApiOperationSupport(order = 6)
    @Parameters({
            @Parameter(name = "cataDataId", description = "数据服务id", required = true),
    })
    public JsonResult<Integer> queryServiceCountByCataId(@RequestParam("cataDataId") String cataDataId){
        return this.categoriesDataService.queryServiceCountByCataId(cataDataId);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/queryServiceCountByCataId2")
    @Operation(summary = "根据功能服务目录id查询其下服务数量", description = "有效的服务数量")
    @ApiOperationSupport(order = 6)
    @Parameters({
            @Parameter(name = "cataDataId", description = "数据服务id", required = true)
    })
    public JsonResult<Integer> queryServiceCountByCataId2(@RequestParam("cataDataId") String cataDataId){
        return this.categoriesDataService.queryServiceCountByCataId2(cataDataId);
    }


    @SaCheckPermission("sys:read")
    @GetMapping("/serviceLists")
    @Operation(summary = "目录id查服务列表(数据资源列表用)", description = "排序(名称,浏览量,时间);根据比例尺，发布单位，时间，列表筛选")
    @ApiOperationSupport(order = 7)
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "cataDataId", description = "资源目录id", required = true),
            @Parameter(name = "sort", description = "排序(sname,views_count,create_time)", required = false),
            @Parameter(name = "order", description = "顺序(asc,desc)", required = false),
            @Parameter(name = "startYear", description = "开始年份", required = false),
            @Parameter(name = "endYear", description = "结束年份", required = false),
            @Parameter(name = "scale", description = "比例尺", required = false),
            @Parameter(name = "publishInstitutionName", description = "发布单位", required = false)
    })
    public  JsonResult<Page<ResourceServices2>> serviceLists(@RequestParam("current") Integer page,
                                                             @RequestParam("size") Integer pageSize,
                                                             @RequestParam("cataDataId") String cataDataId,
                                                             @RequestParam(value = "sort",required = false) String sort,
                                                             @RequestParam(value = "order",required = false) String order,
                                                             @RequestParam(value = "startYear",required = false) String startYear,
                                                             @RequestParam(value = "endYear",required = false) String endYear,
                                                             @RequestParam(value = "scale",required = false) String scale,
                                                             @RequestParam(value = "publishInstitutionName",required = false) String publishInstitutionName){
        Page<SafetyDept> pager = new Page<>(page, pageSize);
        return this.categoriesDataService.serviceLists(cataDataId, pager, sort, order, startYear, endYear, scale,publishInstitutionName);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/serviceLists2")
    @Operation(summary = "目录id查服务列表(门户功能服务列表用)", description = "排序(名称,浏览量,时间);根据比例尺，发布单位，时间，名称,列表筛选")
    @ApiOperationSupport(order = 7)
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "cataDataId", description = "资源目录id", required = true),
            @Parameter(name = "sort", description = "排序(sname,views_count,create_time)", required = false),
            @Parameter(name = "order", description = "顺序(asc,desc)", required = false),
            @Parameter(name = "startYear", description = "开始年份", required = false),
            @Parameter(name = "endYear", description = "结束年份", required = false),
            @Parameter(name = "scale", description = "比例尺", required = false),
            @Parameter(name = "publishInstitutionName", description = "发布单位", required = false),
            @Parameter(name = "name", description = "名称", required = false)
    })
    public  JsonResult<Page<ResourceServices2>> serviceLists2(@RequestParam("current") Integer page,
                                                             @RequestParam("size") Integer pageSize,
                                                             @RequestParam("cataDataId") String cataDataId,
                                                             @RequestParam(value = "sort",required = false) String sort,
                                                             @RequestParam(value = "order",required = false) String order,
                                                             @RequestParam(value = "startYear",required = false) String startYear,
                                                             @RequestParam(value = "endYear",required = false) String endYear,
                                                             @RequestParam(value = "scale",required = false) String scale,
                                                             @RequestParam(value = "publishInstitutionName",required = false) String publishInstitutionName,
                                                              @RequestParam(value = "name",required = false) String name){
        Page<SafetyDept> pager = new Page<>(page, pageSize);
        return this.categoriesDataService.serviceLists2(cataDataId, pager, sort, order, startYear, endYear, scale,publishInstitutionName,name);
    }


    @OperaLog(operaModule = "使用服务增加使用量",operaType = OperaLogConstant.UPDATE,operaDesc = "使用服务增加使用量")
    @SaCheckPermission("sys:write")
    @PostMapping("/addUseCount")
    @Operation(summary = "使用服务增加使用量", description = "使用服务增加使用量")
    @ApiOperationSupport(order = 8)
    @Parameters({
            @Parameter(name = "resourceServiceId", description = "mapserver服务id", required = true)
    })
    public JsonResult addUseCount(@RequestParam("resourceServiceId") String resourceServiceId){
        LambdaUpdateWrapper<ResourceServices> wrapper = new LambdaUpdateWrapper<ResourceServices>();
        wrapper.eq(ResourceServices::getId, resourceServiceId).setSql("accesscount = accesscount + 1 , last_use_time = '" + LocalDateTime.now() + "'");
        return this.resourceServicesService.update(wrapper) ? JsonResult.success() : JsonResult.error();
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/hotData")
    @Operation(summary = "热门数据", description = "xx天内热门数据,不传显示总热门")
    @ApiOperationSupport(order = 9)
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "days", description = "xx天内", required = false)
    })
    public JsonResult<Page<ResourceServices3>> hotData(@RequestParam("current") Integer page,
                                                       @RequestParam("size") Integer pageSize,
                                                       @RequestParam(value = "days",required = false) Integer days){
        Page<ResourceServices3> pager = new Page<>(page, pageSize);
        return this.cataMapService.hotData(pager,days);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/newData")
    @Operation(summary = "最新数据", description = "xx天内最新数据,不传显示总最新")
    @ApiOperationSupport(order = 10)
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "days", description = "xx天内", required = false)
    })
    public JsonResult<Page<ResourceServices3>> newData(@RequestParam("current") Integer page,
                                                        @RequestParam("size") Integer pageSize,
                                                        @RequestParam(value = "days",required = false) Integer days){
        Page<ResourceServices3> pager = new Page<>(page, pageSize);
        return this.cataMapService.newData(pager,days);
    }



    @SaCheckPermission("sys:read")
    @GetMapping("/searchConfList")
    @Operation(summary = "查询配置列表", description = "按专题名查询")
    @ApiOperationSupport(order = 11)
    @Parameters({
            @Parameter(name = "specialPlanName", description = "专题名", required = true)
    })
    public JsonResult<List<SpecialPlanSearchConf>> searchConfList(@RequestParam("specialPlanName") String specialPlanName){
        SpecialPlan specialPlan = this.specialPlanService.getOne(new LambdaQueryWrapper<SpecialPlan>()
                .eq(SpecialPlan::getPlanName, specialPlanName));
        return JsonResult.success(this.specialPlanSearchConfService.list(new LambdaQueryWrapper<SpecialPlanSearchConf>()
                .eq(SpecialPlanSearchConf::getSpecialPlanId,specialPlan.getId())));
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/specialPlanTree")
    @Operation(summary = "专题门户菜单树", description = "专题门户菜单树")
    @ApiOperationSupport(order = 12)
    @Parameters({
            @Parameter(name = "specialPlanName", description = "专题名", required = true)
    })
    public JsonResult<List<Menu>> specialPlanTree(@RequestParam("specialPlanName") String specialPlanName){
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        List<Menu> menuList = this.menuService.selectMenuListSpecialPlan(specialPlanName, userId);
        return JsonResult.success(menuList);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/systemConfig")
    @Operation(summary = "专题名称查询系统配置", description = "专题名称查询系统配置")
    @ApiOperationSupport(order = 13)
    @Parameters({
            @Parameter(name = "specialPlanName", description = "专题名", required = true)
    })
    public JsonResult systemConfig(@RequestParam("specialPlanName") String specialPlanName){
        return JsonResult.build(200, "查询成功", this.platformAppConfService.getOne(new LambdaQueryWrapper<PlatformAppConf>()
                .eq(PlatformAppConf::getAppName,specialPlanName)).getSystemConfigJson());
    }

    @GetMapping("/systemConfig2")
    @Operation(summary = "专题名称查询系统配置2", description = "专题名称查询系统配置")
    @ApiOperationSupport(order = 133)
    @Parameters({
            @Parameter(name = "specialPlanName", description = "专题名", required = true)
    })
    public JsonResult systemConfig2(@RequestParam("specialPlanName") String specialPlanName){
        return JsonResult.build(200, "查询成功", this.platformAppConfService.getOne(new LambdaQueryWrapper<PlatformAppConf>()
                .eq(PlatformAppConf::getAppName,specialPlanName)).getSystemConfigJson());
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/modulePlanTree")
    @Operation(summary = "专题名称查询组件树", description = "专题名称查询组件树")
    @ApiOperationSupport(order = 14)
    @Parameters({
            @Parameter(name = "specialPlanName", description = "专题名", required = true),
            @Parameter(name = "moduleType", description = "组件类型", required = false)
    })
    public JsonResult<List<ModulePlan>> tree(@RequestParam("specialPlanName") String specialPlanName,
                                             @RequestParam(value = "moduleType", required = false) String moduleType){
        Long platformAppConfId = this.platformAppConfService.getOne(new LambdaQueryWrapper<PlatformAppConf>()
                .eq(PlatformAppConf::getAppName,specialPlanName)).getId();
        return JsonResult.success(this.modulePlanService.tree(platformAppConfId,moduleType));
    }


    @OperaLog(operaModule = "导入范围",operaType = OperaLogConstant.CREATE,operaDesc = "导入范围-支持zip/txt/cad")
    @SaCheckPermission("sys:write")
    @Operation(summary = "导入范围", description = "导入范围-支持zip/txt/cad")
    @ApiOperationSupport(order = 15)
    @Parameters({
            @Parameter(name = "file", description = "文件流", required = true, ref = "file")
    })
    @PostMapping("/file2json")
    public JsonResult shp2json(@RequestParam("file") MultipartFile file) throws Exception {
        String filename = file.getOriginalFilename();
        String suf = StrUtil.subSuf(filename, filename.lastIndexOf(".") + 1);
        FileTypeEnum fileTypeEnum = FileTypeEnum.ofSuf(suf);
        switch (fileTypeEnum) {
            case PACKAGE:
                return fileInfosService.shpZip2GeoJson(file);
            case TXT:
                return fileInfosService.txt2GeoJsonByGuo(file);
            case CAD:
                return fileInfosService.cad2GeoJsonByGuo(file);
            case EXCEL:
                // return fileInfoService.excel2GeoJson(file);
                return JsonResult.error("不支持的文件类型 EXCEL");
            case SHP:
                return fileInfosService.shp2GeoJson(file);
            default:
                return JsonResult.error("不支持的文件类型");
        }
    }


    @SaCheckPermission("sys:read")
    @RequestMapping("/goServer/{serverid}/**")
    @Operation(summary = "地图服务地址虚拟化", description = "地图服务地址虚拟化")
    @ApiOperationSupport(order = 16)
    @Parameters({
            @Parameter(name = "f", description = "参数", required = false)
    })
    public Object tree(HttpServletRequest request, HttpServletResponse response,
                           @PathVariable("serverid") String serverid,
                           @RequestParam(value = "f", required = false) String f) throws IOException {
        String requestUrl = request.getRequestURL().toString();

        // if (!requestUrl.contains("Query")) {
        //     requestUrl += "/Query";
        // }
        log.info("requestUrl:" + requestUrl);
        String uri = "";
        if(requestUrl.split("mapServer").length > 1){
            int i = requestUrl.indexOf("mapServer") + 9;
            int j = requestUrl.length();
            uri = requestUrl.substring(i,j);
        }

        String params = "?";
        // 获取所有请求参数的名称
        Enumeration<String> parameterNames = request.getParameterNames();
        // 遍历参数名称并获取对应的值
        while (parameterNames.hasMoreElements()) {
            String paramName = parameterNames.nextElement();
            String paramValue = request.getParameter(paramName);
            log.info("Parameter Name: " + paramName + ", Value: " + paramValue);
            params += paramName + "=" + URLEncoder.encode(paramValue, "UTF-8") + "&";
        }
        params = params.substring(0,params.length()-1);

        if(Objects.isNull(this.resourceServicesService.getById(serverid))) {
            return JsonResult.error("服务不存在");
        }
        //查询用户数据权限
        boolean userPerm = false;
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        List<Long> roleIds = this.safetyRoleService.queryUserRoleId(userId);
        //查询是否公开数据
        if(this.resourceServicesService.getById(serverid).getAuthoritytype() == 1){
            userPerm = true;
        }

        //查询主动申请是否有权限
        List<UserServrReview2> list = this.userServrReviewService.selectListMap(userId,serverid);
        if(!list.isEmpty()) {
            userPerm = true;
        }

        //查询是否被动授权
        List<SysRoleCate> list1 = this.sysRoleCateService.list(new LambdaQueryWrapper<SysRoleCate>()
                .in(SysRoleCate::getRoleId, roleIds)
                .eq(SysRoleCate::getCateId, serverid));
        if(!list1.isEmpty()) {
            userPerm = true;
        }
        if(userPerm){
            //请求转发
            ResourceServices servicesServiceById = resourceServicesService.getById(serverid);
            String url = servicesServiceById.getUrl();
            if(uri.length() != 0){
                url = url + uri;
            }
            if(params.length() > 2){
                url = url + params;
            }
            log.info("finalUrl==========" + url);

            // 发起请求
            HttpResponse execute = HttpRequest.get(url).execute();

            // 是否切片服务
            boolean isSlice = StrUtil.equals(servicesServiceById.getMapType(), "arcgis_tile")
                    || StrUtil.equals(servicesServiceById.getMapType(), "tile");
            if (isSlice) {
                return ResponseEntity.ok()
                        .contentType(MediaType.IMAGE_JPEG)
                        .body(execute.bodyBytes());
            } else {
                return JSON.parse(execute.body());
            }
        } else {
            return JsonResult.error("您没有权限访问该服务");
        }
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/allLabels")
    @Operation(summary = "标签", description = "标签")
    @ApiOperationSupport(order = 17)
    public JsonResult<String> allLabels(){
        return this.cataMapService.allLabels();
    }


    @SaCheckPermission("sys:read")
    @GetMapping("/labelServiceLists")
    @Operation(summary = "标签查服务列表(数据资源列表用)", description = "排序(名称,浏览量,时间);根据比例尺，发布单位，时间，列表筛选")
    @ApiOperationSupport(order = 18)
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "label", description = "标签", required = true),
            @Parameter(name = "sort", description = "排序(sname,views_count,create_time)", required = false),
            @Parameter(name = "order", description = "顺序(asc,desc)", required = false),
            @Parameter(name = "startYear", description = "开始年份", required = false),
            @Parameter(name = "endYear", description = "结束年份", required = false),
            @Parameter(name = "scale", description = "比例尺", required = false),
            @Parameter(name = "publishInstitutionName", description = "发布单位", required = false)
    })
    public  JsonResult<Page<ResourceServices2>> labelServiceLists(@RequestParam("current") Integer page,
                                                              @RequestParam("size") Integer pageSize,
                                                              @RequestParam("label") String label,
                                                              @RequestParam(value = "sort",required = false) String sort,
                                                              @RequestParam(value = "order",required = false) String order,
                                                              @RequestParam(value = "startYear",required = false) String startYear,
                                                              @RequestParam(value = "endYear",required = false) String endYear,
                                                              @RequestParam(value = "scale",required = false) String scale,
                                                              @RequestParam(value = "publishInstitutionName",required = false) String publishInstitutionName){
        Page<SafetyDept> pager = new Page<>(page, pageSize);
        return this.categoriesDataService.labelServiceLists(pager, sort,label, order, startYear, endYear, scale,publishInstitutionName);
    }

}
