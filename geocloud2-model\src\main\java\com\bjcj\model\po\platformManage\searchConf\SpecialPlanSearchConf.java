package com.bjcj.model.po.platformManage.searchConf;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/3/6  11:40
*/
/**
    * 专题查询配置表
    */
@Schema(description="专题查询配置表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "special_plan_search_conf")
public class SpecialPlanSearchConf implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * mapserver服务id
     */
    @TableField(value = "categories_service_id")
    @Schema(description="mapserver服务id")
    @NotNull(message = "mapserver服务idis not null")
    private String categoriesServiceId;

    /**
     * 专题id
     */
    @TableField(value = "special_plan_id")
    @Schema(description="专题id")
    @NotNull(message = "专题idis not null")
    private Long specialPlanId;

    /**
     * 显示名称
     */
    @TableField(value = "cata_sname")
    @Schema(description="显示名称")
    @Size(max = 255,message = "显示名称max length should less than 255")
    private String cataSname;

    /**
     * 显示顺序
     */
    @TableField(value = "show_sort")
    @Schema(description="显示顺序")
    private Integer showSort;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "operator")
    @Schema(description="")
    @Size(max = 50,message = "max length should less than 50")
    private String operator;

    @TableField(value = "address")
    @Schema(description="服务地址")
    private String address;

    private static final long serialVersionUID = 1L;
}