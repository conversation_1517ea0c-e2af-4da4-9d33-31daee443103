package com.bjcj.model.dto.analysisPlan;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/6/7  10:26
*/

/**
    * 分析项表
    */
@Schema(description="分析方案与分析项")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AnalysisAndOrdinaryItemDto implements Serializable {
    @Schema(description="方案id")
    private Long id;

    /**
     * 方案名称
     */
    @Schema(description="方案名称")
    @NotBlank(message = "方案名称idis not null")
    private String planName;


    /**
     * 方案描述
     */
    @Schema(description="方案描述")
    private String remark;

    /**
     * 方案类型
     */
    @Schema(description="方案类型:自定义,普通分析,重叠分析,规划分析,基本农田")
    @NotBlank(message = "方案名称idis not null")
    private String planType;

    @Schema(description="plantype 4以上的为jsoe适配,要填写此字段")
    private String jsonPlan;

    /**
     * 方案类型
     */
    @Schema(description="平台应用配置id")
    @NotNull(message = "平台应用配置ididis not null")
    private Long platformAppConfId;

    @Schema(description="分析项集合")
    private List<AnalysisOrdinaryItemDto> analysisOrdinaryItemDtoList;

    private static final long serialVersionUID = 1L;
}