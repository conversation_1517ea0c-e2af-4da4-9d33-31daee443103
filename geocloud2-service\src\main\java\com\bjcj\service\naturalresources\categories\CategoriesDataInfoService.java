package com.bjcj.service.naturalresources.categories;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.categories.*;
import com.bjcj.mapper.naturalresources.resReview.UserServrReviewMapper;
import com.bjcj.mapper.safetyManage.*;
import com.bjcj.mapper.system.SysXzq14Mapper;
import com.bjcj.model.po.naturalresources.categories.*;
import com.bjcj.model.po.safetyManage.SafetyRole;
import com.bjcj.model.po.safetyManage.SafetyUserRole;
import com.bjcj.model.po.safetyManage.SysRoleCate;
import com.bjcj.model.po.safetyManage.User;
import com.bjcj.model.vo.naturalresources.categories.CategoriesDataInfoVo;
import com.bjcj.model.vo.naturalresources.categories.CategoriesDataInfoVo2;
import com.bjcj.model.vo.naturalresources.categories.CategoriesVo;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/11/28 10:05 周二
 */
@Service
public class CategoriesDataInfoService extends ServiceImpl<CategoriesDataInfoMapper, CategoriesDataInfo> {

    @Resource
    private CategoriesDataInfoMapper categoriesDataInfoMapper;

    @Resource
    private CategoriesServiceMapper categoriesServiceMapper;

    @Resource
    private CategoriesPhysicsMapper categoriesPhysicsMapper;

    @Resource
    private CategoriesFactorMapper categoriesFactorMapper;

    @Resource
    private CategoriesMetaMapper categoriesMetaMapper;

    @Resource
    private SafetyUserRoleMapper safetyUserRoleMapper;

    @Resource
    private SysRoleCateMapper sysRoleCateMapper;

    @Resource
    private UserMapper userMapper;
    @Resource
    private SafetyRoleMapper safetyRoleMapper;

    @Resource
    private SafetyInstitutionMapper institutionMapper;

    @Resource
    private UserServrReviewMapper userServrReviewMapper;

    @Resource
    private CategoriesDataMapper dataServiceMapper;

    @Resource
    private SysXzq14Mapper xzq14Mapper;

    @Resource
    ResourceServicesMapper resourceServicesMapper;

    @Resource
    ResourceDataitemsMapper resourceDataitemsMapper;


    public JsonResult<IPage<CategoriesDataInfoVo>> treeList(int index, String name, String id, int current, int size){
        Page<CategoriesDataInfo> page = new Page<>(current, size);
        IPage<CategoriesDataInfoVo> list = new Page<>(current, size);
        if(index == 1 || index == 2){
            LambdaQueryWrapper<CategoriesDataInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.like(CategoriesDataInfo::getCatalogue, id);
            if(StringUtils.isNotBlank(name)){
                wrapper.like(CategoriesDataInfo::getName, name);
            }
            wrapper.eq(CategoriesDataInfo::getDel, 0)
                    .orderByDesc(CategoriesDataInfo::getCreateTime);
            list = results(page, wrapper);

        }else{
            LambdaQueryWrapper<CategoriesPhysics> lambda1 = new LambdaQueryWrapper<>();
            lambda1.eq(CategoriesPhysics::getName, name)
                    .or().eq(CategoriesPhysics::getSname, name);
            List<CategoriesPhysics> categoriesServices = categoriesPhysicsMapper.selectList(lambda1);
            if(categoriesServices.isEmpty()){
                return JsonResult.success(list);
            }
            StringJoiner ids = new StringJoiner(",");
            categoriesServices.stream().forEach(item->{
                ids.add(item.getParentId().toString());
            });

            LambdaQueryWrapper<CategoriesDataInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.like(CategoriesDataInfo::getCatalogue, id)
                    .in(CategoriesDataInfo::getId, ids)
                    .eq(CategoriesDataInfo::getDel, 0)
                    .orderByDesc(CategoriesDataInfo::getCreateTime);
            list = results(page, wrapper);
        }

        //设置数据注册审核状态
        // list.getRecords().parallelStream().forEachOrdered(item->{
        //     Integer reviewStatus = 0;
        //     List<UserServrReview2> userServrReview2List = this.userServrReviewMapper.selectByServiceId(item.getId());
        //     if(!userServrReview2List.isEmpty()){
        //         reviewStatus = userServrReview2List.get(0).getReviewStatus();
        //     }
        //     item.setReviewStatus(reviewStatus);
        // });
        list.getRecords().forEach(item -> {
            Long district = Long.valueOf(item.getDistrict());
            item.setXzq(this.xzq14Mapper.selectById(district).getName());
        });
        return JsonResult.success(list);
    }

    public IPage results(Page page,LambdaQueryWrapper wrapper){
        IPage<CategoriesDataInfo> categoriesDataInfo =  categoriesDataInfoMapper.selectPage(page, wrapper);

        IPage<CategoriesDataInfoVo> convertPage = categoriesDataInfo.convert(result -> {
            CategoriesDataInfoVo obj = new CategoriesDataInfoVo();
            BeanUtils.copyProperties(result, obj);
            return obj;
        });
        convertPage.getRecords().stream().forEach(item->{

            LambdaQueryWrapper<CategoriesService> lambda1 = new LambdaQueryWrapper<>();
            lambda1.eq(CategoriesService::getParentId, item.getId())
                    .eq(CategoriesService::getDel, 0)
                    .eq(CategoriesService::getShow, 1);
            List<CategoriesService> categoriesService = categoriesServiceMapper.selectList(lambda1);
            //分组
            // Map<String, List<CategoriesService>> collect = categoriesService.stream().collect(Collectors.groupingBy(CategoriesService::getFwgroup));

            item.setCategoriesService(categoriesService);

            LambdaQueryWrapper<CategoriesPhysics> lambda2 = new LambdaQueryWrapper<>();
            lambda2.eq(CategoriesPhysics::getParentId, item.getId())
                    .eq(CategoriesPhysics::getDel, 0);
            List<CategoriesPhysics> categoriesPhysics = categoriesPhysicsMapper.selectList(lambda2);

            item.setCategoriesPhysics(categoriesPhysics);

            LambdaQueryWrapper<CategoriesFactor> lambda3 = new LambdaQueryWrapper<>();
            lambda3.eq(CategoriesFactor::getParentId, item.getId())
                    .eq(CategoriesFactor::getDel, 0);
            List<CategoriesFactor> categoriesFactor = categoriesFactorMapper.selectList(lambda3);

            item.setCategoriesFactor(categoriesFactor);

            LambdaQueryWrapper<CategoriesMeta> lambda4 = new LambdaQueryWrapper<>();
            lambda4.eq(CategoriesMeta::getParentId, item.getId());
            CategoriesMeta categoriesMeta = categoriesMetaMapper.selectOne(lambda4);

            item.setCategoriesMeta(categoriesMeta);

        });

        return convertPage;
    }

    public JsonResult<CategoriesVo> findById(Long id){
        CategoriesVo categoriesVo = new CategoriesVo();

        CategoriesDataInfo categoriesDataInfo = categoriesDataInfoMapper.selectById(id);
        LambdaQueryWrapper<CategoriesService> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(CategoriesService::getParentId, id)
                .eq(CategoriesService::getDel,0);
        List<CategoriesService> categoriesServices = categoriesServiceMapper.selectList(wrapper1);

        LambdaQueryWrapper<CategoriesPhysics> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(CategoriesPhysics::getParentId, id)
                .eq(CategoriesPhysics::getDel,0);
        List<CategoriesPhysics> categoriesPhysics = categoriesPhysicsMapper.selectList(wrapper2);

        LambdaQueryWrapper<CategoriesFactor> wrapper3 = new LambdaQueryWrapper<>();
        wrapper3.eq(CategoriesFactor::getParentId, id)
                .eq(CategoriesFactor::getDel,0);
        List<CategoriesFactor> categoriesFactors = categoriesFactorMapper.selectList(wrapper3);

        LambdaQueryWrapper<CategoriesMeta> wrapper4 = new LambdaQueryWrapper<>();
        wrapper4.eq(CategoriesMeta::getParentId, id);
        CategoriesMeta categoriesMeta = categoriesMetaMapper.selectOne(wrapper4);

        categoriesVo.setCategoriesDataInfo(categoriesDataInfo);
        categoriesVo.setCategoriesService(categoriesServices);
        categoriesVo.setCategoriesPhysics(categoriesPhysics);
        categoriesVo.setCategoriesFactor(categoriesFactors);
        categoriesVo.setCategoriesMeta(categoriesMeta);

        return JsonResult.success(categoriesVo);
    }

    public JsonResult saveData(CategoriesVo categoriesVo){
        CategoriesDataInfo categoriesDataInfo = categoriesVo.getCategoriesDataInfo();
        List<CategoriesPhysics> categoriesPhysics = categoriesVo.getCategoriesPhysics();
        List<CategoriesFactor> categoriesFactors = categoriesVo.getCategoriesFactor();
        List<CategoriesService> categoriesServices = categoriesVo.getCategoriesService();
        CategoriesMeta categoriesMeta = categoriesVo.getCategoriesMeta();
        if(ObjectUtil.isNotEmpty(categoriesDataInfo)){
            //查询当前登录人所属机构为发布单位
            Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
            String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
            String institution_name = this.institutionMapper.selectNameByUserId(userId);
            categoriesDataInfo.setPublishInstitutionName(institution_name);
            categoriesDataInfo.setOperater(username);
            categoriesDataInfoMapper.insert(categoriesDataInfo);
            Long id = categoriesDataInfo.getId();

            if(categoriesServices.size() > 0){
                categoriesServices.stream().forEach(item -> {
                    item.setParentId(id);
                    categoriesServiceMapper.insert(item);
                });
            }

            if(categoriesPhysics.size() > 0){
                categoriesPhysics.stream().forEach(item -> {
                    item.setParentId(id);
                    categoriesPhysicsMapper.insert(item);
                });
            }

            if(categoriesFactors.size() > 0){
                categoriesFactors.stream().forEach(item -> {
                    item.setParentId(id);
                    categoriesFactorMapper.insert(item);
                });
            }

            if(ObjectUtil.isNotEmpty(categoriesMeta)){
                categoriesMeta.setParentId(id);
                categoriesMetaMapper.insert(categoriesMeta);
            }

            return JsonResult.success("注册成功");
        }
        return JsonResult.error();
    }

    public JsonResult updates(CategoriesVo categoriesVo){
        CategoriesDataInfo categoriesDataInfo = categoriesVo.getCategoriesDataInfo();
        List<CategoriesPhysics> categoriesPhysics = categoriesVo.getCategoriesPhysics();
        List<CategoriesFactor> categoriesFactors = categoriesVo.getCategoriesFactor();
        List<CategoriesService> categoriesServices = categoriesVo.getCategoriesService();
        CategoriesMeta categoriesMeta = categoriesVo.getCategoriesMeta();
        if(ObjectUtil.isNotEmpty(categoriesDataInfo)){
            categoriesDataInfoMapper.updateById(categoriesDataInfo);

            if(categoriesServices.size() > 0){
                categoriesServices.stream().forEach(item -> {
                    if(ObjectUtil.isNull(item.getId())){
                        item.setParentId(categoriesDataInfo.getId());
                        categoriesServiceMapper.insert(item);
                    }else{
                        categoriesServiceMapper.updateById(item);
                    }

                });
            }

            if(categoriesPhysics.size() > 0){
                categoriesPhysics.stream().forEach(item -> {
                    if(ObjectUtil.isNull(item.getId())){
                        item.setParentId(categoriesDataInfo.getId());
                        categoriesPhysicsMapper.insert(item);
                    }else{
                        categoriesPhysicsMapper.updateById(item);
                    }

                });
            }

            if(categoriesFactors.size() > 0){
                categoriesFactors.stream().forEach(item -> {
                    if(ObjectUtil.isNull(item.getId())){
                        item.setParentId(categoriesDataInfo.getId());
                        categoriesFactorMapper.insert(item);
                    }else{
                        categoriesFactorMapper.updateById(item);
                    }

                });
            }

            if(ObjectUtil.isNotEmpty(categoriesMeta)){
                categoriesMetaMapper.updateById(categoriesMeta);
            }

            return JsonResult.success();
        }
        return JsonResult.error();
    }

    public JsonResult deleteById(Long id){
        LambdaUpdateWrapper<CategoriesDataInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(CategoriesDataInfo::getDel, 1)
                .in(CategoriesDataInfo::getId, id);
        boolean result = this.update(wrapper);
        return JsonResult.success(result);
    }

    public JsonResult uptStatus(Long id, Boolean status){
        LambdaUpdateWrapper<CategoriesDataInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(CategoriesDataInfo::getStatus, status)
                .eq(CategoriesDataInfo::getId, id);
        categoriesDataInfoMapper.update(wrapper);
        return JsonResult.success("修改成功");
    }

    public JsonResult uptShow(Long id, int show){
        LambdaUpdateWrapper<CategoriesDataInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(CategoriesDataInfo::getShow, show)
                .eq(CategoriesDataInfo::getId, id);
        categoriesDataInfoMapper.update(wrapper);
        return JsonResult.success("修改成功");
    }

    public JsonResult<Page<ResourceServices>> pageDataSort(Page<ResourceServices> pager, String searchStr, String resourceCatalogId) {
        /**
        User principal = (User) StpUtil.getSession().get("user");
        Long userId = principal.getId();
        //查询当前用户有权限的对应数据
//        List<CategoriesDataInfo> list = this.categoriesDataInfoMapper.selectPageList((pager.getCurrent() - 1) * pager.getSize(),pager.getSize(),userId,searchStr,categoriesDataId);
        //查询当前用户的角色
        LambdaQueryWrapper<SafetyUserRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SafetyUserRole::getUserId,userId);
        List<SafetyUserRole> userRoleList = safetyUserRoleMapper.selectList(queryWrapper);
        if(Objects.isNull(userRoleList) || userRoleList.isEmpty()){
            return JsonResult.error("错误,当前用户未查询到角色信息!");
        }
        List<Long> roleIds = userRoleList.stream().map(SafetyUserRole::getRoleId).toList();
        List<CategoriesService> categoriesDataInfoList = this.categoriesDataInfoMapper.selectPageList((pager.getCurrent() - 1) * pager.getSize(),pager.getSize(),roleIds,searchStr,categoriesDataId);
        Long count = this.categoriesDataInfoMapper.selectListCount(roleIds,searchStr,categoriesDataId);
        Page<CategoriesService> page = new Page<CategoriesService>();
        page.setRecords(categoriesDataInfoList);
        page.setTotal(count);
        return JsonResult.success(page);
         */
        List<ResourceServices> categoriesDataInfoList = this.resourceDataitemsMapper.selectPageList((pager.getCurrent() - 1) * pager.getSize(),pager.getSize(),searchStr,resourceCatalogId);
        Long count = this.resourceDataitemsMapper.selectListCount(searchStr,resourceCatalogId);
        categoriesDataInfoList.parallelStream().forEachOrdered(data -> {
            String parentId = data.getDataitemid();
            LambdaQueryWrapper<ResourceDataitems> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ResourceDataitems::getId,parentId);
            ResourceDataitems categoriesDataInfo = this.resourceDataitemsMapper.selectOne(wrapper);
            // data.setAuthItype(categoriesDataInfo.getAuthItype());
        });
        Page<ResourceServices> page = new Page<ResourceServices>();
        page.setRecords(categoriesDataInfoList);
        page.setTotal(count);
        return JsonResult.success(page);
    }

    public JsonResult<Page<User>> queryAuthUser(Page<User> pager, String resourceServicesId) {
        LambdaQueryWrapper<SysRoleCate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleCate::getCateId,resourceServicesId);
        List<SysRoleCate> sysRoleCateList = sysRoleCateMapper.selectList(wrapper);
        if(sysRoleCateList.isEmpty() || Objects.isNull(sysRoleCateList)){
            return JsonResult.success(new Page<User>());
        }
        List<Long> roleIds = sysRoleCateList.stream().map(SysRoleCate::getRoleId).toList();
        List<Long> userids = this.safetyUserRoleMapper.selectUserByRoleids(roleIds);
        LambdaQueryWrapper<User> wapper = new LambdaQueryWrapper<User>();
        wapper.in(User::getId,userids);
        Page<User> userPage = userMapper.selectPage(pager, wapper);
        userPage.getRecords().stream().forEachOrdered( user -> {
            List<SafetyRole> roleList = this.safetyRoleMapper.selectIdInByUserId(user.getId());
            user.setRoleList(roleList);
        });
        return JsonResult.success(userPage);
    }

    public JsonResult<Page<ResourceServices>> serviceAuthList(Page<ResourceServices> pager, Long userId, String searchStr, String resourceCatalogsId) {
        List<Long> roleIds = this.safetyUserRoleMapper.selectList(new LambdaQueryWrapper<SafetyUserRole>().eq(SafetyUserRole::getUserId,userId)).stream().map(SafetyUserRole::getRoleId).toList();
        if(Objects.isNull(roleIds) || roleIds.isEmpty()){
            return JsonResult.error("该人员未拥有角色");
        }
        List<String> cateIds = this.sysRoleCateMapper.selectList(new LambdaQueryWrapper<SysRoleCate>().in(SysRoleCate::getRoleId,roleIds)).stream().map(SysRoleCate::getCateId).toList();
        if(cateIds.isEmpty()){
            return JsonResult.success(new Page<ResourceServices>());
        }else{
//            LambdaQueryWrapper<CategoriesService> wrapper = new LambdaQueryWrapper<CategoriesService>();
//            if(Objects.nonNull(searchStr) && !"".equals(searchStr)){
//                wrapper.and(wp -> wp.like(CategoriesService::getName,searchStr).or().like(CategoriesService::getSname,searchStr));
//            }
//            if(Objects.nonNull(categoriesDataId) &&!"".equals(categoriesDataId)){
//                wrapper.eq(CategoriesService::getParentId,Long.valueOf(categoriesDataId));  //这不对,所以注释了,这应该是爷爷级id,wrapper不支持联查所以不行
//            }
//            wrapper.in(CategoriesService::getId,cateIds).eq(CategoriesService::getDel,0).eq(CategoriesService::getStatus,true).orderByDesc(CategoriesService::getCreateTime);
//            return JsonResult.success(this.categoriesServiceMapper.selectPage(pager,wrapper));
            List<ResourceServices> categoriesServiceList = this.resourceServicesMapper.selectServicePage((pager.getCurrent() - 1) * pager.getSize(),pager.getSize(),searchStr,resourceCatalogsId,cateIds);
            categoriesServiceList.stream().forEachOrdered(data -> {
                data.setAuthFromRoleName(this.sysRoleCateMapper.selectRoleNameByCateId(data.getId()));
            });
            Long count = this.resourceServicesMapper.selectServicePageCount(searchStr,resourceCatalogsId,cateIds);
            Page<ResourceServices> page = new Page<ResourceServices>();
            page.setRecords(categoriesServiceList);
            page.setTotal(count);
            return JsonResult.success(page);
        }
    }

    public JsonResult<List<CategoriesDataInfo>> cataDataInfoBycataId(Long cataId) {
        return JsonResult.success(this.categoriesDataInfoMapper.selectList(new LambdaQueryWrapper<CategoriesDataInfo>().eq(CategoriesDataInfo::getCatalogue,String.valueOf(cataId)).eq(CategoriesDataInfo::getDel,0).eq(CategoriesDataInfo::getStatus,true).orderByDesc(CategoriesDataInfo::getCreateTime)));
    }

    public JsonResult<HashMap<String, Object>> allinfoByCataInfoId(Long cataInfoId) {
        //数据服务   物理数据  要素服务
        List<CategoriesService> categoriesServices = this.categoriesServiceMapper.selectList(new LambdaQueryWrapper<CategoriesService>().eq(CategoriesService::getDel, 0).eq(CategoriesService::getStatus, true).eq(CategoriesService::getParentId, cataInfoId).orderByAsc(CategoriesService::getFwsort));
        List<CategoriesPhysics> categoriesPhysics = this.categoriesPhysicsMapper.selectList(new LambdaQueryWrapper<CategoriesPhysics>().eq(CategoriesPhysics::getDel, 0).eq(CategoriesPhysics::getParentId, cataInfoId));
        List<CategoriesFactor> categoriesFactors = this.categoriesFactorMapper.selectList(new LambdaQueryWrapper<CategoriesFactor>().eq(CategoriesFactor::getDel, 0).eq(CategoriesFactor::getParentId, cataInfoId));
        HashMap<String,Object> result = new HashMap<>();
        categoriesServices.forEach(data -> {
            data.setAddress("涉密数据,无权查看");
        });
        result.put("dataServices",categoriesServices);
        result.put("physics",categoriesPhysics);
        result.put("factors",categoriesFactors);
        return JsonResult.success(result);
    }

    public JsonResult<List<CategoriesDataInfoVo2>> queryDataInfoAndServiceList(Long cataDataId,String searchStr) {
        List<String> ids = new ArrayList<>();
        List<CategoriesData> dataList = this.dataServiceMapper.findSelfAndChildrenById(cataDataId);
        ids.addAll(dataList.stream().map(item -> String.valueOf(item.getId())).toList());
        LambdaQueryWrapper<CategoriesDataInfo> datainfoWrapper = new LambdaQueryWrapper<CategoriesDataInfo>();
        datainfoWrapper.in(CategoriesDataInfo::getCatalogue,ids);
        datainfoWrapper.eq(CategoriesDataInfo::getDel,0)
                .eq(CategoriesDataInfo::getStatus,true)
                .eq(CategoriesDataInfo::getRegisterReviewStatus,1)
                .like(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(searchStr),CategoriesDataInfo::getName,searchStr);
        List<CategoriesDataInfo> datainfoList = this.categoriesDataInfoMapper.selectList(datainfoWrapper);
        if(datainfoList.isEmpty())  return JsonResult.success(new ArrayList<>());
        List<CategoriesDataInfoVo2> resultList = new ArrayList<>(datainfoList.size());
        datainfoList.parallelStream().forEach(item ->{
            CategoriesDataInfoVo2 categoriesDataInfoVo2 = BeanUtil.copyProperties(item, CategoriesDataInfoVo2.class);
            //查询其下服务数据
            List<CategoriesService> cataServiceList = this.categoriesServiceMapper.selectList(new LambdaQueryWrapper<CategoriesService>().eq(CategoriesService::getParentId,item.getId())
                    .eq(CategoriesService::getDel,0)
                    .eq(CategoriesService::getStatus,true)
            );
            categoriesDataInfoVo2.setCategoriesService(cataServiceList);
            resultList.add(categoriesDataInfoVo2);
        });
        return JsonResult.success(resultList);
    }
}
