package com.bjcj.model.dto.cloudportal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author：qinyi
 * @Date：2024/7/11 17:29
 */
@Schema(description="地类统计返回结构")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LandCategoryAreaStatisticsDto {

    @Schema(description="地类名称")
    private String landCategoryName;

    @Schema(description="面积")
    private BigDecimal area;

    @Schema(description="子集")
    private List<LandCategoryAreaStatisticsDto> childrens;
}
