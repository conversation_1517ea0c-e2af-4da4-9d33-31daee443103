<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.platformManage.modulePlan.ModulePlanMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.platformManage.modulePlan.ModulePlan">
    <!--@mbg.generated-->
    <!--@Table public.module_plan-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="module_name" jdbcType="VARCHAR" property="moduleName" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
    <result column="is_lag" jdbcType="CHAR" property="isLag" />
    <result column="show_sort" jdbcType="SMALLINT" property="showSort" />
    <result column="module_type" jdbcType="VARCHAR" property="moduleType" />
    <result column="module_src" jdbcType="VARCHAR" property="moduleSrc" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="conf_json" jdbcType="VARCHAR" property="confJson" />
    <result column="conf_json_schema" jdbcType="VARCHAR" property="confJsonSchema" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, module_name, show_name, is_lag, show_sort, module_type, module_src, parent_id, 
    create_time, update_time, "operator", conf_json, conf_json_schema
  </sql>
</mapper>