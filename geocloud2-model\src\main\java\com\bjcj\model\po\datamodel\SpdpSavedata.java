package com.bjcj.model.po.datamodel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/7/1  9:04
*/
/**
    * 保存数据
    */
@Schema(description="保存数据")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "public.spdp_savedata")
public class SpdpSavedata implements Serializable {
    @TableId(value = "saveid", type = IdType.ASSIGN_UUID)
    @Size(max = 36,message = "max length should less than 36")
    private String saveid;

    @TableField(value = "name")
    @Schema(description="")
    @Size(max = 60,message = "max length should less than 60")
    private String name;

    @TableField(value = "displayname")
    @Schema(description="")
    @Size(max = 100,message = "max length should less than 100")
    private String displayname;

    @TableField(value = "savemodelid")
    @Schema(description="")
    @Size(max = 100,message = "max length should less than 100")
    private String savemodelid;

    @TableField(value = "params")
    @Schema(description="")
    @Size(max = 4000,message = "max length should less than 4000")
    private String params;

    private static final long serialVersionUID = 1L;
}