package com.bjcj.web.datamodel;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.datamodel.DataStandardDto;
import com.bjcj.model.dto.datamodel.MetadataDatastandardsDto;
import com.bjcj.model.po.datamodel.DataStandard;
import com.bjcj.model.po.datamodel.MetadataDatastandards;
import com.bjcj.service.datamodel.DataStandardService;
import com.bjcj.service.datamodel.MetadataDatastandardsService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
* 数据标准表(public.data_standard)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/dataStandard")
@Tag(name = "数据标准")
@Validated
public class DataStandardController {
    /**
    * 服务对象
    */
    @Resource
    private DataStandardService dataStandardService;

    @Resource
    private MetadataDatastandardsService metadataDatastandardsService;

    @OperaLog(operaModule = "数据标准-新增/编辑",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "新增/编辑")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑数据标准", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "addOrEdit")
    public JsonResult add(@Validated @RequestBody DataStandardDto dto){
        return this.dataStandardService.saveData(dto);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/list")
    @Operation(summary = "列表", description = "带搜索框模糊查询(可搜名称/显示名称)")
    @Parameters({
            @Parameter(name = "searchStr", description = "名称/显示名称搜索", required = false)
    })
    @ApiOperationSupport(order = 1)
    public JsonResult<List<DataStandard>> list(@RequestParam(value = "searchStr",required = false) String searchStr){
        return this.dataStandardService.queryList(searchStr);
    }

    @OperaLog(operaModule = "数据标准-删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del")
    @Operation(summary = "删除", description = "根据id删除")
    @Parameters({
            @Parameter(name = "id", description = "id", required = true)
    })
    @ApiOperationSupport(order = 3)
    public JsonResult del(@RequestParam("id") Long id){
        return this.dataStandardService.delData(id);
    }


    @OperaLog(operaModule = "数据标准-新增/编辑",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "新增/编辑")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEditDatastandards")
    @Operation(summary = "1.新增/编辑数据标准", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 4)
    @RequestLock(prefix = "addOrEditDatastandards")
    public JsonResult addOrEditDatastandards(@Validated @RequestBody MetadataDatastandardsDto dto){
        return this.metadataDatastandardsService.saveData(dto);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/list2")
    @Operation(summary = "2.列表", description = "带搜索框模糊查询(可搜名称/显示名称)")
    @Parameters({
            @Parameter(name = "searchStr", description = "名称/显示名称搜索", required = false)
    })
    @ApiOperationSupport(order = 5)
    public JsonResult<List<MetadataDatastandards>> list2(@RequestParam(value = "searchStr",required = false) String searchStr){
        return this.metadataDatastandardsService.queryList(searchStr);
    }


    @OperaLog(operaModule = "数据标准-删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del2")
    @Operation(summary = "3.删除", description = "根据id删除")
    @Parameters({

            @Parameter(name = "datastandardid", description = "datastandardid", required = true)
    })
    @ApiOperationSupport(order = 6)
    public JsonResult del2(@RequestParam("datastandardid") String datastandardid){
        return this.metadataDatastandardsService.delData(datastandardid);
    }

}
