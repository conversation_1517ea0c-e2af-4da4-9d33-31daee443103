package com.bjcj.service.naturalresources.categories;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.datamodel.MetadataTablestructuresMapper;
import com.bjcj.mapper.naturalresources.categories.MetadataTablesMapper;
import com.bjcj.mapper.naturalresources.categories.ResourceDataitemsMapper;
import com.bjcj.model.po.naturalresources.categories.MetadataTables;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *@Author：qinyi
 *@Date：2024/4/24  18:18
*/
@Service
public class MetadataTablesService extends ServiceImpl<MetadataTablesMapper, MetadataTables> {

    @Resource
    MetadataTablesMapper metadataTablesMapper;

    @Resource
    ResourceDataitemsMapper resourceDataitemsMapper;

    @Resource
    MetadataTablestructuresMapper metadataTablestructuresMapper;

    public JsonResult<List<MetadataTables>> findById(String id) {
        List<MetadataTables> metadataTables = this.metadataTablesMapper.selectList(new LambdaQueryWrapper<MetadataTables>().eq(MetadataTables::getDataitemid, id).orderByAsc(MetadataTables::getDisplayorder));
        metadataTables.parallelStream().forEach(table -> {
            table.setDataitemName(this.resourceDataitemsMapper.selectById(table.getDataitemid()).getName());
            table.setTablestructureName(this.metadataTablestructuresMapper.selectById(table.getTablestructureid()).getName());
        });
        return JsonResult.success(metadataTables);
    }
}
