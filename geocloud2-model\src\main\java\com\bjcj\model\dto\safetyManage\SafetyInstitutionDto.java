package com.bjcj.model.dto.safetyManage;

import com.bjcj.common.utils.annotation.RequestKeyParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * @Author：qinyi
 * @Package：com.bjcj.model.po.safemanage
 * @Project：geocloud2
 *@name：SafetyInstitution
 *@Date：2023/11/23  10:43
 *@Filename：SafetyInstitution
*/
@Schema(description="机构表")
@Data
public class SafetyInstitutionDto {
    /**
     * 主键
     */
    @Schema(description="主键")
    private Long id;

    /**
     * 机构名称
     */
    @Schema(description="机构名称")
    @NotBlank(message = "机构名称不能为空")
    @RequestKeyParam(name = "institutionName")
    private String institutionName;

    /**
     * 级别id
     */
    @Schema(description="级别")
    private String level;

    /**
     * 机构简称
     */
    @Schema(description="机构简称")
    private String institutionShortName;

    /**
     * 显示顺序
     */
    @Schema(description="显示顺序")
    @NotNull(message = "显示顺序为必填")
    private Short showSort;

    /**
     * 行政区域编码
     */
    @Schema(description="行政区域编码")
    private String xzqCode;

    /**
     * 传真
     */
    @Schema(description="传真")
    private String faxNum;

    /**
     * 联系电话
     */
    @Schema(description="联系电话")
    private String phoneNum;

    @Schema(description="所属机构")
    private Long parentId;

}