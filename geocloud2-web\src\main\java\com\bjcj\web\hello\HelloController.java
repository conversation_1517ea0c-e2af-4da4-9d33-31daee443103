package com.bjcj.web.hello;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.safetyManage.UserMapper;
import com.bjcj.model.po.safetyManage.User;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/11/20 15:15
 */
@Tag(name = "欢迎", description = "欢迎页面")
@ApiSupport(order = 1, author = "guow")
@RestController
@Slf4j
public class HelloController {

    @Resource
    UserMapper userMapper;

    @Operation(summary = "向客人问好")
    @ApiOperationSupport(order = 3)
    @GetMapping("/")
    public String hello(){

        return "Hello World 222 ！";
    }

    @Operation(summary = "测试post忽略参数")
    @ApiOperationSupport(order = 4, ignoreParameters = {"user.id"})
    @PostMapping("/user/zs")
    @OperaLog
    public JsonResult<User> getUser(@RequestBody User user) {
        log.info("getUser:: {}", user);
        User user1 = userMapper.selectOne(Wrappers.<User>query().lambda().eq(User::getUsername, "zs"));
        return JsonResult.success(user1);
    }

    @Operation(summary = "测试日志")
    @ApiOperationSupport(order = 4)
    @GetMapping("/user/rz")
    @OperaLog
    public JsonResult<User> testRz(String rz) {
        log.info("testRz:: {}", rz);
        User user1 = userMapper.selectOne(Wrappers.<User>query().lambda().eq(User::getUsername, "zs"));
        return JsonResult.success(user1);
    }

    @Operation(summary = "当前登录的用户")
    @ApiOperationSupport(order = 5)
    @GetMapping("/users")
    @OperaLog
    public JsonResult<User> users() {
        // User loginUser = (User) StpUtil.getSession().get("user");
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        User loginUser = userMapper.selectById(userId);

        return JsonResult.success(loginUser);
    }

}
