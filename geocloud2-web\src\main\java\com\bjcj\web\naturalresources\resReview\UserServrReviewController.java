package com.bjcj.web.naturalresources.resReview;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.resReview.UserServrReviewDto;
import com.bjcj.model.dto.resReview.UserServrReviewDto2;
import com.bjcj.model.po.naturalresources.resReview.UserServrReview;
import com.bjcj.service.naturalresources.resReview.UserServrReviewService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
* 用户申请服务审核信息表(public.user_servr_review)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/userServrReview")
@Tag(name = "资源权限")
@Validated
public class UserServrReviewController {
    /**
    * 服务对象
    */
    @Resource
    private UserServrReviewService userServrReviewService;


    @OperaLog(operaModule = "申请资源权限",operaType = OperaLogConstant.CREATE,operaDesc = "申请资源权限")
    @SaCheckPermission("sys:write")
    @PostMapping("/apply")
    @Operation(summary = "申请资源权限", description = "申请资源权限")
    @ApiOperationSupport(order = 1)
    @RequestLock(prefix = "apply")
    public JsonResult apply(@Validated @RequestBody UserServrReviewDto dto) {
        return userServrReviewService.apply(dto);
    }

    @OperaLog(operaModule = "审核资源",operaType = OperaLogConstant.UPDATE,operaDesc = "审核资源")
    @SaCheckPermission("sys:write")
    @PostMapping("/review")
    @Operation(summary = "审核资源", description = "审核资源")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "review")
    @Transactional(rollbackFor = Exception.class)
    public JsonResult review(@Validated @RequestBody UserServrReviewDto2 dto) {
        return userServrReviewService.review(dto);
    }

    @OperaLog(operaModule = "批量删除审核数据",operaType = OperaLogConstant.DELETE,operaDesc = "批量删除审核数据")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delBatch")
    @Operation(summary = "批量删除", description = "批量删除")
    @Parameters({
            @Parameter(name = "idList", description = "idList", required = true)
    })
    @ApiOperationSupport(order = 3)
    @RequestLock(prefix = "delBatchData")
    public JsonResult delBatch(@RequestParam("idList") List<Long> idList) {
        return userServrReviewService.delBatch(idList);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "列表", description = "条件:(分页,审核状态,资源类型,标题,时间段)")
    @ApiOperationSupport(order = 4)
    @GetMapping("/list")
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "searchStr", description = "标题搜索", required = false),
            @Parameter(name = "reviewStatus", description = "审核状态", required = false),
            @Parameter(name = "serviceType", description = "资源类型", required = false),
            @Parameter(name = "startTime", description = "开始时间", required = false),
            @Parameter(name = "endTime", description = "结束时间", required = false),
            @Parameter(name = "orders", description = "排序方式(asc,desc)", required = false)
    })
    public JsonResult<Page<UserServrReview>> list(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam(value = "searchStr",required = false) String searchStr,
            @RequestParam(value = "reviewStatus",required = false) String reviewStatus,
            @RequestParam(value = "serviceType",required = false) String serviceType,
            @RequestParam(value = "startTime",required = false) String startTime,
            @RequestParam(value = "endTime",required = false) String endTime,
            @RequestParam(value = "orders",required = false) String orders) {
        Page<UserServrReview> pager = new Page<>(page, pageSize);
        return this.userServrReviewService.queryPage(pager,searchStr,reviewStatus,serviceType,startTime,endTime,orders);
    }
}
