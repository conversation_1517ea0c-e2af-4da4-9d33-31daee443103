package com.bjcj.service.naturalresources.categories;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.categories.*;
import com.bjcj.mapper.safetyManage.SafetyInstitutionMapper;
import com.bjcj.mapper.system.SysXzq14Mapper;
import com.bjcj.model.po.naturalresources.categories.*;
import com.bjcj.model.po.system.SysXzq14;
import com.bjcj.model.vo.naturalresources.categories.CategoriesVo2;
import com.bjcj.model.vo.naturalresources.categories.ResourceDataitemsVo2;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 *@Author：qinyi
 *@Date：2024/4/24  14:21
*/
@Service
public class ResourceDataitemsService extends ServiceImpl<ResourceDataitemsMapper, ResourceDataitems> {

    @Resource
    private ResourceDataitemsMapper mapper;

    @Resource
    private MetadataTablesMapper metadataTablesMapper;

    @Resource
    private SysXzq14Mapper xzq14Mapper;

    @Resource
    private ResourceServicesMapper resourceServicesMapper;

    @Resource
    private ResourceCatalogsMapper resourceCatalogsMapper;

    @Resource
    private SafetyInstitutionMapper institutionMapper;

    @Resource
    private CategoriesServiceGroupMapper categoriesServiceGroupMapper;

    @Resource
    private SysXzq14Mapper sysXzq14Mapper;

    @Resource
    private ResourceTagsMapper resourceTagsMapper;

    @Resource
    private ResourceDataitemsMapper resourceDataitemsMapper;


    public JsonResult<IPage<ResourceDataitems>> treeList(int index, String name, String id, int current, int size) {
        Page<ResourceDataitems> page = new Page<>(current, size);
        IPage<ResourceDataitems> list = new Page<>(current, size);
        //查询本目录改为查询本目录及子目录
        List<String> resourceCatalogs_ids = this.resourceCatalogsMapper.findSelfAndChildrenById(id).stream().map(ResourceCatalogs::getId).toList();
        if(index == 1 || index == 2){
            LambdaQueryWrapper<ResourceDataitems> wrapper = new LambdaQueryWrapper<>();
            // wrapper.like(ResourceDataitems::getResourcecatalogid, id);
            wrapper.in(ResourceDataitems::getResourcecatalogid, resourceCatalogs_ids);
            if(StringUtils.isNotBlank(name)){
                wrapper.like(ResourceDataitems::getName, name);
            }
            wrapper.orderByDesc(ResourceDataitems::getDisplayorder);
            list = results(page, wrapper);
        }else{
            LambdaQueryWrapper<MetadataTables> lambda1 = new LambdaQueryWrapper<>();
            lambda1.eq(MetadataTables::getName, name)
                    .or().eq(MetadataTables::getDisplayname, name);
            List<MetadataTables> metadataTables = metadataTablesMapper.selectList(lambda1);
            if(metadataTables.isEmpty()){
                return JsonResult.success(list);
            }
            StringJoiner ids = new StringJoiner(",");
            metadataTables.stream().forEach(item->{
                ids.add(item.getDataitemid());
            });

            LambdaQueryWrapper<ResourceDataitems> wrapper = new LambdaQueryWrapper<>();
            // wrapper.like(ResourceDataitems::getResourcecatalogid, id)
            wrapper.in(ResourceDataitems::getResourcecatalogid, resourceCatalogs_ids)
                    .in(ResourceDataitems::getId, ids)
                    .orderByAsc(ResourceDataitems::getDisplayorder);
            list = results(page, wrapper);
        }
        // todo 等行政区id对了再放开
        // list.getRecords().forEach(item -> {
        //     Long district = Long.valueOf(item.getDistrictcode());
        //     item.setXzq(this.xzq14Mapper.selectById(district).getName());
        // });
        return JsonResult.success(list);
    }


    public IPage results(Page page,LambdaQueryWrapper wrapper){
        IPage<ResourceDataitems> resitems =  mapper.selectPage(page, wrapper);

        IPage<ResourceDataitems> convertPage = resitems.convert(result -> {
            ResourceDataitems obj = new ResourceDataitems();
            BeanUtils.copyProperties(result, obj);
            return obj;
        });
        convertPage.getRecords().stream().forEach(item->{

            LambdaQueryWrapper<ResourceServices> lambda1 = new LambdaQueryWrapper<ResourceServices>();
            lambda1.eq(ResourceServices::getDataitemid, item.getId())
                    .eq(ResourceServices::getIsvisiable, true);
            List<ResourceServices> resourceServices = resourceServicesMapper.selectList(lambda1);

            resourceServices.stream().filter(item3 -> Objects.nonNull(item3.getServicegroupid()) && StringUtils.isNotBlank(item3.getServicegroupid())).forEach(item2 -> {
                item2.setGroupName(this.categoriesServiceGroupMapper.selectById(Long.parseLong(item2.getServicegroupid())).getGroupName());
            });
            // List<ResourceServices> resourceServices = resourceServicesMapper.selectListAndGroupName(item.getId());

            //分组
            // Map<String, List<CategoriesService>> collect = categoriesService.stream().collect(Collectors.groupingBy(CategoriesService::getFwgroup));
            //写入标签信息
            resourceServices.forEach(item2 -> {
                List<ResourceTags> tags = resourceTagsMapper.selectList(new LambdaQueryWrapper<ResourceTags>().eq(ResourceTags::getResourceid, item2.getId()));
                if(!tags.isEmpty()) item2.setResourceTags(tags.stream().map(ResourceTags::getName).reduce((a, b) -> a + "," + b).orElse(""));
            });
            item.setResourceServicesList(resourceServices);

            LambdaQueryWrapper<MetadataTables> lambda2 = new LambdaQueryWrapper<MetadataTables>();
            lambda2.eq(MetadataTables::getDataitemid, item.getId());
            List<MetadataTables> metadataTablesList = metadataTablesMapper.selectList(lambda2);

            item.setMetadataTablesList(metadataTablesList);

            //此处先注释掉要素服务 和 元数据 ,新的元数据在json字段中储存
            // LambdaQueryWrapper<CategoriesFactor> lambda3 = new LambdaQueryWrapper<>();
            // lambda3.eq(CategoriesFactor::getParentId, item.getId())
            //         .eq(CategoriesFactor::getDel, 0);
            // List<CategoriesFactor> categoriesFactor = categoriesFactorMapper.selectList(lambda3);
            //
            // item.setCategoriesFactor(categoriesFactor);

            // LambdaQueryWrapper<CategoriesMeta> lambda4 = new LambdaQueryWrapper<>();
            // lambda4.eq(CategoriesMeta::getParentId, item.getId());
            // CategoriesMeta categoriesMeta = categoriesMetaMapper.selectOne(lambda4);
            //
            // item.setCategoriesMeta(categoriesMeta);

        });

        return convertPage;
    }


    public JsonResult<ResourceDataitems> findById(String id) {
        // ResourceDataitems dataitems = new ResourceDataitems();

        ResourceDataitems resourceDataitems = this.mapper.selectById(id);
        LambdaQueryWrapper<ResourceServices> wrapper1 = new LambdaQueryWrapper<ResourceServices>();
        wrapper1.eq(ResourceServices::getDataitemid, id);
        List<ResourceServices> resourceServices = resourceServicesMapper.selectList(wrapper1);

        LambdaQueryWrapper<MetadataTables> wrapper2 = new LambdaQueryWrapper<MetadataTables>();
        wrapper2.eq(MetadataTables::getDataitemid, id);
        List<MetadataTables> metadataTables = metadataTablesMapper.selectList(wrapper2);

        // LambdaQueryWrapper<CategoriesFactor> wrapper3 = new LambdaQueryWrapper<>();
        // wrapper3.eq(CategoriesFactor::getParentId, id)
        //         .eq(CategoriesFactor::getDel,0);
        // List<CategoriesFactor> categoriesFactors = categoriesFactorMapper.selectList(wrapper3);

        // LambdaQueryWrapper<CategoriesMeta> wrapper4 = new LambdaQueryWrapper<>();
        // wrapper4.eq(CategoriesMeta::getParentId, id);
        // CategoriesMeta categoriesMeta = categoriesMetaMapper.selectOne(wrapper4);

        resourceDataitems.setResourceServicesList(resourceServices);
        resourceDataitems.setMetadataTablesList(metadataTables);

        return JsonResult.success(resourceDataitems);
    }

    public JsonResult saveData(CategoriesVo2 categoriesVo2) {
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        ResourceDataitems resourceDataitems = categoriesVo2.getResourceDataitems();
        List<MetadataTables> metadataTablesList = categoriesVo2.getMetadataTablesList();
        List<ResourceServices> resourceServicesList = categoriesVo2.getResourceServicesList();
        if(ObjectUtil.isNotEmpty(resourceDataitems)){
            SysXzq14 sysXzq14 = this.sysXzq14Mapper.selectById(Long.valueOf(resourceDataitems.getDistrictcode()));
            if(null != sysXzq14) resourceDataitems.setDistrictname(sysXzq14.getName());
            //查询当前登录人所属机构为发布单位
            // User principal = (User) StpUtil.getSession().get("user");
            String institution_name = this.institutionMapper.selectNameByUserId(userId);
            resourceDataitems.setPublishInstitutionName(institution_name);
            resourceDataitems.setRegisterman(username);
            resourceDataitems.setRegisterdate(LocalDateTime.now());
            resourceDataitems.setDisplayname(resourceDataitems.getName());
            //转换元数据的json
            // resourceDataitems.setMetadata(JSON.toJSONString(resourceDataitems.getMetadata()));
            mapper.insert(resourceDataitems);
            String id = resourceDataitems.getId();

            if(!resourceServicesList.isEmpty()){
                resourceServicesList.stream().forEach(item -> {
                    item.setDataitemid(id);
                    item.setRegisterman(username);
                    item.setRegisterdate(LocalDateTime.now());
                    item.setTransferurl("/rest/services/"+item.getName()+"/MapServer");
                    item.setResourcecategory(Short.valueOf("6"));
                    if (Objects.isNull(item.getParams())) item.setParams("{}");
                    if(Objects.isNull(item.getMetadata())) item.setMetadata("{}");
                    this.resourceServicesMapper.insert(item);
                    String tags = item.getResourceTags();
                    if(StringUtils.isNotBlank(tags)){
                        List<String> tagList = Arrays.asList(tags.split(","));
                        tagList.forEach(item2 -> {
                            ResourceTags tag = new ResourceTags();
                            tag.setResourcecategory(6);
                            tag.setResourceid(item.getId());
                            tag.setName(item2);
                            this.resourceTagsMapper.insert(tag);
                        });
                    }
                });
            }

            if(!metadataTablesList.isEmpty()){
                metadataTablesList.stream().forEach(item -> {
                    item.setDataitemid(id);
                    item.setRegisterman(username);
                    item.setRegisterdate(LocalDateTime.now());
                    if(Objects.isNull(item.getMetadata())) item.setMetadata("{}");
                    this.metadataTablesMapper.insert(item);
                });
            }
            return JsonResult.success("注册成功");
        }
        return JsonResult.error();
    }

    public JsonResult updates(CategoriesVo2 categoriesVo2) {
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        ResourceDataitems resourceDataitems =  categoriesVo2.getResourceDataitems();
        List<MetadataTables> metadataTablesList = categoriesVo2.getMetadataTablesList();
        List<ResourceServices> resourceServicesList = categoriesVo2.getResourceServicesList();
        if(ObjectUtil.isNotEmpty(resourceDataitems)){
            SysXzq14 sysXzq14 = this.sysXzq14Mapper.selectById(Long.valueOf(resourceDataitems.getDistrictcode()));
            if(null != sysXzq14) resourceDataitems.setDistrictname(sysXzq14.getName());
            resourceDataitems.setRepairman(username);
            this.mapper.updateById(resourceDataitems);

            if(!resourceServicesList.isEmpty()){
                resourceServicesList.forEach(item -> {
                    if(ObjectUtil.isNull(item.getId())){
                        item.setDataitemid(resourceDataitems.getId());
                        item.setRegisterman(username);
                        item.setRegisterdate(LocalDateTime.now());
                        item.setTransferurl("/rest/services/"+item.getName()+"/MapServer");
                        item.setResourcecategory(Short.valueOf("6"));
                        if (Objects.isNull(item.getParams())) item.setParams("{}");
                        if(Objects.isNull(item.getMetadata())) item.setMetadata("{}");
                        this.resourceServicesMapper.insert(item);
                        String tags = item.getResourceTags();
                        if(StringUtils.isNotBlank(tags)){
                            List<String> tagList = Arrays.asList(tags.split(","));
                            tagList.forEach(item2 -> {
                                ResourceTags tag = new ResourceTags();
                                tag.setResourcecategory(6);
                                tag.setResourceid(item.getId());
                                tag.setName(item2);
                                this.resourceTagsMapper.insert(tag);
                            });
                        }
                    }else{
                        item.setRepairman(username);
                        item.setRepairdate(LocalDateTime.now());
                        item.setTransferurl("/rest/services/"+item.getName()+"/MapServer");
                        item.setResourcecategory(Short.valueOf("6"));
                        if (Objects.isNull(item.getParams())) item.setParams("{}");
                        if(Objects.isNull(item.getMetadata())) item.setMetadata("{}");
                        this.resourceServicesMapper.updateById(item);
                        String tags = item.getResourceTags();
                        LambdaQueryWrapper<ResourceTags> wp = new LambdaQueryWrapper<ResourceTags>();
                        wp.eq(ResourceTags::getResourceid,item.getId());
                        this.resourceTagsMapper.delete(wp);
                        if(StringUtils.isNotBlank(tags)){
                            List<String> tagList = Arrays.asList(tags.split(","));
                            tagList.forEach(item2 -> {
                                ResourceTags tag = new ResourceTags();
                                tag.setResourcecategory(6);
                                tag.setResourceid(item.getId());
                                tag.setName(item2);
                                this.resourceTagsMapper.insert(tag);
                            });
                        }
                    }

                });
            }

            if(!metadataTablesList.isEmpty()){
                metadataTablesList.forEach(item -> {
                    if(ObjectUtil.isNull(item.getTableid())){
                        item.setDataitemid(resourceDataitems.getId());
                        item.setRegisterman(username);
                        if(Objects.isNull(item.getMetadata())) item.setMetadata("{}");
                        this.metadataTablesMapper.insert(item);
                    }else{
                        if(Objects.isNull(item.getMetadata())) item.setMetadata("{}");
                        this.metadataTablesMapper.updateById(item);
                    }

                });
            }
            return JsonResult.success();
        }
        return JsonResult.error();
    }

    public JsonResult deleteById(String id) {
        //删除数据项
        this.mapper.deleteById(id);
        //删除服务数据
        this.resourceServicesMapper.delete(new LambdaQueryWrapper<ResourceServices>().eq(ResourceServices::getDataitemid, id));
        //删除物理数据
        this.metadataTablesMapper.delete(new LambdaQueryWrapper<MetadataTables>().eq(MetadataTables::getDataitemid, id));
        return JsonResult.success();
    }

    public JsonResult uptStatus(String id, Boolean status) {
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        LambdaUpdateWrapper<ResourceDataitems> wrapper = new LambdaUpdateWrapper<ResourceDataitems>();
        wrapper.set(ResourceDataitems::getIsvisiable, status)
                .set(ResourceDataitems::getRepairman, username)
                .eq(ResourceDataitems::getId, id);
        mapper.update(wrapper);
        return JsonResult.success("修改成功");
    }

    public JsonResult uptShow(String id, Boolean isvisiable){
        LambdaUpdateWrapper<ResourceDataitems> wrapper = new LambdaUpdateWrapper<ResourceDataitems>();
        wrapper.set(ResourceDataitems::getIsvisiable, isvisiable)
                .eq(ResourceDataitems::getId, id);
        mapper.update(wrapper);
        return JsonResult.success("修改成功");
    }

    public JsonResult<List<ResourceDataitemsVo2>> queryDataInfoAndServiceList(String catalogsid, String searchStr) {
        List<String> ids = new ArrayList<>();
        List<ResourceCatalogs> dataList = this.resourceCatalogsMapper.findSelfAndChildrenById(catalogsid);
        ids.addAll(dataList.stream().map(item -> String.valueOf(item.getId())).toList());
        LambdaQueryWrapper<ResourceDataitems> datainfoWrapper = new LambdaQueryWrapper<ResourceDataitems>();
        datainfoWrapper.in(ResourceDataitems::getResourcecatalogid,ids);
        datainfoWrapper.eq(ResourceDataitems::getStatus,1)
                .like(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(searchStr),ResourceDataitems::getDisplayname,searchStr);
        List<ResourceDataitems> datainfoList = this.resourceDataitemsMapper.selectList(datainfoWrapper);
        if(datainfoList.isEmpty()) return JsonResult.success(new ArrayList<>());
        List<ResourceDataitemsVo2> resultList = new ArrayList<>(datainfoList.size());
        datainfoList.forEach(item ->{
            ResourceDataitemsVo2 resourceDataitemsVo2 = BeanUtil.copyProperties(item,ResourceDataitemsVo2.class);
            //查询其下服务数据
            List<ResourceServices> cataServiceList = this.resourceServicesMapper.selectList(new LambdaQueryWrapper<ResourceServices>().eq(ResourceServices::getDataitemid, item.getId()));
            resourceDataitemsVo2.setResourceServices(cataServiceList);
            resultList.add(resourceDataitemsVo2);
        });
        return JsonResult.success(resultList);
    }

    public JsonResult<List<ResourceDataitems>> cataDataInfoBycataId(String catalogid) {
        return JsonResult.success(this.resourceDataitemsMapper.selectList(new LambdaQueryWrapper<ResourceDataitems>().eq(ResourceDataitems::getResourcecatalogid, catalogid).eq(ResourceDataitems::getStatus,1).orderByDesc(ResourceDataitems::getRegisterdate)));
    }

    public JsonResult<HashMap<String, Object>> allinfoByCataInfoId(String dataitemid) {
        //数据服务   物理数据  要素服务
        List<ResourceServices> categoriesServices = this.resourceServicesMapper.selectList(new LambdaQueryWrapper<ResourceServices>().eq(ResourceServices::getStatus, 1).eq(ResourceServices::getDataitemid, dataitemid).orderByAsc(ResourceServices::getDisplayorder));
        //todo 物理数据和要素服务未实现先不做
        // List<CategoriesPhysics> categoriesPhysics = this.categoriesPhysicsMapper.selectList(new LambdaQueryWrapper<CategoriesPhysics>().eq(CategoriesPhysics::getDel, 0).eq(CategoriesPhysics::getParentId, cataInfoId));
        // List<CategoriesFactor> categoriesFactors = this.categoriesFactorMapper.selectList(new LambdaQueryWrapper<CategoriesFactor>().eq(CategoriesFactor::getDel, 0).eq(CategoriesFactor::getParentId, cataInfoId));
        HashMap<String,Object> result = new HashMap<>();
        categoriesServices.forEach(data -> {
            data.setUrl("涉密数据,无权查看");
        });
        result.put("dataServices",categoriesServices);
        // result.put("physics",categoriesPhysics);
        // result.put("factors",categoriesFactors);
        return JsonResult.success(result);
    }
}
