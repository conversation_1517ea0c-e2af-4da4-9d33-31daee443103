package com.bjcj.common.utils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2025-02-07 18:20 周五
 */
public class AESFixedEncryption {
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";
    private static final String AES_KEY = "7pi1xXQGQkzxzfOpwdWE9A==";

    /**
     * 生成 AES 密钥
     * @return 密钥的 Base64 编码字符串
     * @throws Exception 异常
     */
    public static String generateKey() throws Exception {
        KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
        keyGenerator.init(128);
        SecretKey secretKey = keyGenerator.generateKey();
        return Base64.getEncoder().encodeToString(secretKey.getEncoded());
    }

    /**
     * 使用 AES 加密数据
     * @param plainText 明文
     * @return 加密后的 Base64 编码字符串
     * @throws Exception 异常
     */
    public static String encrypt(String plainText) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(AES_KEY);
        SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, ALGORITHM);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * 使用 AES 解密数据
     * @param encryptedText 加密后的 Base64 编码字符串
     * @return 解密后的明文
     * @throws Exception 异常
     */
    public static String decrypt(String encryptedText) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(AES_KEY);
        SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, ALGORITHM);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
        byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedText));
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }

    public static void main(String[] args) {
        try {
            // 生成密钥
            String key = generateKey();
            String plainText = "Cj@12138";

            // 加密
            String encryptedText = encrypt(plainText);
            System.out.println("加密结果: " + encryptedText);

            // 再次加密相同的明文
            String encryptedText2 = encrypt(plainText);
            System.out.println("再次加密结果: " + encryptedText2);
            System.out.println("两次加密结果是否相同: " + encryptedText.equals(encryptedText2));

            // 解密
            String decryptedText = decrypt(encryptedText);
            System.out.println("解密结果: " + decryptedText);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
