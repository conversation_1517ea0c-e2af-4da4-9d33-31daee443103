package com.bjcj.model.dto.specialPlan;

import com.baomidou.mybatisplus.annotation.TableField;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/1/19  9:09
*/

/**
    * 专题展示方案表
    */
@Schema(description="专题展示方案表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SpecialPlanDto implements Serializable {
    @Schema(description="id")
    private Long id;

    /**
     * 方案名称
     */
    @Schema(description="方案名称")
    @Size(max = 100,message = "方案名称max length should less than 100")
    @NotBlank(message = "方案名称is not blank")
    @RequestKeyParam(name = "planName")
    private String planName;

    /**
     * 描述
     */
    @Schema(description="描述")
    @Size(max = 255,message = "描述max length should less than 255")
    private String remark;

    @TableField(value = "create_time")
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "update_time")
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField(value = "operater")
    @Schema(description="")
    private String operater;

    @TableField(value = "platform_app_conf_id")
    @Schema(description="平台应用配置id")
    @NotNull(message = "平台应用配置不能为空")
    private Long platformAppConfId;

    private static final long serialVersionUID = 1L;
}