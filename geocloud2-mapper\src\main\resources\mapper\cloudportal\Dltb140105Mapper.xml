<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.cloudportal.Dltb140105Mapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.cloudportal.Dltb140105">
    <result column="OBJECTID" jdbcType="DECIMAL" property="objectid" />
    <result column="BSM" jdbcType="VARCHAR" property="bsm" />
    <result column="YSDM" jdbcType="VARCHAR" property="ysdm" />
    <result column="TBYBH" jdbcType="VARCHAR" property="tbybh" />
    <result column="TBBH" jdbcType="VARCHAR" property="tbbh" />
    <result column="DLBM" jdbcType="VARCHAR" property="dlbm" />
    <result column="DLMC" jdbcType="VARCHAR" property="dlmc" />
    <result column="QSXZ" jdbcType="VARCHAR" property="qsxz" />
    <result column="QSDWDM" jdbcType="VARCHAR" property="qsdwdm" />
    <result column="QSDWMC" jdbcType="VARCHAR" property="qsdwmc" />
    <result column="ZLDWDM" jdbcType="VARCHAR" property="zldwdm" />
    <result column="ZLDWMC" jdbcType="VARCHAR" property="zldwmc" />
    <result column="TBMJ" jdbcType="DECIMAL" property="tbmj" />
    <result column="KCDLBM" jdbcType="VARCHAR" property="kcdlbm" />
    <result column="KCXS" jdbcType="DECIMAL" property="kcxs" />
    <result column="KCMJ" jdbcType="DECIMAL" property="kcmj" />
    <result column="TBDLMJ" jdbcType="DECIMAL" property="tbdlmj" />
    <result column="GDLX" jdbcType="VARCHAR" property="gdlx" />
    <result column="GDPDJB" jdbcType="VARCHAR" property="gdpdjb" />
    <result column="XZDWKD" jdbcType="DECIMAL" property="xzdwkd" />
    <result column="TBXHDM" jdbcType="VARCHAR" property="tbxhdm" />
    <result column="TBXHMC" jdbcType="VARCHAR" property="tbxhmc" />
    <result column="ZZSXDM" jdbcType="VARCHAR" property="zzsxdm" />
    <result column="ZZSXMC" jdbcType="VARCHAR" property="zzsxmc" />
    <result column="GDDB" jdbcType="DECIMAL" property="gddb" />
    <result column="FRDBS" jdbcType="VARCHAR" property="frdbs" />
    <result column="CZCSXM" jdbcType="VARCHAR" property="czcsxm" />
    <result column="SJNF" jdbcType="DECIMAL" property="sjnf" />
    <result column="MSSM" jdbcType="VARCHAR" property="mssm" />
    <result column="HDMC" jdbcType="VARCHAR" property="hdmc" />
    <result column="BZ" jdbcType="CLOB" property="bz" />
    <result column="SHAPE" jdbcType="OTHER" property="shape" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    OBJECTID, BSM, YSDM, TBYBH, TBBH, DLBM, DLMC, QSXZ, QSDWDM, QSDWMC, ZLDWDM, ZLDWMC, 
    TBMJ, KCDLBM, KCXS, KCMJ, TBDLMJ, GDLX, GDPDJB, XZDWKD, TBXHDM, TBXHMC, ZZSXDM, ZZSXMC, 
    GDDB, FRDBS, CZCSXM, SJNF, MSSM, HDMC, BZ, SHAPE
  </sql>

  <select id="selectMjSum" resultType="java.math.BigDecimal">
    select sum(TBMJ) from DLTB_140105 where ZLDWDM like #{simplecode} and DLBM in
    <foreach collection="nydCodeList" item="nydCode" separator="," open="(" close=")">
      #{nydCode}
    </foreach>
  </select>

  <select id="selectMjSum2" resultType="java.math.BigDecimal">
    select sum(TBMJ) from DLTB_140105 where ZLDWDM like #{simplecode}
  </select>

  <select id="selectMjSums" resultType="java.math.BigDecimal">
    select sum(TBMJ) from DLTB_140105 where ZLDWDM like #{simplecode} and DLBM = #{dictValue}
  </select>
</mapper>