package com.bjcj.service.datamodel;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.datamodel.SpdpAnalysiscatalogMapper;
import com.bjcj.model.dto.datamodel.SpdpAnalysiscatalogDto;
import com.bjcj.model.po.datamodel.SpdpAnalysiscatalog;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *@Author：qinyi
 *@Date：2024/5/10  17:30
*/
@Service
public class SpdpAnalysiscatalogService extends ServiceImpl<SpdpAnalysiscatalogMapper, SpdpAnalysiscatalog> {

    @Resource
    SpdpAnalysiscatalogMapper spdpAnalysiscatalogMapper;

    public JsonResult saveData(SpdpAnalysiscatalogDto dto) {
        SpdpAnalysiscatalog spdpAnalysiscatalog = BeanUtil.copyProperties(dto, SpdpAnalysiscatalog.class);
        if(spdpAnalysiscatalog.getId() == null){
            //验证名称或显示名称重复数据
            LambdaQueryWrapper<SpdpAnalysiscatalog> queryWrapper = new LambdaQueryWrapper<SpdpAnalysiscatalog>();
            queryWrapper.eq(SpdpAnalysiscatalog::getName,spdpAnalysiscatalog.getName());
            List<SpdpAnalysiscatalog> list = spdpAnalysiscatalogMapper.selectList(queryWrapper);
            if(!list.isEmpty()){
                return JsonResult.error("名称重复");
            }
            spdpAnalysiscatalogMapper.insert(spdpAnalysiscatalog);
        }else{
            spdpAnalysiscatalogMapper.updateById(spdpAnalysiscatalog);
        }
        return JsonResult.success();
    }


}
