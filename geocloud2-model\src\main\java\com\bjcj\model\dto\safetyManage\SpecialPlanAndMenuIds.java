package com.bjcj.model.dto.safetyManage;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author：qinyi
 * @Date：2024/3/11 11:36
 */
@Schema
@Data
public class SpecialPlanAndMenuIds implements Serializable {

    @Schema(description="专题id")
    @NotNull
    private Long specialPlanId;

    @Schema(description="菜单ids(逗号拼接)")
    @NotBlank
    private String menuIds;

    private static final long serialVersionUID = 1L;

}
