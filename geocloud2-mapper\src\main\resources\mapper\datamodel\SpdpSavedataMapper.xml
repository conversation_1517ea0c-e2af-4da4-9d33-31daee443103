<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.SpdpSavedataMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.SpdpSavedata">
    <!--@mbg.generated-->
    <!--@Table public.spdp_savedata-->
    <result column="saveid" jdbcType="CHAR" property="saveid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="displayname" jdbcType="VARCHAR" property="displayname" />
    <result column="savemodelid" jdbcType="VARCHAR" property="savemodelid" />
    <result column="params" jdbcType="VARCHAR" property="params" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    saveid, "name", displayname, savemodelid, params
  </sql>
</mapper>