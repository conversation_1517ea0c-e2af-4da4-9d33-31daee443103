package com.bjcj.model.dto.dict;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import com.bjcj.model.dto.specialPlan.SpecialPlanDataDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/2/1  10:12
*/
/**
    * 字典类型表
    */
@Schema(description="字典类型表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_dict_type")
public class SysDictTypeDto implements Serializable {
    /**
     * 字典主键
     */
    @Schema(description="字典主键")
    @NotNull(message = "id不能为空",groups = {SpecialPlanDataDto.EditGroup.class})
    private Long dictId;

    /**
     * 字典名称
     */
    @Schema(description="字典名称")
    @Size(max = 100,message = "字典名称max length should less than 100")
    @NotBlank(message = "字典名称不能为空",groups = {SpecialPlanDataDto.EditGroup.class,SpecialPlanDataDto.AddGroup.class})
    @RequestKeyParam(name = "dictType")
    private String dictName;

    /**
     * 字典类型
     */
    @Schema(description="字典类型")
    @Size(max = 100,message = "字典类型max length should less than 100")
    @NotBlank(message = "字典类型不能为空",groups = {SpecialPlanDataDto.EditGroup.class,SpecialPlanDataDto.AddGroup.class})
    private String dictType;

    /**
     * 状态（0正常 1停用）
     */
    @Schema(description="状态（0正常 1停用）")
    @NotBlank(message = "状态（0正常 1停用）不能为空",groups = {SpecialPlanDataDto.EditGroup.class,SpecialPlanDataDto.AddGroup.class})
    private String status;

    /**
     * 最后操作人
     */
    @Schema(description="最后操作人")
    @Size(max = 30,message = "最后操作人max length should less than 30")
    private String operater;

    /**
     * 创建时间
     */
    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description="备注")
    @Size(max = 500,message = "备注max length should less than 500")
    private String remark;

    /**
     * 字典类型(1普通,2级联,3分组)
     */
    @Schema(description="字典类型(1普通,2级联,3分组)")
    @NotBlank(message = "字典类型(1普通,2级联,3分组)不能为空",groups = {SpecialPlanDataDto.EditGroup.class,SpecialPlanDataDto.AddGroup.class})
    private String dictDataType;

    private static final long serialVersionUID = 1L;


    public interface AddGroup{
    }
    public interface EditGroup{
    }
}