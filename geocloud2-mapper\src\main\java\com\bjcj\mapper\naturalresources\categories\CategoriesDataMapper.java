package com.bjcj.mapper.naturalresources.categories;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.naturalresources.categories.CategoriesData;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlanDir;

import java.util.List;

/**
 * @ClassName DataServiceMapper
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/11/27 10:06
 */

public interface CategoriesDataMapper extends BaseMapper<CategoriesData> {

    List<CategoriesData> findSelfAndChildrenById(Long cataDataId);

    List<SpecialPlanDir> findSelfAndChildrenByIdDir(String cataDataId);
}
