package com.bjcj.web.datamodel;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.datamodel.SpdpDeletedataDto;
import com.bjcj.model.po.datamodel.SpdpDeletedata;
import com.bjcj.service.datamodel.SpdpDeletedataService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
* 删除数据(public.spdp_deletedata)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/spdp_deletedata")
@Tag(name = "数据删除")
@Validated
public class SpdpDeletedataController {
    /**
    * 服务对象
    */
    @Resource
    private SpdpDeletedataService spdpDeletedataService;

    @SaCheckPermission("sys:read")
    @GetMapping("/list")
    @Operation(summary = "列表", description = "列表")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "searchStr", description = "searchStr", required = false)
    })
    public JsonResult<List<SpdpDeletedata>> list(@RequestParam(value = "searchStr",required = false) String searchStr){
        if(StringUtils.isNotBlank(searchStr)){
            return JsonResult.success(this.spdpDeletedataService.list(new LambdaQueryWrapper<SpdpDeletedata>().and(i -> i.like(SpdpDeletedata::getName, searchStr).or().like(SpdpDeletedata::getDisplayname, searchStr))));
        }
        return JsonResult.success(this.spdpDeletedataService.list());
    }

    @OperaLog(operaModule = "删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del/{deleteid}")
    @Operation(summary = "删除", description = "根据id删除")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "delsjsc")
    public JsonResult del(@PathVariable("deleteid") String deleteid){
        return JsonResult.success(this.spdpDeletedataService.removeById(deleteid));
    }

    @OperaLog(operaModule = "批量删除",operaType = OperaLogConstant.DELETE,operaDesc = "批量删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delBatch")
    @Operation(summary = "批量删除", description = "根据id批量删除")
    @ApiOperationSupport(order = 3)
    @Parameters({
            @Parameter(name = "deleteids", description = "ids逗号拼接", required = true)
    })
    @RequestLock(prefix = "delBatchsjsc")
    public JsonResult delBatch(@RequestParam("deleteids") String deleteids){
        if(deleteids.contains(",")){
            //ids转list
            List<String> ids = List.of(deleteids.split(","));
            return JsonResult.success(this.spdpDeletedataService.removeByIds(ids));
        }else{
            return JsonResult.error("ids格式错误,必须选择一条以上的数据");
        }
    }

    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "注册/编辑", description = "注册/编辑")
    @ApiOperationSupport(order = 4)
    @RequestLock(prefix = "addOrEditsjsc")
    public JsonResult addOrEditsjbc(@Validated @RequestBody SpdpDeletedataDto dto){
        SpdpDeletedata spdpDeletedata = BeanUtil.copyProperties(dto, SpdpDeletedata.class);
        return this.spdpDeletedataService.saveOrUpdate(spdpDeletedata) ? JsonResult.success() : JsonResult.error();
    }

}
