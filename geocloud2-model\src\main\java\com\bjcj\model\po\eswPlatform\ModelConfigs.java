package com.bjcj.model.po.eswPlatform;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024-8-19  15:27
*/
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "model_configs")
public class ModelConfigs implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(description="")
    @Size(max = 40,message = "最大长度要小于 40")
    private String id;

    /**
     * 名称
     */
    @TableField(value = "name")
    @Schema(description="名称")
    @Size(max = 255,message = "名称最大长度要小于 255")
    private String name;

    /**
     * 文件路径
     */
    @TableField(value = "path")
    @Schema(description="文件路径")
    private String path;

    /**
     * 描述
     */
    @TableField(value = "description")
    @Schema(description="描述")
    @Size(max = 255,message = "描述最大长度要小于 255")
    private String description;

    /**
     * 缩略图路径
     */
    @TableField(value = "thumbnail_path")
    @Schema(description="缩略图路径")
    private String thumbnailPath;

    /**
     * 类型
     */
    @TableField(value = "type")
    @Schema(description="类型")
    @Size(max = 255,message = "类型最大长度要小于 255")
    private String type;

    @TableField(value = "special_plan_id")
    @Schema(description="")
    @NotNull(message = "不能为null")
    private Long specialPlanId;

    private static final long serialVersionUID = 1L;
}