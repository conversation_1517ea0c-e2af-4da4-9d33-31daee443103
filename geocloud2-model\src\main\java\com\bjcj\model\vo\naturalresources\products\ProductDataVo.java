package com.bjcj.model.vo.naturalresources.products;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/15 14:46 周五
 */
@Data
public class ProductDataVo implements Serializable {

    private Long id;

    @Schema(description="注册名称")
    private String name;

    @Schema(description="显示名称")
    private String sname;

    @Schema(description="数据服务id")
    private Long productId;

    @Schema(description="角色id")
    private Long roleId;

}
