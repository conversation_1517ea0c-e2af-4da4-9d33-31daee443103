package com.bjcj.service.naturalresources.function;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.function.FunctionProviderMapper;
import com.bjcj.model.po.naturalresources.function.FunctionProvider;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/12/4 14:46 周一
 */
@Service
public class FunctionProviderService extends ServiceImpl<FunctionProviderMapper, FunctionProvider> {

    @Resource
    private FunctionProviderMapper functionProviderMapper;

    public JsonResult del(Long id){
        LambdaUpdateWrapper<FunctionProvider> wrapper = new LambdaUpdateWrapper();
        wrapper.set(FunctionProvider::getDel, 1)
                .eq(FunctionProvider::getId, id);
        int result = functionProviderMapper.update(wrapper);
        return JsonResult.success(result);
    }

}
