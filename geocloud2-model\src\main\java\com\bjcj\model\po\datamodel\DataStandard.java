package com.bjcj.model.po.datamodel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 * 数据标准表
 *@Date：2024/1/3  15:25
*/

@Schema(description = "数据标准表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "data_standard")
public class DataStandard implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    @NotNull(message = "主键is not null")
    private Long id;

    /**
     * 名称
     */
    @TableField(value = "name")
    @Schema(description = "名称")
    @Size(max = 50, message = "名称max length should less than 50")
    @NotBlank(message = "名称is not blank")
    private String name;

    /**
     * 显示名称
     */
    @TableField(value = "show_name")
    @Schema(description = "显示名称")
    @Size(max = 50, message = "显示名称max length should less than 50")
    @NotBlank(message = "显示名称is not blank")
    private String showName;

    /**
     * 标准代码
     */
    @TableField(value = "standard_code")
    @Schema(description = "标准代码")
    @Size(max = 50, message = "标准代码max length should less than 50")
    private String standardCode;

    /**
     * 所属目录id
     */
    @TableField(value = "categories_data_id")
    @Schema(description = "所属目录id")
    @NotNull(message = "所属目录id is not null")
    private Long categoriesDataId;

    /**
     * 所属目录名称
     */
    @TableField(value = "categories_data_name")
    @Schema(description = "所属目录名称")
    @Size(max = 50, message = "所属目录名称max length should less than 50")
    private String categoriesDataName;

    /**
     * 描述
     */
    @TableField(value = "remark")
    @Schema(description = "描述")
    @Size(max = 255, message = "描述max length should less than 255")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 最后操作人
     */
    @TableField(value = "operator")
    @Schema(description = "最后操作人")
    @Size(max = 20, message = "最后操作人max length should less than 20")
    private String operator;

    private static final long serialVersionUID = 1L;
}