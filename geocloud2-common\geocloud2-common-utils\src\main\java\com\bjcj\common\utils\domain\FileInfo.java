package com.bjcj.common.utils.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件信息
 * <AUTHOR>
 * @version 0.0.1
 * @Description
 * @date 2021-08-19 16:09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "文件信息")
public class FileInfo {

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 不带后缀的文件名
     */
    private String noSuffixFileName;

    /**
     * 后缀
     */
    private String suffix;

    /**
     * 新文件名
     */
    private String newFileName;

    /**
     * 本地路径
     */
    private String localUrl;

    /**
     * 服务器路径
     */
    private String serverUrl;

}
