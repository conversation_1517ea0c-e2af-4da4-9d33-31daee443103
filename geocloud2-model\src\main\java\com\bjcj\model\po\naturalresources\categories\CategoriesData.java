package com.bjcj.model.po.naturalresources.categories;

import com.baomidou.mybatisplus.annotation.*;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName CategoriesData
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/11/27 9:20
 */
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "categories_data")
public class CategoriesData implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="", hidden = true)
    private Long id;

    @NotBlank
    @TableField(value = "name")
    @Schema(description="数据目录名称")
    private String name;

    @NotBlank
    @TableField(value = "code")
    @Schema(description="数据目录编码")
    @RequestKeyParam(name = "code")
    private String code;

    @NotNull
    @TableField(value = "parent_id")
    @Schema(description="父级id")
    private Long parentId;

    @TableField(value = "display_order")
    @Schema(description="数据目录排序")
    private int displayOrder;

    @TableField(value = "description")
    @Schema(description="数据目录描述")
    private String description;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "status")
    @Schema(description="显示状态")
    private Boolean status;

    @TableField(value = "del")
    @Schema(description="删除状态（0未删除，1已删除）")
    private int del;

    @TableField(exist = false)
    private List<CategoriesData> children;

    @TableField(exist = false)
    private int serviceCount;

}
