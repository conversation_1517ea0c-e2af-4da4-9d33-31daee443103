<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bjcj</groupId>
        <artifactId>geocloud2</artifactId>
        <version>0.0.1</version>
    </parent>

    <artifactId>geocloud2-service</artifactId>
    <packaging>jar</packaging>

    <name>geocloud2-service</name>
    <url>http://maven.apache.org</url>

    <dependencies>
        <dependency>
            <groupId>com.bjcj</groupId>
            <artifactId>geocloud2-common-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- 处理CAD文件转换 -->
        <dependency>
            <groupId>com.aspose</groupId>
            <artifactId>aspose-cad</artifactId>
            <version>23.7</version>
        </dependency>

        <!-- gdal cad转shp -->
        <dependency>
            <groupId>org.gdal</groupId>
            <artifactId>gdal</artifactId>
            <version>3.8.0</version>
        </dependency>

        <!-- 动态数据源 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
        </dependency>


    </dependencies>




    <repositories>
        <repository>
            <id>osgeo</id>
            <name>OSGeo Release Repository</name>
            <url>https://repo.osgeo.org/repository/release/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>

        <repository>
            <id>aspose</id>
            <url>https://repository.aspose.com/repo</url>
        </repository>

    </repositories>

</project>
