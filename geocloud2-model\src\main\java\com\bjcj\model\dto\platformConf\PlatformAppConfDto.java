package com.bjcj.model.dto.platformConf;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/1/24  17:48
*/

/**
    * 平台应用配置表
    */
@Schema(description="平台应用配置表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PlatformAppConfDto implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 应用名称
     */
    @Schema(description="应用名称")
    @Size(max = 50,message = "应用名称max length should less than 50")
    @RequestKeyParam(name = "appName")
    private String appName;

    /**
     * 显示名称
     */
    @Schema(description="显示名称")
    @Size(max = 50,message = "显示名称max length should less than 50")
    @NotBlank(message = "显示名称is not blank")
    private String showName;

    /**
     * 系统标题
     */
    @Schema(description="系统标题")
    @Size(max = 50,message = "系统标题max length should less than 50")
    @NotBlank(message = "系统标题is not blank")
    private String systemTitle;

    /**
     * 地址
     */
    @Schema(description="地址")
    @Size(max = 255,message = "地址max length should less than 255")
    @NotBlank(message = "地址is not blank")
    private String url;

    /**
     * 登录主题
     */
    @Schema(description="登录主题")
    @Size(max = 100,message = "登录主题max length should less than 100")
    private String loginTheme;

    /**
     * 平台菜单id
     */
    @Schema(description="平台菜单id")
    private Long platformMenuId;

    /**
     * 描述
     */
    @Schema(description="描述")
    @Size(max = 255,message = "描述max length should less than 255")
    private String remark;

    /**
     * 创建时间
     */
    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 最后操作人
     */
    @Schema(description="最后操作人")
    @Size(max = 30,message = "最后操作人max length should less than 30")
    private String operater;

    /**
     * 系统配置
     */
    @Schema(description="系统配置")
    private String systemConfigJson;

    /**
     * 系统配置解释json
     */
    @Schema(description="系统配置解释json")
    private String systemConfigJsonSchema;

    @Schema(description="主题配置")
    private String themeConfJson;

    @Schema(description="主题配置解释json")
    private String themeConfJsonSchema;

    private static final long serialVersionUID = 1L;
}