package com.bjcj.mapper.safetyManage;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.safetyManage.RoleMenu;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 *@Author：qinyi
 *@Date：2023/12/4  10:48
*/
public interface RoleMenuMapper extends BaseMapper<RoleMenu> {
    int selectCountRoleMenuByMenuId(Long menuId);

    void insertBatch(List<RoleMenu> roleMenuList);

    void deleteByRoleAndSpecialPlanId(@Param("roleId") Long roleId,@Param("specialPlanId") Long specialPlanId);
}