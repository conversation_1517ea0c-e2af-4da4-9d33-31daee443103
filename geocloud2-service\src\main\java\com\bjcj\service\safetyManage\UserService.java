package com.bjcj.service.safetyManage;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.AESFixedEncryption;
import com.bjcj.common.utils.Date2String;
import com.bjcj.common.utils.LocalDateUtil;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.common.utils.properties.GeoCloud2Properties;
import com.bjcj.mapper.cloudportal.SysUserMessageMapper;
import com.bjcj.mapper.platformConf.PlatformAppConfMapper;
import com.bjcj.mapper.safetyManage.*;
import com.bjcj.mapper.system.SysXzq14Mapper;
import com.bjcj.model.dto.personnalCenter.ModifyPwdDto;
import com.bjcj.model.dto.safetyManage.UserDto;
import com.bjcj.model.po.cloudportal.SysUserMessage;
import com.bjcj.model.po.platformConf.PlatformAppConf;
import com.bjcj.model.po.safetyManage.*;
import com.bjcj.model.po.system.SysXzq14;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author：qinyi
 * @Package：com.bjcj.service.safetyManage
 * @Project：geocloud2
 * @name：UserService
 * @Date：2023/11/27 10:31
 * @Filename：UserService
 */
@Service
public class UserService extends ServiceImpl<UserMapper, User> {

    private static final Logger log = LoggerFactory.getLogger(UserService.class);

    @Resource
    UserMapper userMapper;

    @Resource
    SafetyInstitutionMapper safetyInstitutionMapper;

    @Resource
    SafetyDeptMapper safetyDeptMapper;

    @Resource
    SafetyUserRoleMapper safetyUserRoleMapper;


    @Resource
    GeoCloud2Properties geoCloud2Properties;

    @Resource
    Date2String date2String;

    @Resource
    SysXzq14Mapper sysXzq14Mapper;

    @Resource
    SafetyXzqCodeMapper safetyXzqCodeMapper;

    @Resource
    CheckIsAdmin checkIsAdmin;

    @Resource
    RoleMenuMapper roleMenuMapper;

    @Resource
    SafetyRoleMapper safetyRoleMapper;

    @Resource
    RolePermsMapper rolePermsMapper;

    @Resource
    SysPermissionMapper sysPermissionMapper;

    @Resource
    SysUserMessageMapper sysUserMessageMapper;

    @Resource
    PlatformAppConfMapper platformAppConfMapper;

    @Resource
    MenuMapper menuMapper;

    private static final String PASSWORD_PATTERN = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&+=])(?=\\S+$).{8,}$";

    public JsonResult saveData(UserDto dto) throws Exception {

        User user = BeanUtil.copyProperties(dto, User.class);

        // 验证用户是否已存在
        User one = getOne(
                Wrappers.<User>lambdaQuery()
                        .eq(User::getUsername, user.getUsername())
        );
        if (ObjUtil.isNotNull(one) && ObjUtil.isNull(user.getId())) {
            return JsonResult.build(400, "该用户名已存在");
        }

        user.setPassword(AESFixedEncryption.encrypt(geoCloud2Properties.getDefaultPassword()));

        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        User loginUser = userMapper.selectById(userId);
        user.setOperater(loginUser.getUsername());
        if (ObjUtil.isNull(user.getId())) {
            user.setCreateTime(LocalDateUtil.now());
            user.setRegisterTime(LocalDateUtil.now());
            user.setRegisterUser(loginUser.getUsername());
            user.setActiveStatus(1);
            return userMapper.insert(user) > 0 ? JsonResult.success() : JsonResult.error();
        } else {
            user.setUpdateTime(LocalDateUtil.now());
            return userMapper.updateById(user) > 0 ? JsonResult.success() : JsonResult.error();
        }
    }

    public JsonResult<Page<User>> pageDataSort(Page<User> pager, String searchStr, Long institutionId, Long deptId, String isLock) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(searchStr)) {
            queryWrapper.and(wrapper -> wrapper.like(User::getNickName, searchStr).or().like(User::getUsername, searchStr));
        }
        queryWrapper.eq(Objects.nonNull(institutionId), User::getInstitutionId, institutionId)
                .eq(Objects.nonNull(deptId), User::getDeptId, deptId)
                .orderByAsc(User::getShowSort);
        if (Objects.nonNull(isLock) && !"".equals(isLock)) {
            queryWrapper.eq(User::getIsLock, Integer.parseInt(isLock));
        }
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        User loginUser = userMapper.selectById(userId);

        if (!checkIsAdmin.checkIsAdmin(loginUser.getId())) {
            queryWrapper.eq(User::getIsLock, 0);
        }
        queryWrapper.orderByAsc(User::getShowSort);
        Page<User> userPage = userMapper.selectPage(pager, queryWrapper);
        userPage.getRecords().stream().forEachOrdered(user -> {
            SafetyDept safetyDept = this.safetyDeptMapper.selectById(user.getDeptId());
            user.setDeptName(safetyDept == null ? "" : safetyDept.getDeptName());
            SafetyInstitution safetyInstitution = this.safetyInstitutionMapper.selectById(user.getInstitutionId());
            user.setInstitution(safetyInstitution == null ? "" : safetyInstitution.getInstitutionName());
        });
        return JsonResult.success(userPage);
    }

    public JsonResult delData(Long id) {
        this.userMapper.deleteById(id);
        this.safetyUserRoleMapper.deleteByUserId(id);
        return JsonResult.success();
    }

    public JsonResult updateActiveStatus(Long id) {
        User user = new User();
        user.setId(id);
        user.setActiveStatus(3);
        return this.userMapper.updateById(user) > 0 ? JsonResult.success() : JsonResult.error();
    }

    public JsonResult updateUserStatus(Long id) {
        User user = new User();
        user.setId(id);
        user.setActiveStatus(1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        user.setUpdateTime(sdf.format(new Date()));
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        User loginUser = userMapper.selectById(userId);
        user.setOperater(loginUser.getUsername());
        return this.userMapper.updateById(user) > 0 ? JsonResult.success() : JsonResult.error();
    }

    public JsonResult resetPassword(Long id) throws Exception {
        User user = this.userMapper.selectById(id);
        user.setPassword(AESFixedEncryption.encrypt(geoCloud2Properties.getDefaultPassword()));
        user.setPwdUpdateDate(date2String.nowDateStr());
        return userMapper.updateById(user) > 0 ? JsonResult.success() : JsonResult.error();
    }

    public JsonResult runXzqData(String level) {
        LambdaQueryWrapper<SysXzq14> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysXzq14::getLevel, Short.parseShort(level));
        List<SysXzq14> sysXzq14List = this.sysXzq14Mapper.selectList(queryWrapper);
        int count = sysXzq14List.size();
        AtomicInteger count1 = new AtomicInteger(1);
//        for(SysXzq14 xzq14 : sysXzq14List){
        sysXzq14List.parallelStream().forEach(xzq14 -> {
            SafetyXzqCode safetyXzqCode = new SafetyXzqCode();
            if (xzq14.getLevel() == 2) {
                safetyXzqCode.setXzqLevel("市级");
                safetyXzqCode.setXzqCode(xzq14.getCode());
                safetyXzqCode.setXzqName(xzq14.getName());
                safetyXzqCode.setParentCode("140000000000");
            }
            if (xzq14.getLevel() == 3) {
//                SafetyXzqCode safetyXzqCode = new SafetyXzqCode();
                safetyXzqCode.setXzqLevel("县区级");
                safetyXzqCode.setXzqCode(xzq14.getCode());
                safetyXzqCode.setXzqName(xzq14.getName());
                LambdaQueryWrapper<SysXzq14> queryWrapper1 = new LambdaQueryWrapper<>();
                String substring = xzq14.getCode().substring(0, 4);
                queryWrapper1.like(SysXzq14::getCode, substring).eq(SysXzq14::getLevel, 2);
                SysXzq14 sysXzq14 = this.sysXzq14Mapper.selectOne(queryWrapper1);
                safetyXzqCode.setParentCode(sysXzq14.getCode());
            }
            if (xzq14.getLevel() == 4) {
//                SafetyXzqCode safetyXzqCode = new SafetyXzqCode();
                safetyXzqCode.setXzqLevel("乡镇街道级");
                safetyXzqCode.setXzqCode(xzq14.getCode());
                safetyXzqCode.setXzqName(xzq14.getName());
                LambdaQueryWrapper<SysXzq14> queryWrapper2 = new LambdaQueryWrapper<>();
                String substring = xzq14.getCode().substring(0, 6);
                queryWrapper2.like(SysXzq14::getCode, substring).eq(SysXzq14::getLevel, 3);
                SysXzq14 sysXzq14 = this.sysXzq14Mapper.selectOne(queryWrapper2);
                safetyXzqCode.setParentCode(sysXzq14.getCode());
            }
            if (xzq14.getLevel() == 5) {
                safetyXzqCode.setXzqLevel("道路居委会级");
                safetyXzqCode.setXzqCode(xzq14.getCode());
                safetyXzqCode.setXzqName(xzq14.getName());
                LambdaQueryWrapper<SysXzq14> queryWrapper3 = new LambdaQueryWrapper<>();
                String substring = xzq14.getCode().substring(0, 9);
                queryWrapper3.like(SysXzq14::getCode, substring).eq(SysXzq14::getLevel, 4);
                SysXzq14 sysXzq14 = this.sysXzq14Mapper.selectOne(queryWrapper3);
                safetyXzqCode.setParentCode(sysXzq14.getCode());
            }
            this.safetyXzqCodeMapper.insert(safetyXzqCode);
            log.info("-------当前进度:" + count1 + "/" + count);
            count1.getAndIncrement();
//        }
        });
        return JsonResult.success();
    }

    public JsonResult modifyPwd(ModifyPwdDto dto) throws Exception {
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        User loginUser = userMapper.selectById(userId);
        String password = dto.getPassword();
        Pattern pattern = Pattern.compile(PASSWORD_PATTERN);
        Matcher matcher = pattern.matcher(password);
        boolean isMatch = matcher.matches();
        if (!isMatch) {
            return JsonResult.error("密码格式不正确,密码中至少包含一个数字,至少包含一个小写字母,至少包含一个大写字母,至少包含一个特殊字符(@#$%^&+=),不能包含空格,长度至少为8个字符");
        } else {
            String encryption = AESFixedEncryption.encrypt(geoCloud2Properties.getDefaultPassword());
            this.userMapper.modifyPassword(loginUser.getId(), encryption);
        }
        SysUserMessage sysUserMessage = SysUserMessage.builder()
                .receiveUserId(loginUser.getId())
                .sendUserId(loginUser.getId())
                .title("密码修改")
                .content("您的密码已修改成功!")
                .build();
        int insert = this.sysUserMessageMapper.insert(sysUserMessage);
        return JsonResult.success();
    }

    public JsonResult<Page<User>> queryAuthUserByMenuId(Page<User> pager, Long menuId, String searchStr) {
        // 查拥有菜单的角色,再查角色下用户
        List<RoleMenu> roleMenuList = this.roleMenuMapper.selectList(new LambdaQueryWrapper<RoleMenu>().eq(RoleMenu::getMenuId, menuId));
        List<Long> roleIds = roleMenuList.stream().map(RoleMenu::getRoleId).toList();
        if (roleIds.isEmpty())
            return JsonResult.error("该菜单未授权任何角色");
        List<SafetyUserRole> userRoleList = this.safetyUserRoleMapper.selectList(new LambdaQueryWrapper<SafetyUserRole>().in(SafetyUserRole::getRoleId, roleIds));
        List<Long> userIds = userRoleList.stream().map(SafetyUserRole::getUserId).toList();
        if (userIds.isEmpty())
            return JsonResult.error("该菜单未授权任何用户");
        if (!roleIds.isEmpty()) {
            LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
            if (Objects.nonNull(searchStr)) {
                queryWrapper.and(i -> i.like(User::getNickName, searchStr).or().like(User::getUsername, searchStr));
            }
            queryWrapper.in(User::getId, userIds);
            Page<User> userPage = this.userMapper.selectPage(pager, queryWrapper);
            userPage.getRecords().parallelStream().forEachOrdered(user -> {
                List<SafetyUserRole> safetyUserRoles = this.safetyUserRoleMapper.selectList(new LambdaQueryWrapper<SafetyUserRole>().eq(SafetyUserRole::getUserId, user.getId()));
                List<Long> roleIds1 = safetyUserRoles.stream().map(SafetyUserRole::getRoleId).toList();
                if (!roleIds1.isEmpty()) {
                    List<SafetyRole> safetyRoles = this.safetyRoleMapper.selectList(new LambdaQueryWrapper<SafetyRole>().in(SafetyRole::getId, roleIds1));
                    user.setRoleList(safetyRoles);
                } else {
                    user.setRoleList(new ArrayList<>());
                }
                List<RolePerms> rolePermsList = this.rolePermsMapper.selectList(new LambdaQueryWrapper<RolePerms>().in(RolePerms::getRoleId, roleIds1));
                if (!rolePermsList.isEmpty()) {
                    List<SysPermission> sysPermissions = this.sysPermissionMapper.selectList(new LambdaQueryWrapper<SysPermission>().in(SysPermission::getId, rolePermsList.stream().map(RolePerms::getPermsId).toList()));
                    user.setPermissionList(sysPermissions);
                } else {
                    user.setPermissionList(new ArrayList<>());
                }
            });
            return JsonResult.success(userPage);
        } else {
            return JsonResult.success(new Page<User>());
        }
    }

    /**
     * 根据用户ID获取用户对应权限标识
     * @return list 权限标识集合
     * zzn 2025/03/24
     */
    public JsonResult<List<String>> queryUserPermissions() {
        return Optional.ofNullable(StpUtil.getLoginIdAsLong())
                .filter(loginId-> loginId !=0 )
//                .map(userMapper::queryUserPermissions)
                .map(userId->userMapper.queryUserPermissions(userId)
                        .stream()
                        .filter(StrUtil::isNotBlank)
                        .collect(Collectors.toList()))
                .map(JsonResult::success)
                .orElseGet(()->JsonResult.error("用户未登录"));
    }


    /**
     * 根据专题名获取用户对应权限标识
     * @param specialPlanName 用户名称
     * @return 权限集合
     * zzn 2025/03/24
     */
    public JsonResult<List<String>> queryUserPermissionsForSpecialPlan(String specialPlanName) {

        //根据专题名获取平台id
        Long platFormAppConfId = this.platformAppConfMapper.selectOne(new LambdaQueryWrapper<PlatformAppConf>()
                .eq(PlatformAppConf::getAppName, specialPlanName)).getId();
        long userId = StpUtil.getLoginIdAsLong();
        if (userId == 0){
            return JsonResult.error("用户未登录");
        }
        //根据平台id和用户id获取菜单
        List<Menu> menuList = this.menuMapper.selectMenuListByUserIdSpecialPlan(platFormAppConfId,userId);
        return Optional.ofNullable(menuList).filter(CollUtil::isNotEmpty)
                .map(
                        menus -> menus.stream()
                        .map(Menu::getPerms)
                        .filter(StrUtil::isNotEmpty)
                        .collect(Collectors.toList())
                )
                .map(JsonResult::success)
                .orElseGet(()-> JsonResult.error("当前登录用户与平台名称没有对应平台菜单。。。"));
    }
}
