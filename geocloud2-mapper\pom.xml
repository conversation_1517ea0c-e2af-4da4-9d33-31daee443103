<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bjcj</groupId>
        <artifactId>geocloud2</artifactId>
        <version>0.0.1</version>
    </parent>

    <artifactId>geocloud2-mapper</artifactId>
    <packaging>jar</packaging>

    <name>geocloud2-mapper</name>
    <url>http://maven.apache.org</url>

    <dependencies>
        <dependency>
            <groupId>com.bjcj</groupId>
            <artifactId>geocloud2-model</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>
    </dependencies>


</project>
