package com.bjcj.model.dto.mobile;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PdaSubconfigDto implements Serializable {

    private String subid;

    private String applykind;

    private String imgpath;

    private String isdisplay;

    private String menuname;

    private String modeltype;

    private String numfileds;

    private String parentid;

    private String pid;

    private String remark;

    private Integer sortid;

    private Boolean template;

    private String value;

    private String key;

    private String mobilephone;

    private String isapproval;

    private String width;

    private String height;

    private String columnsize;

    private String tenantid;

    private int visablenum;

    private String ifremind;

    private int count;

    private String builtmodule;

    private Integer currentPage;   // 页码
    private Integer pageSize;  // 每页条数

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }


}
