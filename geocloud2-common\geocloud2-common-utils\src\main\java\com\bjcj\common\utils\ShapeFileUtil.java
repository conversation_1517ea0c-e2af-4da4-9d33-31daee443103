package com.bjcj.common.utils;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.bjcj.common.utils.properties.GeoCloud2Properties;
import com.vividsolutions.jts.geom.Coordinate;
import com.vividsolutions.jts.geom.GeometryFactory;
import com.vividsolutions.jts.geom.Point;
import lombok.extern.slf4j.Slf4j;
import org.geotools.data.DataStore;
import org.geotools.data.DataStoreFinder;
import org.geotools.data.simple.SimpleFeatureCollection;
import org.geotools.data.simple.SimpleFeatureIterator;
import org.geotools.data.simple.SimpleFeatureSource;
import org.geotools.geojson.feature.FeatureJSON;
import org.geotools.geojson.geom.GeometryJSON;
import org.geotools.geometry.jts.JTS;
import org.geotools.geometry.jts.JTSFactoryFinder;
import org.geotools.referencing.CRS;
import org.opengis.filter.Filter;
import org.opengis.referencing.FactoryException;
import org.opengis.referencing.crs.CoordinateReferenceSystem;
import org.opengis.referencing.operation.MathTransform;
import org.opengis.referencing.operation.TransformException;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/26 15:42 周三
 */
@Slf4j
public class ShapeFileUtil {

    /**
     * <h2>shp文件夹，并返回解析出的 FeatureCollection</h2>
     *
     * @param file: shp文件夹
     * @return org.geotools.feature.FeatureCollection
     * <AUTHOR>
     * @date 2023/7/26 15:48
     */
    public static SimpleFeatureCollection getFeatureCollectionByShpFile(File file) throws IOException {
        try {
            List<String> files = new ArrayList<>();
            getFileList(file, files);
            String shapFileName = "";
            for (String fileName : files) {
                String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
                if ("shp".equals(suffix)) {
                    shapFileName = fileName;
                }
            }
            File shapeFile = new File(shapFileName);
            // List<SimpleFeature> list = new ArrayList<>();
            Map<String, Object> shapeFileParams = new HashMap<>();
            shapeFileParams.put("url", shapeFile.toURI().toURL());
            // 设置编码
            shapeFileParams.put("charset", "UTF-8");
            DataStore dataStore = DataStoreFinder.getDataStore(shapeFileParams);
            if (dataStore == null) {
                throw new RuntimeException("couldn't load the damn data store: " + shapeFileParams);
            }
            String typeName = dataStore.getTypeNames()[0];
            SimpleFeatureSource source = dataStore.getFeatureSource(typeName);
            Filter filter = Filter.INCLUDE;
            return source.getFeatures(filter);
        } catch (Exception e) {
            throw e;
        }
    }


    /**
     * @param zipFile:
     * @return JSONObject
     * <AUTHOR>
     * @description 通过shp压缩文件，将其转换为GeoJson格式
     * @date 2023/7/18 16:04
     */
    public static JSONObject shpZipToGeoJson(File zipFile) throws IOException {
        FeatureJSON fjson = new FeatureJSON(new GeometryJSON(20));
        JSONObject geoJsonObject = new JSONObject();
        geoJsonObject.put("type", "FeatureCollection");
        try {
            // 获取FeatureCollection
            SimpleFeatureCollection collection = getFeatureCollectionByShpFile(zipFile);

            SimpleFeatureIterator iterator = collection.features();
            List<JSONObject> array = new ArrayList<JSONObject>();
            // 遍历feature转为json对象
            while (iterator.hasNext()) {
                String geojson = fjson.toString(iterator.next());
                JSONObject jsonObject = JSON.parseObject(geojson);
                // 将投影坐标转为地理坐标
                paseCoordinates(jsonObject.getJSONObject("geometry").toString());
                array.add(jsonObject);
            }
            iterator.close();
            // 添加到geojsonObject
            geoJsonObject.put("features", array);
            iterator.close();

        } catch (Exception e) {
            throw e;
        }
        return geoJsonObject;
    }


    public static JSONObject shpToGeoJson(String shapefilePath) throws IOException {

        File shapeFile = new File(shapefilePath);
        // List<SimpleFeature> list = new ArrayList<>();
        Map<String, Object> shapeFileParams = new HashMap<>();
        shapeFileParams.put("url", shapeFile.toURI().toURL());
        // 设置编码
        shapeFileParams.put("charset", "UTF-8");

        DataStore dataStore = DataStoreFinder.getDataStore(shapeFileParams);
        if (dataStore == null) {
            throw new RuntimeException("couldn't load the damn data store: " + shapeFileParams);
        }
        String typeName = dataStore.getTypeNames()[0];
        SimpleFeatureSource source = dataStore.getFeatureSource(typeName);
        Filter filter = Filter.INCLUDE;
        SimpleFeatureCollection features = source.getFeatures(filter);

        SimpleFeatureIterator iterator = features.features();

        FeatureJSON fjson = new FeatureJSON(new GeometryJSON(20));
        JSONObject geoJsonObject = new JSONObject();
        geoJsonObject.put("type", "FeatureCollection");

        List<JSONObject> array = new ArrayList<JSONObject>();
        // 遍历feature转为json对象
        while (iterator.hasNext()) {
            String geojson = fjson.toString(iterator.next());
            JSONObject jsonObject = JSON.parseObject(geojson);
            // 将投影坐标转为地理坐标
            paseCoordinates(jsonObject.getJSONObject("geometry").toString());
            array.add(jsonObject);
        }
        iterator.close();
        // 添加到geojsonObject
        geoJsonObject.put("features", array);
        iterator.close();

        return geoJsonObject;
    }

    /**
     * 转换每个Feature的坐标
     *
     * @param geometry Feature原始json字符串
     * @return
     */
    public static StringBuffer paseCoordinates(String geometry) {
        int offset = geometry.indexOf("coordinates\":") + 13;
        String b = geometry.substring(offset);
        // System.out.println(b);
        String[] c = b.split("\\],");
        StringBuffer stringBuffer = new StringBuffer();
        int n = c.length;
        for (String d : c) {
            // System.out.println(d);
            int begin = d.lastIndexOf("[");
            int end = d.indexOf("]");
            String oldZb = d.substring(begin < 0 ? 0 : begin + 1, end < 0 ? d.length() - 1 : end - 1);
            // System.out.println(oldZb);
            String newZb = covStr(oldZb);
            // System.out.println(newZb);
            if (begin > 0) {
                stringBuffer.append(d, 0, begin + 1).append(newZb);
            }
            if (n != 1) {
                stringBuffer.append("]");
            }
            if (end > 0) {
                stringBuffer.append(d.substring(end - 1));
            }
            if (n > 1) {
                stringBuffer.append(",");
            }
            n--;
        }
        stringBuffer.insert(0, geometry.substring(0, offset));
        return stringBuffer;
    }

    public static String covStr(String zb) {
        String[] poi = zb.trim().split(",");

        BigDecimal bg1 = new BigDecimal(poi[0].trim());
        BigDecimal bg2 = new BigDecimal(poi[1].trim());
        BigDecimal bg3 = new BigDecimal("0");
        if (poi.length == 3) {
            bg3 = new BigDecimal(poi[1].trim());
        }

        // 地理坐标不需要转换
        if (bg1.compareTo(bg2) > 0) {
            if (bg1.compareTo(new BigDecimal("180")) < 0 && bg1.compareTo(new BigDecimal("0")) > 0) {
                return bg1.doubleValue() + "," + bg2.doubleValue();
            }
        } else {
            if (bg2.compareTo(new BigDecimal("180")) < 0 && bg2.compareTo(new BigDecimal("0")) > 0) {
                return bg2.doubleValue() + "," + bg1.doubleValue();
            }
        }

        return transformCoordinate(bg2.doubleValue(), bg1.doubleValue(), bg3.doubleValue(),  null);
    }

    /**
     * <h2>投影坐标转地理坐标</h2>
     * @param bg2:
     * @param bg1:
     * @param zone: 度带
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/1/30 14:05
     */
    public static String transformCoordinate(double bg2, double bg1, double bg3, String zone) {
        // 全局配置文件
        GeoCloud2Properties geoCloud2Properties = SpringUtils.getBean(GeoCloud2Properties.class);
        try {
            // 创建投影坐标点
            Coordinate projectionCoordinate = new Coordinate(bg2, bg1, bg3);
            GeometryFactory geometryFactory = JTSFactoryFinder.getGeometryFactory();
            Point geometryFactoryPoint = geometryFactory.createPoint(projectionCoordinate);

            // 定义投影坐标系和地理坐标系
            CoordinateReferenceSystem sourceCRSObject = CRS.decode("EPSG:" + (StrUtil.isBlank(zone) ? geoCloud2Properties.getZone() : zone));
            CoordinateReferenceSystem targetCRSObject = CRS.decode("EPSG:4490");

            // 创建坐标变换对象
            MathTransform transform = CRS.findMathTransform(sourceCRSObject, targetCRSObject, true);

            // 进行坐标转换
            Coordinate coordinate = JTS.transform(geometryFactoryPoint, transform).getCoordinate();

            return NumberUtil.equals(coordinate.z, 0) ?
                    coordinate.y + "," + coordinate.x : coordinate.y + "," + coordinate.x + "," + coordinate.z;
        } catch (FactoryException e) {
            log.error("投影坐标系和地理坐标系创建失败", e);
        } catch (TransformException e) {
            log.error("坐标转换失败", e);
        }
        return null;
    }

    private static void getFileList(File file, List<String> res) {
        File[] files = file.listFiles();
        for (File f : files) {
            if (f.isDirectory()) {
                getFileList(f, res);
            } else {
                res.add(f.getPath());
            }
        }
    }


}
