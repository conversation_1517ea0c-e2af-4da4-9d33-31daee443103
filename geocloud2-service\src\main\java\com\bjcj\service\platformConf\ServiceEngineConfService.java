package com.bjcj.service.platformConf;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.platformConf.ServiceEngineConfMapper;
import com.bjcj.model.po.platformConf.ServiceEngineConf;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 *@Author：qinyi
 *@Date：2024/2/28  14:39
*/
@Service
public class ServiceEngineConfService extends ServiceImpl<ServiceEngineConfMapper, ServiceEngineConf> {

    @Resource
    ServiceEngineConfMapper serviceEngineConfMapper;

    public JsonResult<Page<ServiceEngineConf>> pageDataSort(Page<ServiceEngineConf> pager, String searchStr) {
        LambdaQueryWrapper<ServiceEngineConf> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(searchStr), ServiceEngineConf::getServerColonyName, searchStr);
        queryWrapper.orderByDesc(ServiceEngineConf::getCreateTime);
        return JsonResult.success(this.serviceEngineConfMapper.selectPage(pager, queryWrapper));
    }
}
