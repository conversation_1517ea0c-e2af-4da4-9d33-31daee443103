package com.bjcj.service.naturalresources.categories;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.categories.CategoriesPhysicsMapper;
import com.bjcj.model.po.naturalresources.categories.CategoriesPhysics;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/29 10:54 周三
 */
@Service
public class CategoriesPhysicsService extends ServiceImpl<CategoriesPhysicsMapper, CategoriesPhysics> {

    @Resource
    private CategoriesPhysicsMapper categoriesPhysicsMapper;

    public JsonResult lists(int current, int size){
        Page<CategoriesPhysics> page = new Page<>(current, size);
        LambdaUpdateWrapper<CategoriesPhysics> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(CategoriesPhysics::getDel, 0);
        IPage<CategoriesPhysics> iPage = categoriesPhysicsMapper.selectPage(page, lambdaUpdateWrapper);

        return JsonResult.success(iPage);
    }

    public JsonResult<List<CategoriesPhysics>> findById(Long id){
        LambdaQueryWrapper<CategoriesPhysics> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CategoriesPhysics::getParentId, id)
                .eq(CategoriesPhysics::getDel,0);
        List<CategoriesPhysics> list =  categoriesPhysicsMapper.selectList(wrapper);
        return JsonResult.success(list);
    }

    public JsonResult del(Long id){
        LambdaUpdateWrapper<CategoriesPhysics> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(CategoriesPhysics::getDel, 1)
                .in(CategoriesPhysics::getId, id);
        int result = categoriesPhysicsMapper.update(lambdaUpdateWrapper);

        return JsonResult.success(result);
    }

}
