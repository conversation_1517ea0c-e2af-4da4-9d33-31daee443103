package com.bjcj.model.dto.datamodel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/1/4  14:45
*/
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DbManageDto implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 名称
     */
    @TableField(value = "name")
    @Schema(description="名称")
    @Size(max = 100,message = "名称max length should less than 100")
    @NotBlank(message = "名称is not blank")
    @RequestKeyParam(name = "name")
    private String name;

    /**
     * 空间类型
     */
    @TableField(value = "space_type")
    @Schema(description="空间类型")
    @Size(max = 50,message = "空间类型max length should less than 50")
    @NotBlank(message = "空间类型is not blank")
    private String spaceType;

    /**
     * 实例
     */
    @TableField(value = "example_url")
    @Schema(description="实例")
    @Size(max = 255,message = "实例max length should less than 255")
    @NotBlank(message = "实例is not blank")
    private String exampleUrl;

    /**
     * 版本
     */
    @TableField(value = "db_version")
    @Schema(description="版本")
    @Size(max = 100,message = "版本max length should less than 100")
    @NotBlank(message = "版本is not blank")
    private String dbVersion;

    /**
     * 用户名
     */
    @TableField(value = "username")
    @Schema(description="用户名")
    @Size(max = 50,message = "用户名max length should less than 50")
    @NotBlank(message = "用户名is not blank")
    private String username;

    /**
     * 密码
     */
    @TableField(value = "password")
    @Schema(description="密码")
    @Size(max = 50,message = "密码max length should less than 50")
    @NotBlank(message = "密码is not blank")
    private String password;

    /**
     * 数据库
     */
    @TableField(value = "database")
    @Schema(description="数据库")
    @Size(max = 100,message = "数据库max length should less than 100")
    private String database;

    /**
     * 是否只读
     */
    @TableField(value = "is_only_read")
    @Schema(description="是否只读")
    @NotNull(message = "是否只读is not null")
    private Boolean isOnlyRead;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @Schema(description="备注")
    @Size(max = 255,message = "备注max length should less than 255")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 最后操作人
     */
    @TableField(value = "operator")
    @Schema(description = "最后操作人")
    @Size(max = 20, message = "最后操作人max length should less than 20")
    private String operator;

    private static final long serialVersionUID = 1L;
}