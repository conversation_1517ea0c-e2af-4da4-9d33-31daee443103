package com.bjcj.service.platformManage.modulePlan;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.platformConf.PlatformAppConfMapper;
import com.bjcj.mapper.platformManage.modulePlan.ModulePlanMapper;
import com.bjcj.model.dto.modulePlan.ModulePlanDto;
import com.bjcj.model.po.platformConf.PlatformAppConf;
import com.bjcj.model.po.platformManage.modulePlan.ModulePlan;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *@Author：qinyi
 *@Date：2024/2/27  15:21
*/
@Service
public class ModulePlanService extends ServiceImpl<ModulePlanMapper, ModulePlan> {

    @Resource
    ModulePlanMapper modulePlanMapper;

    @Resource
    PlatformAppConfMapper platformAppConfMapper;

    public JsonResult addOrEdit(ModulePlanDto dto) {
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        ModulePlan modulePlan = BeanUtil.copyProperties(dto, ModulePlan.class);
        modulePlan.setOperator(username);
        if(Objects.nonNull(modulePlan.getId()) && modulePlan.getId().equals(modulePlan.getParentId())) return JsonResult.error("不能选择自己作为父级");
        return this.saveOrUpdate(modulePlan) ? JsonResult.success() : JsonResult.error();
    }

    public List<ModulePlan> tree(Long platformAppConfId, String moduleType) {
        List<ModulePlan> dataList = this.modulePlanMapper.selectList(new LambdaQueryWrapper<ModulePlan>()
                .eq(ModulePlan::getPlatformAppConfId, platformAppConfId)
                .eq(ModulePlan::getParentId, 0L)
                .eq(StringUtils.isNotBlank(moduleType), ModulePlan::getModuleType, moduleType)
                .orderByAsc(ModulePlan::getShowSort)
        );
        List<ModulePlan> collect = new ArrayList<>();
        dataList.forEach(data -> {
            data.setChildren(findById(data.getId()));
            collect.add(data);
        });
        return collect;
    }

    public List<ModulePlan> findById(Long id){
        List<ModulePlan> dataList = this.modulePlanMapper.selectList(
                new LambdaQueryWrapper<ModulePlan>()
                        .eq(ModulePlan::getParentId,id)
        );
        List<ModulePlan> collect = new ArrayList<>();
        if(!dataList.isEmpty()){
            dataList.forEach(data -> {
                data.setChildren(findById(data.getId()));
                collect.add(data);
            });
        }
        return collect;
    }

    public JsonResult importTo(Long fromPlatformAppConfId, Long toPlatformAppConfId) {
        if(Objects.isNull(this.platformAppConfMapper.selectOne(new LambdaQueryWrapper<PlatformAppConf>().eq(PlatformAppConf::getId,toPlatformAppConfId))))
            return JsonResult.error("目标平台应用不存在");
        List<ModulePlan> modulePlanList = this.modulePlanMapper.selectList(new LambdaQueryWrapper<ModulePlan>()
                .eq(ModulePlan::getPlatformAppConfId, fromPlatformAppConfId));
        if(modulePlanList.isEmpty())
            return JsonResult.error("源平台应用无数据");
        List<ModulePlan> finalModuleList = modulePlanList;
        List<ModulePlan> collect = modulePlanList.stream()
                .filter(item -> Objects.equals(item.getParentId(), 0L))
                .map(item -> item.setChildren(getChild(item.getId(), finalModuleList)))
                .sorted(Comparator.comparingInt(module -> (module.getShowSort() == null ? 0 : module.getShowSort())))
                .toList();
        Long parent_id = 0L;
        this.insertModuleTree(parent_id,collect,toPlatformAppConfId);
        return JsonResult.success("导入成功!");
    }

    private void insertModuleTree(Long parent_id, List<ModulePlan> collect, Long toPlatformAppConfId){
        collect.forEach(module -> {
            module.setId(null);
            module.setPlatformAppConfId(toPlatformAppConfId);
            module.setParentId(parent_id);
            this.modulePlanMapper.insert(module);
            if(!module.getChildren().isEmpty())
                this.insertModuleTree(module.getId(),module.getChildren(),toPlatformAppConfId);
        });
    }

    private List<ModulePlan> getChild(Long id, List<ModulePlan> modulePlanList){
        return modulePlanList.stream()
                .filter(item -> Objects.equals(item.getParentId(), id))
                .map(item -> item.setChildren(getChild(item.getId(), modulePlanList)))
                .sorted(Comparator.comparingInt(module -> (module.getShowSort() == null ? 0 : module.getShowSort())))
                .collect(Collectors.toList());
    }
}
