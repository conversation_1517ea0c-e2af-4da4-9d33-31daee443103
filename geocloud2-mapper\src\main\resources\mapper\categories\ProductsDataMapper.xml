<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.naturalresources.products.ProductsDataMapper">

  <select id="selectPageList" resultType="com.bjcj.model.po.naturalresources.products.ProductsData">
        select pd.* from products_data pd
        where 1=1
        <if test="searchStr!= null and searchStr!= ''">
          and ( pd.name like concat('%', #{searchStr}, '%') or pd.sname like concat('%', #{searchStr}, '%') )
        </if>
        <if test="productsId!= null and productsId!=''">
          and pd.catalogue = #{productsId}
        </if>
        and pd.del = 0 and pd.status=true
        order by pd.create_time desc
        LIMIT #{size} OFFSET #{current}
  </select>

  <select id="selectListCount" resultType="java.lang.Long">
      select count(*) from (
      select pd.* from products_data pd
      where 1=1
      <if test="searchStr != null and searchStr != ''">
          and ( pd.name like concat('%', #{searchStr}, '%') or pd.sname like concat('%', #{searchStr}, '%') )
      </if>
      <if test="productsId != null and productsId != ''">
          and pd.catalogue = #{productsId}
      </if>
      and pd.del = 0 and pd.status=true
      ) c
  </select>
</mapper>