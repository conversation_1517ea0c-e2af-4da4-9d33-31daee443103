package com.bjcj.model.po.mobile;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bjcj.common.database.handler.NumericToBooleanTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *  内容管理配置表
 **/
@Schema(description = "内容管理配置表实体类")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("sys_mobile_contentconfig")
public class ContentConfig implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;//主键

    @TableField("NAME")
    @Schema(description = "内容名称")
    private String name;//内容名称

    @TableField("DATASOURCE")
    @Schema(description = "数据源")
    private String dataSource;//数据源

    @TableField("TABLENAME")
    @Schema(description = "数据表名称")
    private String tableName;//数据表名称

    @TableField("PK")
    @Schema(description = "数据表主键字段名称")
    private String pk;//数据表主键字段名称

    @TableField("TITLE")
    @Schema(description = "数据表标题字段名称")
    private String title;//数据表标题字段名称

    @TableField("CREATEUSER")
    @Schema(description = "数据表创建人字段名称")
    private String createUser;//数据表创建人字段名称

    @TableField("CREATEDEPT")
    @Schema(description = "数据表创建部门字段名称")
    private String createDept;//数据表创建部门字段名称

    @TableField(value = "ISATTACHED", typeHandler = NumericToBooleanTypeHandler.class)
    @Schema(description = "是否支持附件")
    private Boolean isAttached;//是否支持附件

    @TableField("CREATEDATE")
    @Schema(description = "数据表创建日期字段名称")
    private String createDate;//数据表创建日期字段名称

    @TableField("ENDDATE")
    @Schema(description = "数据表结束日期字段名称")
    private String endDate;//数据表结束日期字段名称

    @TableField("CONTENT")
    @Schema(description = "数据表内容字段名称")
    private String content;//数据表内容字段名称

    @TableField(value = "ISLOGINED", typeHandler = NumericToBooleanTypeHandler.class)
    @Schema(description = "是否需要登录")
    private Boolean isLogined;//是否需要登录

    @TableField("REMARK")
    @Schema(description = "备注")
    private String remark;//备注

    @TableField(value = "ISQUERY", typeHandler = NumericToBooleanTypeHandler.class)
    @Schema(description = "是否显示查询输入框及按钮")
    private Boolean isQuery; //是否显示查询输入框及按钮

    @TableField(value = "ISPICSHOW", typeHandler = NumericToBooleanTypeHandler.class)
    @Schema(description = "是否显示列表图片内容")
    private Boolean isPicShow; //是否显示列表图片内容

    @TableField(value = "ISREADSTATE", typeHandler = NumericToBooleanTypeHandler.class)
    @Schema(description = "是否显示已读状态")
    private Boolean isReadState; //是否显示已读状态

    @TableField("CONDITION")
    @Schema(description = "查询条件")
    private String condition; //查询条件

    @TableField("SEARCHTYPE")
    @Schema(description = "查询类型")
    private String searchType; //查询类型

    @TableField(value = "ISCOMMENT", typeHandler = NumericToBooleanTypeHandler.class)
    @Schema(description = "是否显示评论信息")
    private Boolean isComment; //是否显示评论信息

    @TableField(value = "ISSHARE", typeHandler = NumericToBooleanTypeHandler.class)
    @Schema(description = "是否显示分享按钮")
    private Boolean isShare; //是否显示分享按钮

    @TableField("DEVICETYPE")
    @Schema(description = "设备类型（手机或平板）")
    private String deviceType; //设备类型（手机或平板）

    @TableField(value = "ISTYPESEARCH", typeHandler = NumericToBooleanTypeHandler.class)
    @Schema (description = "是否需要查询子类型数据")
    private Boolean isTypeSearch; //是否需要查询子类型数据

    @TableField("TEMPLATESTYLE")
    @Schema(description = "页面模板主题风格")
    private String templateStyle; //页面模板主题风格

    @TableField("TEMPLEID")
    @Schema(description = "页面模板主页风格")
    private Integer templeId; //页面模板主页风格

    @TableField("READNAME")
    @Schema(description = "已读关联名称")
    private String readName; //已读关联名称

    @TableField("SQLCONDITION")
    @Schema(description = "查询语句条件sql语句")
    private String sqlCondition; //查询语句条件sql语句

    @TableField(value = "ISSTATEMENT", typeHandler = NumericToBooleanTypeHandler.class)
    @Schema(description = "是否sql语句查询")
    private Boolean isStatement; //是否sql语句查询

    @TableField(value = "ISREADMARK", typeHandler = NumericToBooleanTypeHandler.class)
    @Schema(description = "是否阅读标记")
    private Boolean isReadMark; //是否阅读标记

    @TableField("READCOFIGID")
    @Schema(description = "已读关联的配置ID")
    private String readCofigId; //已读关联的配置ID

    @TableField("QUERYSQL")
    @Schema(description = "查询所有的未读和已读的sql语句信息")
    private String querySql; //查询所有的未读和已读的sql语句信息

    @TableField("SORTFIELD")
    @Schema(description = "排序字段")
    private String sortField; //排序字段

    @TableField("ISSORTASC")
    @Schema(description = "是否升序降序")
    private String isSortAsc; //是否升序降序(1:升序 0:降序)

    @TableField("FOOTERTYPE")
    @Schema(description = "表单配置底部表单按钮样式配置")
    private String footerType; //表单配置底部表单按钮样式配置

    @TableField("ISUPLOADPIC")
    @Schema(description = "是否有上传图片控件表单配置中")
    private String isUploadPic; //是否有上传图片控件表单配置中

    @TableField("TYPE")
    @Schema(description = "内容配置类型（list为列表组件配置）")
    private String type; //内容配置类型（list为列表组件配置）

    @TableField("PAGENUMBER")
    @Schema(description = "分页条数")
    private String pageNumber; //分页条数

    @TableField("ICON")
    @Schema(description = "上传图标的ID，关联sys_mobile_ATTACH表")
    private String icon; //上传图标的ID，关联sys_mobile_ATTACH表

    @TableField("TENANTID")
    @Schema(description = "租户ID")
    private String tenantId;


}
