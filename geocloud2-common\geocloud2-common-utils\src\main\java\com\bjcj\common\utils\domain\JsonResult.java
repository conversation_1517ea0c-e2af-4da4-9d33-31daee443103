package com.bjcj.common.utils.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/30 9:51 周四
 */
@Schema(description = "返回结果")
@Data
public class JsonResult<T> {

    @Schema(description = "状态码")
    private int code;

    @Schema(description = "提示信息")
    private String msg;

    @Schema(description = "数据")
    private T data;

    public JsonResult(int code, String message) {
        this.code = code;
        this.msg = message;
    }

    public JsonResult(Integer code, String message, T data) {
        this.code = code;
        this.msg = message;
        this.data = data;
    }

    public static <T> JsonResult<T> success() {
        return success("操作成功");
    }

    public static <T> JsonResult<T> success(T data) {
        return success("操作成功", data);
    }
    public static <T> JsonResult<T> success(String message) {
        return success(message, null);
    }
    public static <T> JsonResult<T> success(Message message) {
        return success(message.message(), null);
    }

    public static <T> JsonResult<T> success(String message, T data) {
        return new JsonResult<T>(200, message, data);
    }


    public static <T> JsonResult<T> error() {
        return error("操作失败");
    }

    public static <T> JsonResult<T> error(String message) {
        return new JsonResult<T>(500, message, null);
    }

    public static <T> JsonResult<T> error(Message message) {
        return new JsonResult<T>(500, message.message(), null);
    }

    public static <T> JsonResult<T> build(Code code, Message message, T data) {
        return new JsonResult<T>(code.value(), message.message(), data);
    }

    public static <T> JsonResult<T> build(Code code, Message message) {
        return new JsonResult<T>(code.value(), message.message());
    }

    public static <T> JsonResult<T> build(int code, String message, T data) {
        return new JsonResult<T>(code, message, data);
    }

    public static <T> JsonResult<T> build(int code, String message) {
        return new JsonResult<T>(code, message, null);
    }
}
