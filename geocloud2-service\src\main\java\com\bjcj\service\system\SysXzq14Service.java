package com.bjcj.service.system;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.datamodel.MetadataWorkspacesMapper;
import com.bjcj.mapper.platformManage.specialPlan.SpecialPlanMapper;
import com.bjcj.mapper.system.SysXzq14Mapper;
import com.bjcj.model.dto.cloudportal.LandCategoryAreaStatisticsDto;
import com.bjcj.model.dto.cloudportal.LandXzqAreaStatisticsDto;
import com.bjcj.model.po.cloudportal.TbdlStatisticsConf;
import com.bjcj.model.po.datamodel.MetadataWorkspaces;
import com.bjcj.model.po.dict.SysDictData;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlan;
import com.bjcj.model.po.system.SysXzq14;
import com.bjcj.service.cloudportal.Dltb140105Service;
import com.bjcj.service.cloudportal.TbdlStatisticsConfService;
import com.bjcj.service.dict.SysDictDataService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/8 17:35 周五
 */
@Service
public class SysXzq14Service extends ServiceImpl<SysXzq14Mapper, SysXzq14> {
    @Resource
    SysXzq14Mapper sysXzq14Mapper;

    @Resource
    SysDictDataService sysDictDataService;

    @Resource
    Dltb140105Service dltb140105Service;

    @Resource
    TbdlStatisticsConfService tbdlStatisticsConfService;

    @Resource
    MetadataWorkspacesMapper metadataWorkspacesMapper;

    @Resource
    SpecialPlanMapper specialPlanMapper;

    /**
     * <h2>行政区列表</h2>
     * @param pId: 父id，不传则查山西省
     * @return com.bjcj.common.core.domain.JsonResult<java.util.List<com.bjcj.model.po.system.SysXzq14>>
     * <AUTHOR>
     * @date 2023/12/12 16:12
     */
    public JsonResult<List<SysXzq14>> xzqList(Long pId) {
        List<SysXzq14> list = list(
                Wrappers.<SysXzq14>query()
                        .lambda()
                        .isNull(ObjUtil.isEmpty(pId), SysXzq14::getPId)
                        .eq(ObjUtil.isNotEmpty(pId), SysXzq14::getPId, pId)
                        .orderByAsc(SysXzq14::getSort)
        );
        return JsonResult.success(list);
    }

    /**
     * <h2>行政区新增</h2>
     * @param sysXzq:
     * @return com.bjcj.common.core.domain.JsonResult<java.lang.Object>
     * <AUTHOR>
     * @date 2023/12/12 17:34
     */
    public JsonResult<Object> xzqCreate(SysXzq14 sysXzq) {
        save(sysXzq);
        return JsonResult.success();
    }

    /**
     * <h2>行政区删除</h2>
     * @param ids:
     * @return com.bjcj.common.core.domain.JsonResult<java.lang.Object>
     * <AUTHOR>
     * @date 2023/12/12 17:37
     */
    @Transactional(rollbackFor = Exception.class)
    public JsonResult<Object> xzqDelBatch(List<Long> ids) {
        removeBatchByIds(ids);
        return JsonResult.success();
    }

    /**
     * <h2>行政区修改</h2>
     * @param sysXzq:
     * @return com.bjcj.common.core.domain.JsonResult<java.lang.Object>
     * <AUTHOR>
     * @date 2023/12/12 17:44
     */
    public JsonResult<Object> xzqUpdate(SysXzq14 sysXzq) {
        updateById(sysXzq);
        return JsonResult.success();
    }

    /**
     * <h2>行政区信息重复校验</h2>
     * @param sysXzq:
     * @return com.bjcj.common.core.domain.JsonResult<java.lang.Object>
     * <AUTHOR>
     * @date 2023/12/12 17:34
     */
    public JsonResult<Object> xzqNameCheck(SysXzq14 sysXzq) {
        SysXzq14 one = getOne(Wrappers.<SysXzq14>query().lambda()
                .eq(SysXzq14::getName, sysXzq.getName())
                .eq(SysXzq14::getCode, sysXzq.getCode()));
        return ObjUtil.isNull(one) ? JsonResult.success() : JsonResult.error();
    }

    public List<Long> getParentIdList(Long id) {
        return this.sysXzq14Mapper.getParentIdList(id);
    }

    public JsonResult dlList(String code) {
        SysXzq14 sysXzq14 = getOne(Wrappers.<SysXzq14>query().lambda()
                .eq(SysXzq14::getCode, code));
        //根据code查询其子集
        List<SysXzq14> list = list(Wrappers.<SysXzq14>query().lambda()
                .eq(SysXzq14::getPId, sysXzq14.getId()));
        List<LandXzqAreaStatisticsDto> result = new ArrayList<>(list.size());
        list.parallelStream().forEachOrdered(xzq -> {
            LandXzqAreaStatisticsDto resultdto = new LandXzqAreaStatisticsDto();
            resultdto.setXzqName(xzq.getName());
            //计算面积
            String simplecode = xzq.getCode().replaceAll("0*$", "");
            simplecode = simplecode + "%";
            //农用地编码集
            Long dictpid = this.sysDictDataService.getOne(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictLabel, "农用地")).getDictCode();
            List<String> nydCodeList = this.sysDictDataService.list(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getPid, dictpid)).stream().map(SysDictData::getDictValue).toList();
            //计算农用地面积总和
            BigDecimal mj_sum = dltb140105Service.selectMjSum(simplecode, nydCodeList);
            resultdto.setNydArea(mj_sum);

            //建设用地编码集
            Long dictpid1 = this.sysDictDataService.getOne(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictLabel, "建设用地")).getDictCode();
            List<String> jsydCodeList = this.sysDictDataService.list(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getPid, dictpid1)).stream().map(SysDictData::getDictValue).toList();
            //计算建设用地面积总和
            BigDecimal mj_sum1 = dltb140105Service.selectMjSum(simplecode, jsydCodeList);
            resultdto.setJsydArea(mj_sum1);

            //未利用地编码集
            Long dictpid2 = this.sysDictDataService.getOne(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictLabel, "未利用地")).getDictCode();
            List<String> wlydCodeList = this.sysDictDataService.list(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getPid, dictpid2)).stream().map(SysDictData::getDictValue).toList();
            //计算未利用地面积总和
            BigDecimal mj_sum2 = dltb140105Service.selectMjSum(simplecode, wlydCodeList);
            resultdto.setWlydArea(mj_sum2);

            result.add(resultdto);
        });
        return JsonResult.success(result);
    }

    public JsonResult dlAllList(String code) {
        SysXzq14 sysXzq14 = getOne(Wrappers.<SysXzq14>query().lambda()
                .eq(SysXzq14::getCode, code));
        LandXzqAreaStatisticsDto resultdto = new LandXzqAreaStatisticsDto();
        resultdto.setXzqName(sysXzq14.getName());
        //计算面积
        String simplecode = sysXzq14.getCode().replaceAll("0*$", "");
        simplecode = simplecode + "%";
        //农用地编码集
        Long dictpid = this.sysDictDataService.getOne(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictLabel, "农用地")).getDictCode();
        List<String> nydCodeList = this.sysDictDataService.list(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getPid, dictpid)).stream().map(SysDictData::getDictValue).toList();
        //计算农用地面积总和
        BigDecimal mj_sum = dltb140105Service.selectMjSum(simplecode, nydCodeList);
        resultdto.setNydArea(mj_sum);

        //建设用地编码集
        Long dictpid1 = this.sysDictDataService.getOne(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictLabel, "建设用地")).getDictCode();
        List<String> jsydCodeList = this.sysDictDataService.list(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getPid, dictpid1)).stream().map(SysDictData::getDictValue).toList();
        //计算建设用地面积总和
        BigDecimal mj_sum1 = dltb140105Service.selectMjSum(simplecode, jsydCodeList);
        resultdto.setJsydArea(mj_sum1);

        //未利用地编码集
        Long dictpid2 = this.sysDictDataService.getOne(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictLabel, "未利用地")).getDictCode();
        List<String> wlydCodeList = this.sysDictDataService.list(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getPid, dictpid2)).stream().map(SysDictData::getDictValue).toList();
        //计算未利用地面积总和
        BigDecimal mj_sum2 = dltb140105Service.selectMjSum(simplecode, wlydCodeList);
        resultdto.setWlydArea(mj_sum2);

        //计算总面积
        List<String> allCodeList = new ArrayList<>();
        allCodeList.addAll(nydCodeList);
        allCodeList.addAll(jsydCodeList);
        allCodeList.addAll(wlydCodeList);
        BigDecimal mj_sum3 = dltb140105Service.selectMjSum(simplecode, allCodeList);
        resultdto.setAllArea(mj_sum3);

        return JsonResult.success(resultdto);
    }

    public JsonResult dlListTwo(String appName) {
        //根据appName查询专题方案id
        Long special_plan_id = this.specialPlanMapper.selectOne(new LambdaQueryWrapper<SpecialPlan>().eq(SpecialPlan::getPlanName, appName)).getId();
        TbdlStatisticsConf tbdlStatisticsConf = null;
        try {
            tbdlStatisticsConf = this.tbdlStatisticsConfService.list(new LambdaQueryWrapper<TbdlStatisticsConf>().eq(TbdlStatisticsConf::getSpecialPlanId, special_plan_id)).get(0);
        }catch (Exception e){
            return JsonResult.error("专题方案未配置统计配置");
        }
        String code = tbdlStatisticsConf.getXzqCode();
        MetadataWorkspaces workSpace = metadataWorkspacesMapper.selectById(tbdlStatisticsConf.getWorkspaceid());
        List<LandCategoryAreaStatisticsDto> list = new ArrayList<>();
        List<String> dlNameList = this.sysDictDataService.list(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictType, "djdl").eq(SysDictData::getPid, 0)).stream().map(SysDictData::getDictLabel).toList();
        //把dlNameList放入list中
        dlNameList.forEach(dlName -> {
            LandCategoryAreaStatisticsDto dto = new LandCategoryAreaStatisticsDto();
            dto.setLandCategoryName(dlName);
            list.add(dto);
        });

        String simplecode = code.replaceAll("0*$", "");
        simplecode = simplecode + "%";
        //按行政区查询几种地类下的二级地类的面积
        String finalSimplecode = simplecode;
        list.forEach(item -> {
            //地类子集
            Long dictpid = this.sysDictDataService.getOne(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictLabel, item.getLandCategoryName())).getDictCode();
            List<SysDictData> codeList = this.sysDictDataService.list(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getPid, dictpid));
            //算每个子集下的面积
            item.setChildrens(this.childrenArea(codeList, finalSimplecode));
        });

        return JsonResult.success(list);
    }

    private List<LandCategoryAreaStatisticsDto> childrenArea(List<SysDictData> codeList, String simplecode) {
        TbdlStatisticsConf tbdlStatisticsConf =  this.tbdlStatisticsConfService.list().get(0);
        MetadataWorkspaces workSpace = metadataWorkspacesMapper.selectById(tbdlStatisticsConf.getWorkspaceid());
        List<LandCategoryAreaStatisticsDto> result = new ArrayList<>();
        codeList.parallelStream().forEachOrdered(two -> {
            LandCategoryAreaStatisticsDto dto = new LandCategoryAreaStatisticsDto();
            dto.setLandCategoryName(two.getDictLabel());
            //计算面积
            BigDecimal mj_sum = this.jdbcSum2(workSpace, simplecode, two.getDictValue(), tbdlStatisticsConf);
            dto.setArea(mj_sum);
            result.add(dto);
        });
        return result;
    }

    public JsonResult dlList2(String appName) {
        //根据appName查询专题方案id
        Long special_plan_id = this.specialPlanMapper.selectOne(new LambdaQueryWrapper<SpecialPlan>().eq(SpecialPlan::getPlanName, appName)).getId();
        TbdlStatisticsConf tbdlStatisticsConf = null;
        try {
            tbdlStatisticsConf = this.tbdlStatisticsConfService.list(new LambdaQueryWrapper<TbdlStatisticsConf>().eq(TbdlStatisticsConf::getSpecialPlanId, special_plan_id)).get(0);
        }catch (Exception e){
            return JsonResult.error("专题方案未配置统计配置");
        }
        String code = tbdlStatisticsConf.getXzqCode();
        MetadataWorkspaces workSpace = metadataWorkspacesMapper.selectById(tbdlStatisticsConf.getWorkspaceid());
        SysXzq14 sysXzq14 = getOne(Wrappers.<SysXzq14>query().lambda()
                .eq(SysXzq14::getCode, code));
        //根据code查询其子集
        List<SysXzq14> list = list(Wrappers.<SysXzq14>query().lambda()
                .eq(SysXzq14::getPId, sysXzq14.getId()));
        List<LandXzqAreaStatisticsDto> result = new ArrayList<>(list.size());
        TbdlStatisticsConf finalTbdlStatisticsConf = tbdlStatisticsConf;
        list.parallelStream().forEachOrdered(xzq -> {
            LandXzqAreaStatisticsDto resultdto = new LandXzqAreaStatisticsDto();
            resultdto.setXzqName(xzq.getName());
            //计算面积
            String simplecode = xzq.getCode().replaceAll("0*$", "");
            simplecode = simplecode + "%";
            //农用地编码集
            Long dictpid = this.sysDictDataService.getOne(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictLabel, "农用地")).getDictCode();
            List<String> nydCodeList = this.sysDictDataService.list(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getPid, dictpid)).stream().map(SysDictData::getDictValue).toList();
            //计算农用地面积总和
            // BigDecimal mj_sum = dltb140105Service.selectMjSum(simplecode, nydCodeList);
            BigDecimal mj_sum = this.jdbcSum(workSpace, simplecode, nydCodeList, finalTbdlStatisticsConf);
            resultdto.setNydArea(mj_sum);

            //建设用地编码集
            Long dictpid1 = this.sysDictDataService.getOne(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictLabel, "建设用地")).getDictCode();
            List<String> jsydCodeList = this.sysDictDataService.list(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getPid, dictpid1)).stream().map(SysDictData::getDictValue).toList();
            //计算建设用地面积总和
            // BigDecimal mj_sum1 = dltb140105Service.selectMjSum(simplecode, jsydCodeList);
            BigDecimal mj_sum1 = this.jdbcSum(workSpace, simplecode, jsydCodeList, finalTbdlStatisticsConf);
            resultdto.setJsydArea(mj_sum1);

            //未利用地编码集
            Long dictpid2 = this.sysDictDataService.getOne(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictLabel, "未利用地")).getDictCode();
            List<String> wlydCodeList = this.sysDictDataService.list(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getPid, dictpid2)).stream().map(SysDictData::getDictValue).toList();
            //计算未利用地面积总和
            // BigDecimal mj_sum2 = dltb140105Service.selectMjSum(simplecode, wlydCodeList);
            BigDecimal mj_sum2 = this.jdbcSum(workSpace, simplecode, wlydCodeList, finalTbdlStatisticsConf);
            resultdto.setWlydArea(mj_sum2);

            result.add(resultdto);
        });
        return JsonResult.success(result);
    }

    private BigDecimal jdbcSum(MetadataWorkspaces workSpace,String simplecode,List<String> codeList,TbdlStatisticsConf tbdlStatisticsConf){
        String inCondition = codeList.stream().map(code -> "'" + code + "'").collect(Collectors.joining(","));
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            String url = workSpace.getInstance().replace("sde", "jdbc").replace("$", ":thin:@//").replace("/orcl",":1521/orcl");
            Connection conn= DriverManager.getConnection(url,workSpace.getUsername(),workSpace.getPassword());
            Statement stmt=conn.createStatement();
            String query = "select sum(TBMJ) as sums from " + tbdlStatisticsConf.getTableName() + " where ZLDWDM like " + "'" + simplecode + "'" + " and DLBM in (" + inCondition + ")";
            System.out.println("SQL-----"+query);
            ResultSet rs = stmt.executeQuery(query);
            BigDecimal mj_sum = null;
            while (rs.next()){
                mj_sum = (BigDecimal) rs.getObject("SUMS");
            }
            stmt.close();
            rs.close();
            conn.close();
            return mj_sum;
        }catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private BigDecimal jdbcSum2(MetadataWorkspaces workSpace,String simplecode,String codes,TbdlStatisticsConf tbdlStatisticsConf){
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            String url = workSpace.getInstance().replace("sde", "jdbc").replace("$", ":thin:@//").replace("/orcl",":1521/orcl");
            Connection conn= DriverManager.getConnection(url,workSpace.getUsername(),workSpace.getPassword());
            Statement stmt=conn.createStatement();
            String query = "select sum(TBMJ) as sums from " + tbdlStatisticsConf.getTableName() + " where ZLDWDM like " + "'" + simplecode + "'" + " and DLBM = " + "'" + codes + "'";
            System.out.println("SQL-----"+query);
            ResultSet rs = stmt.executeQuery(query);
            BigDecimal mj_sum = null;
            while (rs.next()){
                mj_sum = (BigDecimal) rs.getObject("SUMS");
            }
            stmt.close();
            rs.close();
            conn.close();
            return mj_sum;
        }catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public JsonResult dlAllList2(String appName) {
        //根据appName查询专题方案id
        Long special_plan_id = this.specialPlanMapper.selectOne(new LambdaQueryWrapper<SpecialPlan>().eq(SpecialPlan::getPlanName, appName)).getId();
        TbdlStatisticsConf tbdlStatisticsConf = null;
        try {
            tbdlStatisticsConf = this.tbdlStatisticsConfService.list(new LambdaQueryWrapper<TbdlStatisticsConf>().eq(TbdlStatisticsConf::getSpecialPlanId, special_plan_id)).get(0);
        }catch (Exception e){
            return JsonResult.error("专题方案未配置统计配置");
        }
        String code = tbdlStatisticsConf.getXzqCode();
        MetadataWorkspaces workSpace = metadataWorkspacesMapper.selectById(tbdlStatisticsConf.getWorkspaceid());
        SysXzq14 sysXzq14 = getOne(Wrappers.<SysXzq14>query().lambda()
                .eq(SysXzq14::getCode, code));
        LandXzqAreaStatisticsDto resultdto = new LandXzqAreaStatisticsDto();
        resultdto.setXzqName(sysXzq14.getName());
        //计算面积
        String simplecode = sysXzq14.getCode().replaceAll("0*$", "");
        simplecode = simplecode + "%";
        //农用地编码集
        Long dictpid = this.sysDictDataService.getOne(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictLabel, "农用地")).getDictCode();
        List<String> nydCodeList = this.sysDictDataService.list(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getPid, dictpid)).stream().map(SysDictData::getDictValue).toList();
        //计算农用地面积总和
        // BigDecimal mj_sum = dltb140105Service.selectMjSum(simplecode, nydCodeList);
        BigDecimal mj_sum = this.jdbcSum(workSpace, simplecode, nydCodeList, tbdlStatisticsConf);
        resultdto.setNydArea(mj_sum);

        //建设用地编码集
        Long dictpid1 = this.sysDictDataService.getOne(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictLabel, "建设用地")).getDictCode();
        List<String> jsydCodeList = this.sysDictDataService.list(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getPid, dictpid1)).stream().map(SysDictData::getDictValue).toList();
        //计算建设用地面积总和
        // BigDecimal mj_sum1 = dltb140105Service.selectMjSum(simplecode, jsydCodeList);
        BigDecimal mj_sum1 = this.jdbcSum(workSpace, simplecode, jsydCodeList, tbdlStatisticsConf);
        resultdto.setJsydArea(mj_sum1);

        //未利用地编码集
        Long dictpid2 = this.sysDictDataService.getOne(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictLabel, "未利用地")).getDictCode();
        List<String> wlydCodeList = this.sysDictDataService.list(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getPid, dictpid2)).stream().map(SysDictData::getDictValue).toList();
        //计算未利用地面积总和
        // BigDecimal mj_sum2 = dltb140105Service.selectMjSum(simplecode, wlydCodeList);
        BigDecimal mj_sum2 = this.jdbcSum(workSpace, simplecode, wlydCodeList, tbdlStatisticsConf);
        resultdto.setWlydArea(mj_sum2);

        //计算总面积
        // List<String> allCodeList = new ArrayList<>();
        // allCodeList.addAll(nydCodeList);
        // allCodeList.addAll(jsydCodeList);
        // allCodeList.addAll(wlydCodeList);
        BigDecimal mj_sum3 = mj_sum.add(mj_sum1).add(mj_sum2);
        resultdto.setAllArea(mj_sum3);
        return JsonResult.success(resultdto);
    }

    public String getOnes(Long xzqCode) {
        return this.sysXzq14Mapper.getOnes(xzqCode);
    }
}
