package com.bjcj.mapper.naturalresources.categories;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.naturalresources.categories.CategoriesDataInfo;
import com.bjcj.model.po.naturalresources.categories.CategoriesService;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/28 10:04 周二
 */
public interface CategoriesDataInfoMapper extends BaseMapper<CategoriesDataInfo> {


    List<CategoriesService> selectPageList(@Param("current") Long current, @Param("size") Long size, @Param("searchStr") String searchStr, @Param("categoriesDataId") String categoriesDataId);

    Long selectListCount(@Param("searchStr")  String searchStr,@Param("categoriesDataId")  String categoriesDataId);

    void updateReviewStatus(@Param("resId") Long resId,@Param("status") int status);
}
