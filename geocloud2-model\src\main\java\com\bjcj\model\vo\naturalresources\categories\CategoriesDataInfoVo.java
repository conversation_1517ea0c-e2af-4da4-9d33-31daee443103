package com.bjcj.model.vo.naturalresources.categories;

import com.bjcj.model.po.naturalresources.categories.CategoriesFactor;
import com.bjcj.model.po.naturalresources.categories.CategoriesMeta;
import com.bjcj.model.po.naturalresources.categories.CategoriesPhysics;
import com.bjcj.model.po.naturalresources.categories.CategoriesService;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/29 8:44 周三
 */
@Data
public class CategoriesDataInfoVo implements Serializable {

    private Long id;

    @Schema(description="数据目录名称")
    private String name;

    @Schema(description="资源目录")
    private String catalogue;

    @Schema(description="行政区")
    private String district;

    @Schema(description="行政区")
    private String xzq;

    @Schema(description="年份")
    private String year;

    @Schema(description="比例尺")
    private String scale;

    @Schema(description="坐标系类型")
    private String coordinate;

    @Schema(description="省市县标识")
    private String districtBs;

    @Schema(description="数据类型(1矢量数据2栅格数据)")
    private int dataItype;

    @Schema(description="权限类型（1私有2公开3安全）")
    private int authItype;

    @Schema(description="是否显示（0显示1不显示）")
    private int show;

    @Schema(description="状态（0不运行1运行）")
    private Boolean status;

    @Schema(description="数据类型(1矢量数据2栅格数据)")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDateTime createTime;

    @Schema(description="删除标识（0未删除1已删除）")
    private int del;

    @Schema(description="审核状态")
    private Integer reviewStatus;

    @Schema(description="注册审核状态")
    private Integer registerReviewStatus;

    // private Map<String, List<CategoriesService>> categoriesService;
    private List<CategoriesService> categoriesService;

    private List<CategoriesPhysics> categoriesPhysics;

    private List<CategoriesFactor> categoriesFactor;

    private CategoriesMeta categoriesMeta;

    @Schema(description="最后操作人")
    private String operater;

}
