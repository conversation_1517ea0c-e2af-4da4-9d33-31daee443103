<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.naturalresources.categories.ResourceTagsMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.naturalresources.categories.ResourceTags">
    <!--@mbg.generated-->
    <!--@Table public.resource_tags-->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="resourceid" jdbcType="CHAR" property="resourceid" />
    <result column="resourcecategory" jdbcType="NUMERIC" property="resourcecategory" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", resourceid, resourcecategory
  </sql>
</mapper>