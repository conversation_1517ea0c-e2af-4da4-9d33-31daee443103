package com.bjcj.model.dto.safetyManage;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
*@Author：qinyi
*@Date：2023/11/24  15:46
*/

@Schema(description="部门表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_dept")
public class SafetyDeptDto implements Serializable {
    /**
     * 主键
     */
    @Schema(description="主键")
    private Long id;

    /**
     * 部门名称
     */
    @Schema(description="部门名称")
    @Size(max = 50,message = "部门名称最大长度要小于 50")
    @NotBlank(message = "部门名称不能为空")
    @RequestKeyParam(name = "deptName")
    private String deptName;

    /**
     * 显示顺序
     */
    @Schema(description="显示顺序")
    @NotNull(message = "显示顺序不能为null")
    private Integer showSort;

    /**
     * 所属机构id
     */
    @Schema(description="所属机构id")
    @NotNull(message = "所属机构id不能为null")
    private Long parentInstitutionId;

    /**
     * 上级部门id
     */
    @Schema(description="上级部门id")
    private Long parentDeptId;

    private static final long serialVersionUID = 1L;
}