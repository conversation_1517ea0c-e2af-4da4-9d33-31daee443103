package com.bjcj.web.naturalresources.resReview;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.resReview.ResRegisterLogDto;
import com.bjcj.model.po.naturalresources.categories.CategoriesService;
import com.bjcj.model.po.naturalresources.resReview.ResRegisterLog;
import com.bjcj.service.naturalresources.categories.CategoriesServiceService;
import com.bjcj.service.naturalresources.resReview.ResRegisterLogService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;


/**
* 注册审核记录(public.res_register_log)表控制层
* <AUTHOR>
 * 2024.1.22
*/
@RestController
@RequestMapping("/resRegisterLog")
@Tag(name = "注册审核")
@Validated
public class ResRegisterLogController {
    /**
    * 服务对象
    */
    @Resource
    private ResRegisterLogService resRegisterLogService;

    @Resource
    private CategoriesServiceService cateService;

    @SaCheckPermission("sys:read")
    @Operation(summary = "列表", description = "条件:分页,审核状态,资源类型,名称,时间区间搜索----已审核和待审核的返回数据字段不一样,需要自己匹配一下")
    @ApiOperationSupport(order = 1)
    @GetMapping("/list")
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "searchStr", description = "名称", required = false),
            @Parameter(name = "reviewStatus", description = "审核状态(待审核(默认),已审核)", required = true),
            @Parameter(name = "serviceType", description = "资源类型", required = false),
            @Parameter(name = "startTime", description = "开始时间", required = false),
            @Parameter(name = "endTime", description = "结束时间", required = false),
            @Parameter(name = "orderAsc", description = "排序方式是否asc", required = true),
            @Parameter(name = "orderColumn", description = "排序字段(待审核时候传:registerdate,registerman;已审核时候传:publish_time或publish_user_name)", required = true)
    })
    public JsonResult<Page> list(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam(value = "searchStr",required = false) String searchStr,
            @RequestParam(value = "reviewStatus",required = true) String reviewStatus,
            @RequestParam(value = "serviceType",required = false) String serviceType,
            @RequestParam(value = "startTime",required = false) String startTime,
            @RequestParam(value = "endTime",required = false) String endTime,
            @RequestParam(value = "orderAsc",required = true) Boolean orderAsc,
            @RequestParam(value = "orderColumn",required = true) String orderColumn){
        Page<ResRegisterLog> pager = new Page<>(page,pageSize);
        List<OrderItem> orderList = new ArrayList<>();
        OrderItem orderItem = new OrderItem(){{setAsc(orderAsc);setColumn(orderColumn);}};
        orderList.add(orderItem);
        pager.setOrders(orderList);
        return this.resRegisterLogService.queryPage(pager,searchStr,reviewStatus,serviceType,startTime,endTime,orderAsc,orderColumn,page,pageSize);
    }


    @OperaLog(operaModule = "审核",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "审核注册的数据服务")
    @SaCheckPermission("sys:write")
    @PostMapping("/review")
    @Operation(summary = "审核", description = "审核")
    @ApiOperationSupport(order = 2)
    @Transactional(rollbackFor = Exception.class)
    @RequestLock(prefix = "review")
    public JsonResult review(@Validated @RequestBody ResRegisterLogDto dto){
        return this.resRegisterLogService.saveData(dto);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "获取mapserver", description = "根据资源id获取其下mapserver")
    @ApiOperationSupport(order = 3)
    @GetMapping("/mapServerDetails")
    @Parameters({
            @Parameter(name = "resId", description = "资源id", required = true)
    })
    public JsonResult<List<CategoriesService>> mapServerDetails(@RequestParam("resId") Long resId){
        return JsonResult.success(this.cateService.list(new LambdaQueryWrapper<CategoriesService>().eq(CategoriesService::getId,resId)));
    }
}
