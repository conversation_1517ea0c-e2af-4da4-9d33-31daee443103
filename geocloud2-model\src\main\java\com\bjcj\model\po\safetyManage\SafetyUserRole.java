package com.bjcj.model.po.safetyManage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2023/11/29  11:37
*/
@Schema(description="角色用户关联表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_user_role")
public class SafetyUserRole implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    @NotNull(message = "不能为null")
    private Long id;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    @Schema(description="用户id")
    @NotNull(message = "用户id不能为null")
    private Long userId;

    /**
     * 角色id
     */
    @TableField(value = "role_id")
    @Schema(description="角色id")
    @NotNull(message = "角色id不能为null")
    private Long roleId;


    @TableField(exist = false)
    @Schema(description="用户真实姓名")
    private String nickName;

    /**
     * 机构名称
     */
    @TableField(exist = false)
    @Schema(description="机构名称")
    private String institutionName;

    /**
     * 部门名称
     */
    @TableField(exist = false)
    @Schema(description="部门名称")
    private String deptName;

    @TableField(exist = false)
    @Schema(description="角色名称")
    private String roleName;


    private static final long serialVersionUID = 1L;
}