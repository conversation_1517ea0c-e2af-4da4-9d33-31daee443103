package com.bjcj.web.safetyManage.modulePlan;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.modulePlan.ModulePlanDto;
import com.bjcj.model.po.platformManage.modulePlan.ModulePlan;
import com.bjcj.service.platformManage.modulePlan.ModulePlanService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* 组件配置方案表(public.module_plan)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/module_plan")
@Tag(name = "7组件配置方案")
@Validated
public class ModulePlanController {

    /**
    * 服务对象
    */
    @Resource
    private ModulePlanService modulePlanService;

    @OperaLog(operaModule = "新增/编辑",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "新增/编辑")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑", description = "新增/编辑")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "addOrEdit")
    public JsonResult addOrEdit(@Validated @RequestBody ModulePlanDto dto){
        return modulePlanService.addOrEdit(dto);
    }

    @OperaLog(operaModule = "删除组件",operaType = OperaLogConstant.DELETE,operaDesc = "删除组件")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除组件", description = "删除组件")
    @ApiOperationSupport(order = 3)
    public JsonResult delete(@PathVariable("id") Long id){
        if(!modulePlanService.list(new LambdaQueryWrapper<ModulePlan>().eq(ModulePlan::getParentId, id)).isEmpty())
            return JsonResult.error("请先删除子组件");
        return this.modulePlanService.removeById(id) ? JsonResult.success() : JsonResult.error();
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "组件树", description = "组件树")
    @ApiOperationSupport(order = 1)
    @GetMapping("/tree")
    @Parameters({
            @Parameter(name = "platformAppConfId", description = "平台应用配置id", required = true),
            @Parameter(name = "moduleType", description = "组件类型", required = false)
    })
    public JsonResult<List<ModulePlan>> tree(@RequestParam(value = "platformAppConfId", required = true) Long platformAppConfId,
                                              @RequestParam(value = "moduleType", required = false) String moduleType){
        return JsonResult.success(this.modulePlanService.tree(platformAppConfId,moduleType));
    }

    @OperaLog(operaModule = "导出组件到...",operaType = OperaLogConstant.CREATE,operaDesc = "导出组件到...")
    @SaCheckPermission("sys:write")
    @PostMapping("/importTo")
    @Operation(summary = "导出组件到...", description = "导出组件到...")
    @ApiOperationSupport(order = 4)
    @Parameters({
            @Parameter(name = "fromPlatformAppConfId", description = "导出的平台id", required = true),
            @Parameter(name = "toPlatformAppConfId", description = "导入的平台id", required = true)
    })
    public JsonResult importTo(@RequestParam("fromPlatformAppConfId") Long fromPlatformAppConfId,
                               @RequestParam("toPlatformAppConfId") Long toPlatformAppConfId) {
        return modulePlanService.importTo(fromPlatformAppConfId, toPlatformAppConfId);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "根据id查详情", description = "根据id查详情")
    @ApiOperationSupport(order = 5)
    @GetMapping("/queryById")
    @Parameters({
            @Parameter(name = "id", description = "id", required = true)
    })
    public JsonResult queryById(@RequestParam("id") Long id){
        return JsonResult.success(this.modulePlanService.getById(id));
    }





}
