package com.bjcj.model.dto.fieldPlan;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/2/20  09:19
*/

@Data
public class DataLayerIdsDto implements Serializable {
    @Schema(description="图层id LIST")
    @NotNull(message = "图层id LIST is not null")
    private List<String> dataLayerIdList;

    @Schema(description="字段展示方案id")
    @NotNull(message = "字段展示方案id is not null")
    private Long fieldPlanId;

    private static final long serialVersionUID = 1L;
}