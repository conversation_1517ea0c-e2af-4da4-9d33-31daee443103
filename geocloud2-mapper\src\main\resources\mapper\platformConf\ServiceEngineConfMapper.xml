<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.platformConf.ServiceEngineConfMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.platformConf.ServiceEngineConf">
    <!--@mbg.generated-->
    <!--@Table public.service_engine_conf-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="server_colony_name" jdbcType="VARCHAR" property="serverColonyName" />
    <result column="server_colony_type" jdbcType="VARCHAR" property="serverColonyType" />
    <result column="server_user_name" jdbcType="VARCHAR" property="serverUserName" />
    <result column="server_password" jdbcType="VARCHAR" property="serverPassword" />
    <result column="token_url" jdbcType="VARCHAR" property="tokenUrl" />
    <result column="base_url" jdbcType="VARCHAR" property="baseUrl" />
    <result column="is_run" jdbcType="CHAR" property="isRun" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, server_colony_name, server_colony_type, server_user_name, server_password, token_url, 
    base_url, is_run, remark, create_time, update_time, "operator"
  </sql>
</mapper>