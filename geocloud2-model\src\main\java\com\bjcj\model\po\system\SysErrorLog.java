package com.bjcj.model.po.system;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 系统异常日志
 * <AUTHOR>
 * @date 2023/12/8 10:51 周五
 */
@Schema(description="系统异常日志")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "public.sys_error_log")
public class SysErrorLog {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    @TableField(value = "req_param")
    @Schema(description="请求参数")
    private String reqParam;

    @TableField(value = "\"name\"")
    @Schema(description="异常名称")
    private String name;

    @TableField(value = "message")
    @Schema(description="异常信息")
    private String message;

    @TableField(value = "user_id")
    @Schema(description="操作用户id")
    private Long userId;

    @TableField(value = "user_name")
    @Schema(description="操作用户名字")
    private String userName;

    @TableField(value = "\"method\"")
    @Schema(description="操作方法")
    private String method;

    @TableField(value = "uri")
    @Schema(description="")
    private String uri;

    @TableField(value = "ip")
    @Schema(description="ip")
    private String ip;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description="时间")
    private LocalDateTime createTime;

    @TableField(value = "type")
    @Schema(description="请求方式")
    private String type;
}