package com.bjcj.model.vo.dict;

import com.bjcj.model.po.dict.SysDictData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Author：qinyi
 * @Date：2024/2/2 9:42
 */
@Data
@Schema(description="字典数据分组列表")
public class DictGroupDataVO {

    @Schema(description="组名称")
    private String groupName;

    @Schema(description="组子集")
    private List<SysDictData> dictData;
}
