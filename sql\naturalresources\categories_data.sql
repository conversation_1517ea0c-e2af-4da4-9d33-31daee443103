/*
 Navicat Premium Data Transfer

 Source Server         : pgsql12
 Source Server Type    : PostgreSQL
 Source Server Version : 120015
 Source Host           : localhost:5433
 Source Catalog        : geocloud2
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 120015
 File Encoding         : 65001

 Date: 13/12/2023 08:53:10
*/


-- ----------------------------
-- Table structure for categories_data
-- ----------------------------
DROP TABLE IF EXISTS "public"."categories_data";
CREATE TABLE "public"."categories_data" (
  "id" int8 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "code" varchar(255) COLLATE "pg_catalog"."default",
  "parent_id" int8,
  "display_order" int4,
  "description" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "del" int4 DEFAULT 0,
  "resource_category" int4,
  "resource_count" int4,
  "usabled_resource_count" int4,
  "status" bool
)
;
COMMENT ON COLUMN "public"."categories_data"."name" IS '数据目录名称';
COMMENT ON COLUMN "public"."categories_data"."code" IS '编码';
COMMENT ON COLUMN "public"."categories_data"."parent_id" IS '上级id';
COMMENT ON COLUMN "public"."categories_data"."display_order" IS '数据目录排序';
COMMENT ON COLUMN "public"."categories_data"."description" IS '数据目录描述';
COMMENT ON COLUMN "public"."categories_data"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."categories_data"."del" IS '删除状态（0未删除，1已删除）';
COMMENT ON COLUMN "public"."categories_data"."resource_category" IS '资源类别';
COMMENT ON COLUMN "public"."categories_data"."status" IS '显示状态';

-- ----------------------------
-- Table structure for categories_data_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."categories_data_info";
CREATE TABLE "public"."categories_data_info" (
  "id" int8 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "catalogue" varchar(255) COLLATE "pg_catalog"."default",
  "district" varchar(255) COLLATE "pg_catalog"."default",
  "year" varchar(255) COLLATE "pg_catalog"."default",
  "scale" varchar(255) COLLATE "pg_catalog"."default",
  "coordinate" varchar(255) COLLATE "pg_catalog"."default",
  "district_bs" varchar(255) COLLATE "pg_catalog"."default",
  "data_itype" int4,
  "auth_itype" int4,
  "show" int4,
  "status" bool,
  "create_time" timestamp(6),
  "del" int4 DEFAULT 0
)
;
COMMENT ON COLUMN "public"."categories_data_info"."name" IS '名称';
COMMENT ON COLUMN "public"."categories_data_info"."catalogue" IS '资源目录';
COMMENT ON COLUMN "public"."categories_data_info"."district" IS '行政区';
COMMENT ON COLUMN "public"."categories_data_info"."year" IS '年份';
COMMENT ON COLUMN "public"."categories_data_info"."scale" IS '比例尺';
COMMENT ON COLUMN "public"."categories_data_info"."coordinate" IS '坐标系类型';
COMMENT ON COLUMN "public"."categories_data_info"."district_bs" IS '省市县标识';
COMMENT ON COLUMN "public"."categories_data_info"."data_itype" IS '数据类型(1矢量数据2栅格数据)';
COMMENT ON COLUMN "public"."categories_data_info"."auth_itype" IS '权限类型（1私有2公开3安全）';
COMMENT ON COLUMN "public"."categories_data_info"."show" IS '是否显示';
COMMENT ON COLUMN "public"."categories_data_info"."status" IS '状态';
COMMENT ON COLUMN "public"."categories_data_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."categories_data_info"."del" IS '删除标识（0未删除1已删除）';

-- ----------------------------
-- Table structure for categories_factor
-- ----------------------------
DROP TABLE IF EXISTS "public"."categories_factor";
CREATE TABLE "public"."categories_factor" (
  "id" int8 NOT NULL,
  "parent_id" int8,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "sname" varchar(255) COLLATE "pg_catalog"."default",
  "district" varchar(255) COLLATE "pg_catalog"."default",
  "standard" varchar(255) COLLATE "pg_catalog"."default",
  "structure" varchar(255) COLLATE "pg_catalog"."default",
  "fwitype" varchar(255) COLLATE "pg_catalog"."default",
  "fwaddress" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "del" int4
)
;
COMMENT ON COLUMN "public"."categories_factor"."name" IS '名称';
COMMENT ON COLUMN "public"."categories_factor"."sname" IS '显示名称';
COMMENT ON COLUMN "public"."categories_factor"."district" IS '行政区';
COMMENT ON COLUMN "public"."categories_factor"."standard" IS '数据标准';
COMMENT ON COLUMN "public"."categories_factor"."structure" IS '表结构';
COMMENT ON COLUMN "public"."categories_factor"."fwitype" IS '服务类型';
COMMENT ON COLUMN "public"."categories_factor"."fwaddress" IS '服务地址';
COMMENT ON COLUMN "public"."categories_factor"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."categories_factor"."del" IS '删除';

-- ----------------------------
-- Table structure for categories_meta
-- ----------------------------
DROP TABLE IF EXISTS "public"."categories_meta";
CREATE TABLE "public"."categories_meta" (
  "id" int8 NOT NULL,
  "parent_id" int8,
  "provider_name" varchar(255) COLLATE "pg_catalog"."default",
  "provider_code" varchar(255) COLLATE "pg_catalog"."default",
  "provide_type" varchar(255) COLLATE "pg_catalog"."default",
  "service_web" varchar(255) COLLATE "pg_catalog"."default",
  "sname" varchar(255) COLLATE "pg_catalog"."default",
  "sphone" varchar(255) COLLATE "pg_catalog"."default",
  "provide_address" varchar(255) COLLATE "pg_catalog"."default",
  "service_form" varchar(255) COLLATE "pg_catalog"."default",
  "service_type" varchar(255) COLLATE "pg_catalog"."default",
  "wl_type" varchar(255) COLLATE "pg_catalog"."default",
  "service_capacity" varchar(255) COLLATE "pg_catalog"."default",
  "service_time" timestamp(6),
  "service_itype" varchar(255) COLLATE "pg_catalog"."default",
  "service_zy" varchar(255) COLLATE "pg_catalog"."default",
  "service_content" varchar(255) COLLATE "pg_catalog"."default",
  "scale" varchar(255) COLLATE "pg_catalog"."default",
  "coordinate" varchar(255) COLLATE "pg_catalog"."default",
  "dlongitude" varchar(255) COLLATE "pg_catalog"."default",
  "xlongitude" varchar(255) COLLATE "pg_catalog"."default",
  "blongitude" varchar(255) COLLATE "pg_catalog"."default",
  "nlongitude" varchar(255) COLLATE "pg_catalog"."default",
  "cycle" varchar(255) COLLATE "pg_catalog"."default",
  "fb_time" timestamp(6),
  "zt_time" timestamp(6),
  "gx_time" timestamp(6),
  "zbf" varchar(255) COLLATE "pg_catalog"."default",
  "bgf" varchar(255) COLLATE "pg_catalog"."default",
  "scf" varchar(255) COLLATE "pg_catalog"."default",
  "dire_name" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."categories_meta"."parent_id" IS '父级id';
COMMENT ON COLUMN "public"."categories_meta"."provider_name" IS '提供方姓名';
COMMENT ON COLUMN "public"."categories_meta"."provider_code" IS '提供方代码';
COMMENT ON COLUMN "public"."categories_meta"."provide_type" IS '提供方类型';
COMMENT ON COLUMN "public"."categories_meta"."service_web" IS '数据应用层次';
COMMENT ON COLUMN "public"."categories_meta"."sname" IS '联系人';
COMMENT ON COLUMN "public"."categories_meta"."sphone" IS '联系方式';
COMMENT ON COLUMN "public"."categories_meta"."provide_address" IS '提供方地址';
COMMENT ON COLUMN "public"."categories_meta"."service_form" IS '数据格式';
COMMENT ON COLUMN "public"."categories_meta"."service_type" IS '数据类型';
COMMENT ON COLUMN "public"."categories_meta"."wl_type" IS '网络类型';
COMMENT ON COLUMN "public"."categories_meta"."service_capacity" IS '数据存储量';
COMMENT ON COLUMN "public"."categories_meta"."service_time" IS '数据生产时间';
COMMENT ON COLUMN "public"."categories_meta"."service_itype" IS '数据资源状态';
COMMENT ON COLUMN "public"."categories_meta"."service_zy" IS '数据资源摘要';
COMMENT ON COLUMN "public"."categories_meta"."service_content" IS '数据资源内容';
COMMENT ON COLUMN "public"."categories_meta"."scale" IS '比例尺';
COMMENT ON COLUMN "public"."categories_meta"."coordinate" IS '坐标系统';
COMMENT ON COLUMN "public"."categories_meta"."dlongitude" IS '东边经度';
COMMENT ON COLUMN "public"."categories_meta"."xlongitude" IS '西边经度';
COMMENT ON COLUMN "public"."categories_meta"."blongitude" IS '北边经度';
COMMENT ON COLUMN "public"."categories_meta"."nlongitude" IS '南边经度';
COMMENT ON COLUMN "public"."categories_meta"."cycle" IS '更新周期';
COMMENT ON COLUMN "public"."categories_meta"."fb_time" IS '发布时间';
COMMENT ON COLUMN "public"."categories_meta"."zt_time" IS '停用时间';
COMMENT ON COLUMN "public"."categories_meta"."gx_time" IS '更新时间';
COMMENT ON COLUMN "public"."categories_meta"."zbf" IS '数据资源主办方';
COMMENT ON COLUMN "public"."categories_meta"."bgf" IS '数据资源保管方';
COMMENT ON COLUMN "public"."categories_meta"."scf" IS '数据资源生产方';
COMMENT ON COLUMN "public"."categories_meta"."dire_name" IS '关联目录名称';

-- ----------------------------
-- Table structure for categories_physics
-- ----------------------------
DROP TABLE IF EXISTS "public"."categories_physics";
CREATE TABLE "public"."categories_physics" (
  "id" int8 NOT NULL,
  "parent_id" int8,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "sname" varchar(255) COLLATE "pg_catalog"."default",
  "district" varchar(255) COLLATE "pg_catalog"."default",
  "standard" varchar(255) COLLATE "pg_catalog"."default",
  "structure" varchar(255) COLLATE "pg_catalog"."default",
  "workspace" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "del" int4 DEFAULT 0
)
;
COMMENT ON COLUMN "public"."categories_physics"."name" IS '名称';
COMMENT ON COLUMN "public"."categories_physics"."sname" IS '显示名称';
COMMENT ON COLUMN "public"."categories_physics"."district" IS '行政区';
COMMENT ON COLUMN "public"."categories_physics"."standard" IS '数据标准';
COMMENT ON COLUMN "public"."categories_physics"."structure" IS '表结构';
COMMENT ON COLUMN "public"."categories_physics"."workspace" IS '工作空间';
COMMENT ON COLUMN "public"."categories_physics"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."categories_physics"."del" IS '删除';

-- ----------------------------
-- Table structure for categories_service
-- ----------------------------
DROP TABLE IF EXISTS "public"."categories_service";
CREATE TABLE "public"."categories_service" (
  "id" int8 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "fwbs" varchar(255) COLLATE "pg_catalog"."default",
  "sname" varchar(255) COLLATE "pg_catalog"."default",
  "address" varchar(255) COLLATE "pg_catalog"."default",
  "fwitype" varchar(255) COLLATE "pg_catalog"."default",
  "fwgroup" varchar(255) COLLATE "pg_catalog"."default",
  "show" int4,
  "fwsort" int4,
  "tags" varchar(255) COLLATE "pg_catalog"."default",
  "imgurl" varchar(255) COLLATE "pg_catalog"."default",
  "remark" varchar(255) COLLATE "pg_catalog"."default",
  "fwcs" text COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "del" int4 DEFAULT 0,
  "parent_id" int8,
  "status" bool DEFAULT true
)
;
COMMENT ON COLUMN "public"."categories_service"."name" IS '注册名称';
COMMENT ON COLUMN "public"."categories_service"."fwbs" IS '服务标识';
COMMENT ON COLUMN "public"."categories_service"."sname" IS '显示名称';
COMMENT ON COLUMN "public"."categories_service"."address" IS '注册地址';
COMMENT ON COLUMN "public"."categories_service"."fwitype" IS '服务类型';
COMMENT ON COLUMN "public"."categories_service"."fwgroup" IS '服务分组';
COMMENT ON COLUMN "public"."categories_service"."show" IS '是否显示0显示1不显示';
COMMENT ON COLUMN "public"."categories_service"."fwsort" IS '服务排序';
COMMENT ON COLUMN "public"."categories_service"."tags" IS '标签';
COMMENT ON COLUMN "public"."categories_service"."imgurl" IS '缩略图';
COMMENT ON COLUMN "public"."categories_service"."remark" IS '描述';
COMMENT ON COLUMN "public"."categories_service"."fwcs" IS '服务参数';
COMMENT ON COLUMN "public"."categories_service"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."categories_service"."del" IS '删除0不1是';
COMMENT ON COLUMN "public"."categories_service"."status" IS '状态';

-- ----------------------------
-- Table structure for function_data
-- ----------------------------
DROP TABLE IF EXISTS "public"."function_data";
CREATE TABLE "public"."function_data" (
  "id" int8 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "sort" int4,
  "parent_id" int8,
  "description" varchar(255) COLLATE "pg_catalog"."default",
  "status" bool,
  "create_time" timestamp(6),
  "del" int4 DEFAULT 0
)
;
COMMENT ON COLUMN "public"."function_data"."name" IS '服务目录名称';
COMMENT ON COLUMN "public"."function_data"."sort" IS '排序';
COMMENT ON COLUMN "public"."function_data"."parent_id" IS '上级id';
COMMENT ON COLUMN "public"."function_data"."description" IS '描述';
COMMENT ON COLUMN "public"."function_data"."status" IS '是否显示';
COMMENT ON COLUMN "public"."function_data"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."function_data"."del" IS '删除';

-- ----------------------------
-- Table structure for function_provider
-- ----------------------------
DROP TABLE IF EXISTS "public"."function_provider";
CREATE TABLE "public"."function_provider" (
  "id" int8 NOT NULL,
  "parent_id" int8,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "address" varchar(255) COLLATE "pg_catalog"."default",
  "phone" varchar(255) COLLATE "pg_catalog"."default",
  "email" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "del" int4,
  "request_method" varchar(255) COLLATE "pg_catalog"."default",
  "return_result" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."function_provider"."parent_id" IS '上级id';
COMMENT ON COLUMN "public"."function_provider"."name" IS '提供者名称';
COMMENT ON COLUMN "public"."function_provider"."address" IS '提供者地址';
COMMENT ON COLUMN "public"."function_provider"."phone" IS '提供者电话';
COMMENT ON COLUMN "public"."function_provider"."email" IS '提供者邮箱';
COMMENT ON COLUMN "public"."function_provider"."request_method" IS '请求方式';
COMMENT ON COLUMN "public"."function_provider"."return_result" IS '返回结果';

-- ----------------------------
-- Table structure for function_serve
-- ----------------------------
DROP TABLE IF EXISTS "public"."function_serve";
CREATE TABLE "public"."function_serve" (
  "id" int8 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "sname" varchar(255) COLLATE "pg_catalog"."default",
  "auth" int4,
  "reg_address" varchar(255) COLLATE "pg_catalog"."default",
  "fw_itype" varchar(255) COLLATE "pg_catalog"."default",
  "fw_tag" varchar(255) COLLATE "pg_catalog"."default",
  "imgurl" varchar(255) COLLATE "pg_catalog"."default",
  "description" varchar(255) COLLATE "pg_catalog"."default",
  "reg_name" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "status" bool,
  "del" int4,
  "fw_catalogue" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."function_serve"."name" IS '服务注册名';
COMMENT ON COLUMN "public"."function_serve"."sname" IS '服务显示名';
COMMENT ON COLUMN "public"."function_serve"."auth" IS '权限（1公开2安全）';
COMMENT ON COLUMN "public"."function_serve"."reg_address" IS '注册地址';
COMMENT ON COLUMN "public"."function_serve"."fw_itype" IS '服务类型';
COMMENT ON COLUMN "public"."function_serve"."fw_tag" IS '服务标签';
COMMENT ON COLUMN "public"."function_serve"."imgurl" IS '缩略图';
COMMENT ON COLUMN "public"."function_serve"."description" IS '描述';
COMMENT ON COLUMN "public"."function_serve"."reg_name" IS '注册人';
COMMENT ON COLUMN "public"."function_serve"."create_time" IS '注册时间';
COMMENT ON COLUMN "public"."function_serve"."status" IS '状态';
COMMENT ON COLUMN "public"."function_serve"."del" IS '删除';
COMMENT ON COLUMN "public"."function_serve"."fw_catalogue" IS '服务目录';

-- ----------------------------
-- Table structure for products
-- ----------------------------
DROP TABLE IF EXISTS "public"."products";
CREATE TABLE "public"."products" (
  "id" int8 NOT NULL,
  "parent_id" int8,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "sort" int4,
  "description" varchar(255) COLLATE "pg_catalog"."default",
  "show" bool,
  "create_time" timestamp(6),
  "del" int4
)
;
COMMENT ON COLUMN "public"."products"."parent_id" IS '父级id';
COMMENT ON COLUMN "public"."products"."name" IS '名称';
COMMENT ON COLUMN "public"."products"."sort" IS '排序';
COMMENT ON COLUMN "public"."products"."description" IS '描述';
COMMENT ON COLUMN "public"."products"."show" IS '是否显示';
COMMENT ON COLUMN "public"."products"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."products"."del" IS '删除';

-- ----------------------------
-- Table structure for products_data
-- ----------------------------
DROP TABLE IF EXISTS "public"."products_data";
CREATE TABLE "public"."products_data" (
  "id" int8 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "sname" varchar(255) COLLATE "pg_catalog"."default",
  "district" varchar(255) COLLATE "pg_catalog"."default",
  "catalogue" varchar(255) COLLATE "pg_catalog"."default",
  "auth_itype" int4,
  "reg_address" varchar(255) COLLATE "pg_catalog"."default",
  "access_address" varchar(255) COLLATE "pg_catalog"."default",
  "fw_itype" varchar(255) COLLATE "pg_catalog"."default",
  "tag" varchar(255) COLLATE "pg_catalog"."default",
  "img_url" varchar(255) COLLATE "pg_catalog"."default",
  "description" varchar(255) COLLATE "pg_catalog"."default",
  "prov_name" varchar(255) COLLATE "pg_catalog"."default",
  "prov_address" varchar(255) COLLATE "pg_catalog"."default",
  "prov_phone" varchar(255) COLLATE "pg_catalog"."default",
  "prov_email" varchar(255) COLLATE "pg_catalog"."default",
  "keywords" varchar(255) COLLATE "pg_catalog"."default",
  "coordinate" varchar(255) COLLATE "pg_catalog"."default",
  "reg_name" varchar(255) COLLATE "pg_catalog"."default",
  "status" bool,
  "create_time" timestamp(6),
  "del" int4
)
;
COMMENT ON COLUMN "public"."products_data"."name" IS '注册名称';
COMMENT ON COLUMN "public"."products_data"."sname" IS '显示名称';
COMMENT ON COLUMN "public"."products_data"."district" IS '行政区';
COMMENT ON COLUMN "public"."products_data"."catalogue" IS '资源目录';
COMMENT ON COLUMN "public"."products_data"."auth_itype" IS '权限类型';
COMMENT ON COLUMN "public"."products_data"."reg_address" IS '注册图件';
COMMENT ON COLUMN "public"."products_data"."access_address" IS '访问地址';
COMMENT ON COLUMN "public"."products_data"."fw_itype" IS '服务类型';
COMMENT ON COLUMN "public"."products_data"."tag" IS '标签';
COMMENT ON COLUMN "public"."products_data"."img_url" IS '缩略图';
COMMENT ON COLUMN "public"."products_data"."description" IS '描述';
COMMENT ON COLUMN "public"."products_data"."prov_name" IS '服务提供者名称';
COMMENT ON COLUMN "public"."products_data"."prov_address" IS '服务提供者地址';
COMMENT ON COLUMN "public"."products_data"."prov_phone" IS '服务提供者联系电话';
COMMENT ON COLUMN "public"."products_data"."prov_email" IS '服务提供者邮箱';
COMMENT ON COLUMN "public"."products_data"."keywords" IS '关键字';
COMMENT ON COLUMN "public"."products_data"."coordinate" IS '坐标系';
COMMENT ON COLUMN "public"."products_data"."reg_name" IS '注册人';
COMMENT ON COLUMN "public"."products_data"."status" IS '状态';
COMMENT ON COLUMN "public"."products_data"."create_time" IS '注册时间';
COMMENT ON COLUMN "public"."products_data"."del" IS '删除';

-- ----------------------------
-- Table structure for request_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."request_info";
CREATE TABLE "public"."request_info" (
  "id" int8 NOT NULL,
  "parent_id" int8,
  "request_method" varchar(255) COLLATE "pg_catalog"."default",
  "return_result" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "del" int4
)
;
COMMENT ON COLUMN "public"."request_info"."parent_id" IS '上级id';
COMMENT ON COLUMN "public"."request_info"."request_method" IS '请求方式';
COMMENT ON COLUMN "public"."request_info"."return_result" IS '返回结果';

-- ----------------------------
-- Table structure for request_param
-- ----------------------------
DROP TABLE IF EXISTS "public"."request_param";
CREATE TABLE "public"."request_param" (
  "id" int8 NOT NULL,
  "parent_id" int8,
  "parameter" varchar(255) COLLATE "pg_catalog"."default",
  "emty" bool,
  "type" varchar(255) COLLATE "pg_catalog"."default",
  "description" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "del" int4
)
;
COMMENT ON COLUMN "public"."request_param"."parent_id" IS '父级id';
COMMENT ON COLUMN "public"."request_param"."parameter" IS '参数';
COMMENT ON COLUMN "public"."request_param"."emty" IS '是否为空';
COMMENT ON COLUMN "public"."request_param"."type" IS '类型';
COMMENT ON COLUMN "public"."request_param"."description" IS '说明';

-- ----------------------------
-- Table structure for request_result
-- ----------------------------
DROP TABLE IF EXISTS "public"."request_result";
CREATE TABLE "public"."request_result" (
  "id" int8 NOT NULL,
  "parent_id" int8,
  "param" varchar(255) COLLATE "pg_catalog"."default",
  "type" varchar(255) COLLATE "pg_catalog"."default",
  "description" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "del" int4
)
;
COMMENT ON COLUMN "public"."request_result"."parent_id" IS '父级id';
COMMENT ON COLUMN "public"."request_result"."param" IS '参数';
COMMENT ON COLUMN "public"."request_result"."type" IS '类型';
COMMENT ON COLUMN "public"."request_result"."description" IS '描述';

-- ----------------------------
-- Primary Key structure for table categories_data
-- ----------------------------
ALTER TABLE "public"."categories_data" ADD CONSTRAINT "data_directory_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table categories_data_info
-- ----------------------------
ALTER TABLE "public"."categories_data_info" ADD CONSTRAINT "categories_data_info_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table categories_factor
-- ----------------------------
ALTER TABLE "public"."categories_factor" ADD CONSTRAINT "categories_factor_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table categories_meta
-- ----------------------------
ALTER TABLE "public"."categories_meta" ADD CONSTRAINT "categories_meta_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table categories_physics
-- ----------------------------
ALTER TABLE "public"."categories_physics" ADD CONSTRAINT "categories_physics_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table categories_service
-- ----------------------------
ALTER TABLE "public"."categories_service" ADD CONSTRAINT "categories_service_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table function_data
-- ----------------------------
ALTER TABLE "public"."function_data" ADD CONSTRAINT "function_data_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table function_provider
-- ----------------------------
ALTER TABLE "public"."function_provider" ADD CONSTRAINT "function_provider_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table function_serve
-- ----------------------------
ALTER TABLE "public"."function_serve" ADD CONSTRAINT "function_serve_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table products
-- ----------------------------
ALTER TABLE "public"."products" ADD CONSTRAINT "products_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table products_data
-- ----------------------------
ALTER TABLE "public"."products_data" ADD CONSTRAINT "products_data_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table request_info
-- ----------------------------
ALTER TABLE "public"."request_info" ADD CONSTRAINT "request_info_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table request_param
-- ----------------------------
ALTER TABLE "public"."request_param" ADD CONSTRAINT "request_param_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table request_result
-- ----------------------------
ALTER TABLE "public"."request_result" ADD CONSTRAINT "request_result_pkey" PRIMARY KEY ("id");
