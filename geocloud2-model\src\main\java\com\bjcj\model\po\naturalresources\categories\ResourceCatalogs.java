package com.bjcj.model.po.naturalresources.categories;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/4/23  16:04
*/
/**
    * 资源目录
    */
@Schema(description="资源目录")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "resource_catalogs")
public class ResourceCatalogs implements Serializable {
    /**
     * 标识
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(description="标识")
    private String id;

    /**
     * 名称
     */
    @TableField(value = "name")
    @Schema(description="名称")
    @Size(max = 60,message = "名称max length should less than 60")
    @NotBlank(message = "名称is not blank")
    private String name;

    /**
     * 显示顺序
     */
    @TableField(value = "displayorder")
    @Schema(description="显示顺序")
    @NotNull(message = "显示顺序不能为空")
    private Integer displayorder;

    /**
     * 父级目录ID
     */
    @TableField(value = "parentid")
    @Schema(description="父级目录ID")
    @Size(max = 36,message = "父级目录IDmax length should less than 36")
    private String parentid;

    /**
     * 描述
     */
    @TableField(value = "description")
    @Schema(description="描述")
    @Size(max = 150,message = "描述max length should less than 150")
    private String description;

    /**
     * 资源类别。1应用服务；2应用；3文档；4数据；5数据产品；6数据服务；7数据库；8服务器
     */
    @TableField(value = "resourcecategory")
    @Schema(description="资源类别。1应用服务；2应用；3文档；4数据；5数据产品；6数据服务；7数据库；8服务器")
    @NotNull(message = "资源类别。1应用服务；2应用；3文档；4数据；5数据产品；6数据服务；7数据库；8服务器不能为空")
    private Short resourcecategory;

    /**
     * 资源数量
     */
    @TableField(value = "resourcecount")
    @Schema(description="资源数量")
    private BigDecimal resourcecount;

    /**
     * 可用的或有效的资源数量
     */
    @TableField(value = "usabledresourcecount")
    @Schema(description="可用的或有效的资源数量")
    private BigDecimal usabledresourcecount;

    /**
     * 编码
     */
    @TableField(value = "code")
    @Schema(description="编码")
    @Size(max = 50,message = "编码max length should less than 50")
    @NotBlank(message = "编码is not blank")
    private String code;

    /**
     * 是否显示
     */
    @TableField(value = "isvisiable")
    @Schema(description="是否显示")
    private Boolean isvisiable;

    @TableField(exist = false)
    private List<ResourceCatalogs> children;

    @TableField(exist = false)
    private int serviceCount;

    private static final long serialVersionUID = 1L;
}