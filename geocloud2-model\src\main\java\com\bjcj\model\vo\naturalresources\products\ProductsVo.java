package com.bjcj.model.vo.naturalresources.products;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/5 9:48 周二
 */
@Data
public class ProductsVo implements Serializable {

    @Schema(description="", hidden = true)
    private Long id;

    @Schema(description="父id")
    private Long parentId;

    @Schema(description="服务目录名称")
    private String name;

    @Schema(description="排序")
    private int sort;

    @Schema(description="描述")
    private String description;

    @Schema(description="显示")
    private Boolean show;

    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description="删除状态（0未删除，1已删除）")
    private int del;

    private List<ProductsVo> children;

}
