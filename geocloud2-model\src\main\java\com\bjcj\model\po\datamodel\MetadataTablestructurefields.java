package com.bjcj.model.po.datamodel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/4/19  14:54
*/
/**
    * 表结构字段信息表
    */
@Schema(description="图层字段表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "metadata_tablestructurefields")
public class MetadataTablestructurefields implements Serializable {
    /**
     * 字段ID
     */
    @TableId(value = "fieldid", type = IdType.ASSIGN_UUID)
    @Schema(description="字段ID")
    private String fieldid;

    /**
     * 字段名称
     */
    @TableField(value = "name")
    @Schema(description="字段名称")
    @Size(max = 60,message = "字段名称max length should less than 60")
    @NotBlank(message = "字段名称is not blank")
    private String name;

    /**
     * 字段编码，可用来存字段标准名称
     */
    @TableField(value = "code")
    @Schema(description="字段编码，可用来存字段标准名称")
    @Size(max = 60,message = "字段编码，可用来存字段标准名称max length should less than 60")
    private String code;

    /**
     * 字段说明
     */
    @TableField(value = "description")
    @Schema(description="字段说明")
    @Size(max = 150,message = "字段说明max length should less than 150")
    private String description;

    /**
     * 字段类型
     */
    @TableField(value = "fieldtype")
    @Schema(description="字段类型")
    @NotNull(message = "字段类型is not null")
    private Long fieldtype;

    @TableField(exist = false)
    @Schema(description="字段类型字符串")
    @NotNull(message = "字段类型字符串is not null")
    private String fieldtypeStr;

    /**
     * 字段长度
     */
    @TableField(value = "length")
    @Schema(description="字段长度")
    private Long length;

    /**
     * 字段精度
     */
    @TableField(value = "precision")
    @Schema(description="字段精度")
    private Long precision;

    /**
     * 字段小数位数
     */
    @TableField(value = "scale")
    @Schema(description="字段小数位数")
    private Long scale;

    /**
     * 显示名称
     */
    @TableField(value = "displayname")
    @Schema(description="显示名称")
    @Size(max = 60,message = "显示名称max length should less than 60")
    private String displayname;

    /**
     * 字段域
     */
    @TableField(value = "fielddomainid")
    @Schema(description="字段域")
    @Size(max = 36,message = "字段域max length should less than 36")
    private String fielddomainid;

    /**
     * 表结构的ID
     */
    @TableField(value = "tablestructureid")
    @Schema(description="图层表ID")
    @Size(max = 36,message = "图层表ID max length should less than 36")
    @NotBlank(message = "图层表IDis not blank")
    private String tablestructureid;

    /**
     * 是否可为空
     */
    @TableField(value = "isnullable")
    @Schema(description="是否可为空")
    @NotNull(message = "是否可为空is not null")
    private Boolean isnullable;

    /**
     * 是否必须
     */
    @TableField(value = "required")
    @Schema(description="是否必须")
    @NotNull(message = "是否必须is not null")
    private Boolean required;

    /**
     * 默认值
     */
    @TableField(value = "defaultvalue")
    @Schema(description="默认值")
    @Size(max = 50,message = "默认值max length should less than 50")
    private String defaultvalue;

    /**
     * 字段的约束表达式
     */
    @TableField(value = "checkexpression")
    @Schema(description="字段的约束表达式")
    @Size(max = 50,message = "字段的约束表达式max length should less than 50")
    private String checkexpression;

    /**
     * 数据库字段类型
     */
    @TableField(value = "dbfieldtype")
    @Schema(description="数据库字段类型")
    @Size(max = 50,message = "数据库字段类型max length should less than 50")
    private String dbfieldtype;

    /**
     * 显示顺序
     */
    @TableField(value = "displayorder")
    @Schema(description="显示顺序")
    @NotNull(message = "显示顺序is not null")
    private Integer displayorder;

    /**
     * 标准名称，用途：有些用户的字段不是标准名称，但是和规范中的名称又是同一个意思
     */
    @TableField(value = "standardname")
    @Schema(description="标准名称，用途：有些用户的字段不是标准名称，但是和规范中的名称又是同一个意思")
    @Size(max = 60,message = "标准名称，用途：有些用户的字段不是标准名称，但是和规范中的名称又是同一个意思max length should less than 60")
    private String standardname;

    /**
     * 统计分类 ; 0表示属性，1表示维度，2表示指标
     */
    @TableField(value = "statisticscategory")
    @Schema(description="统计分类 ; 0表示属性，1表示维度，2表示指标")
    private Short statisticscategory;

    /**
     * 维度类型 ; 0表示时间维度，1表示空间维度，2表示业务维度
     */
    @TableField(value = "dimensiontype")
    @Schema(description="维度类型 ; 0表示时间维度，1表示空间维度，2表示业务维度")
    private Short dimensiontype;

    /**
     * 代表单位
     */
    @TableField(value = "representunit")
    @Schema(description="代表单位")
    @Size(max = 255,message = "代表单位max length should less than 255")
    private String representunit;

    /**
     * 时间单位
     */
    @TableField(value = "timeunit")
    @Schema(description="时间单位")
    @Size(max = 20,message = "时间单位max length should less than 20")
    private String timeunit;

    /**
     * 维度字段映射关系
     */
    @TableField(value = "dimensionfieldmapping")
    @Schema(description="维度字段映射关系")
    private String dimensionfieldmapping;

    /**
     * 是否排序
     */
    @TableField(value = "issort")
    @Schema(description="是否排序")
    private Boolean issort;

    /**
     * 排序方式
     */
    @TableField(value = "sortord")
    @Schema(description="排序方式")
    @Size(max = 60,message = "排序方式max length should less than 60")
    private String sortord;

    /**
     * 是否显示
     */
    @TableField(value = "isvisiable")
    @Schema(description="是否显示")
    private Boolean isvisiable;

    /**
     * 导入维度标识
     */
    @TableField(value = "dimensionid")
    @Schema(description="导入维度标识")
    @Size(max = 36,message = "导入维度标识max length should less than 36")
    private String dimensionid;

    private static final long serialVersionUID = 1L;
}