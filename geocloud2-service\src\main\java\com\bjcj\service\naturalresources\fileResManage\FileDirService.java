package com.bjcj.service.naturalresources.fileResManage;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.fileResManage.FileDirMapper;
import com.bjcj.model.po.naturalresources.fileResManage.FileDir;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 *@Author：qinyi
 *@Date：2024/1/15  16:43
*/
@Service
public class FileDirService extends ServiceImpl<FileDirMapper, FileDir> {

    @Resource
    FileDirMapper fileDirMapper;

    public JsonResult<List<FileDir>> lists() {
        //查询根级
        List<FileDir> list = fileDirMapper.selectList(
                        Wrappers.<FileDir>query().lambda()
                                .eq(FileDir::getParentDirId, 0)
                                .orderByAsc(FileDir::getShowSort)
                ).stream()
                .map(item -> BeanUtil.copyProperties(item, FileDir.class)).collect(Collectors.toList());
        list.forEach(item->{getChildren(item,2);});
        return JsonResult.success(list);
    }

    private void getChildren(FileDir item, int index){
        LambdaQueryWrapper<FileDir> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FileDir::getParentDirId, item.getId())
                .orderByAsc(FileDir::getShowSort);
        // if(index == 1){
        //     wrapper.eq(FileDir::getStatus, true);
        // }

        //根据parentId查询
        List<FileDir> list = fileDirMapper.selectList(wrapper);

        List<FileDir> voList = list.stream().map(p -> {
            FileDir vo = new FileDir();
            BeanUtils.copyProperties(p, vo);
            return vo;
        }).collect(Collectors.toList());
        //写入到children
        item.setChildren(voList);

        //如果children不为空，继续往下找
        if (!CollectionUtils.isEmpty(voList)) {
            voList.forEach(items->{getChildren(items,index);});
        }
    }

}
