package com.bjcj.web.cloudportal;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.cloudportal.CollectDto;
import com.bjcj.model.po.cloudportal.UserCollected;
import com.bjcj.service.cloudportal.UserCollectedService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
* (public.user_collected)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/collect")
@Tag(name = "收藏")
@Validated
public class UserCollectedController {
/**
* 服务对象
*/
@Resource
private UserCollectedService userCollectedService;


    @OperaLog(operaModule = "收藏/取消收藏",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "收藏/取消收藏")
    @SaCheckPermission("sys:write")
    @PostMapping("/collectOrDel")
    @Operation(summary = "收藏/取消收藏", description = "收藏/取消收藏")
    @ApiOperationSupport(order = 1)
    public JsonResult collectOrDel(@Validated @RequestBody CollectDto dto){
        return this.userCollectedService.collectOrDel(dto);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "列表", description = "带搜索框模糊查询(可搜名称/显示名称),分页")
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "searchStr", description = "名称搜索", required = false)
    })
    @ApiOperationSupport(order = 2)
    @GetMapping("/page")
    public JsonResult<Page<UserCollected>> page(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam("resouceType") String resouceType,
            @RequestParam(value = "searchStr",required = false) String searchStr){
        Page<UserCollected> pager = new Page<>(page, pageSize);
        return this.userCollectedService.pageData(pager,searchStr,resouceType);
    }

}
