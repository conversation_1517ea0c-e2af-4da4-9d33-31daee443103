package com.bjcj.model.vo.naturalresources.function;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/30 10:11 周四
 */
@Data
public class FunctionDataVo implements Serializable {

    private Long id;

    @Schema(description="父id")
    private Long parentId;

    @Schema(description="服务目录名称")
    private String name;

    @Schema(description="排序")
    private int sort;

    @Schema(description="描述")
    private String description;

    @Schema(description="显示")
    private Boolean status;

    @Schema(description="创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDateTime createTime;

    @Schema(description="删除状态（0未删除，1已删除）")
    private int del;

    private List<FunctionDataVo> children;

}
