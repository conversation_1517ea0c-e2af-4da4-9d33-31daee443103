package com.bjcj.model.po.datamodel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


/**
 *@Author：qinyi
 *@Date：2024/6/27  15:13
*/
/**
    * 数据入ES数据库
    */
@Schema(description="数据入ES数据库")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "resource_es")
public class ResourceEs implements Serializable {
    @TableId(value = "id",type = IdType.ASSIGN_UUID)
    @Schema(description="")
    @Size(max = 36,message = "max length should less than 36")
    @NotBlank(message = "is not blank")
    private String id;

    /**
     * 数据表的ID
     */
    @TableField(value = "tableid")
    @Schema(description="数据表的ID")
    @Size(max = 36,message = "数据表的IDmax length should less than 36")
    private String tableid;

    /**
     * 数据展示需要的专题名称
     */
    @TableField(value = "topicname")
    @Schema(description="数据展示需要的专题名称")
    @Size(max = 60,message = "数据展示需要的专题名称max length should less than 60")
    private String topicname;

    /**
     * 更新时间
     */
    @TableField(value = "updatetime")
    @Schema(description="更新时间")
    private Date updatetime;

    /**
     * 入库状态 0 未入库，1 正在入库，2 入库成功，3 入库失败
     */
    @TableField(value = "status")
    @Schema(description="入库状态 0 未入库，1 正在入库，2 入库成功，3 入库失败")
    private Integer status;

    /**
     * 抽取频率
     */
    @TableField(value = "samplingfrequency")
    @Schema(description="抽取频率")
    @Size(max = 60,message = "抽取频率max length should less than 60")
    private String samplingfrequency;

    /**
     * 抽取时间
     */
    @TableField(value = "samplingtime")
    @Schema(description="抽取时间")
    private Integer samplingtime;

    /**
     * 是否删除索引
     */
    @TableField(value = "isdeleteexitindex")
    @Schema(description="是否删除索引")
    private Boolean isdeleteexitindex;

    @TableField(exist = false)
    @Schema(description="数据项名称")
    private String dataItemName;

    @TableField(exist = false)
    @Schema(description="物理数据的名称")
    private String metadatatablesName;

    @TableField(exist = false)
    @Schema(description="图层名")
    private String metadatatablestructuresName;


    private static final long serialVersionUID = 1L;
}