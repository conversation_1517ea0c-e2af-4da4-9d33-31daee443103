package com.bjcj.model.dto.datamodel;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/4/18  15:29
*/
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MetadataWorkspacesDto implements Serializable {
    /**
     * 工作空间ID
     */
    @Schema(description="工作空间ID")
    private String workspaceid;

    /**
     * 名称
     */
    @Schema(description="名称")
    @Size(max = 60,message = "名称max length should less than 60")
    @NotBlank(message = "名称is not blank")
    private String name;

    /**
     * 别名
     */
    @Schema(description="别名")
    @Size(max = 60,message = "别名max length should less than 60")
    private String displayname;

    /**
     * 用户名
     */
    @Schema(description="用户名")
    @Size(max = 60,message = "用户名max length should less than 60")
    @NotBlank(message = "用户名is not blank")
    private String username;

    /**
     * 密码
     */
    @Schema(description="密码")
    @Size(max = 60,message = "密码max length should less than 60")
    @NotBlank(message = "密码is not blank")
    private String password;

    /**
     * 版本，例如：sde.DEFAULT
     */
    @Schema(description="版本，例如：sde.DEFAULT")
    @Size(max = 20,message = "版本，例如：sde.DEFAULTmax length should less than 20")
    private String version;

    /**
     * 工作空间类型，包括：SDE、FileGDB、Shapefile、MDB、Oracle、SqlServer等
     */
    @Schema(description="工作空间类型，包括：SDE、FileGDB、Shapefile、MDB、Oracle、SqlServer等")
    @Size(max = 20,message = "工作空间类型，包括：SDE、FileGDB、Shapefile、MDB、Oracle、SqlServer等max length should less than 20")
    @NotBlank(message = "工作空间类型，包括：SDE、FileGDB、Shapefile、MDB、Oracle、SqlServer等is not blank")
    private String workspacetype;

    /**
     * 文件型工作空间的路径或者ArcSDE Connection File路径
     */
    @Schema(description="文件型工作空间的路径或者ArcSDE Connection File路径")
    @Size(max = 255,message = "文件型工作空间的路径或者ArcSDE Connection File路径max length should less than 255")
    private String path;

    /**
     * SDE Workspace所在的ArcSDE Server的标识
     */
    @Schema(description="SDE Workspace所在的ArcSDE Server的标识")
    @Size(max = 36,message = "SDE Workspace所在的ArcSDE Server的标识max length should less than 36")
    private String serverid;

    /**
     * 数据库名称，用于SqlServer、DB2、PostgreSQL等非oracle数据库
     */
    @Schema(description="数据库名称，用于SqlServer、DB2、PostgreSQL等非oracle数据库")
    @Size(max = 50,message = "数据库名称，用于SqlServer、DB2、PostgreSQL等非oracle数据库max length should less than 50")
    private String database;

    /**
     * 描述信息
     */
    @Schema(description="描述信息")
    @Size(max = 150,message = "描述信息max length should less than 150")
    private String description;

    /**
     * 服务器名称
     */
    @Schema(description="服务器名称")
    @Size(max = 60,message = "服务器名称max length should less than 60")
    private String servername;

    /**
     * 工作空间实例
     */
    @Schema(description="工作空间实例")
    @Size(max = 300,message = "工作空间实例max length should less than 300")
    private String instance;

    /**
     * 是否只读，默认为0
     */
    @Schema(description="是否只读，默认为0")
    private Boolean readonly;

    private static final long serialVersionUID = 1L;
}