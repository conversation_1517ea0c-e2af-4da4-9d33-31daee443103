package com.bjcj.service.platformConf;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.platformConf.PlatformAppConfMapper;
import com.bjcj.model.dto.platformConf.PlatformAppConfDto;
import com.bjcj.model.po.platformConf.PlatformAppConf;
import com.bjcj.model.po.platformManage.analysisPlan.AnalysisOrdinaryItem;
import com.bjcj.model.po.platformManage.analysisPlan.AnalysisPlan;
import com.bjcj.model.po.platformManage.analysisPlan.AnalysisPlanFieldDetail;
import com.bjcj.model.po.platformManage.analysisPlan.AnalysisPlanFieldStatistics;
import com.bjcj.model.po.platformManage.fieldPlan.FieldPlan;
import com.bjcj.model.po.platformManage.fieldPlan.FieldPlanLayer;
import com.bjcj.model.po.platformManage.fieldPlan.FieldPlanLayerField;
import com.bjcj.model.po.platformManage.modulePlan.ModulePlan;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlan;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlanData;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlanDir;
import com.bjcj.model.po.safetyManage.Menu;
import com.bjcj.service.platformManage.analysisPlan.AnalysisOrdinaryItemService;
import com.bjcj.service.platformManage.analysisPlan.AnalysisPlanFieldDetailService;
import com.bjcj.service.platformManage.analysisPlan.AnalysisPlanFieldStatisticsService;
import com.bjcj.service.platformManage.analysisPlan.AnalysisPlanService;
import com.bjcj.service.platformManage.fieldPlan.FieldPlanLayerFieldService;
import com.bjcj.service.platformManage.fieldPlan.FieldPlanLayerService;
import com.bjcj.service.platformManage.fieldPlan.FieldPlanService;
import com.bjcj.service.platformManage.modulePlan.ModulePlanService;
import com.bjcj.service.platformManage.specialPlan.SpecialPlanDataService;
import com.bjcj.service.platformManage.specialPlan.SpecialPlanDirService;
import com.bjcj.service.platformManage.specialPlan.SpecialPlanService;
import com.bjcj.service.safetyManage.MenuService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 *@Author：qinyi
 *@Date：2024/1/24  17:48
*/
@Service
public class PlatformAppConfService extends ServiceImpl<PlatformAppConfMapper, PlatformAppConf> {

    @Resource
    PlatformAppConfMapper platformAppConfMapper;

    @Resource
    SpecialPlanService specialPlanService;

    @Resource
    FieldPlanService fieldPlanService;

    @Resource
    ModulePlanService modulePlanService;

    @Resource
    AnalysisPlanService analysisPlanService;

    @Resource
    MenuService menuService;

    @Resource
    SpecialPlanDirService specialPlanDirService;

    @Resource
    SpecialPlanDataService specialPlanDataService;

    @Resource
    FieldPlanLayerService fieldPlanLayerService;

    @Resource
    FieldPlanLayerFieldService fieldPlanLayerFieldService;

    @Resource
    AnalysisPlanFieldDetailService analysisPlanFieldDetailService;

    @Resource
    AnalysisPlanFieldStatisticsService analysisPlanFieldStatisticsService;

    @Resource
    AnalysisOrdinaryItemService analysisOrdinaryItemService;

    public JsonResult saveData(PlatformAppConfDto dto) {
        if(Objects.isNull(dto.getId())) {
            PlatformAppConf one = this.platformAppConfMapper.selectOne(new LambdaQueryWrapper<PlatformAppConf>().eq(PlatformAppConf::getShowName, dto.getShowName()));
            if (Objects.nonNull(one)) {
                return JsonResult.error("应用显示名称重复");
            }
        }
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        PlatformAppConf platformAppConf = BeanUtil.copyProperties(dto, PlatformAppConf.class);
        platformAppConf.setOperater(username);
        if(Objects.isNull(platformAppConf.getId())) platformAppConf.setCreateTime(LocalDateTime.now());
        else platformAppConf.setUpdateTime(LocalDateTime.now());

        //创建平台应用后创建一个默认的方案
        if(Objects.isNull(dto.getId())) {
            int insert = platformAppConfMapper.insert(platformAppConf);
            Long platformAppConfId = platformAppConf.getId();
            //创建默认专题方案
            SpecialPlan specialPlan = new SpecialPlan(){{
                setPlanName(platformAppConf.getAppName());
                setPlatformAppConfId(platformAppConfId);
                setOperater(username);
            }};
            this.specialPlanService.save(specialPlan);
            //创建默认字段方案
            FieldPlan fieldPlan = new FieldPlan(){{
                setFieldName(platformAppConf.getShowName());
                setPlatformAppConfId(platformAppConfId);
                setOperater(username);
            }};
            this.fieldPlanService.save(fieldPlan);
            return insert > 0 ? JsonResult.success() : JsonResult.error();
        }else{
            return platformAppConfMapper.updateById(platformAppConf) > 0 ? JsonResult.success():JsonResult.error();
        }
    }


    public Long saveData2(PlatformAppConf dto,String appName) {
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        dto.setOperater(username);
        if(Objects.isNull(dto.getId())) dto.setCreateTime(LocalDateTime.now());
        else dto.setUpdateTime(LocalDateTime.now());

        //创建平台应用后创建一个默认的方案
        int insert = platformAppConfMapper.insert(dto);
        Long platformAppConfId = dto.getId();
        //创建默认专题方案
        SpecialPlan specialPlan = new SpecialPlan(){{
            setPlanName(appName);
            setPlatformAppConfId(platformAppConfId);
            setOperater(username);
        }};
        this.specialPlanService.save(specialPlan);
        //创建默认字段方案
        FieldPlan fieldPlan = new FieldPlan(){{
            setFieldName(dto.getShowName());
            setPlatformAppConfId(platformAppConfId);
            setOperater(username);
        }};
        this.fieldPlanService.save(fieldPlan);
        return platformAppConfId;
    }

    public JsonResult exportTo(Long fromPlatformAppConfId, Long toPlatformAppConfId) {
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        if(Objects.isNull(this.platformAppConfMapper.selectOne(new LambdaQueryWrapper<PlatformAppConf>().eq(PlatformAppConf::getId,toPlatformAppConfId))))
            return JsonResult.error("目标平台应用不存在");
        PlatformAppConf appConf = this.platformAppConfMapper.selectOne(new LambdaQueryWrapper<PlatformAppConf>().eq(PlatformAppConf::getId,fromPlatformAppConfId));
        if(Objects.isNull(appConf)) return JsonResult.error("源平台应用不存在");
        if(Objects.isNull(appConf.getSystemConfigJson()) || appConf.getSystemConfigJson().isEmpty()) return JsonResult.error("源平台应用系统配置为空");
        PlatformAppConf appConf2 = this.platformAppConfMapper.selectOne(new LambdaQueryWrapper<PlatformAppConf>().eq(PlatformAppConf::getId,toPlatformAppConfId));
        appConf2.setSystemConfigJson(appConf.getSystemConfigJson());
        appConf2.setSystemConfigJsonSchema(appConf.getSystemConfigJsonSchema());
        appConf2.setOperater(username);
        return this.saveOrUpdate(appConf2) ? JsonResult.success() : JsonResult.error();
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResult exportAll(Long fromPlatformAppConfId, String appName) {
        //先验证appname是否唯一
        List<PlatformAppConf> platformAppConfs = this.platformAppConfMapper.selectList(new LambdaQueryWrapper<PlatformAppConf>().eq(PlatformAppConf::getAppName, appName));
        if(!platformAppConfs.isEmpty()){
            return JsonResult.error("应用标识重复");
        }
        try {
            //先复制一个新的平台
            PlatformAppConf fromPlatformAppConf = platformAppConfMapper.selectOne(new LambdaQueryWrapper<PlatformAppConf>().eq(PlatformAppConf::getId, fromPlatformAppConfId));
            PlatformAppConf p = BeanUtil.copyProperties(fromPlatformAppConf, PlatformAppConf.class);
            p.setId(null);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            // p.setAppName(fromPlatformAppConf.getAppName() + "_" + sdf.format(new Date()));
            p.setAppName(appName);
            p.setShowName(fromPlatformAppConf.getShowName() + "_" + sdf.format(new Date()));
            p.setSystemTitle(fromPlatformAppConf.getSystemTitle() + "_" + sdf.format(new Date()));
            Long toPlatformAppConfId = this.saveData2(p,appName);
            // Long toPlatformAppConfId = null;

            //导出系统配置
            this.exportTo(fromPlatformAppConfId, toPlatformAppConfId);
            //导出地图工具栏配置  导出分析结果配置
            this.modulePlanService.importTo(fromPlatformAppConfId, toPlatformAppConfId);
            //导出专题展示方案
            this.specialPlanService.exportTo(fromPlatformAppConfId, toPlatformAppConfId);
            //导出字段展示方案
            this.fieldPlanService.exportTo(fromPlatformAppConfId, toPlatformAppConfId);
            //导出分析展示方案
            this.analysisPlanService.exportTo(fromPlatformAppConfId, toPlatformAppConfId);
            //导出菜单管理
            this.menuService.importTo(fromPlatformAppConfId, toPlatformAppConfId);
        }catch (Exception e){
            log.error("一键导出异常,事务回滚");
            log.error(e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return JsonResult.error();
        }
        return JsonResult.success();
    }

    public JsonResult removeData(Long id) {
        //删除地图工具栏配置  删除分析结果配置
        modulePlanService.remove(new LambdaQueryWrapper<ModulePlan>().eq(ModulePlan::getPlatformAppConfId, id));
        //删除专题展示方案
        SpecialPlan one2 = specialPlanService.getOne(new LambdaQueryWrapper<SpecialPlan>().eq(SpecialPlan::getPlatformAppConfId, id));
        if(null != one2){
            Long specialPlanId = one2.getId();
            specialPlanService.removeById(specialPlanId);
            specialPlanDirService.remove(new LambdaQueryWrapper<SpecialPlanDir>().eq(SpecialPlanDir::getSpecialPlanId, specialPlanId));
            specialPlanDataService.remove(new LambdaQueryWrapper<SpecialPlanData>().eq(SpecialPlanData::getSpecialPlanId, specialPlanId));
        }
        //删除字段展示方案
        FieldPlan one1 = fieldPlanService.getOne(new LambdaQueryWrapper<FieldPlan>().eq(FieldPlan::getPlatformAppConfId, id));
        if(null != one1){
            Long fieldPlanId = one1.getId();
            fieldPlanService.removeById(fieldPlanId);
            fieldPlanLayerService.remove(new LambdaQueryWrapper<FieldPlanLayer>().eq(FieldPlanLayer::getFieldPlanId, fieldPlanId));
            fieldPlanLayerFieldService.remove(new LambdaQueryWrapper<FieldPlanLayerField>().eq(FieldPlanLayerField::getFieldPlanId, fieldPlanId));
        }
        //删除分析展示方案
        List<AnalysisPlan> ones = analysisPlanService.list(new LambdaQueryWrapper<AnalysisPlan>().eq(AnalysisPlan::getPlatformAppConfId, id));
        if(!ones.isEmpty()){
            ones.forEach(one -> {
                Long analysisPlanId = one.getId();
                analysisPlanService.removeById(analysisPlanId);
                analysisPlanFieldDetailService.remove(new LambdaQueryWrapper<AnalysisPlanFieldDetail>().eq(AnalysisPlanFieldDetail::getAnalysisPlanId, analysisPlanId));
                analysisPlanFieldStatisticsService.remove(new LambdaQueryWrapper<AnalysisPlanFieldStatistics>().eq(AnalysisPlanFieldStatistics::getAnalysisPlanId, analysisPlanId));
                analysisOrdinaryItemService.remove(new LambdaQueryWrapper<AnalysisOrdinaryItem>().eq(AnalysisOrdinaryItem::getAnalysisPlanId, analysisPlanId));
            });
        }
        //删除菜单管理
        menuService.remove(new LambdaQueryWrapper<Menu>().eq(Menu::getPlatformAppConfId, id));
        //删除系统配置
        platformAppConfMapper.deleteById(id);
        return JsonResult.success();
    }
}
