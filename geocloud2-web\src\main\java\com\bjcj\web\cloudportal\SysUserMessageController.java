package com.bjcj.web.cloudportal;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.cloudportal.SysUserMessage;
import com.bjcj.service.cloudportal.SysUserMessageService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
* 消息中心(public.sys_user_message)表控制层
* <AUTHOR>
*/
@RestController
@RequestMapping("/sys_user_message")
@Tag(name = "消息中心")
@Validated
public class SysUserMessageController {
    /**
    * 服务对象
    */
    @Resource
    private SysUserMessageService sysUserMessageService;

    @SaCheckPermission("sys:read")
    @GetMapping("/userMeaage")
    @Operation(summary = "读取当前登录用户的消息列表", description = "读取当前登录用户的消息列表")
    @ApiOperationSupport(order = 1)
    @RequestLock(prefix = "userMeaage")
    public JsonResult<List<SysUserMessage>> userMeaage(){
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        List<SysUserMessage> list = this.sysUserMessageService.list(
                new LambdaQueryWrapper<SysUserMessage>()
                        .eq(SysUserMessage::getReceiveUserId, userId)
                        .orderByAsc(SysUserMessage::getIsRead)
                        .orderByDesc(SysUserMessage::getUpdateTime)
        );
        // //list中的消息设置为已读
        // if(!list.isEmpty()){
        //     list.stream().filter(item->!item.getIsRead()).forEach(item->{
        //         item.setIsRead(true);
        //     });
        //     this.sysUserMessageService.updateBatchById(list);
        // }
        return JsonResult.success(list);
    }


    @SaCheckPermission("sys:write")
    @GetMapping("/readAll")
    @Operation(summary = "全部已读", description = "全部已读")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "readAll")
    public JsonResult readAll(){
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        List<SysUserMessage> list = this.sysUserMessageService.list(
                new LambdaQueryWrapper<SysUserMessage>()
                        .eq(SysUserMessage::getReceiveUserId, userId)
                        .orderByAsc(SysUserMessage::getIsRead)
        );
        //list中的消息设置为已读
        if(!list.isEmpty()){
            list.stream().filter(item->!item.getIsRead()).forEach(item->{
                item.setIsRead(true);
            });
            this.sysUserMessageService.updateBatchById(list);
        }
        return JsonResult.success();
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/noReadMsgCount")
    @Operation(summary = "未读消息数量", description = "未读消息数量")
    @ApiOperationSupport(order = 3)
    // @RequestLock(prefix = "noReadMsgCount")
    public JsonResult noReadMsgCount(){
        return this.sysUserMessageService.noReadMsgCount();
    }

    @SaCheckPermission("sys:write")
    @PostMapping("/ydMsg/{id}")
    @Operation(summary = "设置已读", description = "设置已读")
    @ApiOperationSupport(order = 4)
    @RequestLock(prefix = "ydMsg")
    public JsonResult ydMsg(@PathVariable("id") String id){
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        LambdaUpdateWrapper<SysUserMessage> updateWrapper = new LambdaUpdateWrapper<SysUserMessage>();
        updateWrapper.set(SysUserMessage::getIsRead, true)
                .eq(SysUserMessage::getId, id)
                .eq(SysUserMessage::getReceiveUserId, userId);
        return this.sysUserMessageService.update(updateWrapper) ? JsonResult.success() : JsonResult.error();
    }
}
