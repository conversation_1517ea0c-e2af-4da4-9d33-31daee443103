package com.bjcj.service.safetyManage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.safetyManage.SafetyLevelMapper;
import com.bjcj.model.po.safetyManage.SafetyLevel;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author：qinyi
 * @Package：com.bjcj.service.safetyManage
 * @Project：geocloud2
 * @name：SafetyLevelService
 * @Date：2023/11/24 14:22
 * @Filename：SafetyLevelService
 */
@Service
public class SafetyLevelService extends ServiceImpl<SafetyLevelMapper, SafetyLevel> {
    @Resource
    SafetyLevelMapper safetyLevelMapper;

    public JsonResult<List<SafetyLevel>> selectAll() {
        LambdaQueryWrapper<SafetyLevel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(SafetyLevel::getLevelName);
        return JsonResult.success(this.safetyLevelMapper.selectList(queryWrapper));
    }
}
