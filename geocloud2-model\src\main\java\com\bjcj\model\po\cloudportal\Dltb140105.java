package com.bjcj.model.po.cloudportal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 *@Author：qinyi
 *@Date：2024/7/12  15:29
*/
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "GEODATA.DLTB_140105")
public class Dltb140105 implements Serializable {
    @TableField(value = "OBJECTID")
    @Schema(description="")
    @NotNull(message = "is not null")
    private BigDecimal objectid;

    @TableField(value = "BSM")
    @Schema(description="")
    @Size(max = 18,message = "max length should less than 18")
    private String bsm;

    @TableField(value = "YSDM")
    @Schema(description="")
    @Size(max = 10,message = "max length should less than 10")
    private String ysdm;

    @TableField(value = "TBYBH")
    @Schema(description="")
    @Size(max = 18,message = "max length should less than 18")
    private String tbybh;

    @TableField(value = "TBBH")
    @Schema(description="")
    @Size(max = 8,message = "max length should less than 8")
    private String tbbh;

    @TableField(value = "DLBM")
    @Schema(description="")
    @Size(max = 5,message = "max length should less than 5")
    private String dlbm;

    @TableField(value = "DLMC")
    @Schema(description="")
    @Size(max = 60,message = "max length should less than 60")
    private String dlmc;

    @TableField(value = "QSXZ")
    @Schema(description="")
    @Size(max = 2,message = "max length should less than 2")
    private String qsxz;

    @TableField(value = "QSDWDM")
    @Schema(description="")
    @Size(max = 19,message = "max length should less than 19")
    private String qsdwdm;

    @TableField(value = "QSDWMC")
    @Schema(description="")
    @Size(max = 60,message = "max length should less than 60")
    private String qsdwmc;

    @TableField(value = "ZLDWDM")
    @Schema(description="")
    @Size(max = 19,message = "max length should less than 19")
    private String zldwdm;

    @TableField(value = "ZLDWMC")
    @Schema(description="")
    @Size(max = 60,message = "max length should less than 60")
    private String zldwmc;

    @TableField(value = "TBMJ")
    @Schema(description="")
    private BigDecimal tbmj;

    @TableField(value = "KCDLBM")
    @Schema(description="")
    @Size(max = 5,message = "max length should less than 5")
    private String kcdlbm;

    @TableField(value = "KCXS")
    @Schema(description="")
    private BigDecimal kcxs;

    @TableField(value = "KCMJ")
    @Schema(description="")
    private BigDecimal kcmj;

    @TableField(value = "TBDLMJ")
    @Schema(description="")
    private BigDecimal tbdlmj;

    @TableField(value = "GDLX")
    @Schema(description="")
    @Size(max = 2,message = "max length should less than 2")
    private String gdlx;

    @TableField(value = "GDPDJB")
    @Schema(description="")
    @Size(max = 2,message = "max length should less than 2")
    private String gdpdjb;

    @TableField(value = "XZDWKD")
    @Schema(description="")
    private BigDecimal xzdwkd;

    @TableField(value = "TBXHDM")
    @Schema(description="")
    @Size(max = 6,message = "max length should less than 6")
    private String tbxhdm;

    @TableField(value = "TBXHMC")
    @Schema(description="")
    @Size(max = 20,message = "max length should less than 20")
    private String tbxhmc;

    @TableField(value = "ZZSXDM")
    @Schema(description="")
    @Size(max = 6,message = "max length should less than 6")
    private String zzsxdm;

    @TableField(value = "ZZSXMC")
    @Schema(description="")
    @Size(max = 20,message = "max length should less than 20")
    private String zzsxmc;

    @TableField(value = "GDDB")
    @Schema(description="")
    private BigDecimal gddb;

    @TableField(value = "FRDBS")
    @Schema(description="")
    @Size(max = 1,message = "max length should less than 1")
    private String frdbs;

    @TableField(value = "CZCSXM")
    @Schema(description="")
    @Size(max = 4,message = "max length should less than 4")
    private String czcsxm;

    @TableField(value = "SJNF")
    @Schema(description="")
    private BigDecimal sjnf;

    @TableField(value = "MSSM")
    @Schema(description="")
    @Size(max = 2,message = "max length should less than 2")
    private String mssm;

    @TableField(value = "HDMC")
    @Schema(description="")
    @Size(max = 100,message = "max length should less than 100")
    private String hdmc;

    @TableField(value = "BZ")
    @Schema(description="")
    private String bz;

    @TableField(value = "SHAPE")
    @Schema(description="")
    private Object shape;

    private static final long serialVersionUID = 1L;
}