package com.bjcj.web.platformConf;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.platformConf.PlatformAppConfDto;
import com.bjcj.model.po.platformConf.PlatformAppConf;
import com.bjcj.model.po.platformManage.fieldPlan.FieldPlan;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlan;
import com.bjcj.service.platformConf.PlatformAppConfService;
import com.bjcj.service.platformManage.fieldPlan.FieldPlanService;
import com.bjcj.service.platformManage.specialPlan.SpecialPlanService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
* 平台应用配置表(public.platform_app_conf)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/platform_app_conf")
@Tag(name = "1平台应用配置")
@Validated
public class PlatformAppConfController {
    /**
    * 服务对象
    */
    @Resource
    private PlatformAppConfService platformAppConfService;

    @Resource
    private SpecialPlanService specialPlanService;

    @Resource
    private FieldPlanService fieldPlanService;


    @OperaLog(operaModule = "平台应用配置-新增/编辑",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "平台应用配置-新增/编辑")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 2)
    @Transactional(rollbackFor = Exception.class)
    @RequestLock(prefix = "addOrEdit")
    public JsonResult addOrEdit(@Validated @RequestBody PlatformAppConfDto dto){
        // if(Objects.isNull(dto.getId())) {
        //     PlatformAppConf one = this.platformAppConfService.getOne(new LambdaQueryWrapper<PlatformAppConf>().eq(PlatformAppConf::getShowName, dto.getShowName()));
        //     if (Objects.nonNull(one)) {
        //         return JsonResult.error("应用显示名称重复");
        //     }
        // }
        // User principal = (User) StpUtil.getSession().get("user");
        // PlatformAppConf platformAppConf = BeanUtil.copyProperties(dto, PlatformAppConf.class);
        // platformAppConf.setOperater(principal.getUsername());
        // if(Objects.isNull(platformAppConf.getId())) platformAppConf.setCreateTime(LocalDateTime.now());
        // else platformAppConf.setUpdateTime(LocalDateTime.now());
        // //创建平台应用后创建一个默认的方案
        // if(Objects.isNull(dto.getId())) {
        //     boolean save = platformAppConfService.save(platformAppConf);
        //     Long platformAppConfId = platformAppConf.getId();
        //     //创建默认方案
        //     SpecialPlan specialPlan = new SpecialPlan(){{
        //         setPlanName(platformAppConf.getShowName());
        //         setPlatformAppConfId(platformAppConfId);
        //     }};
        //     this.specialPlanService.save(specialPlan);
        //     return save ? JsonResult.success() : JsonResult.error();
        // }else{
        //     return platformAppConfService.saveOrUpdate(platformAppConf) ? JsonResult.success():JsonResult.error();
        // }


        return this.platformAppConfService.saveData(dto);
        // return platformAppConfService.saveOrUpdate(platformAppConf) ? JsonResult.success():JsonResult.error();
    }

    @OperaLog(operaModule = "平台应用配置-删除",operaType = OperaLogConstant.DELETE,operaDesc = "平台应用配置-删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del")
    @Operation(summary = "删除", description = "根据id删除")
    @Parameters({
            @Parameter(name = "id", description = "id", required = true)
    })
    @ApiOperationSupport(order = 3)
    @Transactional(rollbackFor = Exception.class)
    public JsonResult del(@RequestParam("id") Long id){
        return this.platformAppConfService.removeData(id);
        // return this.platformAppConfService.removeById(id) ? JsonResult.success():JsonResult.error();
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "列表", description = "列表")
    @ApiOperationSupport(order = 1)
    @GetMapping("/list")
    public JsonResult<List<PlatformAppConf>> list(){
        List<PlatformAppConf> list = this.platformAppConfService.list();
        list.stream().forEach(appConf -> {
            SpecialPlan one = this.specialPlanService.getOne(new LambdaQueryWrapper<SpecialPlan>().eq(SpecialPlan::getPlatformAppConfId, appConf.getId()));
            appConf.setSpecialPlanId(one.getId());
            FieldPlan one1 = this.fieldPlanService.getOne(new LambdaQueryWrapper<FieldPlan>().eq(FieldPlan::getPlatformAppConfId, appConf.getId()));
            appConf.setFieldPlanId(one1.getId());
        });
        return JsonResult.success(list);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "根据id查询", description = "根据id查询")
    @ApiOperationSupport(order = 4)
    @GetMapping("/get/{id}")
    public JsonResult<PlatformAppConf> getById(@PathVariable("id") Long id){
        return JsonResult.success(this.platformAppConfService.getById(id));
    }

    @Operation(summary = "根据name查询", description = "根据name查询")
    @GetMapping("/getByName")
    @Parameters({
            @Parameter(name = "name", description = "name", required = true)
    })
    public JsonResult<PlatformAppConf> getByName(@RequestParam("name") String name){
        PlatformAppConf one = this.platformAppConfService.getOne(new LambdaQueryWrapper<PlatformAppConf>().eq(PlatformAppConf::getAppName, name));
        one.setSpecialPlanId(this.specialPlanService.getOne(new LambdaQueryWrapper<SpecialPlan>().eq(SpecialPlan::getPlatformAppConfId, one.getId())).getId());
        one.setFieldPlanId(this.fieldPlanService.getOne(new LambdaQueryWrapper<FieldPlan>().eq(FieldPlan::getPlatformAppConfId, one.getId())).getId());
        return JsonResult.success(one);
    }

    @OperaLog(operaModule = "系统配置导出到...",operaType = OperaLogConstant.CREATE,operaDesc = "传平台id")
    @SaCheckPermission("sys:write")
    @PostMapping("/export")
    @Operation(summary = "系统配置导出到...", description = "传平台id")
    @Parameters({
            @Parameter(name = "fromPlatformAppConfId", description = "导出的平台id", required = true),
            @Parameter(name = "toPlatformAppConfId", description = "导入的平台id", required = true)
    })
    @ApiOperationSupport(order = 5)
    public JsonResult exportTo(@RequestParam("fromPlatformAppConfId") Long fromPlatformAppConfId,
                               @RequestParam("toPlatformAppConfId") Long toPlatformAppConfId){
        return this.platformAppConfService.exportTo(fromPlatformAppConfId,toPlatformAppConfId);
    }

    @OperaLog(operaModule = "一键导出所有配置...",operaType = OperaLogConstant.CREATE,operaDesc = "传平台id")
    @SaCheckPermission("sys:write")
    @PostMapping("/exportAll")
    @Operation(summary = "一键导出所有配置...", description = "传平台id")
    @Parameters({
            @Parameter(name = "fromPlatformAppConfId", description = "导出的平台id", required = true),
            @Parameter(name = "appName", description = "标识", required = true)
    })
    @ApiOperationSupport(order = 6)
    @Transactional(rollbackFor = Exception.class)
    @RequestLock(prefix = "export_xtpz_all")
    public JsonResult exportAll(@RequestParam("fromPlatformAppConfId") Long fromPlatformAppConfId,
                                @RequestParam("appName") String appName) {
        return this.platformAppConfService.exportAll(fromPlatformAppConfId,appName);
    }


}
