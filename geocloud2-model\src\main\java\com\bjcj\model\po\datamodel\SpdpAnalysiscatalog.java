package com.bjcj.model.po.datamodel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/5/10  17:30
*/
/**
    * 分析规则目录
    */
@Schema(description="分析规则目录")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "spdp_analysiscatalog")
public class SpdpAnalysiscatalog implements Serializable {
    /**
     * 标识
     */
    @TableId(value = "id",type = IdType.ASSIGN_UUID)
    @Schema(description="标识")
    @Size(max = 36,message = "标识max length should less than 36")
    @NotBlank(message = "标识is not blank")
    private String id;

    /**
     * 名称
     */
    @TableField(value = "name")
    @Schema(description="名称")
    @Size(max = 60,message = "名称max length should less than 60")
    private String name;

    /**
     * 显示顺序
     */
    @TableField(value = "displayorder")
    @Schema(description="显示顺序")
    private Short displayorder;

    /**
     * 父级目录ID
     */
    @TableField(value = "parentid")
    @Schema(description="父级目录ID")
    @Size(max = 36,message = "父级目录IDmax length should less than 36")
    private String parentid;

    /**
     * 描述
     */
    @TableField(value = "description")
    @Schema(description="描述")
    @Size(max = 150,message = "描述max length should less than 150")
    private String description;

    /**
     * 编码
     */
    @TableField(value = "code")
    @Schema(description="编码")
    @Size(max = 50,message = "编码max length should less than 50")
    private String code;

    private static final long serialVersionUID = 1L;
}