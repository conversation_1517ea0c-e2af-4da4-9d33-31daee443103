package com.bjcj.model.dto.datamodel;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


/**
 *@Author：qinyi
 *@Date：2024/6/27  15:13
*/

/**
    * 数据入ES数据库
    */
@Schema(description="数据入ES数据库")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "resource_es")
public class ResourceEsDto implements Serializable {
    @Schema(description="id")
    @Size(max = 36,message = "max length should less than 36")
    private String id;

    /**
     * 数据表的ID
     */
    @Schema(description="数据表的ID")
    @Size(max = 36,message = "数据表的IDmax length should less than 36")
    private String tableid;

    /**
     * 数据展示需要的专题名称
     */
    @Schema(description="数据展示需要的专题名称")
    @Size(max = 60,message = "数据展示需要的专题名称max length should less than 60")
    private String topicname;

    /**
     * 更新时间
     */
    @Schema(description="更新时间")
    private Date updatetime;

    /**
     * 入库状态 0 未入库，1 正在入库，2 入库成功，3 入库失败
     */
    @Schema(description="入库状态 0 未入库，1 正在入库，2 入库成功，3 入库失败")
    private Integer status;

    /**
     * 抽取频率
     */
    @Schema(description="抽取频率")
    @Size(max = 60,message = "抽取频率max length should less than 60")
    private String samplingfrequency;

    /**
     * 抽取时间
     */
    @Schema(description="抽取时间")
    private Integer samplingtime;

    /**
     * 是否删除索引
     */
    @Schema(description="是否删除索引")
    private Boolean isdeleteexitindex;

    private static final long serialVersionUID = 1L;
}