package com.bjcj.model.po.naturalresources.resReview;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/1/22  9:43
*/
/**
    * 注册审核记录
    */
@Schema(description="注册审核记录")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "res_register_log")
public class ResRegisterLog implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 审核状态
     */
    @TableField(value = "review_status")
    @Schema(description="审核状态")
    private Boolean reviewStatus;

    /**
     * 资源类型(应用服务,数据产品,数据资源)
     */
    @TableField(value = "res_type")
    @Schema(description="资源类型(应用服务,数据产品,数据资源)")
    private String resType;

    /**
     * 资源名称
     */
    @TableField(value = "res_name")
    @Schema(description="资源名称")
    private String resName;

    /**
     * 发布注册时间
     */
    @TableField(value = "publish_time")
    @Schema(description="发布注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;

    /**
     * 审核时间
     */
    @TableField(value = "review_time")
    @Schema(description="审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewTime;

    /**
     * 发布者nickname
     */
    @TableField(value = "publish_user_name")
    @Schema(description="发布者nickname")
    private String publishUserName;

    /**
     * 审核意见
     */
    @TableField(value = "review_remark")
    @Schema(description="审核意见")
    private String reviewRemark;

    /**
     * 资源id
     */
    @TableField(value = "res_id")
    @Schema(description="资源id")
    private String resId;

    /**
     * 审核人
     */
    @TableField(value = "operater")
    @Schema(description="审核人")
    private String operater;

    private static final long serialVersionUID = 1L;
}