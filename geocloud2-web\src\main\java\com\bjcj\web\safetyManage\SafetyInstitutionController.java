package com.bjcj.web.safetyManage;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.safetyManage.SafetyInstitutionDto;
import com.bjcj.model.po.safetyManage.SafetyInstitution;
import com.bjcj.model.po.safetyManage.SafetyLevel;
import com.bjcj.model.po.system.SysXzq14;
import com.bjcj.service.safetyManage.SafetyInstitutionService;
import com.bjcj.service.safetyManage.SafetyLevelService;
import com.bjcj.service.safetyManage.SafetyXzqService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author：qinyi
 * @Date：2023/11/23 10:46
 */
@RestController
@RequestMapping("/institution")
@Tag(name = "机构管理")
@Validated
public class SafetyInstitutionController {

    @Resource
    SafetyInstitutionService safetyInstitutionService;

    @Resource
    private SafetyLevelService safetyLevelService;

    @Resource
    SafetyXzqService safetyXzqService;

    @SaCheckPermission("sys:read")
    @GetMapping("/page")
    @Operation(summary = "机构列表分页", description = "带搜索框模糊查询")
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "name", description = "名称搜索", required = false),
            @Parameter(name = "level", description = "行政区级别搜索", required = false),
            @Parameter(name = "institutionId", description = "机构id", required = false)
    })
    @ApiOperationSupport(order = 1)
    public JsonResult<Page<SafetyInstitution>> page(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam(value = "name",required = false) String name,
            @RequestParam(value = "level",required = false) String level,
            @RequestParam(value = "institutionId", required = false) String institutionId
            ) {
        Page<SafetyInstitution> pager = new Page<>(page, pageSize);
        return this.safetyInstitutionService.pageDataSort(pager,name,level,institutionId);
    }

    @OperaLog(operaModule = "机构管理-新增/编辑机构",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "新增/编辑机构")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑机构", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "addOrEdit")
    public JsonResult add(@Validated @RequestBody SafetyInstitutionDto dto){
        return this.safetyInstitutionService.saveData(dto);
    }

    @OperaLog(operaModule = "机构管理-删除机构",operaType = OperaLogConstant.DELETE,operaDesc = "删除机构")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del")
    @Operation(summary = "删除机构", description = "根据id删除机构")
    @Parameters({
            @Parameter(name = "id", description = "主键id", required = true)
    })
    @ApiOperationSupport(order = 3)
    public JsonResult del(@RequestParam("id") Long id){
        // return JsonResult.error("机构暂不可删除,如有疑问请联系开发人员");
       return this.safetyInstitutionService.delData(id);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("selectAll")
    @Operation(summary = "查询所有级别", description = "查询所有级别")
    @ApiOperationSupport(order = 4)
    public JsonResult<List<SafetyLevel>> selectAll() {
        return safetyLevelService.selectAll();
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/xzqList")
    @Operation(summary = "行政区列表", description = "每次请求搜一层,默认展示最顶层")
    @Parameters({
            @Parameter(name = "searchStr", description = "搜索字符串", required = false),
            @Parameter(name = "pid", description = "父节点id", required = false)
    })
    @ApiOperationSupport(order = 5)
    public JsonResult<List<SysXzq14>> xzqList(@RequestParam(value = "searchStr",required = false) String searchStr,
                                              @RequestParam(value = "pid",required = false) Long pid){
        return this.safetyXzqService.queryData(searchStr,pid);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/getInstitution")
    @Operation(summary = "查询所有机构", description = "查询所有机构")
    @ApiOperationSupport(order = 6)
    public JsonResult getInstitution(){
        return this.safetyInstitutionService.getInstitution();
    }

}
