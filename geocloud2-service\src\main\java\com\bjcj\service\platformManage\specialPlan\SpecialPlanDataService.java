package com.bjcj.service.platformManage.specialPlan;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.categories.*;
import com.bjcj.mapper.platformManage.specialPlan.SpecialPlanDataMapper;
import com.bjcj.mapper.platformManage.specialPlan.SpecialPlanDirMapper;
import com.bjcj.model.dto.specialPlan.SpecialPlanDataDto;
import com.bjcj.model.po.naturalresources.categories.ResourceCatalogs;
import com.bjcj.model.po.naturalresources.categories.ResourceDataitems;
import com.bjcj.model.po.naturalresources.categories.ResourceServices;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlanData;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlanDir;
import com.bjcj.model.vo.specialPlan.DirDataMixedVo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 *@Author：qinyi
 *@Date：2024/1/26  10:18
*/
@Service
public class SpecialPlanDataService extends ServiceImpl<SpecialPlanDataMapper, SpecialPlanData> {

    @Resource
    private SpecialPlanDataMapper specialPlanDataMapper;

    @Resource
    private CategoriesServiceMapper categoriesServiceMapper;

    @Resource
    private CategoriesDataInfoMapper categoriesDataInfoMapper;

    @Resource
    private SpecialPlanDirMapper specialPlanDirMapper;

    @Resource
    private CategoriesDataMapper categoriesDataMapper;

    @Resource
    private SpecialPlanDirService specialPlanDirService;

    @Resource
    private ResourceCatalogsMapper resourceCatalogsMapper;

    @Resource
    private ResourceDataitemsMapper resourceDataitemsMapper;

    @Resource
    private ResourceServicesMapper resourceServicesMapper;




    public JsonResult addSpecialPlanData(List<String> cataServiceIdList, Long specialPlanId,String specialPlanDirId) {
        if (cataServiceIdList.isEmpty()) return JsonResult.error("请传输至少一条数据");
        //重复校验,表中有唯一索引
        List<HashMap<String,String>> resultMsg = new ArrayList<>();
        cataServiceIdList.forEach(cataServiceId -> {
            SpecialPlanData specialPlanData = specialPlanDataMapper.selectOne(new LambdaQueryWrapper<SpecialPlanData>().eq(SpecialPlanData::getCataServiceId, cataServiceId).eq(SpecialPlanData::getSpecialPlanId, specialPlanId));
            if(Objects.nonNull(specialPlanData)){
                HashMap<String,String> m = new HashMap<>();
                m.put(specialPlanData.getCataServiceId(),specialPlanData.getShowName());
                resultMsg.add(m);
            }
        });
        if(!resultMsg.isEmpty()){
            JSONArray arr = JSONArray.from(resultMsg);
            return JsonResult.error("已存在相同数据，请勿重复添加:"+ arr);
        }
        List<ResourceServices> categoriesServices = resourceServicesMapper.selectList(new LambdaQueryWrapper<ResourceServices>().in(ResourceServices::getId, cataServiceIdList));
        List<SpecialPlanData> specialPlanDataList = new ArrayList<>(categoriesServices.size());
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        //写入数据及关联的主表信息
        categoriesServices.forEach(categoriesService -> {
            ResourceDataitems resourceDataitems = this.resourceDataitemsMapper.selectOne(new LambdaQueryWrapper<ResourceDataitems>().eq(ResourceDataitems::getId, categoriesService.getDataitemid()));
            SpecialPlanData specialPlanData = new SpecialPlanData(){{
                setCataServiceId(categoriesService.getId());
                setCataDataInfoId(categoriesService.getDataitemid());
                setCreateTime(LocalDateTime.now());
                setOperater(username);
                setSpecialName(resourceDataitems.getName());
                setShowName(resourceDataitems.getDisplayname());
                setSpecialPlanId(specialPlanId);
                setShowSort(0);
                setIsShow(true);
                setIsExpand(true);
                setIsInitLoad(false);
                if(StringUtils.isNotBlank(specialPlanDirId)) setSpecialPlanDirId(specialPlanDirId);
            }};
            specialPlanDataList.add(specialPlanData);
        });
        boolean b = this.saveBatch(specialPlanDataList);
        return b ? JsonResult.success("添加成功") : JsonResult.error("添加失败");
    }

    public JsonResult editSpecialPlanData(SpecialPlanDataDto dto) {
        SpecialPlanData specialPlanData = BeanUtil.copyProperties(dto, SpecialPlanData.class);
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        specialPlanData.setUpdateTime(LocalDateTime.now());
        specialPlanData.setOperater(username);
        return this.specialPlanDataMapper.updateById(specialPlanData) > 0 ? JsonResult.success("修改成功") : JsonResult.error("修改失败");
    }

    public JsonResult<List<DirDataMixedVo>> dirAndPlanDataList(Long specialPlanId) {
        List<SpecialPlanDir> dirList = specialPlanDirMapper.selectList(
                Wrappers.<SpecialPlanDir>lambdaQuery()
                        .eq(SpecialPlanDir::getSpecialPlanId, specialPlanId)
                        .eq(SpecialPlanDir::getParentId,"0")
        );
        List<SpecialPlanData> dataList = specialPlanDataMapper.selectList(
                Wrappers.<SpecialPlanData>lambdaQuery()
                        .eq(SpecialPlanData::getSpecialPlanId, specialPlanId)
                        .isNull(SpecialPlanData::getSpecialPlanDirId)
        );
        List<DirDataMixedVo> dirDataMixedVoList = new ArrayList<>(dirList.size() + dataList.size());
        dirList.forEach(dir -> {
            DirDataMixedVo vo = BeanUtil.copyProperties(dir, DirDataMixedVo.class);
            vo.setDirName(dir.getDirName());
            vo.setDataType("dir");
            List<DirDataMixedVo> byDirId = findByDirId(dir.getId(),specialPlanId);
            vo.setChildren(byDirId);
            dirDataMixedVoList.add(vo);
        });
        dataList.forEach(data -> {
            DirDataMixedVo vo = BeanUtil.copyProperties(data, DirDataMixedVo.class);
            vo.setDirName(data.getSpecialName());
            vo.setDataType("data");
            vo.setParentId(data.getSpecialPlanDirId());
            if(StringUtils.isNotEmpty(data.getCataServiceId())) {
                ResourceServices resourceServices = this.resourceServicesMapper.selectOne(new LambdaQueryWrapper<ResourceServices>().eq(ResourceServices::getId, data.getCataServiceId()));
                String url = resourceServices.getUrl();
                vo.setUrl(url);
                String resourcetype = resourceServices.getResourcetype();
                vo.setResourcetype(resourcetype);
            }
            dirDataMixedVoList.add(vo);
        });
        //根据show_sort排序
        dirDataMixedVoList.sort(Comparator.comparingInt(DirDataMixedVo::getShowSort));
        return JsonResult.success(dirDataMixedVoList);
    }

    /**
     * 递归查询是否存在目录或数据
     */
    public List<DirDataMixedVo> findByDirId(String dirId,Long specialPlanId){
        List<SpecialPlanDir> dirList = this.specialPlanDirMapper.selectList(new LambdaQueryWrapper<SpecialPlanDir>().eq(SpecialPlanDir::getParentId, dirId).eq(SpecialPlanDir::getSpecialPlanId,specialPlanId));
        List<SpecialPlanData> dataList = this.specialPlanDataMapper.selectList(new LambdaQueryWrapper<SpecialPlanData>().eq(SpecialPlanData::getSpecialPlanDirId, dirId).eq(SpecialPlanData::getSpecialPlanId,specialPlanId));
        List<DirDataMixedVo> dirDataMixedVoList = new ArrayList<>(dirList.size() + dataList.size());
        if(!dirList.isEmpty()){
            dirList.forEach(dir -> {
                DirDataMixedVo vo = BeanUtil.copyProperties(dir, DirDataMixedVo.class);
                vo.setDirName(dir.getDirName());
                vo.setDataType("dir");
                List<DirDataMixedVo> byDirId = findByDirId(dir.getId(),specialPlanId);
                vo.setChildren(byDirId);
                dirDataMixedVoList.add(vo);

            });
        }
        dataList.forEach(data -> {
            DirDataMixedVo vo = BeanUtil.copyProperties(data, DirDataMixedVo.class);
            vo.setDirName(data.getSpecialName());
            vo.setDataType("data");
            vo.setParentId(data.getSpecialPlanDirId());
            if(StringUtils.isNotEmpty(data.getCataServiceId())){
                ResourceServices resourceServices = this.resourceServicesMapper.selectOne(new LambdaQueryWrapper<ResourceServices>().eq(ResourceServices::getId, data.getCataServiceId()));
                String url = resourceServices.getUrl();
                vo.setUrl(url);
                String resourcetype = resourceServices.getResourcetype();
                vo.setResourcetype(resourcetype);
            }
            dirDataMixedVoList.add(vo);
        });
        return dirDataMixedVoList;
    }


    // public JsonResult copyAllCata(Long specialPlanId) {
    //     //先删除方案下的所有目录及数据 再copy完整资源结构
    //     this.specialPlanDataMapper.delete(new LambdaQueryWrapper<SpecialPlanData>().eq(SpecialPlanData::getSpecialPlanId, specialPlanId));
    //     this.specialPlanDirMapper.delete(new LambdaQueryWrapper<SpecialPlanDir>().eq(SpecialPlanDir::getSpecialPlanId, specialPlanId));
    //     List<CategoriesData> categoriesDataList = this.categoriesDataMapper.selectList(new LambdaQueryWrapper<CategoriesData>().eq(CategoriesData::getDel, 0)
    //             .eq(CategoriesData::getStatus,true));
    //     User principal = (User) StpUtil.getSession().get("user");
    //     List<SpecialPlanDir> saveDirList = new ArrayList<>(categoriesDataList.size());
    //     categoriesDataList.forEach(categoriesData -> {
    //         SpecialPlanDir specialPlanDir = new SpecialPlanDir(){{
    //             setSpecialPlanId(specialPlanId);
    //             setOperater(principal.getUsername());
    //             setDirName(categoriesData.getName());
    //             setShowName(categoriesData.getName());
    //             setParentId(categoriesData.getParentId());
    //             setShowSort(categoriesData.getDisplayOrder());
    //             setSpecialPlanId(specialPlanId);
    //             setIsShow(true);
    //             setIsExpand(true);
    //             setIsInitLoad(false);
    //             setId(categoriesData.getId());
    //         }};
    //         saveDirList.add(specialPlanDir);
    //     });
    //     this.specialPlanDirService.saveBatch(saveDirList);
    //
    //     List<Long> catadataIds = categoriesDataList.stream().map(CategoriesData::getId).toList();
    //     List<String> catadataIdss = catadataIds.stream().map(String::valueOf).toList();
    //     List<CategoriesDataInfo> cataDataInfoList = this.categoriesDataInfoMapper.selectList(new LambdaQueryWrapper<CategoriesDataInfo>().in(CategoriesDataInfo::getCatalogue, catadataIdss)
    //             .eq(CategoriesDataInfo::getDel, 0).eq(CategoriesDataInfo::getStatus,true));
    //     List<SpecialPlanData> dataList = new ArrayList<>();
    //     cataDataInfoList.forEach(catadatainfo -> {
    //         List<CategoriesService> categoriesServices = categoriesServiceMapper.selectList(new LambdaQueryWrapper<CategoriesService>().eq(CategoriesService::getParentId, catadatainfo.getId()).eq(CategoriesService::getDel, 0));
    //         SpecialPlanData specialPlanData = new SpecialPlanData(){{
    //             setSpecialName(catadatainfo.getName());
    //             setShowName(catadatainfo.getName());
    //             setSpecialPlanId(specialPlanId);
    //             setSpecialPlanDirId(Long.valueOf(catadatainfo.getCatalogue()));
    //             setShowSort(0);
    //             setOperater(principal.getUsername());
    //             setIsShow(true);
    //             setIsExpand(true);
    //             setIsInitLoad(false);
    //             setCataDataInfoId(catadatainfo.getId());
    //             if(!categoriesServices.isEmpty()) setCataServiceId(categoriesServices.get(0).getId());
    //             else setCataServiceId(0L);
    //         }};
    //         dataList.add(specialPlanData);
    //     });
    //     this.saveBatch(dataList);
    //     return JsonResult.success();
    // }


    public JsonResult copyAllCata2(Long specialPlanId) {
        //先删除方案下的所有目录及数据 再copy完整资源结构
        this.specialPlanDataMapper.delete(new LambdaQueryWrapper<SpecialPlanData>().eq(SpecialPlanData::getSpecialPlanId, specialPlanId));
        this.specialPlanDirMapper.delete(new LambdaQueryWrapper<SpecialPlanDir>().eq(SpecialPlanDir::getSpecialPlanId, specialPlanId));
        List<ResourceCatalogs> categoriesDataList = this.resourceCatalogsMapper.selectList(new LambdaQueryWrapper<ResourceCatalogs>()
                .eq(ResourceCatalogs::getIsvisiable,true)
                .eq(ResourceCatalogs::getResourcecategory,6));
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        List<SpecialPlanDir> saveDirList = new ArrayList<>(categoriesDataList.size());
        categoriesDataList.forEach(categoriesData -> {
            SpecialPlanDir specialPlanDir = new SpecialPlanDir(){{
                setSpecialPlanId(specialPlanId);
                setOperater(username);
                setDirName(categoriesData.getName());
                setShowName(categoriesData.getName());
                setParentId(categoriesData.getParentid());
                setShowSort(categoriesData.getDisplayorder());
                setSpecialPlanId(specialPlanId);
                setIsShow(true);
                setIsExpand(true);
                setIsInitLoad(false);
                setId(categoriesData.getId());
            }};
            saveDirList.add(specialPlanDir);
        });
        this.specialPlanDirService.saveBatch(saveDirList);

        List<String> catadataIds = categoriesDataList.stream().map(ResourceCatalogs::getId).toList();
        List<String> catadataIdss = catadataIds.stream().map(String::valueOf).toList();
        List<ResourceDataitems> cataDataInfoList = this.resourceDataitemsMapper.selectList(new LambdaQueryWrapper<ResourceDataitems>().in(ResourceDataitems::getResourcecatalogid, catadataIdss)
                .eq(ResourceDataitems::getStatus,1).eq(ResourceDataitems::getIsvisiable,true));
        List<SpecialPlanData> dataList = new ArrayList<>();
        cataDataInfoList.forEach(catadatainfo -> {
            List<ResourceServices> categoriesServices = resourceServicesMapper.selectList(new LambdaQueryWrapper<ResourceServices>().eq(ResourceServices::getDataitemid, catadatainfo.getId()));
            SpecialPlanData specialPlanData = new SpecialPlanData(){{
                setSpecialName(catadatainfo.getName());
                setShowName(catadatainfo.getName());

                setSpecialPlanId(specialPlanId);
                setSpecialPlanDirId(catadatainfo.getResourcecatalogid());
                setShowSort(0);
                setOperater(username);
                setIsShow(true);
                setIsExpand(true);
                setIsInitLoad(false);
                setCataDataInfoId(catadatainfo.getId());
                if(!categoriesServices.isEmpty()) setCataServiceId(categoriesServices.get(0).getId());
                else setCataServiceId("");
            }};
            dataList.add(specialPlanData);
        });
        this.saveBatch(dataList);
        return JsonResult.success();
    }
}
