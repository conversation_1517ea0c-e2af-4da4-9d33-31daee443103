package com.bjcj.service.naturalresources.categories;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.categories.CategoriesMetaMapper;
import com.bjcj.model.po.naturalresources.categories.CategoriesMeta;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Cleanup;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/1 15:22 周五
 */
@Service
public class CategoriesMetaService extends ServiceImpl<CategoriesMetaMapper, CategoriesMeta> {

    @Resource
    private CategoriesMetaMapper categoriesMetaMapper;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private HttpServletResponse response;

    public JsonResult lists(int current, int size){
        Page<CategoriesMeta> page = new Page<>(current, size);
        LambdaQueryWrapper<CategoriesMeta> wrapper = new LambdaQueryWrapper<>();
        IPage<CategoriesMeta> list = categoriesMetaMapper.selectPage(page, wrapper);
        return JsonResult.success(list);
    }

    public JsonResult exportJson(){

        LambdaQueryWrapper<CategoriesMeta> wrapper = new LambdaQueryWrapper<>();

        List<CategoriesMeta> entities = categoriesMetaMapper.selectList(wrapper);
        try {
            // 获取响应输出流
            OutputStream outputStream = response.getOutputStream();
            // 设置响应头
            response.setContentType("application/json");
            response.setHeader("Content-Disposition", "attachment; filename=\"dataServiceCategoriesData.json\"");
            // 将查询到的结果序列化为json格式
            String jsonString = JSON.toJSONString(entities);
            // 将序列化的json数据集写入到输出流中
            outputStream.write(jsonString.getBytes(StandardCharsets.UTF_8));
            // 推送输出流结果到浏览器
            outputStream.flush();
        }catch (Exception e){
            log.error("导出文件失败"+e);
        }

        return JsonResult.success();
    }

    public JsonResult importJson(MultipartFile file){
        List<CategoriesMeta> list = new ArrayList<>();
        try {
            @Cleanup InputStream inputStream = file.getInputStream();
            list = objectMapper.readValue(inputStream, new TypeReference<List<CategoriesMeta>>() {});
            this.saveBatch(list);

        }catch (Exception e) {
            log.error("导入文件失败"+e);
        }

        return JsonResult.success();
    }

}
