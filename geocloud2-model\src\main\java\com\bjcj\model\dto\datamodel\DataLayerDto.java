package com.bjcj.model.dto.datamodel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 * 图层表
 *@Date：2024/1/3  15:32
*/
@Schema(description="图层表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "data_layer")
public class DataLayerDto implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @RequestKeyParam(name = "id")
    private Long id;

    /**
     * 名称
     */
    @TableField(value = "name")
    @Schema(description="名称")
    @Size(max = 100,message = "名称max length should less than 100")
    @NotBlank(message = "名称is not blank")
    private String name;

    /**
     * 显示名称
     */
    @TableField(value = "show_name")
    @Schema(description="显示名称")
    @Size(max = 100,message = "显示名称max length should less than 100")
    private String showName;

    /**
     * 编码
     */
    @TableField(value = "code")
    @Schema(description="编码")
    @Size(max = 50,message = "编码max length should less than 50")
    private String code;

    /**
     * 是否必须
     */
    @TableField(value = "is_must")
    @Schema(description="是否必须")
    private Boolean isMust;

    /**
     * 类型
     */
    @TableField(value = "layer_type_id")
    @Schema(description="类型")
    @NotNull(message = "类型is not null")
    private Long layerTypeId;

    /**
     * 描述
     */
    @TableField(value = "remark")
    @Schema(description="描述")
    @Size(max = 255,message = "描述max length should less than 255")
    private String remark;

    /**
     * 数据标准id
     */
    @TableField(value = "data_standard_id")
    @Schema(description="数据标准id")
    @NotNull(message = "数据标准idis not null")
    private Long dataStandardId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @Schema(description="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 最后操作人
     */
    @TableField(value = "operator")
    @Schema(description="最后操作人")
    @Size(max = 20,message = "最后操作人max length should less than 20")
    private String operator;

    private static final long serialVersionUID = 1L;
}