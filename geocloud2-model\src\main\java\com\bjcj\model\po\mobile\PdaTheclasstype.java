package com.bjcj.model.po.mobile;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 
 * @TableName sys_mobile_theclasstype
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value ="sys_mobile_theclasstype")
@Schema(description = "模版信息表")
public class PdaTheclasstype implements Serializable {
    /**
     * 
     */
    @TableId
    private String objectid;

    /**
     * 
     */
    private String classtypename;

    /**
     * 
     */
    private String typefilename;

    /**
     * 
     */
    private String status;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


}