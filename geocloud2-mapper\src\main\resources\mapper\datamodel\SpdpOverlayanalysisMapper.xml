<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.SpdpOverlayanalysisMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.SpdpOverlayanalysis">
    <!--@mbg.generated-->
    <!--@Table public.spdp_overlayanalysis-->
    <id column="overlayanalysisid" jdbcType="CHAR" property="overlayanalysisid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="displayname" jdbcType="VARCHAR" property="displayname" />
    <result column="overlayanalysismodelid" jdbcType="VARCHAR" property="overlayanalysismodelid" />
    <result column="params" jdbcType="VARCHAR" property="params" />
    <result column="resourceitemid" jdbcType="CHAR" property="resourceitemid" />
    <result column="inputlayers" jdbcType="VARCHAR" property="inputlayers" />
    <result column="overlaylayers" jdbcType="VARCHAR" property="overlaylayers" />
    <result column="componenttypeid" jdbcType="CHAR" property="componenttypeid" />
    <result column="regionlayerid" jdbcType="CHAR" property="regionlayerid" />
    <result column="restrictvalue" jdbcType="NUMERIC" property="restrictvalue" />
    <result column="areacalculaterid" jdbcType="VARCHAR" property="areacalculaterid" />
    <result column="catalogid" jdbcType="CHAR" property="catalogid" />
    <result column="code" jdbcType="NUMERIC" property="code" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    overlayanalysisid, "name", displayname, overlayanalysismodelid, params, resourceitemid, 
    inputlayers, overlaylayers, componenttypeid, regionlayerid, restrictvalue, areacalculaterid, 
    catalogid, code
  </sql>
</mapper>