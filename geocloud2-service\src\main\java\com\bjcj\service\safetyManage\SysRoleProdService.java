package com.bjcj.service.safetyManage;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.safetyManage.SysRoleProdMapper;
import com.bjcj.model.dto.safetyManage.RoleProductsDto;
import com.bjcj.model.po.safetyManage.SysRoleProd;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 *@Author：qinyi
 *@Date：2023/12/13  15:06
*/
@Service
public class SysRoleProdService extends ServiceImpl<SysRoleProdMapper, SysRoleProd> {

    @Resource
    SysRoleProdMapper sysRoleProdMapper;

    @Resource
    CheckIsAdmin checkIsAdmin;

    public JsonResult saveRoleProducts(RoleProductsDto dto) {
        //获取当前登录用户角色
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        String fromRoleName = checkIsAdmin.getUserRoleName(userId);
        /**
        //保存之前清洗角色下历史数据
        LambdaQueryWrapper<SysRoleProd> queryWrapper = new LambdaQueryWrapper<SysRoleProd>()
               .eq(SysRoleProd::getRoleId,dto.getRoleId());
        sysRoleProdMapper.delete(queryWrapper);

        Long roleId = dto.getRoleId();
        List<Long> prodIdList = Arrays.stream(dto.getProdIds().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<SysRoleProd> list = new ArrayList<SysRoleProd>();
        prodIdList.forEach(prodId -> {
            SysRoleProd sysRoleProd = new SysRoleProd(){{setProductId(prodId);setRoleId(roleId);setFromRoleName(fromRoleName);}};
            list.add(sysRoleProd);
        });
        Integer count = sysRoleProdMapper.insertBatch(list);
        if(count > 0){
            return JsonResult.success();
        }else{
            return JsonResult.error();
        }
        */
        Long roleId = dto.getRoleId();
        if(!dto.getAddIdList().isEmpty()){
            List<SysRoleProd> list = new ArrayList<SysRoleProd>();
            dto.getAddIdList().forEach(productId -> {
                SysRoleProd sysRoleProd =  new SysRoleProd(){{setProductId(productId);setRoleId(roleId);setFromRoleName(fromRoleName);}};
                list.add(sysRoleProd);
            });
            sysRoleProdMapper.insertBatch(list);
        }
        if(!dto.getDelIdList().isEmpty()){
            sysRoleProdMapper.deleteBatch(dto.getDelIdList(),roleId);
        }
        return JsonResult.success();
    }
}
