package com.bjcj.service.datamodel;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.datamodel.DataLayerFieldMapper;
import com.bjcj.model.dto.datamodel.DataLayerFieldDto;
import com.bjcj.model.po.datamodel.DataLayerField;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 *@Author：qinyi
 *@Date：2024/1/3  15:32
*/
@Service
public class DataLayerFieldService extends ServiceImpl<DataLayerFieldMapper, DataLayerField> {

    @Resource
    DataLayerFieldMapper dataLayerFieldMapper;

    public JsonResult saveData(DataLayerFieldDto dto) {
        DataLayerField dataLayerField = BeanUtil.copyProperties(dto, DataLayerField.class);
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        if(Objects.isNull(dataLayerField.getId())){
            //验证名称或显示名称重复数据
            LambdaQueryWrapper<DataLayerField> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DataLayerField::getDataLayerId,dataLayerField.getDataLayerId());
            queryWrapper.and(i->i.eq(DataLayerField::getFieldName,dataLayerField.getFieldName()).or().eq(DataLayerField::getShowName,dataLayerField.getShowName()));
            List<DataLayerField> list = this.dataLayerFieldMapper.selectList(queryWrapper);
            if(!list.isEmpty()){
                return JsonResult.error("名称或显示名称重复");
            }
            dataLayerField.setOperator(username);
            dataLayerField.setCreateTime(LocalDateTime.now());
            dataLayerFieldMapper.insert(dataLayerField);
        }else{
            dataLayerField.setOperator(username);
            dataLayerField.setUpdateTime(LocalDateTime.now());
            dataLayerFieldMapper.updateById(dataLayerField);
        }
        return JsonResult.success();
    }
}
