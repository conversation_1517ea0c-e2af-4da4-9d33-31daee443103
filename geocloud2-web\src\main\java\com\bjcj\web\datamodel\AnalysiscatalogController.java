package com.bjcj.web.datamodel;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.datamodel.SpdpAnalysiscatalogDto;
import com.bjcj.model.po.datamodel.SpdpAnalysiscatalog;
import com.bjcj.service.datamodel.SpdpAnalysiscatalogService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
* 数据分析规则表(spdp_analysiscatalog)表控制层
* <AUTHOR>
*/
@RestController
@RequestMapping("/spdp_analysiscatalog")
@Tag(name = "11.数据分析规则")
@Validated
public class AnalysiscatalogController {
    /**
    * 服务对象
    */
    @Resource
    private SpdpAnalysiscatalogService spdpAnalysiscatalogService;

    @OperaLog(operaModule = "新增/编辑-数据分析规则",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "新增/编辑")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑数据分析规则", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "addOrEdit")
    public JsonResult add(@Validated @RequestBody SpdpAnalysiscatalogDto dto){
        return this.spdpAnalysiscatalogService.saveData(dto);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/list")
    @Operation(summary = "列表", description = "列表")
    @ApiOperationSupport(order = 1)
    public JsonResult<List<SpdpAnalysiscatalog>> list(){
        return JsonResult.success(this.spdpAnalysiscatalogService.list());
    }

    @OperaLog(operaModule = "数据分析规则-删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del")
    @Operation(summary = "删除", description = "根据id删除")
    @Parameters({
            @Parameter(name = "id", description = "id", required = true)
    })
    @ApiOperationSupport(order = 3)
    public JsonResult del(@RequestParam("id") String id){
        return this.spdpAnalysiscatalogService.removeById(id) ? JsonResult.success() : JsonResult.error();
    }

}
