<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.platformManage.specialPlan.SpecialPlanDataMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.platformManage.specialPlan.SpecialPlanData">
    <!--@mbg.generated-->
    <!--@Table public.special_plan_data-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="special_name" jdbcType="VARCHAR" property="specialName" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
    <result column="special_plan_id" jdbcType="BIGINT" property="specialPlanId" />
    <result column="special_plan_dir_id" jdbcType="BIGINT" property="specialPlanDirId" />
    <result column="show_sort" jdbcType="INTEGER" property="showSort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operater" jdbcType="VARCHAR" property="operater" />
    <result column="is_show" jdbcType="BOOLEAN" property="isShow" />
    <result column="is_expand" jdbcType="BOOLEAN" property="isExpand" />
    <result column="is_init_load" jdbcType="BOOLEAN" property="isInitLoad" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, special_name, show_name, special_plan_id, special_plan_dir_id, show_sort, create_time, 
    update_time, operater, is_show, is_expand, is_init_load
  </sql>
</mapper>