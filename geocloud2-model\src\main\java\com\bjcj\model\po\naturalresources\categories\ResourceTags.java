package com.bjcj.model.po.naturalresources.categories;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/5/6  10:28
*/
/**
    * 资源标签
    */
@Schema(description="资源标签")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "resource_tags")
public class ResourceTags implements Serializable {
    /**
     * 标识
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(description="标识")
    @Size(max = 36,message = "标识max length should less than 36")
    private String id;

    /**
     * 标签名称
     */
    @TableField(value = "name")
    @Schema(description="标签名称")
    @Size(max = 200,message = "标签名称max length should less than 200")
    @NotBlank(message = "标签名称is not blank")
    private String name;

    /**
     * 资源id
     */
    @TableField(value = "resourceid")
    @Schema(description="资源id")
    @Size(max = 36,message = "资源idmax length should less than 36")
    private String resourceid;

    /**
     * 资源类别。1应用服务；2应用；3文档；4数据；5数据产品；6数据服务；7数据库；8服务器
     */
    @TableField(value = "resourcecategory")
    @Schema(description="资源类别。1应用服务；2应用；3文档；4数据；5数据产品；6数据服务；7数据库；8服务器")
    private Integer resourcecategory;

    private static final long serialVersionUID = 1L;
}