package com.bjcj.model.dto.specialPlan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/1/26  10:17
*/

/**
    * 专题展示方案目录
    */
@Schema(description="专题展示方案目录")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "special_plan_dir")
public class SpecialPlanDirDto implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private String id;

    @TableField(value = "special_plan_id")
    @Schema(description="")
    @NotNull(message = "is not null")
    private Long specialPlanId;

    /**
     * 目录名称
     */
    @TableField(value = "dir_name")
    @Schema(description="目录名称")
    @Size(max = 100,message = "目录名称max length should less than 100")
    @NotBlank(message = "目录名称is not blank")
    @RequestKeyParam(name = "dirName")
    private String dirName;

    /**
     * 显示名称
     */
    @TableField(value = "show_name")
    @Schema(description="显示名称")
    @Size(max = 100,message = "显示名称max length should less than 100")
    private String showName;

    /**
     * 父id
     */
    @TableField(value = "parent_id")
    @Schema(description="父id")
    @NotNull(message = "父idis not null")
    private String parentId;

    /**
     * 显示顺序
     */
    @TableField(value = "show_sort")
    @Schema(description="显示顺序")
    @NotNull(message = "显示顺序is not null")
    private Integer showSort;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "update_time")
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField(value = "operater")
    @Schema(description="")
    @Size(max = 30,message = "max length should less than 30")
    private String operater;

    /**
     * 是否显示
     */
    @TableField(value = "is_show")
    @Schema(description="是否显示")
    @NotNull(message = "是否显示is not null")
    private Boolean isShow;

    /**
     * 是否展开
     */
    @TableField(value = "is_expand")
    @Schema(description="是否展开")
    @NotNull(message = "是否展开is not null")
    private Boolean isExpand;

    /**
     * 是否初始化加载
     */
    @TableField(value = "is_init_load")
    @Schema(description="是否初始化加载")
    @NotNull(message = "是否初始化加载is not null")
    private Boolean isInitLoad;

    private static final long serialVersionUID = 1L;
}