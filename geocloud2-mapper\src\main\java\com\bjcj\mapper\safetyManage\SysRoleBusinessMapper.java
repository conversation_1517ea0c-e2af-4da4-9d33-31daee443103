package com.bjcj.mapper.safetyManage;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.safetyManage.SysRoleBusiness;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 *@Author：qinyi
 *@Date：2023/12/14  9:52
*/
public interface SysRoleBusinessMapper extends BaseMapper<SysRoleBusiness> {
    Integer insertBatch(List<SysRoleBusiness> list);

    void deleteBatch(@Param("list") List<Long> list, @Param("roleId") Long roleId);
}