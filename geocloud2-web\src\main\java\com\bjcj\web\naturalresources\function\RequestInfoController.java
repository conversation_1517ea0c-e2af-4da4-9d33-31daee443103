package com.bjcj.web.naturalresources.function;

import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/12/4 15:04 周一
 */
@Tag(name = "功能服务信息调用信息(暂时无用)",description = "功能服务信息调用信息(暂时无用)")
@ApiSupport(order = 57)
@RestController
@Slf4j
@RequestMapping(value = "/request_info")
public class RequestInfoController {

    /*@Resource
    private RequestInfoService requestInfoService;

    @Operation(summary = "功能服务信息调用信息注册")
    @PostMapping(value = "/save")
    public JsonResult save(@RequestBody @Validated RequestInfo requestInfo){
        requestInfoService.save(requestInfo);
        Long id = requestInfo.getId();
        return JsonResult.success(id);
    }

    @Operation(summary = "功能服务信息调用信息修改")
    @PostMapping(value = "/update")
    public JsonResult update(@RequestBody @Validated RequestInfo requestInfo){

        return JsonResult.success(requestInfoService.saveOrUpdate(requestInfo));
    }

    @Operation(summary = "功能服务信息调用信息删除")
    @GetMapping(value = "/del/{id}")
    public JsonResult del(@PathVariable Long id){

        return requestInfoService.del(id);
    }*/

}
