<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bjcj</groupId>
        <artifactId>geocloud2</artifactId>
        <version>0.0.1</version>
    </parent>

    <artifactId>geocloud2-web</artifactId>
    <packaging>jar</packaging>

    <name>geocloud2-web</name>
    <url>http://maven.apache.org</url>

    <dependencies>
        <dependency>
            <groupId>com.bjcj</groupId>
            <artifactId>geocloud2-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- actuator 监控中心 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>${spring-boot.version}</version>
        </dependency>

        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc6</artifactId>
            <version>11.2.0.4</version>
        </dependency>


        <dependency>
            <groupId>com.vaadin.external.google</groupId>
            <artifactId>guava</artifactId>
            <version>16.0.1.vaadin1</version>
        </dependency>

        <dependency>
            <groupId>org.gdal</groupId>
            <artifactId>gdal</artifactId>
            <version>3.3.0</version> <!-- 或者您选择的版本 -->
        </dependency>

        <!-- 添加jcifs依赖 SMB连接共享文件支持-->
        <dependency>
            <groupId>jcifs</groupId>
            <artifactId>jcifs</artifactId>
            <version>1.3.17</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.78</version> <!-- 请使用最新的稳定版本 -->
        </dependency>

        <dependency>
            <groupId>org.modelmapper</groupId>
            <artifactId>modelmapper</artifactId>
            <version>3.1.0</version> <!-- 请检查是否有新版本并使用最新版本 -->
        </dependency>

        <!-- 动态数据源 -->
        <!-- <dependency> -->
        <!--     <groupId>com.baomidou</groupId> -->
        <!--     <artifactId>dynamic-datasource-spring-boot3-starter</artifactId> -->
        <!-- </dependency> -->

        <!-- <dependency> -->
        <!--     <groupId>com.esri</groupId> -->
        <!--     <artifactId>arcobjects</artifactId> -->
        <!--     <version>10.2</version> -->
        <!-- </dependency> -->

        <!-- OkHttps网络请求库： http://okhttps.ejlchina.com/ -->
        <dependency>
            <groupId>com.ejlchina</groupId>
            <artifactId>okhttps</artifactId>
            <version>3.1.1</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <!-- 指定该Main Class为全局的唯一入口 -->
                    <mainClass>com.bjcj.GeoCloud2Application</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

    </build>

</project>
