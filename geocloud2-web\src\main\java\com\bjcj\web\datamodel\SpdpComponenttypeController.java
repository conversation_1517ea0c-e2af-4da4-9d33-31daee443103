package com.bjcj.web.datamodel;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.datamodel.SpdpComponenttype;
import com.bjcj.service.datamodel.SpdpComponenttypeService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
* 分析中的组件类型(spdp_componenttype)表控制层
* <AUTHOR>
*/
@RestController
@RequestMapping("/spdp_componenttype")
@Tag(name = "13.分析模型")
@Validated
public class SpdpComponenttypeController {
/**
* 服务对象
*/
    @Resource
    private SpdpComponenttypeService spdpComponenttypeService;

    @SaCheckPermission("sys:read")
    @GetMapping("/firstTypeList")
    @Operation(summary = "分析模型", description = "分析模型")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "modulename", description = "分类名称", required = false),
            @Parameter(name = "category", description = "category", required = false)
    })
    public JsonResult<List<SpdpComponenttype>> firstTypeList(@RequestParam("modulename") String modulename,
                                                             @RequestParam("category") String category){
        if(Objects.isNull(modulename)){
            List<SpdpComponenttype> list = this.spdpComponenttypeService.list();
            return JsonResult.success(list);
        }else{
            return JsonResult.success(this.spdpComponenttypeService.list(new LambdaQueryWrapper<SpdpComponenttype>().like(SpdpComponenttype::getModulename,modulename).eq(StringUtils.isNotBlank(category),SpdpComponenttype::getCategory,category)));
        }
    }
}
