package com.bjcj.model.dto.safetyManage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2023/12/4  9:19
*/
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_menu")
public class MenuDto implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 菜单名称
     */
    @TableField(value = "menu_name")
    @Schema(description="菜单名称")
    @Size(max = 100,message = "菜单名称最大长度要小于 100")
//    @NotBlank(message = "菜单名称不能为空")
    @RequestKeyParam(name = "menuName")
    private String menuName;

    /**
     * 父菜单ID
     */
    @TableField(value = "parent_id")
    @Schema(description="父菜单ID")
//    @NotNull(message = "父菜单ID不能为null")
    private Long parentId;

    /**
     * 显示顺序
     */
    @TableField(value = "show_sort")
    @Schema(description="显示顺序")
//    @NotNull(message = "显示顺序不能为null")
    private Integer showSort;

    /**
     * 组件地址
     */
    @TableField(value = "url")
    @Schema(description="组件地址")
    @Size(max = 255,message = "请求地址最大长度要小于 255")
    // @NotBlank(message = "组件地址不能为空")
    private String url;

    /**
     * 路由地址
     */
    @TableField(value = "req_path")
    @Schema(description="路由地址")
    @Size(max = 255,message = "路由地址最大长度要小于 255")
//    @NotBlank(message = "路由地址不能为空")
    private String reqPath;

    /**
     * 是否缓存
     */
    @TableField(value = "is_cache")
    @Schema(description="是否缓存")
    @Size(max = 2,message = "是否缓存最大长度要小于 2")
    private String isCache;

    /**
     * 打开方式（menuItem页签 menuBlank新窗口）
     */
    @TableField(value = "target")
    @Schema(description="打开方式（menuItem页签 menuBlank新窗口）")
    @Size(max = 255,message = "打开方式（menuItem页签 menuBlank新窗口）最大长度要小于 255")
    private String target;

    /**
     * 菜单类型（M目录 C菜单 F按钮）
     */
    @TableField(value = "menu_type")
    @Schema(description="菜单类型（M目录 C菜单 F按钮）")
    @Size(max = 2,message = "菜单类型（M目录 C菜单 F按钮）最大长度要小于 2")
    private String menuType;

    /**
     * 菜单状态（0显示 1隐藏）
     */
    @TableField(value = "visible")
    @Schema(description="菜单状态（0显示 1隐藏）")
    @Size(max = 2,message = "菜单状态（0显示 1隐藏）最大长度要小于 2")
    private String visible;

    /**
     * 是否刷新（0刷新 1不刷新）
     */
    @TableField(value = "is_refresh")
    @Schema(description="是否刷新（0刷新 1不刷新）")
    @Size(max = 2,message = "是否刷新（0刷新 1不刷新）最大长度要小于 2")
    private String isRefresh;

    /**
     * 权限标识
     */
    @TableField(value = "perms")
    @Schema(description="权限标识")
    @Size(max = 255,message = "权限标识最大长度要小于 255")
    private String perms;

    /**
     * 菜单图标
     */
    @TableField(value = "icon")
    @Schema(description="菜单图标")
    @Size(max = 255,message = "菜单图标最大长度要小于 255")
    private String icon;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description="创建时间")
    @Size(max = 30,message = "创建时间最大长度要小于 30")
    private String createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @Schema(description="更新时间")
    @Size(max = 30,message = "更新时间最大长度要小于 30")
    private String updateTime;

    /**
     * 最后操作人
     */
    @TableField(value = "operater")
    @Schema(description="最后操作人")
    @Size(max = 30,message = "最后操作人最大长度要小于 30")
    private String operater;

    @Schema(description="组件名称")
    @Size(max = 255,message = "组件名称最大长度要小于 255")
    // @NotBlank(message = "组件名称不能为空")
    private String componentName;

    @Schema(description="平台应用配置id")
    private Long platformAppConfId;

    @Schema(description="菜单权限:0游客1授权")
    private Long showPerm;

    @Schema(description="菜单元数据:JSON串")
    private String meta;

    private static final long serialVersionUID = 1L;

    public interface PageGroup {
    }

    public interface ListGroup {
    }

    public interface SaveGroup {
    }
}