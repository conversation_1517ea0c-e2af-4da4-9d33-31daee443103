package com.bjcj.model.po.mobile;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_mobile_subconfig")
@Schema(description = "子模块信息表")
public class PdaSubconfig implements Serializable {

    @TableId(type = IdType.INPUT)
    private String subid;

    private String applykind;

    private String imgpath;

    private String isdisplay;

    private String menuname;

    private String modeltype;

    private String numfileds;

    private String parentid;

    private String pid;

    private String remark;

    private Integer sortid;

    private Boolean template;

    private String value;

    @TableField(exist = false)
    private String key;

    private String mobilephone;

    private String isapproval;

    private String width;

    private String height;

    private String columnsize;

    private String tenantid;

    private int visablenum;

    private String ifremind;

    private String builtmodule;

    private Integer pubcomponent;

    private Integer numdepend;

//    private Integer watermark;
//
//    public Integer getWaterMark() {
//        return watermark;
//    }
//
//    public void setWaterMark(Integer waterMark) {
//        this.watermark = waterMark;
//    }

    @TableField(exist = false)
    private Integer oldpubcomponent;

    @TableField(exist = false)
    private List<PdaSubconfig> children;


}
