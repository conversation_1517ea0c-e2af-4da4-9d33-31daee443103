package com.bjcj.model.vo.safetyManage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.beans.Transient;
import java.util.ArrayList;
import java.util.List;

@Schema(description="机构")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InstitutionVo {

    @Schema(description="机构id")
    private String id;

    @Schema(description="机构名称")
    private String name;

    @Schema(description = "父机构id")
    private String parentId;

    @Schema(description="子机构")
    private List<InstitutionVo> children = new ArrayList<>();
    public void addChild(InstitutionVo child) {
        this.children.add(child);
    }
}
