package com.bjcj.service.safetyManage;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.safetyManage.SysRoleBusinessMapper;
import com.bjcj.model.dto.safetyManage.SysRoleBusinessDto;
import com.bjcj.model.po.safetyManage.SysRoleBusiness;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 *@Author：qinyi
 *@Date：2023/12/14  9:52
*/
@Service
public class SysRoleBusinessService extends ServiceImpl<SysRoleBusinessMapper, SysRoleBusiness> {

    @Resource
    SysRoleBusinessMapper sysRoleBusinessMapper;
    public JsonResult saveRoleBusiness(SysRoleBusinessDto dto) {
        Long roleId = dto.getRoleId();
        if(!dto.getAddIdList().isEmpty()){
            List<SysRoleBusiness> list = new ArrayList<SysRoleBusiness>();
            dto.getAddIdList().forEach(businessId -> {
                SysRoleBusiness sysRoleBusiness =  new SysRoleBusiness(){{setBusinessId(businessId);setRoleId(roleId);}};
                list.add(sysRoleBusiness);
            });
            sysRoleBusinessMapper.insertBatch(list);
        }
        if(!dto.getDelIdList().isEmpty()){
            sysRoleBusinessMapper.deleteBatch(dto.getDelIdList(),roleId);
        }
        return JsonResult.success();

    }
}
