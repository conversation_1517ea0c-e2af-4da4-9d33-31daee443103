package com.bjcj.model.po.datamodel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 * 图层类型表
 *@Date：2024/1/3  15:29
*/
@Schema(description="图层类型表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "data_layer_type")
public class DataLayerType implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    @NotNull(message = "is not null")
    private Long id;

    /**
     * 类型名称
     */
    @TableField(value = "type_name")
    @Schema(description="类型名称")
    @Size(max = 255,message = "类型名称max length should less than 255")
    private String typeName;

    private static final long serialVersionUID = 1L;
}