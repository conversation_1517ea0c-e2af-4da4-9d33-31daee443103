package com.bjcj.service.platformManage.fieldPlan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.platformManage.fieldPlan.FieldPlanLayerFieldMapper;
import com.bjcj.model.dto.fieldPlan.FieldPlanLayerFieldDto;
import com.bjcj.model.po.platformManage.fieldPlan.FieldPlanLayerField;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 *@Author：qinyi
 *@Date：2024/2/19  17:22
*/
@Service
public class FieldPlanLayerFieldService extends ServiceImpl<FieldPlanLayerFieldMapper, FieldPlanLayerField> {

    @Resource
    private FieldPlanLayerFieldMapper fieldPlanLayerFieldMapper;


    public JsonResult editFieldPlanLayerField(FieldPlanLayerFieldDto dto) {
        this.fieldPlanLayerFieldMapper.editData(dto);
        return JsonResult.success();
    }

    public JsonResult addField(FieldPlanLayerField field) {
        //名称不能重复
        if(!this.fieldPlanLayerFieldMapper.selectList(
                new LambdaQueryWrapper<FieldPlanLayerField>()
                        .eq(FieldPlanLayerField::getFieldName, field.getFieldName()))
                .isEmpty()) {
            return JsonResult.build(400, "字段名称不能重复");
        }
        return this.fieldPlanLayerFieldMapper.insert(field) > 0 ? JsonResult.success() : JsonResult.error();
    }
}
