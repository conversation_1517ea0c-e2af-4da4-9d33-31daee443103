<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.eswPlatform.ProjectsMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.eswPlatform.Projects">
    <!--@mbg.generated-->
    <!--@Table public.projects-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="status" jdbcType="NUMERIC" property="status" />
    <result column="description" jdbcType="VARCHAR" property="description" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", content, "status", description
  </sql>
</mapper>