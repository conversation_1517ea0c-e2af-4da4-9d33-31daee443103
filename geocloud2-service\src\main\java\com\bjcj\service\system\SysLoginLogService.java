package com.bjcj.service.system;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.system.SysLoginLogMapper;
import com.bjcj.model.po.system.SysLoginLog;
import com.bjcj.model.qo.SysLoginLogQo;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/12/4 16:05 周一
 */
@Service
public class SysLoginLogService extends ServiceImpl<SysLoginLogMapper, SysLoginLog> {


    /**
     * <h2>获取登录日志列表</h2>
     * @param sysLoginLogQo:
     * @return com.bjcj.common.core.domain.JsonResult<Page<com.bjcj.model.po.system.SysLoginLog>>
     * <AUTHOR>
     * @date 2023/12/20 14:54
     */
    public JsonResult<Page<SysLoginLog>> loginLogList(SysLoginLogQo sysLoginLogQo) {
        Page<SysLoginLog> page = page(Page.of(sysLoginLogQo.getCurrent(), sysLoginLogQo.getSize()),
                Wrappers.<SysLoginLog>query().lambda()
                        .like(StrUtil.isNotBlank(sysLoginLogQo.getUsername()), SysLoginLog::getUsername, sysLoginLogQo.getUsername())
                        .like(StrUtil.isNotBlank(sysLoginLogQo.getIp()), SysLoginLog::getId, sysLoginLogQo.getUsername())
                        .like(StrUtil.isNotBlank(sysLoginLogQo.getDevice()), SysLoginLog::getDevice, sysLoginLogQo.getDevice())
                        .ge(ObjUtil.isNotEmpty(sysLoginLogQo.getStartTime()), SysLoginLog::getCreateTime, sysLoginLogQo.getStartTime())
                        .le(ObjUtil.isNotEmpty(sysLoginLogQo.getEndTime()), SysLoginLog::getCreateTime, sysLoginLogQo.getEndTime())
                        .orderByDesc(SysLoginLog::getCreateTime)
        );
        return JsonResult.success(page);
    }
}
