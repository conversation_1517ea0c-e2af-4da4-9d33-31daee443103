package com.bjcj.service.naturalresources.products;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.products.ProductsMapper;
import com.bjcj.model.po.naturalresources.products.Products;
import com.bjcj.model.vo.naturalresources.products.ProductsVo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Cleanup;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/5 9:22 周二
 */
@Service
public class ProductsService extends ServiceImpl<ProductsMapper, Products> {

    @Resource
    private ProductsMapper productsMapper;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private HttpServletResponse response;

    public JsonResult treeList(){
        LambdaQueryWrapper<Products> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Products::getParentId, 0)
                .eq(Products::getDel, 0)
                .eq(Products::getShow, true)
                .orderByAsc(Products::getSort);
        List<Products> products = productsMapper.selectList(wrapper);
        List<ProductsVo> list = products.stream().map(item -> {
            ProductsVo productsVo = new ProductsVo();
            BeanUtils.copyProperties(item, productsVo);
            return productsVo;
        }).collect(Collectors.toList());

        list.forEach(s -> {
            this.getChildren(s,1);
        });

        return JsonResult.success(list);
    }

    private void getChildren(ProductsVo item, int index){
        LambdaQueryWrapper<Products> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Products::getParentId, item.getId());
        if(index == 1){
            wrapper.eq(Products::getShow, true);
        }
        wrapper.eq(Products::getDel, 0);
        wrapper.orderByAsc(Products::getSort);
        //根据parentId查询
        List<Products> list = productsMapper.selectList(wrapper);

        List<ProductsVo> voList = list.stream().map(p -> {
            ProductsVo vo = new ProductsVo();
            BeanUtils.copyProperties(p, vo);
            return vo;
        }).collect(Collectors.toList());
        //写入到children
        item.setChildren(voList);

        //如果children不为空，继续往下找
        if (!CollectionUtils.isEmpty(voList)) {
            voList.forEach(s->{
                this.getChildren(s, index);
            });
        }
    }

    public JsonResult lists(){

        LambdaQueryWrapper<Products> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Products::getParentId, 0)
                .eq(Products::getDel, 0)
                .orderByAsc(Products::getSort);
        List<Products> products = productsMapper.selectList(wrapper);
        List<ProductsVo> list = products.stream().map(item -> {
            ProductsVo productsVo = new ProductsVo();
            BeanUtils.copyProperties(item, productsVo);
            return productsVo;
        }).collect(Collectors.toList());

        list.forEach(s -> {
            this.getChildren(s,2);
        });

        return JsonResult.success(list);
    }

    public JsonResult del(Long id){
        LambdaUpdateWrapper<Products> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(Products::getDel, 1)
                .eq(Products::getId, id);
        int result = productsMapper.update(wrapper);
        if(result < 1){
            return JsonResult.error();
        }
        return JsonResult.success();
    }

    public JsonResult ishow(Long id, Boolean show){
        LambdaUpdateWrapper<Products> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(Products::getShow, show)
                .eq(Products::getId, id);
        int result = productsMapper.update(wrapper);
        if(result < 1){
            return JsonResult.error();
        }

        return JsonResult.success();
    }

    public JsonResult exportJson(){

        LambdaQueryWrapper<Products> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Products::getDel, 0);
        List<Products> entities = productsMapper.selectList(wrapper);
        try{
            // 获取响应输出流
            OutputStream outputStream = response.getOutputStream();

            // 设置响应头
            response.setContentType("application/json");
            response.setHeader("Content-Disposition", "attachment; filename=\"dataServiceCategoriesData.json\"");
            // 将查询到的结果序列化为json格式
            String jsonString = JSON.toJSONString(entities);
            // 将序列化的json数据集写入到输出流中
            outputStream.write(jsonString.getBytes(StandardCharsets.UTF_8));
            // 推送输出流结果到浏览器
            outputStream.flush();
        }catch (Exception e){
            log.error("导出文件失败"+e);
        }

        return JsonResult.success();
    }

    public JsonResult importJson(MultipartFile file){
        List<Products> list = new ArrayList<>();
        try {
            @Cleanup InputStream inputStream = file.getInputStream();
            list = objectMapper.readValue(inputStream, new TypeReference<List<Products>>() {});
            this.saveBatch(list);
        }catch (Exception e) {
            log.error("导入文件失败"+e);
            JsonResult.error();
        }

        return JsonResult.success();
    }

}
