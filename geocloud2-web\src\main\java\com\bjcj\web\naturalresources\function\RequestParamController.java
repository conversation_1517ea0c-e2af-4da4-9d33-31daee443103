package com.bjcj.web.naturalresources.function;

import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/12/4 15:11 周一
 */
@Tag(name = "功能服务信息参数说明(无用)",description = "功能服务信息参数说明(无用)")
@ApiSupport(order = 57)
@RestController
@Slf4j
@RequestMapping(value = "/request_param")
public class RequestParamController {

    /*@Resource
    private RequestParamsService requestParamsService;

    @Operation(summary = "功能服务信息参数说明注册")
    @PostMapping(value = "/save")
    public JsonResult save(@RequestBody @Validated RequestParams requestParams){
        requestParamsService.save(requestParams);
        Long id = requestParams.getId();
        return JsonResult.success(id);
    }

    @Operation(summary = "功能服务信息参数说明修改")
    @PostMapping(value = "/update")
    public JsonResult update(@RequestBody @Validated RequestParams requestParams){

        return JsonResult.success(requestParamsService.saveOrUpdate(requestParams));
    }

    @Operation(summary = "功能服务信息参数说明删除")
    @GetMapping(value = "/del/{id}")
    public JsonResult del(@PathVariable Long id){

        return requestParamsService.del(id);
    }

    @Operation(summary = "功能服务信息参数说明id查询")
    @GetMapping(value = "/findById/{id}")
    public JsonResult<RequestParams> findById(@PathVariable Long id){

        return requestParamsService.findById(id);
    }

    @Operation(summary = "功能服务信息参数说明parentid查询")
    @GetMapping(value = "/findByParentId/{parentid}")
    public JsonResult<List<RequestParams>> findByParentId(@PathVariable Long parentid){

        return requestParamsService.findByParentId(parentid);
    }*/

}
