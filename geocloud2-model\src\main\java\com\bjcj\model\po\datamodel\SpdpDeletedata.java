package com.bjcj.model.po.datamodel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/7/1  10:07
*/
/**
    * 删除数据
    */
@Schema(description="删除数据")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "spdp_deletedata")
public class SpdpDeletedata implements Serializable {
    @TableId(value = "deleteid", type = IdType.ASSIGN_UUID)
    @Size(max = 36,message = "max length should less than 36")
    private String deleteid;

    @TableField(value = "name")
    @Schema(description="")
    @Size(max = 60,message = "max length should less than 60")
    @NotBlank(message = "name is not blank")
    private String name;

    @TableField(value = "displayname")
    @Schema(description="")
    @Size(max = 100,message = "max length should less than 100")
    @NotBlank(message = "displayname is not blank")
    private String displayname;

    @TableField(value = "deletemodelid")
    @Schema(description="")
    @Size(max = 100,message = "max length should less than 100")
    private String deletemodelid;

    @TableField(value = "params")
    @Schema(description="")
    @Size(max = 4000,message = "max length should less than 4000")
    private String params;

    private static final long serialVersionUID = 1L;
}