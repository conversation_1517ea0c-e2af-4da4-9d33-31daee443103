package com.bjcj.model.po.datamodel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/4/19  14:53
*/
/**
    * 表结构
    */
@Schema(description="图层表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "metadata_tablestructures")
public class MetadataTablestructures implements Serializable {
    /**
     * 标识
     */
    @TableId(value = "tablestructureid", type = IdType.ASSIGN_UUID)
    @Schema(description="主键")
    private String tablestructureid;

    /**
     * 表结构的名称
     */
    @TableField(value = "name")
    @Schema(description="表结构的名称")
    @Size(max = 60,message = "表结构的名称max length should less than 60")
    @NotBlank(message = "表结构的名称is not blank")
    private String name;

    /**
     * 说明
     */
    @TableField(value = "description")
    @Schema(description="说明")
    @Size(max = 150,message = "说明max length should less than 150")
    private String description;

    /**
     * 要素类的Shape的类型，如：1表示点，2表示多点，3表示线，4表示面
     */
    @TableField(value = "shapetype")
    @Schema(description="要素类的Shape的类型，如：1表示点，2表示多点，3表示线，4表示面")
    private Short shapetype;

    /**
     * 表的类型，例如：0，表示注记类；1，表示要素类，2，表示镶嵌数据集，3，表示注记要素类，4，表示属性表，5，表示栅格数据集，6，表示栅格目录
     */
    @TableField(value = "tabletype")
    @Schema(description="表的类型，例如：0，表示注记类；1，表示要素类，2，表示镶嵌数据集，3，表示注记要素类，4，表示属性表，5，表示栅格数据集，6，表示栅格目录")
    private Integer tabletype;

    /**
     * 显示名称
     */
    @TableField(value = "displayname")
    @Schema(description="显示名称")
    @Size(max = 60,message = "显示名称max length should less than 60")
    private String displayname;

    /**
     * 显示字段ID
     */
    @TableField(value = "displayfieldid")
    @Schema(description="显示字段ID")
    @Size(max = 36,message = "显示字段IDmax length should less than 36")
    private String displayfieldid;

    /**
     * 数据标准的ID
     */
    @TableField(value = "datastandardid")
    @Schema(description="数据标准的ID")
    @Size(max = 36,message = "数据标准的IDmax length should less than 36")
    private String datastandardid;

    /**
     * 编码
     */
    @TableField(value = "code")
    @Schema(description="编码")
    @Size(max = 20,message = "编码max length should less than 20")
    private String code;

    /**
     * 是否必须
     */
    @TableField(value = "required")
    @Schema(description="是否必须")
    private Boolean required;

    /**
     * 显示顺序
     */
    @TableField(value = "displayorder")
    @Schema(description="显示顺序")
    @NotNull(message = "显示顺序is not null")
    private Integer displayorder;

    private static final long serialVersionUID = 1L;
}