package com.bjcj.model.po.ore;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bjcj.common.utils.support.Create;
import com.bjcj.common.utils.support.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Null;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-12-04 09:22 周三
 */
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "ore_watch_info")
public class OreWatchInfo {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    @NotBlank(message = "id不能为空", groups = {Update.class})
    @Null(message = "id必须为null,或者不传", groups = {Create.class})
    private String id;

    /**
     * 部级/省级
     */
    @TableField(value = "\"level\"")
    @Schema(description="部级/省级")
    private String level;

    /**
     * 矿山编号
     */
    @TableField(value = "ore_number")
    @Schema(description="矿山编号")
    private String oreNumber;

    /**
     * 矿区名称
     */
    @TableField(value = "ore_name")
    @Schema(description="矿区名称")
    private String oreName;

    /**
     * 下辖矿山名称
     */
    @TableField(value = "sub_ore_name")
    @Schema(description="下辖矿山名称")
    private String subOreName;

    /**
     * 矿种
     */
    @TableField(value = "ore_species")
    @Schema(description="矿种")
    private String oreSpecies;

    /**
     * 状态
     */
    @TableField(value = "\"status\"")
    @Schema(description="状态")
    private String status;

    /**
     * 开采方式
     */
    @TableField(value = "mining_way")
    @Schema(description="开采方式")
    private String miningWay;

    /**
     * 采矿证号
     */
    @TableField(value = "ore_certificate_number")
    @Schema(description="采矿证号")
    private String oreCertificateNumber;

    /**
     * 矿业用地手续办理情况
     */
    @TableField(value = "procedure_status")
    @Schema(description="矿业用地手续办理情况")
    private String procedureStatus;

    /**
     * 《开发治理方案》是否编制
     */
    @TableField(value = "scheme")
    @Schema(description="《开发治理方案》是否编制")
    private Boolean scheme;

    /**
     * 年度治理方案是否编制
     */
    @TableField(value = "year_scheme")
    @Schema(description="年度治理方案是否编制")
    private Boolean yearScheme;

    /**
     * 2024 年生态修复任务是否按照年度方案执行
     */
    @TableField(value = "repair_2024")
    @Schema(description="2024年生态修复任务是否按照年度方案执行")
    private Boolean repair2024;

    /**
     * 2024 年生态义务是否按照年度方案履行完成
     */
    @TableField(value = "duty_2024")
    @Schema(description="2024年生态义务是否按照年度方案履行完成")
    private Boolean duty2024;

    /**
     * 基金账户是否开设
     */
    @TableField(value = "fund_account")
    @Schema(description="基金账户是否开设")
    private Boolean fundAccount;

    /**
     * 基金是否足额提取
     */
    @TableField(value = "fund_extract")
    @Schema(description="基金是否足额提取")
    private Boolean fundExtract;

    /**
     * 土地复垦费用是否足额预存
     */
    @TableField(value = "cost_take")
    @Schema(description="土地复垦费用是否足额预存")
    private Boolean costTake;

    /**
     * 矿产资源开发利用情况、非煤矿山初步设计等的执行情况
     */
    @TableField(value = "execute_situation")
    @Schema(description="矿产资源开发利用情况、非煤矿山初步设计等的执行情况")
    private String executeSituation;

    /**
     * 矿山开发利用涉嫌违法违规情况
     */
    @TableField(value = "disobey_situation")
    @Schema(description="矿山开发利用涉嫌违法违规情况")
    private String disobeySituation;

    /**
     * 采矿权出让收益、采矿权使用费
     */
    @TableField(value = "use_cost")
    @Schema(description="采矿权出让收益、采矿权使用费")
    private String useCost;

    /**
     * 矿山储量年报“三率”指标
     */
    @TableField(value = "three_rate_index")
    @Schema(description="矿山储量年报“三率”指标")
    private String threeRateIndex;

    /**
     * 矿山占林占草情况
     */
    @TableField(value = "chamlin_situation")
    @Schema(description="矿山占林占草情况")
    private String chamlinSituation;

}