<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.naturalresources.categories.ResourceServicesMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.naturalresources.categories.ResourceServices">
    <!--@mbg.generated-->
    <!--@Table public.resource_services-->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="displayname" jdbcType="VARCHAR" property="displayname" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="authoritytype" jdbcType="NUMERIC" property="authoritytype" />
    <result column="status" jdbcType="NUMERIC" property="status" />
    <result column="transferurl" jdbcType="VARCHAR" property="transferurl" />
    <result column="registerman" jdbcType="VARCHAR" property="registerman" />
    <result column="registerdate" jdbcType="TIMESTAMP" property="registerdate" />
    <result column="repairman" jdbcType="VARCHAR" property="repairman" />
    <result column="repairdate" jdbcType="TIMESTAMP" property="repairdate" />
    <result column="version" jdbcType="NUMERIC" property="version" />
    <result column="resourcecatalogid" jdbcType="CHAR" property="resourcecatalogid" />
    <result column="serviceclusterid" jdbcType="CHAR" property="serviceclusterid" />
    <result column="thumbnailurl" jdbcType="VARCHAR" property="thumbnailurl" />
    <result column="registertype" jdbcType="NUMERIC" property="registertype" />
    <result column="cachetype" jdbcType="NUMERIC" property="cachetype" />
    <result column="resourcetype" jdbcType="VARCHAR" property="resourcetype" />
    <result column="requesttype" jdbcType="NUMERIC" property="requesttype" />
    <result column="evaluationlevel" jdbcType="NUMERIC" property="evaluationlevel" />
    <result column="accesscount" jdbcType="NUMERIC" property="accesscount" />
    <result column="resourcecategory" jdbcType="NUMERIC" property="resourcecategory" />
    <result column="organizationid" jdbcType="VARCHAR" property="organizationid" />
    <result column="dataitemid" jdbcType="CHAR" property="dataitemid" />
    <result column="isdefaultbrowser" jdbcType="NUMERIC" property="isdefaultbrowser" />
    <result column="params" jdbcType="VARCHAR" property="params" />
    <result column="titlemaxscale" jdbcType="NUMERIC" property="titlemaxscale" />
    <result column="isfilter" jdbcType="BOOLEAN" property="isfilter" />
    <result column="displayorder" jdbcType="NUMERIC" property="displayorder" />
    <result column="metadata" jdbcType="OTHER" property="metadata" />
    <result column="isvisiable" jdbcType="BOOLEAN" property="isvisiable" />
    <result column="servicegroupid" jdbcType="CHAR" property="servicegroupid" />
    <result column="districtcode" jdbcType="VARCHAR" property="districtcode" />
    <result column="districtname" jdbcType="VARCHAR" property="districtname" />
    <result column="servicetag" jdbcType="VARCHAR" property="servicetag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", displayname, description, url, authoritytype, "status", transferurl, 
    registerman, registerdate, repairman, repairdate, version, resourcecatalogid, serviceclusterid, 
    thumbnailurl, registertype, cachetype, resourcetype, requesttype, evaluationlevel, 
    accesscount, resourcecategory, organizationid, dataitemid, isdefaultbrowser, params, 
    titlemaxscale, isfilter, displayorder, metadata, isvisiable, servicegroupid, districtcode, 
    districtname, servicetag
  </sql>

  <select id="lists" resultType="com.bjcj.model.vo.naturalresources.categories.ResourceServicesVo">
    select c.id,c.name,c.displayname,c.resourcetype,s.cate_id,s.role_id from resource_services c
    left join sys_role_cate s on s.cate_id = c.id and s.role_id = #{roleId}
    where c.status=1
    <if test="name!=null and name!='' ">
      and c.name like CONCAT('%', #{name}, '%') or c.displayname like CONCAT('%', #{name}, '%')
    </if>
    <if test="ids!=null and ids.size > 0">
      and c.dataitemid in
      <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="ids.size == 0 and id!=''">
      and c.dataitemid is null
    </if>
    <if test="auth==1">
      and s.cate_id is not null
    </if>
    order by c.displayorder
  </select>

  <select id="selectListAndGroupName" resultType="com.bjcj.model.po.naturalresources.categories.ResourceServices">
    select c.*,g.group_name from resource_services c left join categories_service_group g on c.servicegroupid::bigint = g.id
    where c.dataitemid=#{itemid} and c.isvisiable=true
    </select>

  <select id="selectServicePage2" resultType="com.bjcj.model.po.naturalresources.categories.ResourceServices2">
    select cs.*,cdi.scale,cdi.publish_institution_name from resource_services cs left join resource_dataitems cdi on cs.dataitemid=cdi.id
    where 1=1
    <if test="datainfoids!=null and datainfoids.size>0">
      and cs.dataitemid in
      <foreach item="item" index="index" collection="datainfoids" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="startYear!=0 and endYear!=0">
      and ( cdi.year BETWEEN #{startYear} and #{endYear})
    </if>
    <if test="scale!=null and scale!=''">
      and cdi.scale = #{scale}
    </if>
    <if test="scale!=null and scale!=''">
      and cdi.scale = #{scale}
    </if>
    <if test="publishInstitutionName!=null and publishInstitutionName!=''">
      and cdi.publish_institution_name=#{publishInstitutionName}
    </if>
    and cdi.status=1
    <if test="ordersql!=null and ordersql!=''">
      ${ordersql}
    </if>
    LIMIT #{size} OFFSET #{current}
  </select>

  <select id="selectServicePage22" resultType="com.bjcj.model.po.naturalresources.categories.ResourceServices2">
    select * from resource_services cs
    where resourcecatalogid in
    <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
      #{item}
    </foreach>
    <if test="name!=null and name!=''">
      and cs.name like CONCAT('%', #{name}, '%') or cs.displayname like CONCAT('%', #{name}, '%')
    </if>
    LIMIT #{size} OFFSET #{current}
  </select>

  <select id="selectServicePage2Count" resultType="int">
    select count(*) from (
    select * from resource_services cs left join resource_dataitems cdi on cs.dataitemid=cdi.id
    where 1=1
    <if test="datainfoids!=null and datainfoids.size>0">
      and cs.dataitemid in
      <foreach item="item" index="index" collection="datainfoids" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="startYear!=null and startYear!='' and endYear!=null and endYear!=''">
      and ( cdi.year BETWEEN #{startYear} and #{endYear})
    </if>
    <if test="scale!=null and scale!=''">
      and cdi.scale = #{scale}
    </if>
    <if test="scale!=null and scale!=''">
      and cdi.scale = #{scale}
    </if>
    <if test="publishInstitutionName!=null and publishInstitutionName!=''">
      and cdi.publish_institution_name=#{publishInstitutionName}
    </if>
    and cdi.status=1
    ) c
  </select>

  <select id="selectServicePage22Count" resultType="int">
    select count(*) from (
      select * from resource_services cs
      where resourcecatalogid in
      <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
        #{item}
      </foreach>
      <if test="name!=null and name!=''">
        and cs.name like CONCAT('%', #{name}, '%') or cs.displayname like CONCAT('%', #{name}, '%')
      </if>
    ) c
  </select>

  <select id="selectServiceCountByIdIn" resultType="int">
    select count(*) from (
    select * from resource_services
    where 1=1
    <if test="datainfoids!=null and datainfoids.size>0">
      and dataitemid in
      <foreach item="item" index="index" collection="datainfoids" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    ) c
  </select>

  <select id="selectServiceCountByIdIn2" resultType="int">
    select count(*) from (
    select * from resource_services
    where 1=1
    <if test="resourcecatalogids!=null and resourcecatalogids.size>0">
      and resourcecatalogid in
      <foreach item="item" index="index" collection="resourcecatalogids" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    ) c
  </select>

  <select id="selectServicePage3" resultType="com.bjcj.model.po.naturalresources.categories.ResourceServices2">
    select cs.*,cdi.scale,cdi.publish_institution_name from resource_services cs left join resource_dataitems cdi on cs.dataitemid=cdi.id
    left join resource_tags rt on cs.id=rt.resourceid
    where  rt.name like CONCAT('%', #{label}, '%')
    <if test="startYear!=null and startYear!='' and endYear!=null and endYear!=''">
      and ( cdi.year BETWEEN #{startYear} and #{endYear})
    </if>
    <if test="scale!=null and scale!=''">
      and cdi.scale = #{scale}
    </if>
    <if test="scale!=null and scale!=''">
      and cdi.scale = #{scale}
    </if>
    <if test="publishInstitutionName!=null and publishInstitutionName!=''">
      and cdi.publish_institution_name=#{publishInstitutionName}
    </if>
    and cdi.status=1
    <if test="ordersql!=null and ordersql!=''">
      ${ordersql}
    </if>
    LIMIT #{size} OFFSET #{current}
  </select>

  <select id="selectServicePage3Count" resultType="int">
    select count(*) from (
    select cs.*,cdi.scale,cdi.publish_institution_name from resource_services cs left join resource_dataitems cdi on cs.dataitemid=cdi.id
    left join resource_tags rt on cs.id=rt.resourceid
    where rt.name like CONCAT('%', #{label}, '%')
    <if test="startYear!=null and startYear!='' and endYear!=null and endYear!=''">
      and ( cdi.year BETWEEN #{startYear} and #{endYear})
    </if>
    <if test="scale!=null and scale!=''">
      and cdi.scale = #{scale}
    </if>
    <if test="scale!=null and scale!=''">
      and cdi.scale = #{scale}
    </if>
    <if test="publishInstitutionName!=null and publishInstitutionName!=''">
      and cdi.publish_institution_name=#{publishInstitutionName}
    </if>
    and cdi.status=1
    ) c
  </select>

  <select id="selectServicePage" resultMap="BaseResultMap">
    select cs.* from resource_services cs
    left join resource_dataitems cdi on cs.dataitemid=cdi.id
    where 1=1
    <if test="searchStr!=null and searchStr!=''">
      and (cs.name like CONCAT('%', #{searchStr}, '%') or cs.displayname like CONCAT('%', #{searchStr}, '%') )
    </if>
    <if test="catalogsId!=null and catalogsId!=''">
      and cdi.resourcecatalogid=#{catalogsId}
    </if>
    and cs.id in
    <foreach item="item" index="index" collection="cateIds" open="(" separator="," close=")">
      #{item}
    </foreach>
    and cs.status=1
    order by cs.registerdate desc
    LIMIT #{size} OFFSET #{current}
  </select>

  <select id="selectServicePageCount" resultType="java.lang.Long">
    select count(*) from (
    select cs.* from resource_services cs
    left join resource_dataitems cdi on cs.dataitemid=cdi.id
    where 1=1
    <if test="searchStr!=null and searchStr!=''">
      and (cs.name like CONCAT('%', #{searchStr}, '%') or cs.displayname like CONCAT('%', #{searchStr}, '%') )
    </if>
    <if test="catalogsId!=null and catalogsId!=''">
      and cdi.resourcecatalogid=#{catalogsId}
    </if>
    and cs.id in
    <foreach item="item" index="index" collection="cateIds" open="(" separator="," close=")">
      #{item}
    </foreach>
    and cs.status=1
    ) c
  </select>

  <select id="selectPageList" resultMap="BaseResultMap">
    select c.* from resource_services c
    where 1=1
    <if test="searchStr!= null and searchStr!= ''">
      and ( c.name like concat('%', #{searchStr}, '%') or c.displayname like concat('%', #{searchStr}, '%') )
    </if>
    <if test="resourcecatalogid!= null and resourcecatalogid!=''">
      and c.resourcecatalogid = #{resourcecatalogid}
    </if>
    and c.status=1 and c.resourcecategory=1
    order by c.registerdate desc
    LIMIT #{size} OFFSET #{current}
  </select>

  <select id="selectListCount" resultType="java.lang.Long">
    select count(*) from (
    select c.* from resource_services c
    where 1=1
    <if test="searchStr!= null and searchStr!= ''">
      and ( c.name like concat('%', #{searchStr}, '%') or c.displayname like concat('%', #{searchStr}, '%') )
    </if>
    <if test="resourcecatalogid!= null and resourcecatalogid!=''">
      and c.resourcecatalogid = #{resourcecatalogid}
    </if>
    and c.status=1 and c.resourcecategory=1
    ) c
  </select>

  <select id="serviceListByName" resultType="com.bjcj.model.po.naturalresources.categories.ResourceServices2">
    select * from resource_services where name like CONCAT('%', #{name}, '%') or displayname like CONCAT('%', #{name}, '%')
    </select>

  <select id="selectList2" resultType="com.bjcj.model.po.naturalresources.categories.ResourceServices2">
    select * from resource_services where id in
    <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
      #{item}
    </foreach>
    </select>

  <select id="selectOnes" resultType="com.bjcj.model.po.naturalresources.categories.ResourceServices2">
    select * from resource_services where id=#{id}
  </select>
</mapper>