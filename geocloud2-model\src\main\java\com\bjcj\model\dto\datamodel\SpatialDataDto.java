package com.bjcj.model.dto.datamodel;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author：qinyi
 * @Date：2024/7/2 9:58
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SpatialDataDto {
    private String rulecatalogid;
    private String name;
    private String displayname;
    private Object params;
    private String resourceItemId;
    private String overLayanalysisModelId;
    private String queryModelId;
    private String deleteModelId;
    private String dataTransFormModelId;
    private String saveModelId;
    private List<RuleDto> ruleDtos;
}
