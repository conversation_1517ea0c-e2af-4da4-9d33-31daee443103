<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.mobile.PdaTheclasstypeMapper">

    <resultMap id="BaseResultMap" type="com.bjcj.model.po.mobile.PdaTheclasstype">
            <id property="objectid" column="OBJECTID" jdbcType="INTEGER"/>
            <result property="classtypename" column="CLASSTYPENAME" jdbcType="VARCHAR"/>
            <result property="typefilename" column="TYPEFILENAME" jdbcType="VARCHAR"/>
            <result property="status" column="STATUS" jdbcType="VARCHAR"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="enable" column="ENABLE_" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        OBJECTID,CLASSTYPENAME,TYPEFILENAME,
        STATUS,REMARK,CREATE_TIME,
        ENABLE_,UPDATE_TIME
    </sql>
</mapper>
