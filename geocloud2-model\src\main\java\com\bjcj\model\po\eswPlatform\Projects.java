package com.bjcj.model.po.eswPlatform;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024-8-16  11:38
*/
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "projects")
public class Projects implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Size(max = 40,message = "最大长度要小于 40")
    private String id;

    /**
     * 名称
     */
    @TableField(value = "name")
    @Schema(description="名称")
    @Size(max = 255,message = "名称最大长度要小于 255")
    private String name;

    /**
     * 位置信息
     */
    @TableField(value = "content")
    @Schema(description="位置信息")
    private String content;

    /**
     * 状态
     */
    @TableField(value = "status")
    @Schema(description="状态")
    private Short status;

    /**
     * 描述
     */
    @TableField(value = "description")
    @Schema(description="描述")
    @Size(max = 255,message = "描述最大长度要小于 255")
    private String description;

    @TableField(value = "special_plan_id")
    @Schema(description="")
    @NotNull(message = "不能为null")
    private Long specialPlanId;

    @TableField(exist = false)
    @Schema(description="Schema子集")
    private List<Schemes> childrens;


    private static final long serialVersionUID = 1L;
}