<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.safetyManage.RoleMenuMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.safetyManage.RoleMenu">
    <!--@mbg.generated-->
    <!--@Table public.sys_role_menu-->
    <id column="role_id" jdbcType="BIGINT" property="roleId" />
    <id column="menu_id" jdbcType="BIGINT" property="menuId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    role_id, menu_id
  </sql>

  <select id="selectCountRoleMenuByMenuId" resultType="Integer">
    select count(1) from sys_role_menu where menu_id=#{menuId}
  </select>

  <insert id="insertBatch">
    insert into sys_role_menu (role_id, menu_id) values
    <foreach item="item" index="index" collection="list" separator=",">
      (#{item.roleId},#{item.menuId})
    </foreach>
  </insert>

  <delete id="deleteByRoleAndSpecialPlanId">
    delete from sys_role_menu where role_id=#{roleId} and special_plan_id=#{specialPlanId}
  </delete>
</mapper>