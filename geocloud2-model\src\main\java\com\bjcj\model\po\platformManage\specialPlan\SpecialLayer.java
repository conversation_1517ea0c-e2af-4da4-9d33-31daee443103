package com.bjcj.model.po.platformManage.specialPlan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/1/26  10:18
*/
/**
    * 专题图层关联表
    */
@Schema(description="专题图层关联表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "special_layer")
public class SpecialLayer implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 图层id
     */
    @TableField(value = "data_layer_id")
    @Schema(description="图层id")
    @NotNull(message = "图层idis not null")
    private Long dataLayerId;

    /**
     * 图层名称
     */
    @TableField(value = "layer_name")
    @Schema(description="图层名称")
    @Size(max = 100,message = "图层名称max length should less than 100")
    private String layerName;

    /**
     * 图层显示名称
     */
    @TableField(value = "show_name")
    @Schema(description="图层显示名称")
    @Size(max = 100,message = "图层显示名称max length should less than 100")
    private String showName;

    /**
     * 显示顺序
     */
    @TableField(value = "show_sort")
    @Schema(description="显示顺序")
    private Integer showSort;

    /**
     * 专题id
     */
    @TableField(value = "special_plan_data_id")
    @Schema(description="专题id")
    @NotNull(message = "专题idis not null")
    private Long specialPlanDataId;

    private static final long serialVersionUID = 1L;
}