package com.bjcj.web.safetyManage.specialPlan;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.specialPlan.SpecialPlanDataDto;
import com.bjcj.model.dto.specialPlan.SpecialPlanDirDto;
import com.bjcj.model.dto.specialPlan.SpecialPlanDto;
import com.bjcj.model.po.naturalresources.categories.ResourceCatalogs;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlan;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlanData;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlanDir;
import com.bjcj.model.vo.naturalresources.categories.CategoriesDataInfoVo2;
import com.bjcj.model.vo.naturalresources.categories.CategoriesDataVo;
import com.bjcj.model.vo.naturalresources.categories.ResourceDataitemsVo2;
import com.bjcj.model.vo.specialPlan.DirDataMixedVo;
import com.bjcj.model.vo.specialPlan.DirTreeVo;
import com.bjcj.service.naturalresources.categories.CategoriesDataInfoService;
import com.bjcj.service.naturalresources.categories.CategoriesDataService;
import com.bjcj.service.naturalresources.categories.ResourceCatalogsService;
import com.bjcj.service.naturalresources.categories.ResourceDataitemsService;
import com.bjcj.service.platformManage.specialPlan.SpecialPlanDataService;
import com.bjcj.service.platformManage.specialPlan.SpecialPlanDirService;
import com.bjcj.service.platformManage.specialPlan.SpecialPlanService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


/**
 * 专题展示方案表(public.special_plan)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/special_plan")
@Tag(name = "2专题展示方案")
@Validated
public class SpecialPlanController {
    /**
     * 服务对象
     */
    @Resource
    private SpecialPlanService specialPlanService;

    @Resource
    private SpecialPlanDirService specialPlanDirService;

    @Resource
    private SpecialPlanDataService specialPlanDataService;

    @Resource
    private CategoriesDataService categoriesDataService;

    @Resource
    private CategoriesDataInfoService categoriesDataInfoService;

    @Resource
    private ResourceCatalogsService resourceCatalogsService;

    @Resource
    private ResourceDataitemsService resourceDataitemsService;

    @OperaLog(operaModule = "新增/编辑专题方案", operaType = OperaLogConstant.CREATE_OR_UPDATE, operaDesc = "新增/编辑专题方案")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑", description = "新增/编辑")
    @ApiOperationSupport(order = 1)
    @RequestLock(prefix = "addOrEdit")
    public JsonResult addOrEdit(@Validated @RequestBody SpecialPlanDto dto) {
        if (Objects.nonNull(this.specialPlanService.getOne(new LambdaQueryWrapper<SpecialPlan>().eq(SpecialPlan::getPlanName, dto.getPlanName()))))
            return JsonResult.error("该方案名称已存在");
        SpecialPlan specialPlan = BeanUtil.copyProperties(dto, SpecialPlan.class);
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        if (Objects.isNull(specialPlan.getId())) specialPlan.setCreateTime(LocalDateTime.now());
        else specialPlan.setUpdateTime(LocalDateTime.now());
        specialPlan.setOperater(username);
        return specialPlanService.saveOrUpdate(specialPlan) ? JsonResult.success() : JsonResult.error();
    }

    @OperaLog(operaModule = "删除专题方案", operaType = OperaLogConstant.DELETE, operaDesc = "删除专题方案")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del")
    @Operation(summary = "删除", description = "删除")
    @Parameters({
            @Parameter(name = "id", description = "id", required = true)
    })
    @ApiOperationSupport(order = 3)
    public JsonResult del(@RequestParam("id") Long id) {
        List<SpecialPlanDir> dirlist = this.specialPlanDirService.list(new LambdaQueryWrapper<SpecialPlanDir>().eq(SpecialPlanDir::getSpecialPlanId, id));
        List<SpecialPlanData> datalist = this.specialPlanDataService.list(new LambdaQueryWrapper<SpecialPlanData>().eq(SpecialPlanData::getSpecialPlanId, id));
        if (!dirlist.isEmpty() || !datalist.isEmpty()) return JsonResult.error("该方案下有数据，不能删除");
        return specialPlanService.removeById(id) ? JsonResult.success() : JsonResult.error();
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "列表", description = "列表")
    @ApiOperationSupport(order = 2)
    @GetMapping("/list")
    public JsonResult<List<SpecialPlan>> list() {
        return JsonResult.success(specialPlanService.list());
    }

    @OperaLog(operaModule = "专题目录-新增/编辑", operaType = OperaLogConstant.CREATE_OR_UPDATE, operaDesc = "专题目录-新增/编辑")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEditDir")
    @Operation(summary = "专题目录新增/编辑", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 4)
    @RequestLock(prefix = "specialPlan_addOrEditDir")
    public JsonResult addOrEditDir(@Validated @RequestBody SpecialPlanDirDto dto) {
        SpecialPlanDir one = specialPlanDirService.getOne(
                Wrappers.<SpecialPlanDir>lambdaQuery()
                        .eq(SpecialPlanDir::getDirName, dto.getDirName())
                        .eq(SpecialPlanDir::getParentId, dto.getParentId())
                        .eq(SpecialPlanDir::getSpecialPlanId, dto.getSpecialPlanId())
        );

        if (ObjUtil.isNotNull(one) && ObjUtil.isNull(dto.getId())) {
            return JsonResult.error("该目录名称已存在");
        }
        SpecialPlanDir specialPlanDir = BeanUtil.copyProperties(dto, SpecialPlanDir.class);
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        if (ObjUtil.isNull(specialPlanDir.getId())) {
            specialPlanDir.setCreateTime(LocalDateTime.now());
        } else {
            specialPlanDir.setUpdateTime(LocalDateTime.now());
        }
        specialPlanDir.setOperater(username);
        // return specialPlanDirService.saveOrUpdate(specialPlanDir) ? JsonResult.success():JsonResult.error();
        if (specialPlanDir.getId() == null) {
            return specialPlanDirService.save(specialPlanDir) ? JsonResult.success() : JsonResult.error();
        } else {
            UpdateWrapper<SpecialPlanDir> wrapper = new UpdateWrapper<>();
            wrapper.eq("id", specialPlanDir.getId());
            wrapper.eq("special_plan_id", specialPlanDir.getSpecialPlanId());
            return specialPlanDirService.update(specialPlanDir, wrapper) ? JsonResult.success() : JsonResult.error();
        }
    }

    @OperaLog(operaModule = "专题目录-删除", operaType = OperaLogConstant.DELETE, operaDesc = "专题目录-删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delDir")
    @Operation(summary = "删除", description = "根据id删除")
    @Parameters({
            @Parameter(name = "id", description = "id", required = true),
            @Parameter(name = "specialPlanId", description = "专题id", required = true)
    })
    @ApiOperationSupport(order = 5)
    @Transactional(rollbackFor = Exception.class)
    public JsonResult delDir(@RequestParam("id") String id,
                             @RequestParam("specialPlanId") Long specialPlanId) {
        return this.specialPlanDirService.delDataById(id, specialPlanId);
    }

    // @SaCheckPermission("sys:read")
    // @Operation(summary = "专题与目录列表", description = "混合显示不分页")
    // @ApiOperationSupport(order = 6)
    // @GetMapping("/mixedList")
    // @Parameters({
    //         @Parameter(name = "specialPlanId", description = "左侧专题列表id", required = true)
    // })
    // public JsonResult<List<DirDataMixedVo>> mixedList(@RequestParam("specialPlanId") Long specialPlanId) {
    //     return JsonResult.success(this.specialPlanDirService.mixedList(specialPlanId));
    // }

    @SaCheckPermission("sys:read")
    @Operation(summary = "专题树", description = "专题树（数据服务树）")
    @ApiOperationSupport(order = 7)
    @GetMapping("/cataDataTree")
    public JsonResult<List<CategoriesDataVo>> cataDataTree() {
        return this.categoriesDataService.treeList();
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "1.专题树", description = "专题树（数据服务树）")
    @ApiOperationSupport(order = 17)
    @GetMapping("/cataDataTree2")
    public JsonResult<List<ResourceCatalogs>> cataDataTree2() {
        return this.resourceCatalogsService.treeList2();
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "查询目录下服务列表", description = "专题树下服务列表")
    @ApiOperationSupport(order = 8)
    @GetMapping("/serviceList")
    @Parameters({
            @Parameter(name = "cataDataId", description = "服务目录id", required = true),
            @Parameter(name = "searchStr", description = "搜索目录名称", required = false)
    })
    public JsonResult<List<CategoriesDataInfoVo2>> serviceList(@RequestParam("cataDataId") Long cataDataId,
                                                               @RequestParam(value = "searchStr", required = false) String searchStr) {
        return this.categoriesDataInfoService.queryDataInfoAndServiceList(cataDataId, searchStr);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "2.查询目录下服务列表", description = "专题树下服务列表")
    @ApiOperationSupport(order = 18)
    @GetMapping("/serviceList2")
    @Parameters({
            @Parameter(name = "catalogsid", description = "服务目录id", required = true),
            @Parameter(name = "searchStr", description = "搜索目录名称", required = false)
    })
    public JsonResult<List<ResourceDataitemsVo2>> serviceList2(@RequestParam("catalogsid") String catalogsid,
                                                               @RequestParam(value = "searchStr", required = false) String searchStr) {
        return this.resourceDataitemsService.queryDataInfoAndServiceList(catalogsid, searchStr);
    }

    @OperaLog(operaModule = "添加专题", operaType = OperaLogConstant.CREATE, operaDesc = "添加专题（批量）")
    @SaCheckPermission("sys:write")
    @PostMapping("/addSpecialPlanData")
    @Operation(summary = "添加专题", description = "添加专题（批量）")
    @ApiOperationSupport(order = 9)
    @RequestLock(prefix = "addSpecialPlanData")
    public JsonResult addSpecialPlanData(@Validated(SpecialPlanDataDto.AddGroup.class) @RequestBody SpecialPlanDataDto dto) {
        return this.specialPlanDataService.addSpecialPlanData(dto.getCataServiceIdList(), dto.getSpecialPlanId(), dto.getSpecialPlanDirId() == null ? null : dto.getSpecialPlanDirId());
    }

    @OperaLog(operaModule = "编辑专题", operaType = OperaLogConstant.UPDATE, operaDesc = "编辑专题")
    @SaCheckPermission("sys:write")
    @PostMapping("/editSpecialPlanData")
    @Operation(summary = "编辑专题", description = "编辑专题")
    @ApiOperationSupport(order = 10)
    @RequestLock(prefix = "editSpecialPlanData")
    public JsonResult editSpecialPlanData(@Validated(SpecialPlanDataDto.EditGroup.class) @RequestBody SpecialPlanDataDto dto) {
        return this.specialPlanDataService.editSpecialPlanData(dto);
    }

    @OperaLog(operaModule = "删除专题", operaType = OperaLogConstant.UPDATE, operaDesc = "删除专题")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delSpecialPlanData")
    @Operation(summary = "删除专题", description = "根据id删除")
    @Parameters({
            @Parameter(name = "id", description = "id", required = true)

    })
    @ApiOperationSupport(order = 11)
    public JsonResult delSpecialPlanData(@RequestParam("id") Long id) {
        return this.specialPlanDataService.removeById(id) ? JsonResult.success() : JsonResult.error();
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "专题目录与专题混合列表(不分页)", description = "专题目录与专题混合列表(不分页)")
    @ApiOperationSupport(order = 12)
    @GetMapping("/dirAndPlanDataList")
    @Parameters({
            @Parameter(name = "specialPlanId", description = "专题方案id", required = true)
    })
    public JsonResult<List<DirDataMixedVo>> dirAndPlanDataList(@RequestParam("specialPlanId") Long specialPlanId) {
        return this.specialPlanDataService.dirAndPlanDataList(specialPlanId);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "专题目录树", description = "专题目录树")
    @ApiOperationSupport(order = 13)
    @GetMapping("/dirTree")
    @Parameters({
            @Parameter(name = "specialPlanId", description = "专题方案id", required = true)
    })
    public JsonResult<List<DirTreeVo>> dirTree(@RequestParam("specialPlanId") Long specialPlanId) {
        return this.specialPlanDirService.dirTree(specialPlanId);
    }


    // @OperaLog(operaModule = "创建完整资源目录",operaType = OperaLogConstant.CREATE,operaDesc = "会覆盖现有数据,谨慎操作")
    // @Operation(summary = "创建完整资源目录", description = "会覆盖现有数据,谨慎操作")
    // @SaCheckPermission("sys:write")
    // @PostMapping("/copyAllCata")
    // @ApiOperationSupport(order = 14)
    // @Parameters({
    //         @Parameter(name = "specialPlanId", description = "专题方案id", required = true)
    // })
    // @Transactional(rollbackFor = Exception.class)
    // public JsonResult copyAllCata(@RequestParam("specialPlanId") Long specialPlanId) {
    //     return this.specialPlanDataService.copyAllCata(specialPlanId);
    // }

    @OperaLog(operaModule = "创建完整资源目录", operaType = OperaLogConstant.CREATE, operaDesc = "会覆盖现有数据,谨慎操作")
    @Operation(summary = "3.创建完整资源目录", description = "会覆盖现有数据,谨慎操作")
    @SaCheckPermission("sys:write")
    @PostMapping("/copyAllCata2")
    @ApiOperationSupport(order = 114)
    @Parameters({
            @Parameter(name = "specialPlanId", description = "专题方案id", required = true)
    })
    @Transactional(rollbackFor = Exception.class)
    public JsonResult copyAllCata2(@RequestParam("specialPlanId") Long specialPlanId) {
        return this.specialPlanDataService.copyAllCata2(specialPlanId);
    }


    @OperaLog(operaModule = "导出到...", operaType = OperaLogConstant.CREATE, operaDesc = "传平台id")
    @SaCheckPermission("sys:write")
    @PostMapping("/export")
    @Operation(summary = "导出到...", description = "传平台id")
    @Parameters({
            @Parameter(name = "fromPlatformAppConfId", description = "导出的平台id", required = true),
            @Parameter(name = "toPlatformAppConfId", description = "导入的平台id", required = true)
    })
    @ApiOperationSupport(order = 15)
    @Transactional(rollbackFor = Exception.class)
    public JsonResult exportTo(@RequestParam("fromPlatformAppConfId") Long fromPlatformAppConfId,
                               @RequestParam("toPlatformAppConfId") Long toPlatformAppConfId) {
        return this.specialPlanService.exportTo(fromPlatformAppConfId, toPlatformAppConfId);
    }


}
