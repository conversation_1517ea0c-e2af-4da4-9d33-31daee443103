package com.bjcj.web.naturalresources.function;

import com.bjcj.service.naturalresources.function.FunctionProviderService;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/12/4 14:47 周一
 */
@Tag(name = "功能服务信息提供者信息(暂时无用)",description = "功能服务信息提供者信息(暂时无用)")
@ApiSupport(order = 57)
@RestController
@Slf4j
@RequestMapping(value = "/function_provider")
public class FunctionProviderController {

    @Resource
    private FunctionProviderService functionProviderService;

    /*@Operation(summary = "功能服务信息提供者信息注册")
    @PostMapping(value = "/save")
    public JsonResult save(@RequestBody @Validated FunctionProvider functionProvider){
        functionProviderService.save(functionProvider);
        Long id = functionProvider.getId();
        return JsonResult.success(id);
    }

    @Operation(summary = "功能服务信息提供者信息修改")
    @PostMapping(value = "/update")
    public JsonResult update(@RequestBody @Validated FunctionProvider functionProvider){

        return JsonResult.success(functionProviderService.saveOrUpdate(functionProvider));
    }

    @Operation(summary = "功能服务信息提供者信息删除")
    @GetMapping(value = "/del/{id}")
    public JsonResult del(@PathVariable Long id){

        return functionProviderService.del(id);
    }*/

}
