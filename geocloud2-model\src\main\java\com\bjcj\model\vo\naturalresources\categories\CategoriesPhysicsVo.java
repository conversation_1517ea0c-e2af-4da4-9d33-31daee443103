package com.bjcj.model.vo.naturalresources.categories;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/11/29 8:50 周三
 */
@Data
public class CategoriesPhysicsVo implements Serializable {

    private Long id;

    private Long parentId;

    private String name;

    private String sname;

    private String district;

    private String standard;

    private String structure;

    private String workspace;

    private LocalDateTime createTime;

    private int del;

}
