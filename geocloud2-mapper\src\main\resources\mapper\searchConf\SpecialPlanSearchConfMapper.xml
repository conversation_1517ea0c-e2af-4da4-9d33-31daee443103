<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.platformManage.searchConf.SpecialPlanSearchConfMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.platformManage.searchConf.SpecialPlanSearchConf">
    <!--@mbg.generated-->
    <!--@Table public.special_plan_search_conf-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="categories_service_id" jdbcType="BIGINT" property="categoriesServiceId" />
    <result column="special_plan_id" jdbcType="BIGINT" property="specialPlanId" />
    <result column="cata_sname" jdbcType="VARCHAR" property="cataSname" />
    <result column="show_sort" jdbcType="INTEGER" property="showSort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, categories_service_id, special_plan_id, cata_sname, show_sort, create_time, update_time, 
    "operator"
  </sql>
</mapper>