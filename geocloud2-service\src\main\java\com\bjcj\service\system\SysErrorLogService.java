package com.bjcj.service.system;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.system.SysErrorLogMapper;
import com.bjcj.model.po.system.SysErrorLog;
import com.bjcj.model.qo.SysErrorLogQo;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/12/4 16:05 周一
 */
@Service
public class SysErrorLogService extends ServiceImpl<SysErrorLogMapper, SysErrorLog> {


    public JsonResult<Page<SysErrorLog>> errorLogList(SysErrorLogQo sysErrorLogQo) {
        Page<SysErrorLog> page = page(Page.of(sysErrorLogQo.getCurrent(), sysErrorLogQo.getSize()),
                Wrappers.<SysErrorLog>query().lambda()
                        .eq(StrUtil.isNotBlank(sysErrorLogQo.getErrorName()), SysErrorLog::getName, sysErrorLogQo.getErrorName())
                        .like(
                                StrUtil.isNotBlank(sysErrorLogQo.getUsername()),
                                SysErrorLog::getUserName,
                                StrUtil.isNotBlank(sysErrorLogQo.getUsername()) ? sysErrorLogQo.getUsername().toLowerCase() : null
                        )
                        .like(StrUtil.isNotBlank(sysErrorLogQo.getIp()), SysErrorLog::getIp, sysErrorLogQo.getIp())
                        .like(
                                StrUtil.isNotBlank(sysErrorLogQo.getType()),
                                SysErrorLog::getType,
                                StrUtil.isNotBlank(sysErrorLogQo.getType()) ? sysErrorLogQo.getType().toUpperCase() : null
                        )
                        .ge(ObjUtil.isNotEmpty(sysErrorLogQo.getStartTime()), SysErrorLog::getCreateTime, sysErrorLogQo.getStartTime())
                        .le(ObjUtil.isNotEmpty(sysErrorLogQo.getEndTime()), SysErrorLog::getCreateTime, sysErrorLogQo.getEndTime())
                        .orderByDesc(SysErrorLog::getCreateTime)
        );
        return JsonResult.success(page);
    }
}
