package com.bjcj.model.po.ore;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-02-12 11:06 周三
 */
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "soe_yfk_two_powers_analysis_result")
public class SoeYfkTwoPowersAnalysisResult {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private String id;

    /**
     * 输入的地块数组
     */
    @TableField(value = "input_dk")
    @Schema(description="输入的地块数组")
    private String inputDk;

    /**
     * 列json
     */
    @TableField(value = "table_columns")
    @Schema(description="列json")
    private String tableColumns;

    /**
     * 行json
     */
    @TableField(value = "table_rows")
    @Schema(description="行json")
    private String tableRows;

    /**
     * 属性json
     */
    @TableField(value = "attributes")
    @Schema(description="属性json")
    private String attributes;

    @TableField(value = "create_time")
    @Schema(description="")
    private LocalDateTime createTime;

    @TableField(value = "update_time")
    @Schema(description="")
    private LocalDateTime updateTime;
}