package com.bjcj.model.po.naturalresources.resReview;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/1/17  16:00
*/
/**
    * 用户申请服务审核信息表
    */
@Schema(description="用户申请服务审核信息表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "user_servr_review")
public class UserServrReview implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    @Schema(description="用户id")
    @NotNull(message = "用户idis not null")
    private Long userId;

    /**
     * 服务id
     */
    @TableField(value = "service_id")
    @Schema(description="服务id")
    @NotNull(message = "服务idis not null")
    private String serviceId;

    /**
     * 服务类型(应用服务,数据下载,数据产品,数据服务,数据上报,数据分发)
     */
    @TableField(value = "service_type")
    @Schema(description="服务类型(应用服务,数据下载,数据产品,数据服务,数据上报,数据分发)")
    @Size(max = 20,message = "服务类型(应用服务,数据下载,数据产品,数据服务,数据上报,数据分发)max length should less than 20")
    @NotBlank(message = "服务类型(应用服务,数据下载,数据产品,数据服务,数据上报,数据分发)is not blank")
    private String serviceType;

    /**
     * 审核状态0.待审核,1.通过,2.拒绝
     */
    @TableField(value = "review_status")
    @Schema(description="审核状态0.待审核,1.通过,2.拒绝")
    private Integer reviewStatus;

    /**
     * 审核人id
     */
    @TableField(value = "review_user_id")
    @Schema(description="审核人id")
    private Long reviewUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description="创建时间")
    @NotNull(message = "创建时间is not null")
    private LocalDateTime createTime;

    /**
     * 审核时间
     */
    @TableField(value = "review_time")
    @Schema(description="审核时间")
    private LocalDateTime reviewTime;

    /**
     * 标题
     */
    @TableField(value = "title")
    @Schema(description="标题")
    @Size(max = 100,message = "标题max length should less than 100")
    @NotBlank(message = "标题is not blank")
    private String title;

    /**
     * 申请时限(三个月,六个月,一年,两年,无限期)
     */
    @TableField(value = "apply_time")
    @Schema(description="申请时限(三个月,六个月,一年,两年,无限期)")
    @Size(max = 100,message = "申请时限(三个月,六个月,一年,两年,无限期)max length should less than 100")
    @NotBlank(message = "申请时限(三个月,六个月,一年,两年,无限期)is not blank")
    private String applyTime;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @Schema(description="备注")
    @Size(max = 255,message = "备注max length should less than 255")
    private String remark;

    /**
     * 审核意见
     */
    @TableField(value = "review_remark")
    @Schema(description="审核意见")
    @Size(max = 255,message = "审核意见max length should less than 255")
    private String reviewRemark;

    @TableField(value = "sname")
    @Schema(description="数据名称")
    private String sname;

    @TableField(value = "nick_name")
    @Schema(description="申请人姓名")
    private String nickName;

    @TableField(value = "dept_name")
    @Schema(description="部门名称")
    private String deptName;

    private static final long serialVersionUID = 1L;
}