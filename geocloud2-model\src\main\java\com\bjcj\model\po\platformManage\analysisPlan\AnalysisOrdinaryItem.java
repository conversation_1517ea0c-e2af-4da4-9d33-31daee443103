package com.bjcj.model.po.platformManage.analysisPlan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/6/7  10:26
*/
/**
    * 分析项表
    */
@Schema(description="分析项表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "analysis_ordinary_item")
public class AnalysisOrdinaryItem implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 分析方案id
     */
    @TableField(value = "analysis_plan_id")
    @Schema(description="分析方案id")
    private Long analysisPlanId;

    @TableField(value = "analysis_plan_name")
    @Schema(description="分析方案名称")
    private String analysisPlanName;

    @TableField(value = "res_service_name")
    @Schema(description="数据服务名称")
    private String resServiceName;

    @TableField(value = "func_service_name")
    @Schema(description="功能服务名称")
    private String funcServiceName;

    @TableField(value = "field_plan_name")
    @Schema(description="字段展示方案名称")
    private String fieldPlanName;

    @TableField(exist = false)
    @Schema(description="功能服务权限")
    private Boolean funcServiceAuthority;

    @TableField(exist = false)
    @Schema(description="数据服务权限")
    private Boolean resServiceAuthority;

    /**
     * 数据服务id
     */
    @TableField(value = "res_service_id")
    @Schema(description="数据服务id")
    @Size(max = 36,message = "数据服务idmax length should less than 36")
    @NotBlank(message = "数据服务idis not blank")
    private String resServiceId;

    /**
     * 功能服务id
     */
    @TableField(value = "func_service_id")
    @Schema(description="功能服务id")
    @Size(max = 36,message = "功能服务idmax length should less than 36")
    @NotBlank(message = "功能服务idis not blank")
    private String funcServiceId;

    /**
     * 字段展示方案id
     */
    @TableField(value = "field_plan_id")
    @Schema(description="字段展示方案id")
    @NotNull(message = "字段展示方案idis not null")
    private String fieldPlanId;

    /**
     * 最后操作人
     */
    @TableField(value = "operator")
    @Schema(description="最后操作人")
    @Size(max = 50,message = "最后操作人max length should less than 50")
    private String operator;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "update_time",fill = FieldFill.UPDATE)
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;



    /**
     * 分析项名称
     */
    @TableField(value = "name")
    @Schema(description="分析项名称")
    @Size(max = 100,message = "分析项名称max length should less than 100")
    @NotBlank(message = "分析项名称is not blank")
    private String name;


    private static final long serialVersionUID = 1L;
}