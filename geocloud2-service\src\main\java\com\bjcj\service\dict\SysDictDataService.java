package com.bjcj.service.dict;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.dict.SysDictDataMapper;
import com.bjcj.mapper.dict.SysDictTypesMapper;
import com.bjcj.model.po.dict.SysDictData;
import com.bjcj.model.po.dict.SysDictType;
import com.bjcj.model.vo.dict.DictGroupDataVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *@Author：qinyi
 *@Date：2024/2/1  10:13
*/
@Service
public class SysDictDataService extends ServiceImpl<SysDictDataMapper, SysDictData> {

    @Resource
    SysDictDataMapper sysDictDataMapper;

    @Resource
    SysDictTypesMapper sysDictTypesMapper;

    public JsonResult<List<DictGroupDataVO>> queryGroupList(String dictType, String dictLabel, String status) {
        List<SysDictData> dataList = this.sysDictDataMapper.selectList(
                new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictType, dictType)
                        .eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dictLabel), SysDictData::getDictLabel, dictLabel)
                        .eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(status), SysDictData::getStatus, status)
        );
        List<String> groupNames = this.sysDictDataMapper.selectList(
                new LambdaQueryWrapper<SysDictData>().select(SysDictData::getGroupName)
                        .eq(SysDictData::getDictType, dictType)
                        .eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dictLabel), SysDictData::getDictLabel, dictLabel)
                        .eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(status), SysDictData::getStatus, status)
                        .groupBy(SysDictData::getGroupName)
        ).stream().map(SysDictData::getGroupName).distinct().toList();
        List<DictGroupDataVO> voList = new ArrayList<>(groupNames.size());
        groupNames.forEach(name -> {
            DictGroupDataVO vo = new DictGroupDataVO();
            vo.setGroupName(name);
            vo.setDictData(dataList.stream().filter(data -> Objects.equals(data.getGroupName(), name)).toList());
            voList.add(vo);
        });
        return JsonResult.success(voList);
    }

    public JsonResult<List<SysDictData>> queryTreeList(String dictType, String dictLabel, String status,String groupName) {
        List<SysDictData> dataList = this.sysDictDataMapper.selectList(
                new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictType, dictType)
                        .eq(SysDictData::getPid, 0L)
                        .eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dictLabel), SysDictData::getDictLabel, dictLabel)
                        .eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(status), SysDictData::getStatus, status)
                        .eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(groupName),SysDictData::getGroupName, groupName)
        );
        List<SysDictData> collect = new ArrayList<>();
        dataList.forEach(data -> {
            data.setChildren(findByCode(data.getDictCode()));
            collect.add(data);
        });
        //根据groupName排序 处理空指针
        if(this.sysDictTypesMapper.selectOne(new LambdaQueryWrapper<SysDictType>().eq(SysDictType::getDictType, dictType)).getDictDataType().equals("3")){
            collect.sort((o1, o2) -> o1.getGroupName().compareTo(o2.getGroupName()));
        }
        return JsonResult.success(collect);
    }

    public List<SysDictData> findByCode(Long id){
        List<SysDictData> dataList = this.sysDictDataMapper.selectList(
                new LambdaQueryWrapper<SysDictData>()
                        .eq(SysDictData::getPid, id)
        );
        List<SysDictData> collect = new ArrayList<>();
        if(!dataList.isEmpty()){
            dataList.forEach(data -> {
                data.setChildren(findByCode(data.getDictCode()));
                collect.add(data);
            });
        }
        return collect;
    }

    public JsonResult queryGroups(String dictType) {
        List<String> groups = this.sysDictDataMapper.selectList(
               new LambdaQueryWrapper<SysDictData>()
                       .select(SysDictData::getGroupName)
                       .eq(SysDictData::getDictType, dictType)
                       .groupBy(SysDictData::getGroupName)
        ).stream().map(SysDictData::getGroupName).distinct().toList();
        return JsonResult.success(groups);
    }

    public int selectMaxSortValue(Long pid, String dictType) {
        return this.sysDictDataMapper.selectMaxSortValue(pid, dictType);
    }
}
