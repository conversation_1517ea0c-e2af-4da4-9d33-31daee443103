<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.DataLayerFieldTypeMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.DataLayerFieldType">
    <!--@mbg.generated-->
    <!--@Table public.data_layer_field_type-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name"
  </sql>

  <select id="selectByName" resultType="java.lang.Long">
    select id from data_layer_field_type where name = #{fieldtypeStr}
  </select>
</mapper>