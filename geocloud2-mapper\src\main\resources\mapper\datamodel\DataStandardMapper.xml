<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.DataStandardMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.DataStandard">
    <!--@mbg.generated-->
    <!--@Table public.data_standard-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
    <result column="standard_code" jdbcType="VARCHAR" property="standardCode" />
    <result column="categories_data_id" jdbcType="BIGINT" property="categoriesDataId" />
    <result column="categories_data_name" jdbcType="VARCHAR" property="categoriesDataName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", show_name, standard_code, categories_data_id, categories_data_name, remark, create_time,
    update_time, "operator"
  </sql>
</mapper>