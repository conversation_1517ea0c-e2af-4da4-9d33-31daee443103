package com.bjcj.service.naturalresources.categories;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.categories.CategoriesServiceGroupMapper;
import com.bjcj.model.po.naturalresources.categories.CategoriesServiceGroup;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 *@Author：qinyi
 *@Date：2024/3/21  9:16
*/
@Service
public class CategoriesServiceGroupService extends ServiceImpl<CategoriesServiceGroupMapper, CategoriesServiceGroup> {

    @Resource
    private CategoriesServiceGroupMapper categoriesServiceGroupMapper;

    public JsonResult addOrEditGroup(CategoriesServiceGroup po) {
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        po.setOperator(username);
        return this.saveOrUpdate(po) ? JsonResult.success() : JsonResult.error();
    }
}
