<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.dict.SysDictGroupMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.dict.SysDictGroup">
    <!--@mbg.generated-->
    <!--@Table public.sys_dict_group-->
    <id column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operater" jdbcType="VARCHAR" property="operater" />
    <result column="dict_id" jdbcType="BIGINT" property="dictId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    group_id, group_name, create_time, update_time, operater, dict_id
  </sql>
</mapper>