<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.safetyManage.SysRoleBusinessMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.safetyManage.SysRoleBusiness">
    <!--@mbg.generated-->
    <!--@Table public.sys_role_business-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="role_id" jdbcType="BIGINT" property="roleId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, business_id, role_id
  </sql>

  <insert id="insertBatch">
    insert into sys_role_business (
    <include refid="Base_Column_List" />
    ) values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.businessId,jdbcType=BIGINT}, #{item.roleId,jdbcType=BIGINT})
    </foreach>
  </insert>

  <delete id="deleteBatch">
    delete from sys_role_business where role_id=#{roleId} and business_id in
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </delete>
</mapper>