package com.bjcj.model.po.naturalresources.fileResManage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/1/16  11:09
*/
/**
    * 文件集信息表
    */
@Schema(description="文件集信息表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "file_set_info")
public class FileSetInfo implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 名称
     */
    @TableField(value = "name")
    @Schema(description="名称")
    @Size(max = 100,message = "名称max length should less than 100")
    @NotBlank(message = "名称is not blank")
    private String name;

    /**
     * 文件资源目录
     */
    @TableField(value = "file_dir_id")
    @Schema(description="文件资源目录")
    @NotNull(message = "文件资源目录is not null")
    private Long fileDirId;

    /**
     * 行政区
     */
    @TableField(value = "xzq_id")
    @Schema(description="行政区")
    private Long xzqId;

    /**
     * 开放级别-公开/安全
     */
    @TableField(value = "open_level")
    @Schema(description="开放级别-公开/安全")
    @Size(max = 10,message = "开放级别-公开/安全max length should less than 10")
    private String openLevel;

    /**
     * 标签
     */
    @TableField(value = "label")
    @Schema(description="标签")
    @Size(max = 50,message = "标签max length should less than 50")
    private String label;

    /**
     * 更新日期
     */
    @TableField(value = "update_date")
    @Schema(description="更新日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDate;

    /**
     * 数据类型
     */
    @TableField(value = "data_type")
    @Schema(description="数据类型")
    @Size(max = 50,message = "数据类型max length should less than 50")
    private String dataType;

    /**
     * 描述
     */
    @TableField(value = "remark")
    @Schema(description="描述")
    @Size(max = 255,message = "描述max length should less than 255")
    private String remark;

    @TableField(value = "create_time")
    @Schema(description="")
    @NotNull(message = "is not null")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "update_time")
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 最后操作人
     */
    @TableField(value = "operator")
    @Schema(description="最后操作人")
    @Size(max = 20,message = "最后操作人max length should less than 20")
    private String operator;

    /**
     * 数据应用层次
     */
    @TableField(value = "data_use_level")
    @Schema(description="数据应用层次")
    @Size(max = 20,message = "数据应用层次max length should less than 20")
    private String dataUseLevel;

    /**
     * 提供方名字
     */
    @TableField(value = "provider_name")
    @Schema(description="提供方名字")
    @Size(max = 50,message = "提供方名字max length should less than 50")
    @NotBlank(message = "提供方名字is not blank")
    private String providerName;

    /**
     * 提供方代码
     */
    @TableField(value = "provider_code")
    @Schema(description="提供方代码")
    @Size(max = 255,message = "提供方代码max length should less than 255")
    @NotBlank(message = "提供方代码is not blank")
    private String providerCode;

    /**
     * 提供方类型
     */
    @TableField(value = "provider_type")
    @Schema(description="提供方类型")
    @Size(max = 50,message = "提供方类型max length should less than 50")
    private String providerType;

    /**
     * 联系人
     */
    @TableField(value = "contacter")
    @Schema(description="联系人")
    @Size(max = 50,message = "联系人max length should less than 50")
    @NotBlank(message = "联系人is not blank")
    private String contacter;

    /**
     * 联系方式
     */
    @TableField(value = "contacter_phonenum")
    @Schema(description="联系方式")
    @Size(max = 11,message = "联系方式max length should less than 11")
    @NotBlank(message = "联系方式is not blank")
    private String contacterPhonenum;

    /**
     * 提供方地址
     */
    @TableField(value = "provider_addr")
    @Schema(description="提供方地址")
    @Size(max = 255,message = "提供方地址max length should less than 255")
    @NotBlank(message = "提供方地址is not blank")
    private String providerAddr;

    /**
     * 数据格式
     */
    @TableField(value = "data_format")
    @Schema(description="数据格式")
    @Size(max = 20,message = "数据格式max length should less than 20")
    private String dataFormat;

    /**
     * 数据类型
     */
    @TableField(value = "data_types")
    @Schema(description="数据类型")
    @Size(max = 20,message = "数据类型max length should less than 20")
    private String dataTypes;

    /**
     * 网络类型
     */
    @TableField(value = "network_type")
    @Schema(description="网络类型")
    @Size(max = 20,message = "网络类型max length should less than 20")
    private String networkType;

    /**
     * 数据存储容量
     */
    @TableField(value = "data_storage_capacity")
    @Schema(description="数据存储容量")
    @Size(max = 20,message = "数据存储容量max length should less than 20")
    private String dataStorageCapacity;

    /**
     * 数据生产时间
     */
    @TableField(value = "data_production_time")
    @Schema(description="数据生产时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dataProductionTime;

    /**
     * 数据资源状态
     */
    @TableField(value = "data_res_status")
    @Schema(description="数据资源状态")
    @Size(max = 20,message = "数据资源状态max length should less than 20")
    private String dataResStatus;

    /**
     * 数据资源摘要
     */
    @TableField(value = "data_res_summary")
    @Schema(description="数据资源摘要")
    @Size(max = 100,message = "数据资源摘要max length should less than 100")
    private String dataResSummary;

    /**
     * 数据资源内容
     */
    @TableField(value = "data_res_content")
    @Schema(description="数据资源内容")
    @Size(max = 100,message = "数据资源内容max length should less than 100")
    private String dataResContent;

    /**
     * 共享类型
     */
    @TableField(value = "share_type")
    @Schema(description="共享类型")
    @Size(max = 50,message = "共享类型max length should less than 50")
    @NotBlank(message = "共享类型is not blank")
    private String shareType;

    /**
     * 共享条件
     */
    @TableField(value = "share_condition")
    @Schema(description="共享条件")
    @Size(max = 50,message = "共享条件max length should less than 50")
    private String shareCondition;

    /**
     * 共享方式
     */
    @TableField(value = "share_way")
    @Schema(description="共享方式")
    @Size(max = 50,message = "共享方式max length should less than 50")
    @NotBlank(message = "共享方式is not blank")
    private String shareWay;

    /**
     * 更新周期
     */
    @TableField(value = "update_cycle")
    @Schema(description="更新周期")
    @Size(max = 20,message = "更新周期max length should less than 20")
    private String updateCycle;

    /**
     * 发布日期
     */
    @TableField(value = "publish_date")
    @Schema(description="发布日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishDate;

    /**
     * 停用日期
     */
    @TableField(value = "stop_date")
    @Schema(description="停用日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime stopDate;

    /**
     * 更新日期
     */
    @TableField(value = "update_dates")
    @Schema(description="更新日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDates;

    /**
     * 数据资源主办方
     */
    @TableField(value = "data_res_organizer")
    @Schema(description="数据资源主办方")
    @Size(max = 50,message = "数据资源主办方max length should less than 50")
    private String dataResOrganizer;

    /**
     * 数据资源保管方
     */
    @TableField(value = "data_res_custodian")
    @Schema(description="数据资源保管方")
    @Size(max = 50,message = "数据资源保管方max length should less than 50")
    private String dataResCustodian;

    /**
     * 数据资源生产方
     */
    @TableField(value = "data_res_manufacturer")
    @Schema(description="数据资源生产方")
    @Size(max = 50,message = "数据资源生产方max length should less than 50")
    private String dataResManufacturer;

    /**
     * 关联目录名称
     */
    @TableField(value = "relation_res_name")
    @Schema(description="关联目录名称")
    @Size(max = 50,message = "关联目录名称max length should less than 50")
    private String relationResName;

    @TableField(exist = false)
    @Schema(description="关联的文件信息")
    private List<FileInfo> fileInfoDtos;

    private static final long serialVersionUID = 1L;
}