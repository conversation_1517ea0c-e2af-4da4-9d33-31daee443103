<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.MetadataWorkspacesMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.MetadataWorkspaces">
    <!--@mbg.generated-->
    <!--@Table public.metadata_workspaces-->
    <id column="workspaceid" jdbcType="CHAR" property="workspaceid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="displayname" jdbcType="VARCHAR" property="displayname" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="workspacetype" jdbcType="VARCHAR" property="workspacetype" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="serverid" jdbcType="CHAR" property="serverid" />
    <result column="database" jdbcType="VARCHAR" property="database" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="servername" jdbcType="VARCHAR" property="servername" />
    <result column="instance" jdbcType="VARCHAR" property="instance" />
    <result column="readonly" jdbcType="BOOLEAN" property="readonly" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    workspaceid, "name", displayname, username, "password", version, workspacetype, "path", 
    serverid, "database", description, servername, "instance", readonly
  </sql>
</mapper>