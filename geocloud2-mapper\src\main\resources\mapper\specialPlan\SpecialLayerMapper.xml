<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.platformManage.specialPlan.SpecialLayerMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.platformManage.specialPlan.SpecialLayer">
    <!--@mbg.generated-->
    <!--@Table public.special_layer-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="data_layer_id" jdbcType="BIGINT" property="dataLayerId" />
    <result column="layer_name" jdbcType="VARCHAR" property="layerName" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
    <result column="show_sort" jdbcType="INTEGER" property="showSort" />
    <result column="special_plan_data_id" jdbcType="BIGINT" property="specialPlanDataId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, data_layer_id, layer_name, show_name, show_sort, special_plan_data_id
  </sql>
</mapper>