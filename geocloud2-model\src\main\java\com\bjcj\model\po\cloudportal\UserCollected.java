package com.bjcj.model.po.cloudportal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/1/15  9:55
*/
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "user_collected")
public class UserCollected implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    @Schema(description="用户id")
    private Long userId;

    /**
     * 数据服务,应用服务,数据产品
     */
    @TableField(value = "resouce_type")
    @Schema(description="数据服务,应用服务,数据产品")
    private String resouceType;

    /**
     * 资源id
     */
    @TableField(value = "res_id")
    @Schema(description="资源id")
    private String resId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;



    @TableField(value = "name")
    @Schema(description="服务名称")
    private String name;

    @TableField(value = "sname")
    @Schema(description="服务显示名称")
    private String sname;

    @TableField(value = "address")
    @Schema(description="服务地址")
    private String address;

    @TableField(value = "fwitype")
    @Schema(description="服务类型")
    private String fwitype;

    @TableField(value = "publish_institution_name")
    @Schema(description="发布机构")
    private String publishInstitutionName;

    @TableField(value = "del")
    @Schema(description="是否删除")
    private String del;

    @TableField(value = "imgurl")
    @Schema(description="缩略图")
    private String imgurl;

    @TableField(value = "parent_id")
    @Schema(description="父id")
    private String parentId;

    private static final long serialVersionUID = 1L;
}