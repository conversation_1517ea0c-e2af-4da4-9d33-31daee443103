<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.SpdpRuleMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.SpdpRule">
    <!--@mbg.generated-->
    <!--@Table public.spdp_rule-->
    <result column="ruleid" jdbcType="CHAR" property="ruleid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="displayname" jdbcType="VARCHAR" property="displayname" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="errorlevel" jdbcType="NUMERIC" property="errorlevel" />
    <result column="isneed" jdbcType="BOOLEAN" property="isneed" />
    <result column="rulecode" jdbcType="VARCHAR" property="rulecode" />
    <result column="ruleitemcompositetype" jdbcType="NUMERIC" property="ruleitemcompositetype" />
    <result column="rulecatalogid" jdbcType="CHAR" property="rulecatalogid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ruleid, "name", displayname, description, errorlevel, isneed, rulecode, ruleitemcompositetype, 
    rulecatalogid
  </sql>
</mapper>