package com.bjcj.service.naturalresources.fileResManage;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.fileResManage.FileInfoMapper;
import com.bjcj.mapper.naturalresources.fileResManage.FileSetInfoMapper;
import com.bjcj.model.dto.fileResManage.FileInfoDto;
import com.bjcj.model.dto.fileResManage.FileSetInfoDto;
import com.bjcj.model.po.naturalresources.fileResManage.FileInfo;
import com.bjcj.model.po.naturalresources.fileResManage.FileSetInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *@Author：qinyi
 *@Date：2024/1/16  11:09
*/
@Service
public class FileSetInfoService extends ServiceImpl<FileSetInfoMapper, FileSetInfo> {

    @Resource
    FileSetInfoMapper fileSetInfoMapper;

    @Resource
    FileInfoMapper fileInfoMapper;

    @Resource
    FileInfoService fileInfoService;

    public JsonResult addOrEdit(FileSetInfoDto dto, List<FileInfoDto> fileInfoDtos) {
        FileSetInfo fileSetInfo = BeanUtil.copyProperties(dto, FileSetInfo.class);
        List<FileInfo> fileInfoList = new ArrayList<>();
        if(fileInfoDtos.size() != 0 || fileInfoDtos != null){
            fileInfoDtos.forEach(infodto -> {
                FileInfo fileInfo = BeanUtil.copyProperties(infodto, FileInfo.class);
                fileInfoList.add(fileInfo);
            });
        }
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        //新增
        if(Objects.isNull(fileSetInfo.getId())){
            fileSetInfo.setCreateTime(LocalDateTime.now());
            fileSetInfo.setOperator(username);
            int insert = this.fileSetInfoMapper.insert(fileSetInfo);
            if(insert>0 && fileInfoList.size() > 0){
                Long file_set_info_id = fileSetInfo.getId();
                fileInfoList.forEach(fileInfo -> {
                    fileInfo.setFileSetInfoId(file_set_info_id);
                    fileInfo.setOperator(username);
                    fileInfo.setCreateTime(LocalDateTime.now());
                });
                this.fileInfoService.saveBatch(fileInfoList);
            }
        }else{//编辑
            fileSetInfo.setUpdateTime(LocalDateTime.now());
            fileSetInfo.setOperator(username);
            int update = this.fileSetInfoMapper.updateById(fileSetInfo);
            // if(update>0 && fileInfoList.size() > 0){
                //先删除旧数据  写入新集合
                Long pid = fileSetInfo.getId();
                this.fileInfoMapper.deleteByPid(pid);
                if(fileInfoList != null && fileInfoList.size()!=0){
                    fileInfoList.forEach(fileInfo -> {
                        fileInfo.setFileSetInfoId(pid);
                        fileInfo.setUpdateTime(LocalDateTime.now());
                        fileInfo.setOperator(username);
                    });
                    this.fileInfoService.saveBatch(fileInfoList);
                }
            // }

        }
        return JsonResult.success();
    }

    public JsonResult delData(Long id) {
        this.fileSetInfoMapper.deleteById(id);
        this.fileInfoMapper.deleteByPid(id);
        return JsonResult.success();
    }

    public JsonResult<Page<FileSetInfo>> pageData(Page<FileSetInfo> pager,Long fileDirId, String searchStr) {
        LambdaQueryWrapper<FileSetInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FileSetInfo::getFileDirId, fileDirId);
        if(Objects.nonNull(searchStr)){
            wrapper.like(FileSetInfo::getName, searchStr);
        }
        wrapper.orderByDesc(FileSetInfo::getCreateTime);
        Page<FileSetInfo> page = this.fileSetInfoMapper.selectPage(pager, wrapper);
        page.getRecords().forEach(item -> {
            List<FileInfo> fileInfos = this.fileInfoMapper.selectList(new LambdaQueryWrapper<FileInfo>().eq(FileInfo::getFileSetInfoId, item.getId()));
            item.setFileInfoDtos(fileInfos);
        });
        return JsonResult.success(page);
    }
}
