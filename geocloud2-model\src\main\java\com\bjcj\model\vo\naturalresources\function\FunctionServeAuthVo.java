package com.bjcj.model.vo.naturalresources.function;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/12/14 15:20 周四
 */
@Data
public class FunctionServeAuthVo implements Serializable {

    private String id;

    @Schema(description="服务注册名")
    private String name;

    @Schema(description="服务显示名")
    private String sname;

    @Schema(description="服务目录")
    private String fwCatalogue;

    @Schema(description="权限")
    private int auth;

    @Schema(description="注册地址")
    private String regAddress;

    @Schema(description="服务类型")
    private String fwItype;

    @Schema(description="服务标签")
    private String fwTag;

    @Schema(description="缩略图")
    private String imgurl;

    @Schema(description="描述")
    private String description;

    @Schema(description="注册人")
    private String regName;

    @Schema(description="注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description="运行状态")
    private Boolean status;

    @Schema(description="删除状态（0未删除，1已删除）")
    private int del;

    @Schema(description="数据服务id")
    private String funcId;

    @Schema(description="角色id")
    private Long roleId;

}
