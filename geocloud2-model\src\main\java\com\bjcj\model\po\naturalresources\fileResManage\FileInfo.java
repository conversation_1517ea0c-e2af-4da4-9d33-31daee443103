package com.bjcj.model.po.naturalresources.fileResManage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/1/16  11:04
*/
/**
    * 文件信息表
    */
@Schema(description="文件信息表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "file_info")
public class FileInfo implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 所属文件集id
     */
    @TableField(value = "file_set_info_id")
    @Schema(description="所属文件集id")
    private Long fileSetInfoId;

    /**
     * 文件名称
     */
    @TableField(value = "file_name")
    @Schema(description="文件名称")
    @Size(max = 100,message = "文件名称max length should less than 100")
    private String fileName;

    /**
     * 地址
     */
    @TableField(value = "file_url")
    @Schema(description="地址")
    @Size(max = 500,message = "地址max length should less than 500")
    private String fileUrl;

    /**
     * 文件大小
     */
    @TableField(value = "file_size")
    @Schema(description="文件大小")
    @Size(max = 20,message = "文件大小max length should less than 20")
    private String fileSize;

    /**
     * 文件类型
     */
    @TableField(value = "file_type")
    @Schema(description="文件类型")
    @Size(max = 50,message = "文件类型max length should less than 50")
    private String fileType;

    @TableField(value = "create_time")
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "update_time")
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField(value = "operator")
    @Schema(description="")
    @Size(max = 20,message = "max length should less than 20")
    private String operator;

    private static final long serialVersionUID = 1L;
}