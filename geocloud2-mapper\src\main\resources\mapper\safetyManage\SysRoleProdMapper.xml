<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.safetyManage.SysRoleProdMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.safetyManage.SysRoleProd">
    <!--@mbg.generated-->
    <!--@Table public.sys_role_prod-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="role_id" jdbcType="BIGINT" property="roleId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id,product_id, role_id,from_role_name
  </sql>

  <insert id="insertBatch">
    insert into sys_role_prod (
      <include refid="Base_Column_List" />
    ) values
      <foreach collection="list" item="item" separator=",">
        (#{item.id,jdbcType=BIGINT}, #{item.productId,jdbcType=BIGINT}, #{item.roleId,jdbcType=BIGINT}, #{item.fromRoleName,jdbcType=VARCHAR})
      </foreach>
    </insert>

  <delete id="deleteBatch">
    delete from sys_role_prod where role_id=#{roleId} and product_id in
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </delete>

  <select id="selectRoleNameByProductId" resultType="java.lang.String">
    select string_agg(r.role_name,',') from sys_role_prod rc
    left join sys_role r on rc.role_id=r.id
    where product_id=#{id}
  </select>
</mapper>