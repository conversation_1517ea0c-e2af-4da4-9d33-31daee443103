package com.bjcj.model.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/27 15:01 周三
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description="有道api响应对象")
public class YdRes {

    @Schema(description = "错误返回码, 一定存在")
    private String errorCode;

    @Schema(description = "源语言, 查询正确时，一定存在")
    private String query;

    @Schema(description = "翻译结果, 查询正确时，一定存在")
    private List<String> translation;

    @Schema(description = "基本词典，查词时才有")
    private String basic;

    @Schema(description = "网络释义，该结果不一定存在")
    private List<String> web;

    @Schema(description = "源语言和目标语言, 一定存在")
    private String l;

    @Schema(description = "词典deeplink")
    private String dict;

    @Schema(description = "webdeeplink")
    private String webdict;

    @Schema(description = "翻译结果发音地址")
    private String tSpeakUrl;

    @Schema(description = "源语言发音地址")
    private String speakUrl;

    @Schema(description = "单词校验后的结果")
    private List<String> returnPhrase;


}
