package com.bjcj.service.mobile;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.mobile.PdaConfigPageMapper;
import com.bjcj.model.po.mobile.PdaConfigPage;
import org.springframework.stereotype.Service;

@Service
public class PdaConfigPageService extends ServiceImpl<PdaConfigPageMapper, PdaConfigPage> {

    /**
     * @description 获取模板数据（不分页）
     * <AUTHOR>
     * @create 2025/7/2
     **/
    public JsonResult getTemplateStyle() {
        return JsonResult.success(this.list());
    }



}
