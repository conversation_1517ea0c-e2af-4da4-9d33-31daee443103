package com.bjcj.model.dto.resReview;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/1/17  16:00
*/

/**
    * 用户申请服务审核信息表
    */
@Schema(description="用户申请服务审核信息表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "user_servr_review")
public class UserServrReviewDto2 implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    @NotNull(message = "id is not null")
    private Long id;


    /**
     * 审核状态0.待审核,1.通过,2.拒绝
     */
    @TableField(value = "review_status")
    @Schema(description="审核状态0.待审核,1.通过,2.拒绝")
    @NotNull(message = "审核状态0.待审核,1.通过,2.拒绝is not null")
    private Integer reviewStatus;


    /**
     * 标题
     */
    @TableField(value = "title")
    @Schema(description="标题")
    @Size(max = 100,message = "标题max length should less than 100")
    @NotBlank(message = "标题is not blank")
    @RequestKeyParam(name = "title")
    private String title;

    /**
     * 申请时限(三个月,六个月,一年,两年,无限期)
     */
    @TableField(value = "apply_time")
    @Schema(description="申请时限(三个月,六个月,一年,两年,无限期)")
    @Size(max = 100,message = "申请时限(三个月,六个月,一年,两年,无限期)max length should less than 100")
    @NotBlank(message = "申请时限(三个月,六个月,一年,两年,无限期)is not blank")
    private String applyTime;

    /**
     * 审核意见
     */
    @TableField(value = "review_remark")
    @Schema(description="审核意见")
    @Size(max = 255,message = "审核意见max length should less than 255")
    private String reviewRemark;

    private static final long serialVersionUID = 1L;
}