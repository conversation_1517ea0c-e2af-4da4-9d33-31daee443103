package com.bjcj.service.platformManage.specialPlan;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.platformManage.specialPlan.SpecialPlanDataMapper;
import com.bjcj.mapper.platformManage.specialPlan.SpecialPlanDirMapper;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlanData;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlanDir;
import com.bjcj.model.vo.specialPlan.DirDataMixedVo;
import com.bjcj.model.vo.specialPlan.DirTreeVo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Queue;

/**
 *@Author：qinyi
 *@Date：2024/1/26  10:17
*/
@Service
public class SpecialPlanDirService extends ServiceImpl<SpecialPlanDirMapper, SpecialPlanDir> {

    @Resource
    SpecialPlanDirMapper specialPlanDirMapper;

    @Resource
    SpecialPlanDataMapper specialPlanDataMapper;

    public JsonResult delDataById(String id,Long specialPlanId) {
        List<String> finalIds = new ArrayList<>();
        List<String> dirIds = this.specialPlanDirMapper.selectList(new LambdaQueryWrapper<SpecialPlanDir>().eq(SpecialPlanDir::getParentId, id).eq(SpecialPlanDir::getSpecialPlanId, specialPlanId)).stream().map(SpecialPlanDir::getId).toList();
        finalIds.addAll(dirIds);
        finalIds.add(id);
        //删除本目录及子目录.删除目录下所有的专题数据
        this.specialPlanDirMapper.delDataById(id,specialPlanId);
        this.specialPlanDataMapper.delete(new LambdaQueryWrapper<SpecialPlanData>().in(SpecialPlanData::getSpecialPlanDirId, finalIds));
        return JsonResult.success();
    }

    public List<DirDataMixedVo> mixedList(Long specialPlanId) {
        //分别查询该方案下俩子表关联的数据  然后统一塞进VO中  再统一排序
        return null;
    }

    /*
    public JsonResult<List<DirTreeVo>> dirTree(Long specialPlanId) {
        List<SpecialPlanDir> dirList = this.specialPlanDirMapper.selectList(new LambdaQueryWrapper<SpecialPlanDir>().eq(SpecialPlanDir::getParentId,0).eq(SpecialPlanDir::getSpecialPlanId, specialPlanId));
        List<DirTreeVo> voList = new ArrayList<DirTreeVo>(dirList.size());
        dirList.forEach(dir -> {
            DirTreeVo vo = BeanUtil.copyProperties(dir, DirTreeVo.class);
            List<DirTreeVo> children = findChildren(dir.getId());
            vo.setChildren(children);
            voList.add(vo);
        });
        return JsonResult.success(voList);
    }

    public List<DirTreeVo> findChildren(Long dirId) {
        List<SpecialPlanDir> dirList = this.specialPlanDirMapper.selectList(new LambdaQueryWrapper<SpecialPlanDir>().eq(SpecialPlanDir::getParentId, dirId));
        List<DirTreeVo> voList = new ArrayList<DirTreeVo>(dirList.size());
        if(!dirList.isEmpty()){
            dirList.forEach(dir -> {
                DirTreeVo vo = BeanUtil.copyProperties(dir, DirTreeVo.class);
                List<DirTreeVo> children = findChildren(dir.getId());
                vo.setChildren(children);
                voList.add(vo);
            });
        }
        return voList;
    }
    */

    public JsonResult<List<DirTreeVo>> dirTree(Long specialPlanId) {
        List<SpecialPlanDir> dirList = specialPlanDirMapper.selectList(
                new LambdaQueryWrapper<SpecialPlanDir>()
                        .eq(SpecialPlanDir::getParentId,"0")
                        .eq(SpecialPlanDir::getSpecialPlanId, specialPlanId));

        List<DirTreeVo> voList = new ArrayList<>();
        for (SpecialPlanDir dir : dirList) {
            DirTreeVo vo = BeanUtil.copyProperties(dir, DirTreeVo.class);
            List<DirTreeVo> children = findChildrenIterative(dir.getId(), specialPlanId);
            vo.setChildren(children);
            voList.add(vo);
        }

        return JsonResult.success(voList);
    }

    private List<DirTreeVo> findChildrenIterative(String parentId, Long specialPlanId) {
        List<DirTreeVo> voList = new ArrayList<>();
        Queue<String> queue = new LinkedList<>();
        queue.offer(parentId);

        while (!queue.isEmpty()) {
            String dirId = queue.poll();
            List<SpecialPlanDir> dirList = specialPlanDirMapper.selectList(
                    new LambdaQueryWrapper<SpecialPlanDir>().eq(SpecialPlanDir::getParentId, dirId).eq(SpecialPlanDir::getSpecialPlanId, specialPlanId));
            for (SpecialPlanDir dir : dirList) {
                DirTreeVo vo = BeanUtil.copyProperties(dir, DirTreeVo.class);
                List<DirTreeVo> children = findChildrenIterative(dir.getId(),specialPlanId);
                vo.setChildren(children);
                voList.add(vo);
            }
        }

        return voList;
    }


}
