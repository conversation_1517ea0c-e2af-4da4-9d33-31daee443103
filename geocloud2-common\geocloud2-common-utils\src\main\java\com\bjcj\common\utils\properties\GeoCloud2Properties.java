package com.bjcj.common.utils.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/11/23 16:09 周四
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "geocloud2")
public class GeoCloud2Properties {


    /**
     * 文件
     */
    private file file;

    /**
     * 默认密码
     */
    private String defaultPassword;

    private String clientId;

    private String clientSecret;

    private String serverurl;

    /**
     * 翻译服务
     */
    private translation translation;

    /**
     * 度带
     */
    private String zone;

    /**
     * 是否校验验证码
     */
    private boolean checkCaptcha = false;


    @Data
    public static class file {
        /**
         * 文件上传路径
         */
        private String upload;
        /**
         * 日志存储路径
         */
        private String log;
    }

    @Data
    public static class translation {
        /**
         * api请求路径
         */
        private String url;
        /**
         * 应用id
         */
        private String appKey;
        /**
         * 应用秘钥
         */
        private String appSecret;
    }

}
