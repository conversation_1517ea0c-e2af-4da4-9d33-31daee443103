package com.bjcj.service.eswPlatform;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.eswPlatform.SchemeModelsMapper;
import com.bjcj.mapper.eswPlatform.SchemesMapper;
import com.bjcj.model.po.eswPlatform.SchemeModels;
import com.bjcj.model.po.eswPlatform.Schemes;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *@Author：qinyi
 *@Date：2024-8-20  08:58
*/
@Service
public class SchemesService extends ServiceImpl<SchemesMapper, Schemes> {
    @Resource
    private SchemesMapper schemesMapper;

    @Resource
    private SchemeModelsMapper schemeModelsMapper;

    public JsonResult removeAllById(String id) {
        this.schemesMapper.deleteById(id);
        this.schemeModelsMapper.delete(new LambdaQueryWrapper<SchemeModels>().eq(SchemeModels::getSchemeid, id));
        return JsonResult.success();
    }

    public JsonResult removeAllByIds(List<String> idss) {
        this.schemesMapper.deleteByIds(idss);
        this.schemeModelsMapper.delete(new LambdaQueryWrapper<SchemeModels>().in(SchemeModels::getSchemeid, idss));
        return JsonResult.success();
    }
}
