package com.bjcj.common.core.constant;

/**
 * @Author：qinyi
 * @Date：2024/3/22 15:07
 */
public class RegularConstant {
    //邮箱格式
    public static final String EMAIL_REGEX = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";

    //手机号格式
    public static final String PHONE_REGEX = "^1[3-9]\\d{9}$";

    // 区号 + 分隔符 - + 7到8位数字
    private static final String REGEX_LANDLINE_PHONE_WITH_AREA_CODE = "^0[1-9]\\d{2}-\\d{7,8}$";

    // 直接7到8位数字（假设没有区号时）
    private static final String REGEX_LANDLINE_PHONE_WITHOUT_AREA_CODE = "^\\d{7,8}$";

}
