package com.bjcj.model.po.safetyManage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2023/12/13  15:09
*/
/**
    * 数据资源角色关联表
    */
@Schema(description="数据资源角色关联表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "public.sys_role_cate")
public class SysRoleCate implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    @NotNull(message = "主键不能为null")
    private Long id;

    /**
     * 数据资源id
     */
    @TableField(value = "cate_id")
    @Schema(description="数据资源id")
    @NotNull(message = "数据资源id不能为null")
    private String cateId;

    /**
     * 角色id
     */
    @TableField(value = "role_id")
    @Schema(description="角色id")
    @NotNull(message = "角色id不能为null")
    private Long roleId;

    /**
     * 授权角色
     */
    @TableField(value = "from_role_name")
    @Schema(description="授权角色")
    private String fromRoleName;

    private static final long serialVersionUID = 1L;
}