<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.SpdpComponenttypeMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.SpdpComponenttype">
    <!--@mbg.generated-->
    <!--@Table public.spdp_componenttype-->
    <id column="componenttypeid" jdbcType="CHAR" property="componenttypeid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="typeidentify" jdbcType="VARCHAR" property="typeidentify" />
    <result column="metadata" jdbcType="VARCHAR" property="metadata" />
    <result column="modulename" jdbcType="VARCHAR" property="modulename" />
    <result column="paramediturl" jdbcType="VARCHAR" property="paramediturl" />
    <result column="supportmode" jdbcType="NUMERIC" property="supportmode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    componenttypeid, "name", description, category, typeidentify, metadata, modulename, 
    paramediturl, supportmode
  </sql>

</mapper>