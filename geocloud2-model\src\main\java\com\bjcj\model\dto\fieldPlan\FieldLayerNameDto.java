package com.bjcj.model.dto.fieldPlan;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @Author：qinyi
 * @Date：2024/2/20 16:39
 */

@Data
public class FieldLayerNameDto {

    @Schema(description="id")
    @NotNull(message = "id is not null")
    private String id;

    @Schema(description="名称")
    private String name;

    @Schema(description="显示名称")
    private String showName;

}
