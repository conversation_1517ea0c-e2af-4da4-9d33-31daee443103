package com.bjcj.common.database.mybatisUUID;

import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;

import java.util.UUID;

/**
 * @Author：qinyi
 * @Date：2024/6/6 10:34
 */
public class CustomUUIDGenerator implements IdentifierGenerator {

    @Override
    public boolean assignId(Object idValue) {
        return IdentifierGenerator.super.assignId(idValue);
    }

    @Override
    public Number nextId(Object entity) {
        return SequenceUtil.makeId();
    }

    @Override
    public String nextUUID(Object entity) {
        return UUID.randomUUID().toString();
    }
}
