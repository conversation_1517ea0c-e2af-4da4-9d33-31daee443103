package com.bjcj.model.dto.cloudportal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author：qinyi
 * @Date：2024/7/11 17:29
 */
@Schema(description="行政区地类统计返回结构")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LandXzqAreaStatisticsDto {

    @Schema(description="行政区名称")
    private String xzqName;

    @Schema(description="农用地面积")
    private BigDecimal nydArea;

    @Schema(description="建设用地面积")
    private BigDecimal jsydArea;

    @Schema(description="未利用地面积")
    private BigDecimal wlydArea;

    @Schema(description="总面积")
    private BigDecimal AllArea;

}
