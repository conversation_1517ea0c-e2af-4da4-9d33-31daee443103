package com.bjcj.model.po.datamodel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 * 图层字段表
 *@Date：2024/1/3  15:32
*/
@Schema(description="图层字段表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "data_layer_field")
public class DataLayerFieldAnalysis implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    @NotNull(message = "is not null")
    private Long id;

    /**
     * 字段名称
     */
    @TableField(value = "field_name")
    @Schema(description="字段名称")
    @Size(max = 50,message = "字段名称max length should less than 50")
    private String fieldName;

    /**
     * 字段显示名称
     */
    @TableField(value = "show_name")
    @Schema(description="字段显示名称")
    @Size(max = 50,message = "字段显示名称max length should less than 50")
    private String showName;

    /**
     * 标准名称
     */
    @TableField(value = "standard_name")
    @Schema(description="标准名称")
    @Size(max = 50,message = "标准名称max length should less than 50")
    private String standardName;

    /**
     * 字段类型
     */
    @TableField(value = "field_type_id")
    @Schema(description="字段类型id")
    @NotNull(message = "字段类型is not null")
    private Long fieldTypeId;

    @TableField(exist = false)
    private String fieldType;

    /**
     * 字段长度
     */
    @TableField(value = "field_length")
    @Schema(description="字段长度")
    private Integer fieldLength;

    /**
     * 字段精度
     */
    @TableField(value = "field_accuracy")
    @Schema(description="字段精度")
    private Integer fieldAccuracy;

    /**
     * 小数位数
     */
    @TableField(value = "decimal_places")
    @Schema(description="小数位数")
    private Integer decimalPlaces;

    /**
     * 是否必填
     */
    @TableField(value = "is_must")
    @Schema(description="是否必填")
    private Boolean isMust;

    /**
     * 是否可为空
     */
    @TableField(value = "is_null")
    @Schema(description="是否可为空")
    private Boolean isNull;

    /**
     * 描述
     */
    @TableField(value = "remark")
    @Schema(description="描述")
    @Size(max = 255,message = "描述max length should less than 255")
    private String remark;

    /**
     * 统计分类
     */
    @TableField(value = "statistics_type_id")
    @Schema(description="统计分类id")
    private Long statisticsTypeId;

    @TableField(exist = false)
    private String statisticsType;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @Schema(description="修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 最后操作人
     */
    @TableField(value = "operator")
    @Schema(description="最后操作人")
    @Size(max = 50,message = "最后操作人max length should less than 50")
    private String operator;

    /**
     * 图层id
     */
    @TableField(value = "data_layer_id")
    @Schema(description="图层id")
    @NotNull(message = "图层idis not null")
    private Long dataLayerId;

    /**
     * 数据标准id
     */
    @TableField(value = "data_standard_id")
    @Schema(description="数据标准id")
    private Long dataStandardId;

    /**
     * 是否排序
     */
    @TableField(value = "is_sort")
    @Schema(description="是否排序")
    private Boolean isSort;


    /**
     * 显示顺序
     */
    @TableField(value = "show_sort")
    @Schema(description="显示顺序")
    private Integer showSort;

    /**
     * 是否显示
     */
    @TableField(value = "is_show")
    @Schema(description="是否显示")
    private Boolean isShow;

    @TableField(exist = false)
    @Schema(description="来源(分析/被分析图层)")
    private String fieldSource;


    private static final long serialVersionUID = 1L;
}