package com.bjcj.model.po.naturalresources.function;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/11/30 9:21 周四
 */
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "function_data")
public class FunctionData implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="", hidden = true)
    private Long id;

    @NotNull
    @TableField(value = "parent_id")
    @Schema(description="父id")
    private Long parentId;

    @NotBlank
    @TableField(value = "name")
    @Schema(description="服务目录名称")
    private String name;

    @TableField(value = "sort")
    @Schema(description="排序")
    private int sort;

    @TableField(value = "description")
    @Schema(description="描述")
    private String description;

    @TableField(value = "status")
    @Schema(description="显示")
    private Boolean status;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "del")
    @Schema(description="删除状态（0未删除，1已删除）")
    private int del;

}
