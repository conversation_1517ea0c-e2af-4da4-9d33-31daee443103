package com.bjcj.model.po.system;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/11/29 17:12 周三
 */
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "public.sys_login_log")
public class SysLoginLog {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    @TableField(value = "username")
    @Schema(description="")
    private String username;

    @TableField(value = "user_id")
    @Schema(description="")
    private Long userId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description="")
    private LocalDateTime createTime;

    @TableField(value = "ip")
    @Schema(description="登录ip")
    private String ip;

    @TableField(value = "device")
    @Schema(description="设备")
    private String device;

    @TableField(value = "login_or_out")
    @Schema(description = "登录或登出")
    private String loginOrOut;

    @TableField(value = "status")
    @Schema(description = "状态")
    private Boolean status;

    @TableField(value = "failed_msg")
    @Schema(description = "失败原因")
    private String failedMsg;
}