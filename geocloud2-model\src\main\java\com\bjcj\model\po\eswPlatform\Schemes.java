package com.bjcj.model.po.eswPlatform;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024-8-20  08:58
*/
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "schemes")
public class Schemes implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(description="")
    @Size(max = 40,message = "最大长度要小于 40")
    private String id;

    /**
     * 方案名称
     */
    @TableField(value = "name")
    @Schema(description="方案名称")
    @Size(max = 255,message = "方案名称最大长度要小于 255")
    @NotBlank(message = "方案名称不能为空")
    private String name;

    /**
     * 项目Id
     */
    @TableField(value = "projectid")
    @Schema(description="项目Id")
    @Size(max = 40,message = "项目Id最大长度要小于 40")
    @NotBlank(message = "项目Id不能为空")
    private String projectid;

    /**
     * 类型
     */
    @TableField(value = "type")
    @Schema(description="类型")
    @Size(max = 255,message = "类型最大长度要小于 255")
    private String type;

    /**
     * 描述
     */
    @TableField(value = "description")
    @Schema(description="描述")
    @Size(max = 255,message = "描述最大长度要小于 255")
    private String description;

    private static final long serialVersionUID = 1L;
}