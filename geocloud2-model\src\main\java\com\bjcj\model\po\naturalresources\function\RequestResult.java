package com.bjcj.model.po.naturalresources.function;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/12/1 10:11 周五
 */
@Schema(description="功能服务信息返回参数")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "request_result")
public class RequestResult implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="", hidden = true)
    private Long id;

    @NotBlank
    @TableField(value = "parent_id")
    @Schema(description="父级id")
    private Long parentId;

    @NotBlank
    @TableField(value = "param")
    @Schema(description="参数")
    private String param;

    @NotBlank
    @TableField(value = "type")
    @Schema(description="类型")
    private String type;

    @NotBlank
    @TableField(value = "description")
    @Schema(description="说明")
    private String description;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description="注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "del")
    @Schema(description="删除状态（0未删除，1已删除）")
    private int del;

}
