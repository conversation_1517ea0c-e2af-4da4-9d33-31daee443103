package com.bjcj.common.utils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

public class LocalDateUtil {
    
    /**
     * @desc 当前时间
     
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021-09-29 09:13
     */
    public static String now() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    public static String slashNowDate() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
    }

    /**
     * <h2>带横杠的年月日</h2>
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/10/23 14:55
     */
    public static String barreNowDate() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * <h2>不带横杠的年月日</h2>
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/10/23 14:55
     */
    public static String nowDate() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    public static String timestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    }

    /**
     * <h2>获取前几个月的月份列表</h2>
     * @param localDate: 起始月份，可传null
     * @param span: 跨度，前几个月，包含
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2022/11/23 10:11
     */
    public static List<String> getBeforeMonth(LocalDate localDate, int span) {
        List<String> result = new ArrayList<>();
        if (null == localDate) {
            localDate = LocalDate.now();
        }
        for (int i = 0; i <= span; i++) {
            LocalDate lastMonth = localDate.minusMonths(i);
            result.add(lastMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")));
        }
        return result;
    }

    // public static String distance(LocalDateTime time1, LocalDateTime time2) {
        // if (time1.isAfter(time2)) {
        //     LocalDateTime time3 = time1;
        //     time1 = time2;
        //     time2 = time3;
        // }
    //     Duration duration = Duration.between(time1, time2);
    //     return null;
    // }
    
    public static void main(String[] args) {
//        LocalDateTime time1 = LocalDateTime.now();
//        LocalDateTime time2 = LocalDateTime.parse("2021-12-16T14:00:00");
//        System.out.println(LocalTime.ofSecondOfDay(Duration.between(time1, time2).getSeconds()));

//        LocalDate localDate = LocalDate.now();
//        System.out.println(localDate.minusMonths(1));
        System.out.println(getBeforeMonth(null, 2));
    }
    
}
