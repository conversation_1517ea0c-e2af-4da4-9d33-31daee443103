package com.bjcj.web.naturalresources.function;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.naturalresources.function.FunctionData;
import com.bjcj.service.naturalresources.function.FunctionDataService;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2023/11/30 9:55 周四
 */
@Tag(name = "功能服务管理(应用服务分类)",description = "功能服务管理(应用服务分类)")
@ApiSupport(order = 56)
@RestController
@Slf4j
@RequestMapping(value = "/function_data")
public class FunctionDataController {

    @Resource
    private FunctionDataService functionDataService;

    @SaCheckPermission("sys:read")
    @Operation(summary = "应用服务分类树形")
    @GetMapping(value = "/treeList")
    public JsonResult treeList(){

        return functionDataService.treeList();
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "应用服务分类列表")
    @GetMapping(value = "/lists")
    public JsonResult lists(){

        return functionDataService.lists();
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "新增功能服务",
            operaType = OperaLogConstant.CREATE,
            operaDesc = "新增功能服务"
    )
    @Operation(summary = "新增功能服务")
    @PostMapping(value = "/save")
    public JsonResult save(@RequestBody @Validated FunctionData functionData){

        return JsonResult.success(functionDataService.save(functionData));
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "修改功能服务",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "修改功能服务"
    )
    @Operation(summary = "修改功能服务")
    @PutMapping(value = "/update")
    public JsonResult update(@RequestBody @Validated FunctionData functionData){

        return JsonResult.success(functionDataService.saveOrUpdate(functionData));
    }

    @SaCheckPermission("sys:del")
    @OperaLog(
            operaModule = "删除功能服务",
            operaType = OperaLogConstant.DELETE,
            operaDesc = "删除功能服务"
    )
    @Operation(summary = "删除功能服务")
    @DeleteMapping(value = "/del/{id}")
    public JsonResult del(@PathVariable Long id){

        return functionDataService.del(id);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "是否显示",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "是否显示"
    )
    @Operation(summary = "是否显示")
    @PostMapping(value = "/uptStatus")
    public JsonResult uptStatus(@RequestParam Long id, Boolean status){

        return functionDataService.uptStatus(id,status);
    }

    @Operation(summary = "导出")
    @GetMapping(value = "/export")
    public JsonResult exportJson(){

        return functionDataService.exportJson();
    }

    @Operation(summary = "导入")
    @PostMapping(value = "/import")
    public JsonResult importJson(@RequestParam("file") MultipartFile file){

        return functionDataService.importJson(file);
    }

}
