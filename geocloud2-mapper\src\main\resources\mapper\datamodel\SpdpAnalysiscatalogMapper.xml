<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.SpdpAnalysiscatalogMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.SpdpAnalysiscatalog">
    <!--@mbg.generated-->
    <!--@Table public.spdp_analysiscatalog-->
    <result column="id" jdbcType="CHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="displayorder" jdbcType="NUMERIC" property="displayorder" />
    <result column="parentid" jdbcType="CHAR" property="parentid" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="code" jdbcType="VARCHAR" property="code" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", displayorder, parentid, description, code
  </sql>
</mapper>