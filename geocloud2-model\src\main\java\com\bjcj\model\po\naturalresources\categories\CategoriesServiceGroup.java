package com.bjcj.model.po.naturalresources.categories;

import com.baomidou.mybatisplus.annotation.*;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/3/21  9:16
*/
/**
    * 数据服务分组表
    */
@Schema(description="数据服务分组表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "categories_service_group")
public class CategoriesServiceGroup implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 分组名称
     */
    @TableField(value = "group_name")
    @Schema(description="分组名称")
    @Size(max = 100,message = "分组名称max length should less than 100")
    @NotBlank(message = "分组名称is not blank")
    @RequestKeyParam(name = "groupName")
    private String groupName;

    /**
     * 显示名称
     */
    @TableField(value = "show_name")
    @Schema(description="显示名称")
    @Size(max = 100,message = "显示名称max length should less than 100")
    @NotBlank(message = "显示名称is not blank")
    private String showName;

    /**
     * 显示顺序
     */
    @TableField(value = "show_sort")
    @Schema(description="显示顺序")
    private Integer showSort;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "update_time",fill = FieldFill.UPDATE)
    @Schema(description="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField(value = "operator")
    @Schema(description="")
    @Size(max = 50,message = "max length should less than 50")
    private String operator;

    private static final long serialVersionUID = 1L;
}