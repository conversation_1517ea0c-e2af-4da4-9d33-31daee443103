package com.bjcj.model.po.safetyManage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
*@Author：qinyi
*@Date：2023/11/24  14:20
*/
@Schema(description="机构级别表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_level")
public class SafetyLevel implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    @NotNull(message = "主键不能为null")
    private Long id;

    /**
     * 级别名称
     */
    @TableField(value = "level_name")
    @Schema(description="级别名称")
    @Size(max = 50,message = "级别名称最大长度要小于 50")
    @NotBlank(message = "级别名称不能为空")
    private String levelName;

    private static final long serialVersionUID = 1L;
}