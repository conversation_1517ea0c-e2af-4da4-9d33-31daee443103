package com.bjcj.model.dto.fieldPlan;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/2/19  17:22
*/

@Data
public class FieldPlanLayerFieldDto implements Serializable {

    @Schema(description="id")
    @NotNull(message = "id is not null")
    private Long id;

    /**
     * 显示顺序
     */
    @Schema(description="显示顺序")
    private Integer showSort;

    /**
     * 是否显示
     */
    @Schema(description="是否显示")
    private Boolean isShow;

    /**
     * 映射方式
     */
    @Schema(description="映射方式")
    private String mappingMethod;

    /**
     * 参数
     */
    @Schema(description="参数")
    private String mappingParam;

    /**
     * 是否图层显示字段
     */
    @Schema(description="是否图层显示字段")
    private Boolean isLayerShow;

    private static final long serialVersionUID = 1L;
}