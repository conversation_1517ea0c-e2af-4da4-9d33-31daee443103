package com.bjcj.web.mobile;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.mobile.ContentConfigDto;
import com.bjcj.model.dto.mobile.ContentTbFieldDto;
import com.bjcj.service.mobile.ContentConfigService;
import com.bjcj.service.mobile.ContentTbFieldService;
import com.bjcj.service.mobile.PdaConfigPageService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 内容管理控制器
 *
 * <AUTHOR>
 * @create 2025-06-30-14:12
 */
@RestController
@RequestMapping("contentconfig")
@Tag(name = "内容管理")
@Validated
public class PdaContentConfigController {


    // 内容管理配置
    @Resource
    private ContentConfigService contentConfigService;

    // 模板数据
    @Resource
    private PdaConfigPageService pdaConfigPageService;

    // 内容管理表字段
    @Resource
    private ContentTbFieldService contentTbFieldService;

    @SaCheckPermission("sys:read")
    @Operation(summary = "获取配置的dataSource", description = "获取配置的dataSource")
    @ApiOperationSupport(order = 1)
    @GetMapping(value = "getDataSource")
    public JsonResult getDataSource() {
        return contentConfigService.getDataSource();
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "获取dataSource的所有表名", description = "获取dataSource的所有表名")
    @ApiOperationSupport(order = 2)
    @Parameters({
            @Parameter(name = "dbName", description = "数据源名称", required = true)
    })
    @GetMapping(value = "getTableName")
    public JsonResult getTableName(@RequestParam("dbName") String dbName) {
        return contentConfigService.getTableName(dbName);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "获取table的所有列名", description = "获取table的所有列名")
    @ApiOperationSupport(order = 3)
    @Parameters({
            @Parameter(name = "ds", description = "数据源名称", required = true),
            @Parameter(name = "tableName", description = "数据库表名", required = true)
    })
    @GetMapping(value = "getTableColumn")
    public JsonResult getTableColumn(@RequestParam("ds") String ds,
                                     @RequestParam("tableName") String tableName) {
        return contentConfigService.getTableColumn(ds, tableName);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "获取模板数据", description = "获取模板数据")
    @ApiOperationSupport(order = 4)
    @GetMapping(value = "getTemplateStyle")
    public JsonResult getTemplateStyle() {
        return pdaConfigPageService.getTemplateStyle();
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "获取所有的配置数据", description = "获取所有的配置数据")
    @ApiOperationSupport(order = 5)
    @GetMapping(value = "getContentConfigList")
    public JsonResult getContentConfigList() {
        return contentConfigService.getContentConfigList();
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "获取配置数据(分页配置和表字段)", description = "获取配置数据（分页配置和表字段）")
    @ApiOperationSupport(order = 6)
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "contentTbId", description = "配置表ID", required = false)
    })
    @GetMapping("getContentConfigByPage")
    public Object getContentConfigByPage(@RequestParam("current") Integer page,
                                         @RequestParam("size") Integer pageSize,
                                         @RequestParam(value = "contentTbId", required = false) String contentTbId) {
        return contentConfigService.pageData(page, pageSize, contentTbId);
    }

    @SaCheckPermission("sys:write")
    @Operation(summary = "添加模块配置", description = "添加模块配置")
    @ApiOperationSupport(order = 7)
    @PostMapping(value = "saveContentConfig")
    public JsonResult saveContentConfig(@RequestBody ContentConfigDto contentConfigDto) {
        return contentConfigService.saveContentConfig(contentConfigDto);
    }

    @SaCheckPermission("sys:write")
    @Operation(summary = "修改模块配置", description = "修改模块配置")
    @ApiOperationSupport(order = 8)
    @PutMapping(value = "updateContentConfig")
    public JsonResult updateContentConfig(@RequestBody ContentConfigDto contentConfigDto) {
        return contentConfigService.updateContentConfig(contentConfigDto);
    }

    @SaCheckPermission("sys:del")
    @Operation(summary = "删除模块配置", description = "删除模块配置")
    @ApiOperationSupport(order = 9)
    @DeleteMapping(value = "removeContentConfig/{id}")
    public JsonResult removeContentConfig(@PathVariable("id") String id) {
        return contentConfigService.removeContentConfig(id);
    }

    @SaCheckPermission("sys:del")
    @Operation(summary = "批量删除模块配置", description = "批量删除模块配置")
    @ApiOperationSupport(order = 10)
    @DeleteMapping(value = "removeContentConfigIds")
    public JsonResult removeContentConfigByIds(@RequestParam("deleteids") String deleteids) {
        return contentConfigService.removeContentConfigByIds(deleteids);
    }

    @SaCheckPermission("sys:write")
    @Operation(summary = "添加列表字段", description = "添加列表字段")
    @ApiOperationSupport(order = 11)
    @PostMapping(value = "saveContentTbField")
    public JsonResult saveContentTbField(@RequestBody ContentTbFieldDto contentTbFieldDto) {
        return contentTbFieldService.saveContentTbField(contentTbFieldDto);
    }

    @SaCheckPermission("sys:write")
    @Operation(summary = "修改列表字段", description = "修改列表字段")
    @ApiOperationSupport(order = 12)
    @PutMapping(value = "updateContentTbField")
    public JsonResult updateContentTbField(@RequestBody ContentTbFieldDto contentTbFieldDto) {
        return contentTbFieldService.updateContentTbField(contentTbFieldDto);
    }

    @SaCheckPermission("sys:del")
    @Operation(summary = "删除列表字段", description = "删除列表字段")
    @ApiOperationSupport(order = 13)
    @DeleteMapping(value = "removeContentTbField/{id}")
    public JsonResult removeContentTbField(@PathVariable("id") String id) {
        return contentTbFieldService.removeContentTbField(id);
    }

    @SaCheckPermission("sys:del")
    @Operation(summary = "批量删除列表字段", description = "批量删除列表字段")
    @ApiOperationSupport(order = 14)
    @DeleteMapping(value = "removeContentTbFieldIds")
    public JsonResult removeContentTbFieldIds(@RequestParam("deleteids") String deleteids) {
        return contentTbFieldService.removeContentTbFieldByIds(deleteids);
    }
}
