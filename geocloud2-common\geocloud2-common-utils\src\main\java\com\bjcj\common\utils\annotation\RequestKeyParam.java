package com.bjcj.common.utils.annotation;

/**
 * @Author：qinyi
 * @Date：2024/4/10 11:30
 * @description 加上这个注解可以将参数也设置为key，唯一key来源
 */

import java.lang.annotation.*;

@Target({ElementType.METHOD, ElementType.PARAMETER, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface RequestKeyParam {
    /**
     * key值名称
     *
     * @return 默认为空
     */
    String name() default "";
}
