package com.bjcj.model.vo.naturalresources.categories;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName CategoriesDataVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/11/27 10:56
 */
@Data
public class CategoriesDataVo implements Serializable {

    private Long id;

    @Schema(description="数据目录名称")
    private String name;

    @Schema(description="数据目录编码")
    private String code;

    @Schema(description="父级id")
    private Long parentId;

    @Schema(description="数据目录排序")
    private int displayOrder;

    @Schema(description="数据目录描述")
    private String description;

    @Schema(description="注册时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDateTime createTime;

    @Schema(description="显示状态")
    private Boolean status;

    @Schema(description="删除状态（0未删除，1已删除）")
    private int del;

    private List<CategoriesDataVo> children;
}
