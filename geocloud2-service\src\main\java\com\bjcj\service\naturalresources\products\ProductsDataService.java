package com.bjcj.service.naturalresources.products;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.products.ProductsDataMapper;
import com.bjcj.mapper.safetyManage.SafetyRoleMapper;
import com.bjcj.mapper.safetyManage.SafetyUserRoleMapper;
import com.bjcj.mapper.safetyManage.SysRoleProdMapper;
import com.bjcj.mapper.safetyManage.UserMapper;
import com.bjcj.model.po.naturalresources.products.ProductsData;
import com.bjcj.model.po.safetyManage.SafetyRole;
import com.bjcj.model.po.safetyManage.SafetyUserRole;
import com.bjcj.model.po.safetyManage.SysRoleProd;
import com.bjcj.model.po.safetyManage.User;
import com.bjcj.model.vo.naturalresources.categories.CategoriesParamVo;
import com.bjcj.model.vo.naturalresources.products.ProductDataVo;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/12/5 14:06 周二
 */
@Service
public class ProductsDataService extends ServiceImpl<ProductsDataMapper, ProductsData> {

    @Resource
    private ProductsDataMapper productsDataMapper;

    @Resource
    private SysRoleProdMapper sysRoleProdMapper;

    @Resource
    private SafetyUserRoleMapper safetyUserRoleMapper;
    @Resource
    private UserMapper userMapper;
    @Resource
    private SafetyRoleMapper safetyRoleMapper;

    public JsonResult lists(String id, String name, int current, int size){
        Page<ProductsData> page = new Page<>(current, size);
        LambdaQueryWrapper<ProductsData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductsData::getDel, 0);
        if(StringUtils.isNotBlank(name)){
            wrapper.like(ProductsData::getName, name).or().like(ProductsData::getSname, name);
        }
        wrapper.like(ProductsData::getCatalogue, id);
        IPage<ProductsData> list =  productsDataMapper.selectPage(page, wrapper);

        return JsonResult.success(list);
    }

    public JsonResult<IPage<ProductDataVo>> listsAuth(CategoriesParamVo categoriesParamVo){
        String name = categoriesParamVo.getName();
        String id = categoriesParamVo.getId();
        int auth = categoriesParamVo.getAuth();
        int current = categoriesParamVo.getCurrent();
        int size = categoriesParamVo.getSize();
        Long roleId = categoriesParamVo.getRoleId();

        Page<ProductDataVo> page = new Page<>(current, size);
        IPage<ProductDataVo> list = productsDataMapper.listSAuth(name,id,auth,roleId,page);
        return JsonResult.success(list);
    }

    public JsonResult del(List<Long> ids){

        LambdaUpdateWrapper<ProductsData> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProductsData::getDel, 1)
                .in(ProductsData::getId, ids);
        int result = productsDataMapper.update(wrapper);
        if(result < 0){
            return JsonResult.error();
        }

        return JsonResult.success();
    }

    public JsonResult uptStatus(Long id, Boolean status){
        LambdaUpdateWrapper<ProductsData> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProductsData::getStatus, status)
                .eq(ProductsData::getId, id);
        int result = productsDataMapper.update(wrapper);
        if(result != 1){
            return JsonResult.error();
        }
        return JsonResult.success();
    }

    public JsonResult uptAuth(Long id, int auth){
        LambdaUpdateWrapper<ProductsData> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProductsData::getAuthItype, auth)
                .eq(ProductsData::getId, id);
        int result = productsDataMapper.update(wrapper);
        if(result != 1){
            return JsonResult.error();
        }
        return JsonResult.success();
    }

    public JsonResult<Page<ProductsData>> pageDataSort(Page<ProductsData> pager, String searchStr, String productsId) {
        List<ProductsData> productsDataList = this.productsDataMapper.selectPageList((pager.getCurrent() - 1) * pager.getSize(),pager.getSize(),searchStr,productsId);
        Long count = this.productsDataMapper.selectListCount(searchStr,productsId);
        Page<ProductsData> page = new Page<>();
        page.setRecords(productsDataList);
        page.setTotal(count);
        return JsonResult.success(page);
    }

    public JsonResult<Page<User>> queryAuthUser(Page<User> pager, Long productsDataId) {
        LambdaQueryWrapper<SysRoleProd> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleProd::getProductId,productsDataId);
        List<SysRoleProd> sysRoleProdList = sysRoleProdMapper.selectList(wrapper);
        if(sysRoleProdList.isEmpty() || Objects.isNull(sysRoleProdList)){
            return JsonResult.success(new Page<User>());
        }
        List<Long> roleIds = sysRoleProdList.stream().map(SysRoleProd::getRoleId).toList();
        if(roleIds.isEmpty()) return JsonResult.success(new Page<User>());
        List<Long> userids = this.safetyUserRoleMapper.selectUserByRoleids(roleIds);
        LambdaQueryWrapper<User> wapper = new LambdaQueryWrapper<User>();
        wapper.in(User::getId,userids);
        Page<User> userPage = userMapper.selectPage(pager, wapper);
        userPage.getRecords().stream().forEachOrdered( user -> {
            List<SafetyRole> roleList = this.safetyRoleMapper.selectIdInByUserId(user.getId());
            user.setRoleList(roleList);
        });
        return JsonResult.success(userPage);
    }

    public JsonResult<Page<ProductsData>> serviceAuthList(Page<ProductsData> pager, Long userId, String searchStr, String productsId) {
        List<Long> roleIds = this.safetyUserRoleMapper.selectList(new LambdaQueryWrapper<SafetyUserRole>().eq(SafetyUserRole::getUserId,userId)).stream().map(SafetyUserRole::getRoleId).toList();
        if(Objects.isNull(roleIds) || roleIds.isEmpty()){
            return JsonResult.error("该人员未拥有角色");
        }
        List<Long> productsIds = this.sysRoleProdMapper.selectList(new LambdaQueryWrapper<SysRoleProd>().in(SysRoleProd::getRoleId,roleIds)).stream().map(SysRoleProd::getProductId).toList();
        if(productsIds.isEmpty()){
            return JsonResult.success(new Page<ProductsData>());
        }else{
            LambdaQueryWrapper<ProductsData> wrapper = new LambdaQueryWrapper<ProductsData>();
            if(Objects.nonNull(searchStr) && !"".equals(searchStr)){
                wrapper.and(wp -> wp.like(ProductsData::getName,searchStr).or().like(ProductsData::getSname,searchStr));
            }
            if(Objects.nonNull(productsId) &&!"".equals(productsId)){
                wrapper.eq(ProductsData::getCatalogue,productsId);
            }
            wrapper.in(ProductsData::getId,productsIds).eq(ProductsData::getDel,0).eq(ProductsData::getStatus,true).orderByDesc(ProductsData::getCreateTime);
            Page<ProductsData> productsDataPage = this.productsDataMapper.selectPage(pager, wrapper);
            Page<ProductsData> page = this.productsDataMapper.selectPage(pager, wrapper);
            page.getRecords().stream().forEachOrdered(data -> {
                data.setAuthFromRoleName(this.sysRoleProdMapper.selectRoleNameByProductId(data.getId()));
            });
            return JsonResult.success(page);
        }
    }
}
