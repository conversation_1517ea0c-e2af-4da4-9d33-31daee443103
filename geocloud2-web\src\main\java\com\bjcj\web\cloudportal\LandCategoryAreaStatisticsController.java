package com.bjcj.web.cloudportal;

import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.service.system.SysXzq14Service;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author：qinyi
 * @Date：2024/7/11 11:01
 */
@RestController
@RequestMapping("/Land_category_area_statistics")
@Tag(name = "云门户/地类统计")
@Validated
public class LandCategoryAreaStatisticsController {

    @Resource
    private SysXzq14Service sysXzq14Service;

    @GetMapping("/xzqdlList")
    @Operation(summary = "以行政区划统计列表", description = "以行政区划统计列表")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "code", description = "行政区code", required = true)
    })
    public JsonResult xzqdlList(@RequestParam("code") String code) {
        return this.sysXzq14Service.dlList(code);
    }

    @GetMapping("/xzqdlList2")
    @Operation(summary = "以行政区划统计列表2", description = "以行政区划统计列表2")
    @ApiOperationSupport(order = 11)
    @Parameters({
            @Parameter(name = "appName", description = "专题名称", required = true)
    })
    public JsonResult xzqdlList2(@RequestParam("appName") String appName) {
        return this.sysXzq14Service.dlList2(appName);
    }

    @GetMapping("/xzqdlAllList")
    @Operation(summary = "以行政区划统计总和", description = "以行政区划统计总和")
    @ApiOperationSupport(order = 2)
    @Parameters({
            @Parameter(name = "code", description = "行政区code", required = true)
    })
    public JsonResult xzqdlAllList(@RequestParam("code") String code) {
        return this.sysXzq14Service.dlAllList(code);
    }

    @GetMapping("/xzqdlAllList2")
    @Operation(summary = "以行政区划统计总和2", description = "以行政区划统计总和2")
    @ApiOperationSupport(order = 22)
    @Parameters({
            @Parameter(name = "appName", description = "专题名称", required = true)
    })
    public JsonResult xzqdlAllList2(@RequestParam("appName") String appName) {
        return this.sysXzq14Service.dlAllList2(appName);
    }

    @GetMapping("/dlList")
    @Operation(summary = "地类统计列表", description = "地类统计列表")
    @ApiOperationSupport(order = 3)
    @Parameters({
            @Parameter(name = "appName", description = "专题名称", required = true)
    })
    public JsonResult dlList(@RequestParam("appName") String appName) {
        return this.sysXzq14Service.dlListTwo(appName);
    }

}