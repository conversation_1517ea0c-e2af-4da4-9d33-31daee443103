package com.bjcj.common.core.config;

import com.bjcj.common.utils.properties.GeoCloud2Properties;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @date 2023/8/21 8:19
 */
@Configuration
public class FileUploadConfig implements WebMvcConfigurer {

    @Resource
    GeoCloud2Properties geoCloud2Properties;

    /**
     * SpringBoot 静态资源配置
     *
     * @param registry 注册类
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/file/**")
                .addResourceLocations("file:" + geoCloud2Properties.getFile().getUpload());
        registry.addResourceHandler("/log/**")
                .addResourceLocations("file:" + geoCloud2Properties.getFile().getLog());
    }
}
