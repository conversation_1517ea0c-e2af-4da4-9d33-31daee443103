package com.bjcj.model.po.platformManage.analysisPlan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/2/21  17:36
*/
@Schema(description="分析展示方案-字段明细配置表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "analysis_plan_field_detail")
public class AnalysisPlanFieldDetail implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 分析展示方案id
     */
    @TableField(value = "analysis_plan_id")
    @Schema(description="分析展示方案id")
    @NotNull(message = "分析展示方案idis not null")
    private Long analysisPlanId;

    /**
     * 字段名称
     */
    @TableField(value = "field_name")
    @Schema(description="字段名称")
    @Size(max = 50,message = "字段名称max length should less than 50")
    private String fieldName;

    /**
     * 显示名称
     */
    @TableField(value = "show_name")
    @Schema(description="显示名称")
    @Size(max = 50,message = "显示名称max length should less than 50")
    private String showName;

    /**
     * 宽度
     */
    @TableField(value = "field_width")
    @Schema(description="宽度")
    @Size(max = 10,message = "宽度max length should less than 10")
    private String fieldWidth;

    /**
     * 0否1是
     */
    @TableField(value = "is_locate")
    @Schema(description="0否1是")
    private String isLocate;

    /**
     * 映射方式
     */
    @TableField(value = "mapping_method")
    @Schema(description="映射方式")
    @Size(max = 50,message = "映射方式max length should less than 50")
    private String mappingMethod;

    /**
     * 映射参数
     */
    @TableField(value = "mapping_param")
    @Schema(description="映射参数")
    @Size(max = 255,message = "映射参数max length should less than 255")
    private String mappingParam;

    /**
     * 来源
     */
    @TableField(value = "source")
    @Schema(description="来源")
    @Size(max = 100,message = "来源max length should less than 100")
    private String source;

    /**
     * 0目录1字段
     */
    @TableField(value = "detail_type")
    @Schema(description="0目录1字段")
    private String detailType;

    private static final long serialVersionUID = 1L;
}