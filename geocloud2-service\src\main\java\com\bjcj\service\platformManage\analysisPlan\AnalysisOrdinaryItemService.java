package com.bjcj.service.platformManage.analysisPlan;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.categories.ResourceDataitemsMapper;
import com.bjcj.mapper.naturalresources.categories.ResourceServicesMapper;
import com.bjcj.mapper.naturalresources.resReview.UserServrReviewMapper;
import com.bjcj.mapper.platformManage.analysisPlan.AnalysisOrdinaryItemMapper;
import com.bjcj.mapper.platformManage.analysisPlan.AnalysisPlanMapper;
import com.bjcj.mapper.safetyManage.SafetyUserRoleMapper;
import com.bjcj.mapper.safetyManage.SysRoleCateMapper;
import com.bjcj.model.dto.analysisPlan.AnalysisAndOrdinaryItemDto;
import com.bjcj.model.dto.analysisPlan.AnalysisOrdinaryItemDto;
import com.bjcj.model.po.naturalresources.categories.ResourceServices2;
import com.bjcj.model.po.naturalresources.resReview.UserServrReview;
import com.bjcj.model.po.platformManage.analysisPlan.AnalysisOrdinaryItem;
import com.bjcj.model.po.platformManage.analysisPlan.AnalysisPlan;
import com.bjcj.model.po.safetyManage.SafetyUserRole;
import com.bjcj.model.po.safetyManage.SysRoleCate;
import com.bjcj.model.po.safetyManage.User;
import jakarta.annotation.Resource;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *@Author：qinyi
 *@Date：2024/6/7  10:26
*/
@Service
public class AnalysisOrdinaryItemService extends ServiceImpl<AnalysisOrdinaryItemMapper, AnalysisOrdinaryItem> {

    @Resource
    AnalysisOrdinaryItemMapper mapper;

    @Resource
    AnalysisPlanMapper analysisPlanMapper;

    @Resource
    ResourceServicesMapper resourceServicesMapper;

    @Resource
    UserServrReviewMapper userServrReviewMapper;

    @Resource
    SafetyUserRoleMapper safetyUserRoleMapper;

    @Resource
    SysRoleCateMapper sysRoleCateMapper;

    @Resource
    ResourceDataitemsMapper resourceDataitemsMapper;

    public JsonResult add(AnalysisAndOrdinaryItemDto dto) {
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        if(!StringUtils.isNotBlank(dto.getJsonPlan())) dto.setJsonPlan("");
        AnalysisPlan analysisPlan = new AnalysisPlan(){{
            setPlanName(dto.getPlanName());
            setRemark(dto.getRemark());
            setPlatformAppConfId(dto.getPlatformAppConfId());
            setPlanType(dto.getPlanType());
            setOperater(username);
            setCreateTime(LocalDateTime.now());
            setJsonPlan(dto.getJsonPlan());
        }};
        analysisPlanMapper.insert(analysisPlan);
        if(dto.getAnalysisOrdinaryItemDtoList().isEmpty() && Integer.parseInt(dto.getPlanType()) < 5){
            return JsonResult.error("分析项不能为空集合");
        }
        if(Integer.parseInt(dto.getPlanType()) < 5){
            dto.getAnalysisOrdinaryItemDtoList().forEach(item->{
                AnalysisOrdinaryItem ordinaryItem = BeanUtil.copyProperties(item,AnalysisOrdinaryItem.class);
                ordinaryItem.setAnalysisPlanId(analysisPlan.getId());
                ordinaryItem.setOperator(username);
                this.mapper.insert(ordinaryItem);
            });
        }
        return JsonResult.success();
    }

    public JsonResult edit(AnalysisAndOrdinaryItemDto dto) {
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        if(!StringUtils.isNotBlank(dto.getJsonPlan())) dto.setJsonPlan("");
        AnalysisPlan analysisPlan = new AnalysisPlan(){{
            setId(dto.getId());
            setPlanName(dto.getPlanName());
            setRemark(dto.getRemark());
            setPlatformAppConfId(dto.getPlatformAppConfId());
            setPlanType(dto.getPlanType());
            setOperater(username);
            setUpdateTime(LocalDateTime.now());
            setJsonPlan(dto.getJsonPlan());
        }};
        analysisPlanMapper.updateById(analysisPlan);
        if(!dto.getAnalysisOrdinaryItemDtoList().isEmpty()){
            //先删原来的再增新数据
            this.mapper.delete(new LambdaQueryWrapper<AnalysisOrdinaryItem>().eq(AnalysisOrdinaryItem::getAnalysisPlanId,dto.getId()));
            dto.getAnalysisOrdinaryItemDtoList().forEach(item->{
                AnalysisOrdinaryItem ordinaryItem = BeanUtil.copyProperties(item,AnalysisOrdinaryItem.class);
                ordinaryItem.setAnalysisPlanId(dto.getId());
                ordinaryItem.setOperator(username);
                this.mapper.insert(ordinaryItem);
            });
        }
        return JsonResult.success();
    }

    public JsonResult detail(Long id) {
        AnalysisPlan analysisPlan = this.analysisPlanMapper.selectById(id);
        List<AnalysisOrdinaryItem> list = this.mapper.selectList(new LambdaQueryWrapper<AnalysisOrdinaryItem>().eq(AnalysisOrdinaryItem::getAnalysisPlanId,id));
        List<AnalysisOrdinaryItemDto> list2 = new ArrayList<>();
        list.forEach(item->{
            AnalysisOrdinaryItemDto dto = BeanUtil.copyProperties(item,AnalysisOrdinaryItemDto.class);
            list2.add(dto);
        });
        return JsonResult.success(new AnalysisAndOrdinaryItemDto(){{
            setId(analysisPlan.getId());
            setPlanName(analysisPlan.getPlanName());
            setRemark(analysisPlan.getRemark());
            setPlanType(analysisPlan.getPlanType());
            setPlatformAppConfId(analysisPlan.getPlatformAppConfId());
            setAnalysisOrdinaryItemDtoList(list2);
        }});
    }

    public JsonResult del(Long id) {
        this.analysisPlanMapper.deleteById(id);
        this.mapper.delete(new LambdaQueryWrapper<AnalysisOrdinaryItem>().eq(AnalysisOrdinaryItem::getAnalysisPlanId,id));
        return JsonResult.success();
    }

    public JsonResult analysisOrdinaryItemlist(Long id) {
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        List<AnalysisOrdinaryItem> analysisOrdinaryItems = this.mapper.selectList(new LambdaQueryWrapper<AnalysisOrdinaryItem>().eq(AnalysisOrdinaryItem::getAnalysisPlanId, id));
        AnalysisPlan analysisPlan = this.analysisPlanMapper.selectById(id);
        //5是适配JSOE的,不需要权限验证----只验证数据服务的权限
        if(Integer.parseInt(analysisPlan.getPlanType()) > 4){
            analysisOrdinaryItems.forEach(item->{
                item.setResServiceAuthority(false);
                item.setFuncServiceAuthority(false);
                String resServiceId = item.getResServiceId();
                // String funcServiceId = item.getFuncServiceId();
                ResourceServices2 resourceServices = this.resourceServicesMapper.selectOnes(resServiceId);
                // ResourceServices2 resourceServices2 = this.resourceServicesMapper.selectOnes(funcServiceId);
                ResourceServices2 resourceServices21 = this.setAuthority(resourceServices, userId,"res");
                // ResourceServices2 resourceServices22 = this.setAuthority(resourceServices2, principal,"func");
                if(resourceServices21.getHasPermission().equals("1") || resourceServices21.getHasPermission().equals("2")){
                    item.setResServiceAuthority(true);
                }
                // if(resourceServices22.getHasPermission().equals("1") || resourceServices22.getHasPermission().equals("2")){
                //     item.setFuncServiceAuthority(true);
                // }
            });
            analysisPlan.setAnalysisOrdinaryItemList(analysisOrdinaryItems);
            return JsonResult.success(analysisPlan);
        }
        analysisOrdinaryItems.forEach(item->{
            item.setResServiceAuthority(false);
            item.setFuncServiceAuthority(false);
            String resServiceId = item.getResServiceId();
            String funcServiceId = item.getFuncServiceId();
            ResourceServices2 resourceServices = this.resourceServicesMapper.selectOnes(resServiceId);
            ResourceServices2 resourceServices2 = this.resourceServicesMapper.selectOnes(funcServiceId);
            ResourceServices2 resourceServices21 = this.setAuthority(resourceServices, userId,"res");
            ResourceServices2 resourceServices22 = this.setAuthority(resourceServices2, userId,"func");
            if(resourceServices21.getHasPermission().equals("1") || resourceServices21.getHasPermission().equals("2")){
                item.setResServiceAuthority(true);
            }
            if(resourceServices22.getHasPermission().equals("1") || resourceServices22.getHasPermission().equals("2")){
                item.setFuncServiceAuthority(true);
            }
        });
        return JsonResult.success(analysisOrdinaryItems);
    }

    private ResourceServices2 setAuthority(ResourceServices2 cataservice, Long loginuserid,String type) {
        //写入当前数据权限信息
        UserServrReview userServrReview = this.userServrReviewMapper.selectByUserIdAndResId(loginuserid, cataservice.getId());
        //查询用户是否超管  //todo 超管查看所有数据的权限也去掉,现在专题里都需要申请
        // Boolean admin = checkIsAdmin.checkIsAdmin(principal.getId());
        // if(admin) cataservice.setHasPermission("2");
        // else{
        if(Objects.isNull(userServrReview)) cataservice.setHasPermission("0");
        else{
            if(userServrReview.getReviewStatus() == 0) cataservice.setHasPermission("3");
            if(userServrReview.getReviewStatus() == 1) cataservice.setHasPermission("1");
            if(userServrReview.getReviewStatus() == 2) cataservice.setHasPermission("0");
        }

        //如果父级datainfo中的类型是公开  设置为2  无需申请
        //安全0 公开1 私有2
        if(type.equals("res")){
            int authItype = Integer.parseInt(String.valueOf(this.resourceDataitemsMapper.selectById(cataservice.getDataitemid()).getAuthoritytype()));
            // int authItype = cataservice.getAuthoritytype();

            if(authItype == 1) cataservice.setHasPermission("2");
        }else{
            int authItype = cataservice.getAuthoritytype();
            if(authItype == 1) cataservice.setHasPermission("2");
        }

            // }
        List<Long> roleIds = new ArrayList<>();
        List<SafetyUserRole> userRoles = this.safetyUserRoleMapper.selectList(new LambdaQueryWrapper<SafetyUserRole>().eq(SafetyUserRole::getUserId, loginuserid));
        if(!userRoles.isEmpty()){
            roleIds.addAll(userRoles.stream().map(item -> item.getRoleId()).toList());
            List<SysRoleCate> sysRoleCateList = this.sysRoleCateMapper.selectList(new LambdaQueryWrapper<SysRoleCate>().in(SysRoleCate::getRoleId, roleIds));
            List<String> cataIds = sysRoleCateList.stream().map(item -> item.getCateId()).toList();
            if(cataIds.contains(cataservice.getId())){
                cataservice.setHasPermission("1");
            }
        }
        return cataservice;
    }

    /**
     * 只验证数据服务权限,不验证功能服务权限,适配JSOE
     */
    private ResourceServices2 setAuthority2(ResourceServices2 cataservice, User principal,String type) {
        Long loginuserid = principal.getId();
        //写入当前数据权限信息
        UserServrReview userServrReview = this.userServrReviewMapper.selectByUserIdAndResId(loginuserid, cataservice.getId());
        //查询用户是否超管  //todo 超管查看所有数据的权限也去掉,现在专题里都需要申请
        // Boolean admin = checkIsAdmin.checkIsAdmin(principal.getId());
        // if(admin) cataservice.setHasPermission("2");
        // else{
        if(Objects.isNull(userServrReview)) cataservice.setHasPermission("0");
        else{
            if(userServrReview.getReviewStatus() == 0) cataservice.setHasPermission("3");
            if(userServrReview.getReviewStatus() == 1) cataservice.setHasPermission("1");
            if(userServrReview.getReviewStatus() == 2) cataservice.setHasPermission("0");
        }

        //如果父级datainfo中的类型是公开  设置为2  无需申请
        //安全0 公开1 私有2
        int authItype = cataservice.getAuthoritytype();
        if(authItype == 1) cataservice.setHasPermission("2");

        List<Long> roleIds = new ArrayList<>();
        List<SafetyUserRole> userRoles = this.safetyUserRoleMapper.selectList(new LambdaQueryWrapper<SafetyUserRole>().eq(SafetyUserRole::getUserId, loginuserid));
        if(!userRoles.isEmpty()){
            roleIds.addAll(userRoles.stream().map(item -> item.getRoleId()).toList());
            List<SysRoleCate> sysRoleCateList = this.sysRoleCateMapper.selectList(new LambdaQueryWrapper<SysRoleCate>().in(SysRoleCate::getRoleId, roleIds));
            List<String> cataIds = sysRoleCateList.stream().map(item -> item.getCateId()).toList();
            if(cataIds.contains(cataservice.getId())){
                cataservice.setHasPermission("1");
            }
        }
        return cataservice;
    }



}
