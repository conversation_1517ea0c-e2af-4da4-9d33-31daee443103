package com.bjcj.web.naturalresources.categories;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.naturalresources.categories.CategoriesPhysics;
import com.bjcj.model.po.naturalresources.categories.MetadataTables;
import com.bjcj.service.naturalresources.categories.CategoriesPhysicsService;
import com.bjcj.service.naturalresources.categories.MetadataTablesService;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/29 10:59 周三
 */
@Tag(name = "数据服务管理(物理数据)", description = "数据服务管理(物理数据)")
@ApiSupport(order = 53)
@RestController
@Slf4j
@RequestMapping(value = "/categories_physics")
public class CategoriesPhysicsController {

    @Resource
    private CategoriesPhysicsService categoriesPhysicsService;

    @Resource
    private MetadataTablesService metadataTablesService;

    /*@Operation(summary = "物理数据列表")
    @GetMapping(value = "/lists")
    public JsonResult lists(int current, int size){

        return categoriesPhysicsService.lists(current,size);
    }

    @Operation(summary = "注册物理数据")
    @PostMapping(value = "/save")
    public JsonResult save(@RequestBody @Validated CategoriesPhysics categoriesPhysics){

        return JsonResult.success(categoriesPhysicsService.save(categoriesPhysics));
    }*/

    @Operation(summary = "根据id查询详情")
    @GetMapping(value = "/findById/{id}")
    public JsonResult<List<CategoriesPhysics>> findById(@PathVariable Long id){

        return categoriesPhysicsService.findById(id);
    }

    @Operation(summary = "修改物理数据")
    @PutMapping(value = "/update")
    public JsonResult update(@RequestBody @Validated CategoriesPhysics categoriesPhysics){

        return JsonResult.success(categoriesPhysicsService.saveOrUpdate(categoriesPhysics));
    }

    @Operation(summary = "删除物理数据")
    @DeleteMapping(value = "/del")
    public JsonResult del(@RequestParam Long id){

        return categoriesPhysicsService.del(id);
    }


    @SaCheckPermission("sys:read")
    @Operation(summary = "1.根据id查询详情")
    @GetMapping(value = "/findById2/{id}")
    public JsonResult<List<MetadataTables>> findById(@PathVariable String id){
        return metadataTablesService.findById(id);
    }


    @OperaLog(operaModule = "修改物理数据",operaType = OperaLogConstant.UPDATE,operaDesc = "修改物理数据")
    @SaCheckPermission("sys:write")
    @Operation(summary = "2.修改物理数据")
    @PutMapping(value = "/update2")
    public JsonResult update2(@RequestBody @Validated MetadataTables metadataTables){

        return JsonResult.success(metadataTablesService.saveOrUpdate(metadataTables));
    }


    @OperaLog(operaModule = "删除物理数据",operaType = OperaLogConstant.DELETE,operaDesc = "删除物理数据")
    @SaCheckPermission("sys:del")
    @Operation(summary = "3.删除物理数据")
    @DeleteMapping(value = "/del2")
    @Parameters({
            @Parameter(name = "tableid", description = "物理数据id", required = true)
    })
    public JsonResult del2(@RequestParam String tableid){
        return metadataTablesService.removeById(tableid) ? JsonResult.success() : JsonResult.error();
    }

}
