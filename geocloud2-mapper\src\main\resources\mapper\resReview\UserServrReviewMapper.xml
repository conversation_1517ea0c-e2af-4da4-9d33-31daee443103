<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.naturalresources.resReview.UserServrReviewMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.naturalresources.resReview.UserServrReview">
    <!--@mbg.generated-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="service_id" jdbcType="BIGINT" property="serviceId" />
    <result column="service_type" jdbcType="VARCHAR" property="serviceType" />
    <result column="review_status" jdbcType="INTEGER" property="reviewStatus" />
    <result column="review_user_id" jdbcType="BIGINT" property="reviewUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="review_time" jdbcType="TIMESTAMP" property="reviewTime" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="apply_time" jdbcType="VARCHAR" property="applyTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="review_remark" jdbcType="VARCHAR" property="reviewRemark" />
  </resultMap>

  <select id="selectPageData" resultType="com.bjcj.model.po.naturalresources.resReview.UserServrReview">
    select  usr.*,cs.displayname as sname,su.nick_name,sd.dept_name from user_servr_review usr
    left join resource_services cs on cs.id=usr.service_id
    left join sys_user su on su.id=usr.user_id
    left join sys_dept sd on sd.id=su.dept_id
    where 1=1
    <if test="searchStr!=null and searchStr!=''">
      and usr.title like CONCAT('%', #{searchStr}, '%')
    </if>
<!--    <if test="reviewStatus!=null and reviewStatus!=''">-->
      and usr.review_status = #{reviewStatus}
<!--    </if>-->
    <if test="serviceType!=null and serviceType!=''">
      and usr.service_type = #{serviceType}
    </if>
    <if test="startTime!=null and endTime!=null">
      and ( usr.create_time between #{startTime} and #{endTime})
    </if>
    order by usr.create_time ${orders}
    LIMIT #{size} OFFSET #{current}
  </select>

  <select id="selectPageDataCount" resultType="int">
    select count(*) from (
    select  usr.*,cs.displayname as sname,su.nick_name,sd.dept_name from user_servr_review usr
    left join resource_services cs on cs.id=usr.service_id
    left join sys_user su on su.id=usr.user_id
    left join sys_dept sd on sd.id=su.dept_id
    where 1=1
    <if test="searchStr!=null and searchStr!=''">
      and usr.title like CONCAT('%', #{searchStr}, '%')
    </if>
<!--    <if test="reviewStatus!=null and reviewStatus!=''">-->
      and usr.review_status = #{reviewStatus}
<!--    </if>-->
    <if test="serviceType!=null and serviceType!=''">
      and usr.service_type = #{serviceType}
    </if>
    <if test="startTime!=null and endTime!=null">
      and ( usr.create_time between #{startTime} and #{endTime})
    </if>
    ) c
  </select>

  <select id="selectByUserIdAndResId" resultType="com.bjcj.model.po.naturalresources.resReview.UserServrReview">
    select * from user_servr_review where user_id = #{loginuserid} and service_id = #{id}
  </select>

  <select id="selectByServiceId" resultType="com.bjcj.model.po.naturalresources.resReview.UserServrReview2">
    select * from user_servr_review where service_id=#{id}
  </select>

  <select id="selectListMap" resultType="com.bjcj.model.po.naturalresources.resReview.UserServrReview2">
    select * from user_servr_review where user_id=#{userId} and service_id=#{serverid}
  </select>

</mapper>