<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.safetyManage.RolePermsMapper">
    <resultMap id="BaseResultMap" type="com.bjcj.model.po.safetyManage.RolePerms">
        <!--@mbg.generated-->
        <!--@Table public.sys_role_perms-->
        <id column="perms_id" jdbcType="BIGINT" property="permsId"/>
        <id column="role_id" jdbcType="BIGINT" property="roleId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        perms_id, role_id
    </sql>

    <insert id="insertBatch">
        insert into sys_role_perms (role_id, perms_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.roleId}, #{item.permsId})
        </foreach>
    </insert>


</mapper>