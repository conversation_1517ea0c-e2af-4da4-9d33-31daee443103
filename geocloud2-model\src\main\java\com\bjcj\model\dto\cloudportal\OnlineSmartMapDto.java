package com.bjcj.model.dto.cloudportal;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/7/3  15:39
*/
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "online_smart_map")
public class OnlineSmartMapDto implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(description="")
    @Size(max = 50,message = "max length should less than 50")
    private String id;

    @TableField(value = "name")
    @Schema(description="")
    @Size(max = 100,message = "max length should less than 100")
    private String name;

    @TableField(value = "json_str")
    @Schema(description="")
    @Size(max = 4000,message = "max length should less than 4000")
    private String jsonStr;

    @TableField(value = "descstr")
    @Schema(description="描述")
    @Size(max = 255,message = "max length should less than 255")
    private JSONObject descstr;

    private static final long serialVersionUID = 1L;
}