<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.cloudportal.UserCollectedMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.cloudportal.UserCollected">
    <!--@mbg.generated-->
    <!--@Table public.user_collected-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="resouce_type" jdbcType="VARCHAR" property="resouceType" />
    <result column="res_id" jdbcType="BIGINT" property="resId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, user_id, resouce_type, res_id, create_time
  </sql>

  <select id="selectPageData" resultMap="BaseResultMap">
    select uc.*,cs.name,cs.displayname as sname,cs.url as address,cs.resourcetype as fwitype,cs.thumbnailurl as imgurl,cs.dataitemid as parent_id,cdi.publish_institution_name from user_collected uc left join resource_services cs on uc.res_id=cs.id left join resource_dataitems cdi on cs.dataitemid=cdi.id
    where 1=1
      and uc.resouce_type=#{resouceType}
      and uc.user_id=#{userId}
    <if test="searchStr!=null and searchStr!=''">
      and (cs.name like concat('%', #{searchStr}, '%') or cs.displayname like concat('%', #{searchStr}, '%'))
    </if>
    order by uc.create_time desc
    LIMIT #{size} OFFSET #{current}
  </select>

  <select id="selectPageDataCount" resultType="int">
    select count(*) from (
    select uc.*,cs.name,cs.displayname as sname,cs.url as address,cs.resourcetype as fwitype,cs.thumbnailurl as imgurl,cs.dataitemid as parent_id,cdi.publish_institution_name from user_collected uc left join resource_services cs on uc.res_id=cs.id left join resource_dataitems cdi on cs.dataitemid=cdi.id
    where 1=1
    and uc.resouce_type=#{resouceType}
    and uc.user_id=#{userId}
    <if test="searchStr!=null and searchStr!=''">
      and (cs.name like concat('%', #{searchStr}, '%') or cs.displayname like concat('%', #{searchStr}, '%'))
    </if>
    ) c
  </select>

  <select id="selectCountByUserIdAndResId" resultType="int">
    select count(*) from user_collected where user_id=#{id} and res_id=#{resId}
  </select>

  <select id="selectOneData" resultType="com.bjcj.model.po.cloudportal.UserCollected2">
    select * from user_collected where user_id=#{loginuserid} and res_id=#{id}
    </select>
</mapper>