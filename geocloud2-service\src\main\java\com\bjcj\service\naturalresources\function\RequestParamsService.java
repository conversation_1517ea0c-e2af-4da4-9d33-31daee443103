package com.bjcj.service.naturalresources.function;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.function.RequestParamsMapper;
import com.bjcj.model.po.naturalresources.function.RequestParams;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/4 15:09 周一
 */
@Service
public class RequestParamsService extends ServiceImpl<RequestParamsMapper, RequestParams> {

    @Resource
    private RequestParamsMapper requestParamMapper;

    public JsonResult del(Long id){
        LambdaUpdateWrapper<RequestParams> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(RequestParams::getDel, 1)
                .eq(RequestParams::getId, id);
        int result = requestParamMapper.update(wrapper);
        return JsonResult.success(result);
    }

    public JsonResult<RequestParams> findById(Long id){
        RequestParams requestParams = requestParamMapper.selectById(id);
        return JsonResult.success(requestParams);
    }

    public JsonResult<List<RequestParams>> findByParentId(Long parentid){
        LambdaQueryWrapper<RequestParams> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RequestParams::getParentId, parentid)
                .eq(RequestParams::getDel, 0);
        List<RequestParams> list = requestParamMapper.selectList(wrapper);
        return JsonResult.success(list);
    }

}
