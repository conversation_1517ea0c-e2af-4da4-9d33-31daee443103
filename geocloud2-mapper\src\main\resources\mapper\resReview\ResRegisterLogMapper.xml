<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.naturalresources.resReview.ResRegisterLogMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.naturalresources.resReview.ResRegisterLog">
    <!--@mbg.generated-->
    <!--@Table public.res_register_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="review_status" jdbcType="BOOLEAN" property="reviewStatus" />
    <result column="res_type" jdbcType="VARCHAR" property="resType" />
    <result column="res_name" jdbcType="VARCHAR" property="resName" />
    <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime" />
    <result column="review_time" jdbcType="TIMESTAMP" property="reviewTime" />
    <result column="publish_user_name" jdbcType="VARCHAR" property="publishUserName" />
    <result column="review_remark" jdbcType="VARCHAR" property="reviewRemark" />
    <result column="res_id" jdbcType="BIGINT" property="resId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, review_status, res_type, res_name, publish_time, review_time, publish_user_name, 
    review_remark, res_id
  </sql>
</mapper>