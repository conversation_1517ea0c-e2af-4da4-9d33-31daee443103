package com.bjcj.web.platformConf;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.platformConf.ServiceEngineConfDto;
import com.bjcj.model.po.platformConf.ServiceEngineConf;
import com.bjcj.service.platformConf.ServiceEngineConfService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
* 服务引擎配置表(public.service_engine_conf)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/service_engine_conf")
@Tag(name = "8服务引擎配置")
@Validated
public class ServiceEngineConfController {
    /**
    * 服务对象
    */
    @Resource
    private ServiceEngineConfService serviceEngineConfService;

    @SaCheckPermission("sys:read")
    @Operation(summary = "服务引擎-列表", description = "服务引擎-列表")
    @ApiOperationSupport(order = 1)
    @GetMapping("/page")
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "searchStr", description = "名称搜索", required = false)
    })
    public JsonResult<Page<ServiceEngineConf>> page(@RequestParam("current") Integer page,
                                                    @RequestParam("size") Integer pageSize,
                                                    @RequestParam(value = "searchStr",required = false) String searchStr){
        Page<ServiceEngineConf> pager = new Page<>(page, pageSize);
        return this.serviceEngineConfService.pageDataSort(pager, searchStr);
    }

    @OperaLog(operaModule = "服务引擎-新增/编辑",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "服务引擎-新增/编辑")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑服务引擎", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "addOrEdit")
    public JsonResult addOrEdit(@Validated @RequestBody ServiceEngineConfDto dto){
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        ServiceEngineConf serviceEngineConf = BeanUtil.copyProperties(dto, ServiceEngineConf.class);
        serviceEngineConf.setOperator(username);
        return this.serviceEngineConfService.saveOrUpdate(serviceEngineConf) ? JsonResult.success() : JsonResult.error();
    }

    @OperaLog(operaModule = "服务引擎-删除",operaType = OperaLogConstant.DELETE,operaDesc = "服务引擎-删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "服务引擎-删除", description = "服务引擎-删除")
    @ApiOperationSupport(order = 3)
    public JsonResult delete(@PathVariable("id") Long id){
        return this.serviceEngineConfService.removeById(id) ? JsonResult.success() : JsonResult.error();
    }
}
