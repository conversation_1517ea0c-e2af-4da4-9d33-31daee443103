<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.system.SysXzq14Mapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.system.SysXzq14">
    <!--@Table sys_xzq14-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="rural_code" jdbcType="VARCHAR" property="ruralCode" />
    <result column="level" jdbcType="SMALLINT" property="level" />
    <result column="p_id" jdbcType="BIGINT" property="pId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, code, "name", rural_code, "level", p_id
  </sql>

  <select id="getParentIdList" resultType="java.lang.Long">
    WITH RECURSIVE parents AS (
      SELECT id, p_id
      FROM sys_xzq14
      WHERE id = #{id}
      UNION ALL
      SELECT c.id, c.p_id
      FROM sys_xzq14 c
             INNER JOIN parents p ON c.id = p.p_id
    )
    SELECT id FROM parents;
    </select>

  <select id="getOnes" resultType="java.lang.String">
    select code from sys_xzq14 where id = #{xzqCode}
    </select>
</mapper>