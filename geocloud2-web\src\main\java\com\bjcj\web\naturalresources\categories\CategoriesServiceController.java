package com.bjcj.web.naturalresources.categories;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.fileResManage.FilesetCataserviceDto;
import com.bjcj.model.po.naturalresources.categories.*;
import com.bjcj.model.po.naturalresources.fileResManage.FileSetInfo;
import com.bjcj.model.vo.naturalresources.categories.CategoriesParamVo;
import com.bjcj.model.vo.naturalresources.categories.CategoriesServiceVo;
import com.bjcj.model.vo.naturalresources.categories.ResourceServicesVo;
import com.bjcj.service.naturalresources.categories.*;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
@Tag(name = "数据服务管理(数据服务)", description = "数据服务管理(数据服务)")
@RestController
@Slf4j
@RequestMapping(value = "/categories_service")
@Validated
public class CategoriesServiceController {

    @Resource
    private CategoriesServiceService categoriesServiceService;

    @Resource
    private FilesetCataserviceService filesetCataserviceService;

    @Resource
    private CategoriesServiceGroupService categoriesServiceGroupService;

    @Resource
    private ResourceServicesService resourceServicesService;

    @Resource
    private CategoriesDataService categoriesDataService;


    @SaCheckPermission("sys:read")
    @Operation(summary = "安全管理角色授权数据项信息列表")
    @PostMapping(value = "/lists")
    public JsonResult<IPage<CategoriesServiceVo>> lists(@RequestBody CategoriesParamVo categoriesParamVo){

        return categoriesServiceService.lists(categoriesParamVo);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "1.安全管理角色授权数据项信息列表")
    @PostMapping(value = "/lists2")
    public JsonResult<IPage<ResourceServicesVo>> lists2(@RequestBody CategoriesParamVo categoriesParamVo){
        return resourceServicesService.lists(categoriesParamVo);
    }

    /*@Operation(summary = "注册数据服务")
    @PostMapping(value = "/save")
    public JsonResult save(@RequestBody @Validated CategoriesService categoriesService){

        return JsonResult.success(categoriesServiceService.save(categoriesService));
    }*/

    @SaCheckPermission("sys:read")
    @OperaLog(
            operaModule = "根据id查询详情",
            operaType = OperaLogConstant.LOOK,
            operaDesc = "根据id查询详情"
    )
    @Operation(summary = "根据id查询详情")
    @GetMapping(value = "/findById/{id}")
    public JsonResult<List<ResourceServices>> findById(@PathVariable String id){

        return categoriesServiceService.findById(id);
    }

    @SaCheckPermission("sys:read")
    @OperaLog(
            operaModule = "根据id查询详情",
            operaType = OperaLogConstant.LOOK,
            operaDesc = "根据id查询详情"
    )
    @Operation(summary = "2.根据id查询详情")
    @GetMapping(value = "/findById2/{id}")
    public JsonResult<List<ResourceServices>> findById2(@PathVariable String id){

        return resourceServicesService.findById(id);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "修改数据服务",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "修改数据服务"
    )
    @Operation(summary = "修改数据服务")
    @PutMapping(value = "/update")
    @RequestLock(prefix = "update")
    public JsonResult update(@RequestBody @Validated CategoriesService categoriesService){
        return JsonResult.success(categoriesServiceService.saveOrUpdate(categoriesService));
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "修改数据服务",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "修改数据服务"
    )
    @Operation(summary = "3.修改数据服务")
    @PutMapping(value = "/update2")
    @RequestLock(prefix = "update2")
    @Transactional(rollbackFor = Exception.class)
    public JsonResult update2(@RequestBody @Validated ResourceServices resourceServices){
        resourceServices.setRepairdate(LocalDateTime.now());
        return this.resourceServicesService.updateData(resourceServices);
    }

    @SaCheckPermission("sys:del")
    @OperaLog(
            operaModule = "删除数据服务",
            operaType = OperaLogConstant.DELETE,
            operaDesc = "删除数据服务"
    )
    @Operation(summary = "删除数据服务")
    @DeleteMapping(value = "/del")
    public JsonResult del(@RequestParam String id){
        return categoriesServiceService.del(id);
    }

    @SaCheckPermission("sys:del")
    @OperaLog(
            operaModule = "删除数据服务",
            operaType = OperaLogConstant.DELETE,
            operaDesc = "删除数据服务"
    )
    @Operation(summary = "4.删除数据服务")
    @DeleteMapping(value = "/del2")
    public JsonResult del2(@RequestParam String id){
        return resourceServicesService.removeById(id) ? JsonResult.success() : JsonResult.error();
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "修改数据服务状态",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "修改数据服务状态"
    )
    @Operation(summary = "修改数据服务状态")
    @PostMapping(value = "/uptStatus")
    public JsonResult uptStatus(@RequestParam String id, Integer status){

        return categoriesServiceService.uptStatus(id,status);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "修改数据服务状态",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "修改数据服务状态"
    )
    @Operation(summary = "5.修改数据服务状态")
    @PostMapping(value = "/uptStatus2")
    public JsonResult uptStatus2(@RequestParam String id, Integer status){
        return resourceServicesService.uptStatus(id,status);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "是否显示数据服务",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "是否显示数据服务"
    )
    @Operation(summary = "是否显示数据服务")
    @PostMapping(value = "/updShow")
    public JsonResult updShow(@RequestParam String id, Boolean show){

        return categoriesServiceService.updShow(id,show);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "是否显示数据服务",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "是否显示数据服务"
    )
    @Operation(summary = "6.是否显示数据服务")
    @PostMapping(value = "/updShow2")
    public JsonResult updShow2(@RequestParam String id, Boolean show){
        return resourceServicesService.updShow(id,show);
    }

    @OperaLog(operaModule = "数据服务关联文件",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "数据服务关联文件")
    @SaCheckPermission("sys:write")
    @PostMapping("/cataRelationFile")
    @Operation(summary = "数据服务关联文件(改版直接用)", description = "数据服务关联文件(改版直接用)")
    @ApiOperationSupport(order = 8)
    public JsonResult cataRelationFile(@Validated @RequestBody FilesetCataserviceDto dto){
        List<FilesetCataservice> saveList = new ArrayList<>();
        if(dto.getFileSetInfoIdList().size()>0){
            for(int i=0;i<dto.getFileSetInfoIdList().size();i++){
                FilesetCataservice filesetCataservice = new FilesetCataservice();
                filesetCataservice.setCataServiceId(dto.getCataServiceId());
                filesetCataservice.setFileSetInfoId(dto.getFileSetInfoIdList().get(i));
                saveList.add(filesetCataservice);
            }
        }
        return this.filesetCataserviceService.saveBatch(saveList) ? JsonResult.success() : JsonResult.error();
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "根据数据服务查询已关联的文件列表(改版直接用)", description = "根据数据服务查询已关联的文件列表(改版直接用)")
    @ApiOperationSupport(order = 9)
    @GetMapping("/exitsFileList")
    @Parameters({
            @Parameter(name = "resourceServicesId", description = "数据服务id", required = true)
    })
    public JsonResult<List<FileSetInfo>> exitsFileList(@RequestParam("resourceServicesId") String resourceServicesId){
        return this.filesetCataserviceService.exitsFileList(resourceServicesId);
    }

    @OperaLog(operaModule = "批量删除关联文件",operaType = OperaLogConstant.DELETE,operaDesc = "批量删除关联文件")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delBatch")
    @Operation(summary = "批量删除关联文件(改版直接用)", description = "批量删除关联文件(改版直接用)")
    @Parameters({
            @Parameter(name = "ids", description = "关联的文件集ids逗号拼接", required = true),
            @Parameter(name = "resourceServicesId", description = "数据服务id", required = true)
    })
    @ApiOperationSupport(order = 10)
    @RequestLock(prefix = "delBatch")
    public JsonResult delBatch(@RequestParam("ids") String ids,@RequestParam("resourceServicesId") String resourceServicesId){
        return this.filesetCataserviceService.delBatch(ids,resourceServicesId);
    }

    @Operation(summary = "服务分组列表", description = "服务分组列表")
    @SaCheckPermission("sys:read")
    @ApiOperationSupport(order = 11)
    @GetMapping("/serviceGroupList")
    public JsonResult<List<CategoriesServiceGroup>> serviceGroupList(){
        return JsonResult.success(categoriesServiceGroupService.list());
    }


    @Operation(summary = "服务分组新增/编辑", description = "编辑传id新增不传")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEditGroup")
    @OperaLog(operaModule = "新增/编辑服务分组",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "新增/编辑服务分组")
    @ApiOperationSupport(order = 12)
    @RequestLock(prefix = "addOrEditGroup")
    public JsonResult addOrEditGroup(@Validated @RequestBody CategoriesServiceGroup po){
        return categoriesServiceGroupService.addOrEditGroup(po);
    }

    @OperaLog(operaModule = "删除服务分组",operaType = OperaLogConstant.DELETE,operaDesc = "删除服务分组")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/deleteGroup/{id}")
    @Operation(summary = "删除服务分组", description = "删除服务分组")
    @ApiOperationSupport(order = 13)
    public JsonResult deleteGroup(@PathVariable("id") Long id){
        return categoriesServiceGroupService.removeById(id) ? JsonResult.success() : JsonResult.error();
    }

    @Operation(summary = "根据名称查询服务(云门户树用)", description = "根据名称查询服务(云门户树用)")
    @SaCheckPermission("sys:read")
    @ApiOperationSupport(order = 14)
    @GetMapping("/serviceListByName")
    @Parameters({
            @Parameter(name = "name", description = "名称或显示名称", required = true)
    })
    public JsonResult<List<ResourceServices2>> serviceListByName(@RequestParam("name") String name){
        return this.categoriesDataService.serviceListByName(name);
    }

}
