package com.bjcj.model.po.platformConf;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/2/28  14:39
*/
/**
    * 服务引擎配置表
    */
@Schema(description="服务引擎配置表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "service_engine_conf")
public class ServiceEngineConf implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 服务器集群名称
     */
    @TableField(value = "server_colony_name")
    @Schema(description="服务器集群名称")
    @Size(max = 100,message = "服务器集群名称max length should less than 100")
    @NotBlank(message = "服务器集群名称is not blank")
    private String serverColonyName;

    /**
     * 集群类型
     */
    @TableField(value = "server_colony_type")
    @Schema(description="集群类型")
    @Size(max = 50,message = "集群类型max length should less than 50")
    private String serverColonyType;

    /**
     * 服务用户名
     */
    @TableField(value = "server_user_name")
    @Schema(description="服务用户名")
    @Size(max = 50,message = "服务用户名max length should less than 50")
    private String serverUserName;

    /**
     * 服务密码
     */
    @TableField(value = "server_password")
    @Schema(description="服务密码")
    @Size(max = 50,message = "服务密码max length should less than 50")
    private String serverPassword;

    /**
     * token请求url
     */
    @TableField(value = "token_url")
    @Schema(description="token请求url")
    @Size(max = 255,message = "token请求urlmax length should less than 255")
    private String tokenUrl;

    /**
     * 基地址url
     */
    @TableField(value = "base_url")
    @Schema(description="基地址url")
    @Size(max = 255,message = "基地址urlmax length should less than 255")
    @NotBlank(message = "基地址urlis not blank")
    private String baseUrl;

    /**
     * 是否启动0否1是
     */
    @TableField(value = "is_run")
    @Schema(description="是否启动0否1是")
    private String isRun;

    /**
     * 服务器集群描述
     */
    @TableField(value = "remark")
    @Schema(description="服务器集群描述")
    @Size(max = 255,message = "服务器集群描述max length should less than 255")
    private String remark;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "update_time",fill = FieldFill.UPDATE)
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField(value = "operator")
    @Schema(description="")
    @Size(max = 20,message = "max length should less than 20")
    private String operator;

    private static final long serialVersionUID = 1L;
}