package com.bjcj.model.po.safetyManage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
*@Author：qinyi
*@Date：2023/11/23  10:43
*/
@Schema(description="机构表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_institution")
public class SafetyInstitution {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

    /**
     * 机构名称
     */
    @TableField(value = "institution_name")
    @Schema(description="机构名称")
    private String institutionName;

    /**
     * 级别id
     */
    @TableField(value = "level")
    @Schema(description="级别")
    private String level;

    /**
     * 机构简称
     */
    @TableField(value = "institution_short_name")
    @Schema(description="机构简称")
    private String institutionShortName;

    /**
     * 显示顺序
     */
    @TableField(value = "show_sort")
    @Schema(description="显示顺序")
    private Short showSort;

    /**
     * 行政区域编码
     */
    @TableField(value = "xzq_code")
    @Schema(description="行政区域编码")
    private String xzqCode;

    /**
     * 传真
     */
    @TableField(value = "fax_num")
    @Schema(description="传真")
    private String faxNum;

    /**
     * 联系电话
     */
    @TableField(value = "phone_num")
    @Schema(description="联系电话")
    private String phoneNum;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description="创建时间")
    private String createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @Schema(description="更新时间")
    private String updateTime;

    /**
     * 最后操作人
     */
    @TableField(value = "operater")
    @Schema(description="最后操作人")
    private String operater;

    @TableField(value = "parent_id")
    @Schema(description = "父级id")
    private Long parentId;
}