<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.ore.OreWatchInfoMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.ore.OreWatchInfo">
    <!--@mbg.generated-->
    <!--@Table ore_watch_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="level" jdbcType="VARCHAR" property="level" />
    <result column="ore_number" jdbcType="VARCHAR" property="oreNumber" />
    <result column="ore_name" jdbcType="VARCHAR" property="oreName" />
    <result column="sub_ore_name" jdbcType="VARCHAR" property="subOreName" />
    <result column="ore_species" jdbcType="VARCHAR" property="oreSpecies" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="mining_way" jdbcType="VARCHAR" property="miningWay" />
    <result column="ore_certificate_number" jdbcType="VARCHAR" property="oreCertificateNumber" />
    <result column="procedure_status" jdbcType="VARCHAR" property="procedureStatus" />
    <result column="scheme" jdbcType="BOOLEAN" property="scheme" />
    <result column="year_scheme" jdbcType="BOOLEAN" property="yearScheme" />
    <result column="2024_repair" jdbcType="BOOLEAN" property="2024Repair" />
    <result column="2024_duty" jdbcType="BOOLEAN" property="2024Duty" />
    <result column="fund_account" jdbcType="BOOLEAN" property="fundAccount" />
    <result column="fund_extract" jdbcType="BOOLEAN" property="fundExtract" />
    <result column="cost_take" jdbcType="BOOLEAN" property="costTake" />
    <result column="execute_situation" jdbcType="VARCHAR" property="executeSituation" />
    <result column="disobey_situation" jdbcType="VARCHAR" property="disobeySituation" />
    <result column="use_cost" jdbcType="VARCHAR" property="useCost" />
    <result column="three_rate_index" jdbcType="VARCHAR" property="threeRateIndex" />
    <result column="chamincha_situation" jdbcType="VARCHAR" property="chaminchaSituation" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "level", ore_number, ore_name, sub_ore_name, ore_species, "status", mining_way, 
    ore_certificate_number, procedure_status, scheme, year_scheme, 2024_repair, 2024_duty, 
    fund_account, fund_extract, cost_take, execute_situation, disobey_situation, use_cost, 
    three_rate_index, chamincha_situation
  </sql>
</mapper>