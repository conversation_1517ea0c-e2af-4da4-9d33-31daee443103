<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.platformConf.GisResConfMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.platformConf.GisResConf">
    <!--@mbg.generated-->
    <!--@Table public.gis_res_conf-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="website_name" jdbcType="VARCHAR" property="websiteName" />
    <result column="website_base_url" jdbcType="VARCHAR" property="websiteBaseUrl" />
    <result column="webadaptor_server" jdbcType="CHAR" property="webadaptorServer" />
    <result column="website_user_name" jdbcType="VARCHAR" property="websiteUserName" />
    <result column="website_password" jdbcType="VARCHAR" property="websitePassword" />
    <result column="cache_url" jdbcType="VARCHAR" property="cacheUrl" />
    <result column="website_state" jdbcType="CHAR" property="websiteState" />
    <result column="owner_name" jdbcType="VARCHAR" property="ownerName" />
    <result column="owner_unit" jdbcType="VARCHAR" property="ownerUnit" />
    <result column="owner_phone" jdbcType="VARCHAR" property="ownerPhone" />
    <result column="owner_email" jdbcType="VARCHAR" property="ownerEmail" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, website_name, website_base_url, webadaptor_server, website_user_name, website_password, 
    cache_url, website_state, owner_name, owner_unit, owner_phone, owner_email, create_time, 
    update_time, "operator"
  </sql>
</mapper>