<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.system.SysLoginLogMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.system.SysLoginLog">
    <!--@Table public.sys_login_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="device" jdbcType="VARCHAR" property="device" />
  </resultMap>
  <sql id="Base_Column_List">
    id, username, user_id, create_time, ip, device
  </sql>
</mapper>