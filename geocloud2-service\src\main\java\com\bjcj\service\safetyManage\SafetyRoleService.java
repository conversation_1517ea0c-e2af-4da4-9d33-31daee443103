package com.bjcj.service.safetyManage;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.safetyManage.*;
import com.bjcj.model.dto.safetyManage.SafetyRoleDto;
import com.bjcj.model.po.safetyManage.*;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 *@Author：qinyi
 *@Date：2023/11/29  8:49
*/
@Service
public class SafetyRoleService extends ServiceImpl<SafetyRoleMapper, SafetyRole> {

    @Resource
    SafetyRoleMapper safetyRoleMapper;

    @Resource
    SafetyUserRoleMapper safetyUserRoleMapper;

    @Resource
    SafetyInstitutionMapper safetyInstitutionMapper;

    @Resource
    SafetyDeptMapper safetyDeptMapper;

    @Resource
    UserMapper userMapper;

    public JsonResult saveData(SafetyRoleDto dto) {
        SafetyRole safetyRole = BeanUtil.copyProperties(dto,SafetyRole.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        safetyRole.setOperater(username);
        LambdaQueryWrapper<SafetyRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(safetyRole.getRoleName()),SafetyRole::getRoleName,safetyRole.getRoleName());
        queryWrapper.eq(StringUtils.isNotEmpty(safetyRole.getRoleType()),SafetyRole::getRoleType,safetyRole.getRoleType());
        List<SafetyRole> safetyRoleList = this.safetyRoleMapper.selectList(queryWrapper);
        if(!safetyRoleList.isEmpty() && Objects.isNull(safetyRole.getId()))
            return JsonResult.build(400,"数据重复!");
        //add
        if(Objects.isNull(safetyRole.getId())){
            safetyRole.setCreateTime(sdf.format(new Date()));
            return safetyRoleMapper.insert(safetyRole) > 0 ? JsonResult.success() : JsonResult.error();
        }else{//edit
            safetyRole.setUpdateTime(sdf.format(new Date()));
            return safetyRoleMapper.updateById(safetyRole) > 0 ? JsonResult.success() : JsonResult.error();
        }

    }

    public JsonResult<Page<SafetyRole>> pageDataSort(Page<SafetyRole> pager, String searchStr) {
        LambdaQueryWrapper<SafetyRole> queryWrapper = new LambdaQueryWrapper<>();
        if(StringUtils.isNotEmpty(searchStr))
            queryWrapper.and(wrapper -> wrapper.like(SafetyRole::getRoleName,searchStr).or().like(SafetyRole::getRoleType,searchStr));
        queryWrapper.orderByAsc(SafetyRole::getShowSort);
        return JsonResult.success(safetyRoleMapper.selectPage(pager,queryWrapper));
    }

    public JsonResult delData(Long id) {
        this.safetyUserRoleMapper.delete(new LambdaQueryWrapper<SafetyUserRole>().eq(SafetyUserRole::getRoleId,id));
        this.safetyRoleMapper.deleteById(id);
        return JsonResult.success();
    }


    public JsonResult<List<SafetyInstitution>> allInstitutionList() {
        return JsonResult.success(this.safetyInstitutionMapper.selectList(null));
    }

    public JsonResult<List<SafetyDept>> queryTopDeptByInstitution(Long institutionId,Long deptId) {
        LambdaQueryWrapper<SafetyDept> queryWrapper = new LambdaQueryWrapper<>();
        if(Objects.nonNull(institutionId)){
            queryWrapper.eq(SafetyDept::getParentInstitutionId,institutionId);
            queryWrapper.eq(SafetyDept::getParentDeptId,0L);
        }
        if(Objects.nonNull(deptId))
            queryWrapper.eq(SafetyDept::getParentDeptId,deptId);
        return JsonResult.success(this.safetyDeptMapper.selectList(queryWrapper));
    }

    public JsonResult<List<User>> queryUsersByDeptId(Long deptId) {
        return JsonResult.success(this.userMapper.selectList(new LambdaQueryWrapper<User>().eq(User::getDeptId,deptId)));
    }

    public JsonResult<Page<SafetyUserRole>> queryExistingUserRole(Page<SafetyUserRole> pager,Long roleId) {
        List<SafetyUserRole> safetyUserRoleList = this.safetyUserRoleMapper.selectListByIdsLeftJoinUser((pager.getCurrent() - 1) * pager.getSize(),pager.getSize(),roleId);
        Long count = this.safetyUserRoleMapper.selectListByIdsLeftJoinUserCount(roleId);
        Page<SafetyUserRole> page = new Page<SafetyUserRole>();
        page.setRecords(safetyUserRoleList);
        page.setTotal(count);
        return JsonResult.success(page);
    }

    public JsonResult<List<User>> searchUser(String searchStr) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        if(StringUtils.isNotEmpty(searchStr))
            queryWrapper.and(wrapper -> wrapper.like(User::getNickName,searchStr).or().like(User::getUsername,searchStr));
        List<User> users = this.userMapper.selectList(queryWrapper);
        users.forEach(user -> {
            user.setInstitution(this.safetyInstitutionMapper.selectById(user.getInstitutionId()).getInstitutionName());
            user.setDeptName(this.safetyDeptMapper.selectById(user.getDeptId()).getDeptName());
        });
        return JsonResult.success(users);
    }

    public JsonResult saveRoleUsers(Long roleId, String userIds) {
        //先删除原数据 再保存新数据
        if(StringUtils.isNotEmpty(userIds)){
            String[] userIdsArr = userIds.split(",");
            this.safetyUserRoleMapper.delete(new LambdaQueryWrapper<SafetyUserRole>().eq(SafetyUserRole::getRoleId,roleId));
            for (String userId : userIdsArr) {
                SafetyUserRole safetyUserRole = new SafetyUserRole();
                safetyUserRole.setRoleId(roleId);
                safetyUserRole.setUserId(Long.valueOf(userId));
                this.safetyUserRoleMapper.insert(safetyUserRole);
            }
        }
        return JsonResult.success();
    }

    public JsonResult<List<SafetyRole>> selectAllRoles() {
        return JsonResult.success(this.safetyRoleMapper.selectList(null));
    }

    public List<Long> queryUserRoleId(Long userId) {
        return this.safetyUserRoleMapper.queryUserRoleId(userId);
    }
}
