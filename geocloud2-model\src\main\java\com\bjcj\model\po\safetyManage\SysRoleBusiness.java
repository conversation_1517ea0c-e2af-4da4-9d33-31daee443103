package com.bjcj.model.po.safetyManage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 *@Author：qinyi
 *@Date：2023/12/14  9:52
*/
/**
    * 业务数据角色关联表
    */
@Schema(description="业务数据角色关联表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "public.sys_role_business")
public class SysRoleBusiness implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    @NotNull(message = "主键不能为null")
    private Long id;

    /**
     * 业务数据id
     */
    @TableField(value = "business_id")
    @Schema(description="业务数据id")
    @NotNull(message = "业务数据id不能为null")
    private Long businessId;

    /**
     * 角色id
     */
    @TableField(value = "role_id")
    @Schema(description="角色id")
    @NotNull(message = "角色id不能为null")
    private Long roleId;

    private static final long serialVersionUID = 1L;
}