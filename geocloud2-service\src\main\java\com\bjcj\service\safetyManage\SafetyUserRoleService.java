package com.bjcj.service.safetyManage;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.safetyManage.SafetyUserRoleMapper;
import com.bjcj.model.po.safetyManage.SafetyUserRole;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *@Author：qinyi
 *@Date：2023/11/29  11:37
*/
@Service
public class SafetyUserRoleService extends ServiceImpl<SafetyUserRoleMapper, SafetyUserRole> {

    @Resource
    SafetyUserRoleMapper safetyUserRoleMapper;

    public JsonResult<List<SafetyUserRole>> queryExistingUserRoles(Long userId) {
        List<SafetyUserRole> safetyUserRoles = safetyUserRoleMapper.queryExistingUserRoles(userId);
        return JsonResult.success(safetyUserRoles);
    }

    public JsonResult saveRoleUsers(Long userId, String roleIds) {
        //先清空当前操作用户所有的角色关联数据  再储存本次设置的角色信息
        safetyUserRoleMapper.deleteByUserId(userId);
        if (roleIds!= null &&!"".equals(roleIds)) {
            String[] roleIdArr = roleIds.split(",");
            for (String roleId : roleIdArr) {
                SafetyUserRole safetyUserRole = new SafetyUserRole();
                safetyUserRole.setUserId(userId);
                safetyUserRole.setRoleId(Long.valueOf(roleId));
                safetyUserRoleMapper.insert(safetyUserRole);
            }
        }
        return JsonResult.success();
    }
}
