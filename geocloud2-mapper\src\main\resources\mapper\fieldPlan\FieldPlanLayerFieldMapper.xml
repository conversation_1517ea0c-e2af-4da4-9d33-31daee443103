<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.platformManage.fieldPlan.FieldPlanLayerFieldMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.platformManage.fieldPlan.FieldPlanLayerField">
    <!--@mbg.generated-->
    <!--@Table public.field_plan_layer_field-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
    <result column="standard_name" jdbcType="VARCHAR" property="standardName" />
    <result column="field_type_id" jdbcType="BIGINT" property="fieldTypeId" />
    <result column="field_length" jdbcType="INTEGER" property="fieldLength" />
    <result column="field_accuracy" jdbcType="INTEGER" property="fieldAccuracy" />
    <result column="decimal_places" jdbcType="INTEGER" property="decimalPlaces" />
    <result column="is_must" jdbcType="BOOLEAN" property="isMust" />
    <result column="is_null" jdbcType="BOOLEAN" property="isNull" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="statistics_type_id" jdbcType="BIGINT" property="statisticsTypeId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="data_layer_id" jdbcType="BIGINT" property="dataLayerId" />
    <result column="field_plan_id" jdbcType="BIGINT" property="fieldPlanId" />
    <result column="is_sort" jdbcType="BOOLEAN" property="isSort" />
    <result column="show_sort" jdbcType="INTEGER" property="showSort" />
    <result column="is_show" jdbcType="BOOLEAN" property="isShow" />
    <result column="mapping_method" jdbcType="VARCHAR" property="mappingMethod" />
    <result column="mapping_param" jdbcType="VARCHAR" property="mappingParam" />
    <result column="is_layer_show" jdbcType="BOOLEAN" property="isLayerShow" />
  </resultMap>

  <update id="editData" parameterType="com.bjcj.model.dto.fieldPlan.FieldPlanLayerFieldDto">
      update field_plan_layer_field
      <trim prefix="set" suffixOverrides=",">
          <if test="showSort != null and showSort!=''">
              show_sort = #{showSort},
          </if>
          <if test="isShow != null">
              is_show = #{isShow,jdbcType=BOOLEAN},
          </if>
          <if test="mappingMethod != null and mappingMethod!=''">
              mapping_method = #{mappingMethod},
          </if>
          <if test="mappingParam != null and mappingParam!=''">
              mapping_param = #{mappingParam},
          </if>
          <if test="isLayerShow != null">
              is_layer_show = #{isLayerShow,jdbcType=BOOLEAN},
          </if>
      </trim>
      where id = #{id}
    </update>
</mapper>