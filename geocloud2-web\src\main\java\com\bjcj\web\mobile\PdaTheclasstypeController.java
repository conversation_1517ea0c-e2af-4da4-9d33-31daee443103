package com.bjcj.web.mobile;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.mobile.PdaTheclasstype ;
import com.bjcj.service.mobile.PdaTheclasstypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/PdaTheclasstype")
@Tag(name = "App模块菜单配置（模版查询）")
public class PdaTheclasstypeController {

    @Autowired
    PdaTheclasstypeService pdaTheclasstypeService;

    @SaCheckPermission("sys:read")
    @Operation(summary = "查询所有模版")
    @GetMapping("/allEnable")
    public JsonResult<List<PdaTheclasstype>> getAllEnable(){

        return JsonResult.success(pdaTheclasstypeService.getEnable());
    }
}
