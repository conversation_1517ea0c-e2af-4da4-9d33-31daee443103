<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.SpdpQueryMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.SpdpQuery">
    <!--@mbg.generated-->
    <!--@Table public.spdp_query-->
    <result column="queryid" jdbcType="CHAR" property="queryid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="displayname" jdbcType="VARCHAR" property="displayname" />
    <result column="querymodelid" jdbcType="VARCHAR" property="querymodelid" />
    <result column="params" jdbcType="VARCHAR" property="params" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    queryid, "name", displayname, querymodelid, params
  </sql>
</mapper>