<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.MetadataTablestructurefieldsMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.MetadataTablestructurefields">
    <!--@mbg.generated-->
    <!--@Table public.metadata_tablestructurefields-->
    <id column="fieldid" jdbcType="CHAR" property="fieldid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="fieldtype" jdbcType="NUMERIC" property="fieldtype" />
    <result column="length" jdbcType="NUMERIC" property="length" />
    <result column="precision" jdbcType="NUMERIC" property="precision" />
    <result column="scale" jdbcType="NUMERIC" property="scale" />
    <result column="displayname" jdbcType="VARCHAR" property="displayname" />
    <result column="fielddomainid" jdbcType="CHAR" property="fielddomainid" />
    <result column="tablestructureid" jdbcType="CHAR" property="tablestructureid" />
    <result column="isnullable" jdbcType="BOOLEAN" property="isnullable" />
    <result column="required" jdbcType="BOOLEAN" property="required" />
    <result column="defaultvalue" jdbcType="VARCHAR" property="defaultvalue" />
    <result column="checkexpression" jdbcType="VARCHAR" property="checkexpression" />
    <result column="dbfieldtype" jdbcType="VARCHAR" property="dbfieldtype" />
    <result column="displayorder" jdbcType="NUMERIC" property="displayorder" />
    <result column="standardname" jdbcType="VARCHAR" property="standardname" />
    <result column="statisticscategory" jdbcType="NUMERIC" property="statisticscategory" />
    <result column="dimensiontype" jdbcType="NUMERIC" property="dimensiontype" />
    <result column="representunit" jdbcType="VARCHAR" property="representunit" />
    <result column="timeunit" jdbcType="VARCHAR" property="timeunit" />
    <result column="dimensionfieldmapping" jdbcType="VARCHAR" property="dimensionfieldmapping" />
    <result column="issort" jdbcType="BOOLEAN" property="issort" />
    <result column="sortord" jdbcType="VARCHAR" property="sortord" />
    <result column="isvisiable" jdbcType="BOOLEAN" property="isvisiable" />
    <result column="dimensionid" jdbcType="CHAR" property="dimensionid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    fieldid, "name", code, description, fieldtype, "length", "precision", "scale", displayname, 
    fielddomainid, tablestructureid, isnullable, required, defaultvalue, checkexpression, 
    dbfieldtype, displayorder, standardname, statisticscategory, dimensiontype, representunit, 
    timeunit, dimensionfieldmapping, issort, sortord, isvisiable, dimensionid
  </sql>
</mapper>