package com.bjcj.service.naturalresources.function;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.function.RequestInfoMapper;
import com.bjcj.model.po.naturalresources.function.RequestInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/12/4 15:02 周一
 */
@Service
public class RequestInfoService extends ServiceImpl<RequestInfoMapper, RequestInfo> {

    @Resource
    private RequestInfoMapper requestInfoMapper;

    public JsonResult del(Long id){
        LambdaUpdateWrapper<RequestInfo> wrapper = new LambdaUpdateWrapper();
        wrapper.set(RequestInfo::getDel, 1)
                .eq(RequestInfo::getId, id);
        int result = requestInfoMapper.update(wrapper);
        return JsonResult.success(result);
    }

}
