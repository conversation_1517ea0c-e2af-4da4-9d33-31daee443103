package com.bjcj.model.po.mobile;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_mobile_sudoconfig")
@Schema(description = "根模块信息表")
public class PdaSudoconfig implements Serializable {

    @TableId(type = IdType.INPUT)
    private String pid;

    private String name;

    private String remark;

    @TableField(value = "imgpath")
    private String imgPath;

    @TableField(value = "isdisplay")
    private String isDisplay;

    @TableField(value = "menuname")
    private String menuname;

    @TableField(value = "phonetype")
    private String phoneType;

    @TableField(value = "sysname")
    private String sysName;

    @TableField(exist = false)
    private String className;

    @TableField(exist = false)
    private String titleImageUrl;

    @TableField(value = "isapproval")
    private String isApproval;

    @TableField(value = "mobilephone")
    private String mobilePhone;

    private String width;

    private String height;

    @TableField(value = "numfileds")
    private String numFileds;

    @TableField(exist = false)
    private String key;

    @TableField(value = "columnsize")
    private String columnSize;

    @TableField(value = "issystem")
    private String isSystem;

    @TableField(exist = false)
    private List<PdaSubconfig> children;

    @TableField(exist = false)
    private String subid;


}
