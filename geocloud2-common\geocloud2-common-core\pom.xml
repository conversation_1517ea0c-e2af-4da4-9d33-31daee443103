<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bjcj</groupId>
        <artifactId>geocloud2-common</artifactId>
        <version>0.0.1</version>
    </parent>

    <artifactId>geocloud2-common-core</artifactId>
    <packaging>jar</packaging>

    <name>geocloud2-common-core</name>
    <url>http://maven.apache.org</url>

    <dependencies>
        <dependency>
            <groupId>com.bjcj</groupId>
            <artifactId>geocloud2-common-utils</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjcj</groupId>
            <artifactId>geocloud2-mapper</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>backport-util-concurrent</groupId>
            <artifactId>backport-util-concurrent-java12</artifactId>
            <version>3.1</version>
        </dependency>

        <!-- Sa-Token 权限认证, 在线文档：https://sa-token.cc/ -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot3-starter</artifactId>
        </dependency>

        <!-- Sa-Token 插件：整合SSO -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-sso</artifactId>
        </dependency>

        <!-- Sa-Token 整合redis (使用jackson序列化方式) -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-redis-jackson</artifactId>
        </dependency>

        <!-- 提供Redis连接池 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>

        <!-- Sa-Token插件：权限缓存与业务缓存分离 -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-alone-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-jwt</artifactId>
        </dependency>


    </dependencies>

</project>
