package com.bjcj.model.dto.analysisPlan;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


/**
 *@Author：qinyi
 *@Date：2024/6/7  10:26
*/

/**
    * 分析项表
    */
@Schema(description="分析项表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "analysis_ordinary_item")
public class AnalysisOrdinaryItemDto implements Serializable {
    @Schema(description="分析项id")
    private Long id;

    /**
     * 分析项名称
     */
    @Schema(description="分析项名称")
    @Size(max = 100,message = "分析项名称max length should less than 100")
    @NotBlank(message = "分析项名称is not blank")
    private String name;

    /**
     * 分析方案id
     */
    @Schema(description="分析方案id")
    @NotNull(message = "分析方案idis not null")
    private Long analysisPlanId;

    /**
     * 数据服务id
     */
    @Schema(description="数据服务id")
    @Size(max = 36,message = "数据服务idmax length should less than 36")
    @NotBlank(message = "数据服务idis not blank")
    private String resServiceId;

    /**
     * 功能服务id
     */
    @Schema(description="功能服务id")
    @Size(max = 36,message = "功能服务idmax length should less than 36")
    @NotBlank(message = "功能服务idis not blank")
    private String funcServiceId;

    /**
     * 字段展示方案id
     */
    @Schema(description="字段展示方案id")
    @NotNull(message = "字段展示方案idis not null")
    private String fieldPlanId;

    /**
     * 最后操作人
     */
    @TableField(value = "operator")
    @Schema(description="最后操作人")
    @Size(max = 50,message = "最后操作人max length should less than 50")
    private String operator;

    @TableField(value = "create_time")
    @Schema(description="")
    private Date createTime;

    @TableField(value = "update_time")
    @Schema(description="")
    private Date updateTime;

    @TableField(value = "analysis_plan_name")
    @Schema(description="分析方案名称")
    private String analysisPlanName;

    @TableField(value = "res_service_name")
    @Schema(description="数据服务名称")
    private String resServiceName;

    @TableField(value = "func_service_name")
    @Schema(description="功能服务名称")
    private String funcServiceName;

    @TableField(value = "field_plan_name")
    @Schema(description="字段展示方案名称")
    private String fieldPlanName;

    private static final long serialVersionUID = 1L;
}