package com.bjcj.model.po.safetyManage;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2023/12/5  9:32
*/
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_role_perms")
public class RolePerms implements Serializable {
    /**
     * 权限id
     */
    @Schema(description="权限id")
    @NotNull(message = "权限id不能为null")
    private Long permsId;

    /**
     * 角色id
     */
    @Schema(description="角色id")
    @NotNull(message = "角色id不能为null")
    private Long roleId;

    @Schema(description="专题id")
    private Long specialPlanId;

    private static final long serialVersionUID = 1L;
}