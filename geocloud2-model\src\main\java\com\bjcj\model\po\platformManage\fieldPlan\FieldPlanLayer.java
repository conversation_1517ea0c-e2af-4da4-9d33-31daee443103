package com.bjcj.model.po.platformManage.fieldPlan;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/2/19  17:21
*/
/**
    * 字段展示方案图层表
    */
@Schema(description="字段展示方案图层表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "field_plan_layer")
public class FieldPlanLayer implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(description="")
    private String id;

    /**
     * 名称
     */
    @TableField(value = "name")
    @Schema(description="名称")
    @Size(max = 100,message = "名称max length should less than 100")
    @NotBlank(message = "名称is not blank")
    private String name;

    /**
     * 显示名称
     */
    @TableField(value = "show_name")
    @Schema(description="显示名称")
    @Size(max = 100,message = "显示名称max length should less than 100")
    private String showName;

    /**
     * 编码
     */
    @TableField(value = "code")
    @Schema(description="编码")
    @Size(max = 50,message = "编码max length should less than 50")
    private String code;

    /**
     * 是否必须
     */
    @TableField(value = "is_must")
    @Schema(description="是否必须")
    @NotNull(message = "是否必须is not null")
    private Boolean isMust;

    /**
     * 类型
     */
    @TableField(value = "layer_type_id")
    @Schema(description="类型")
    @NotNull(message = "类型is not null")
    private Long layerTypeId;

    /**
     * 描述
     */
    @TableField(value = "remark")
    @Schema(description="描述")
    @Size(max = 255,message = "描述max length should less than 255")
    private String remark;

    /**
     * 字段展示方案id
     */
    @TableField(value = "field_plan_id")
    @Schema(description="字段展示方案id")
    @NotNull(message = "字段展示方案idis not null")
    private Long fieldPlanId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 最后操作人
     */
    @TableField(value = "operator")
    @Schema(description="最后操作人")
    @Size(max = 20,message = "最后操作人max length should less than 20")
    private String operator;

    private static final long serialVersionUID = 1L;
}