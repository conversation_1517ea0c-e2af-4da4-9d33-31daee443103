package com.bjcj.service.datamodel;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.FieldType;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.datamodel.*;
import com.bjcj.model.dto.datamodel.MetadataTablestructurefieldsDto;
import com.bjcj.model.dto.datamodel.MetadataTablestructuresAllDto;
import com.bjcj.model.dto.datamodel.MetadataTablestructuresDto;
import com.bjcj.model.po.datamodel.*;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.util.*;

/**
 *@Author：qinyi
 *@Date：2024/1/4  14:45
*/
@Service
public class DbManageService extends ServiceImpl<DbManageMapper, DbManage> {

    Logger logger = LoggerFactory.getLogger(DbManageService.class);

    @Resource
    DbManageMapper dbManageMapper;

    @Resource
    DataLayerMapper dataLayerMapper;

    @Resource
    DataLayerFieldMapper dataLayerFieldMapper;

    @Resource
    DataLayerService dataLayerService;

    @Resource
    DataLayerFieldService dataLayerFieldService;

    @Resource
    MetadataTablestructuresMapper metadataTablestructuresMapper;

    @Resource
    MetadataTablestructurefieldsMapper metadataTablestructurefieldsMapper;

    @Resource
    MetadataTablestructuresService metadataTablestructuresService;

    @Resource
    MetadataTablestructurefieldsService metadataTablestructurefieldsService;

    @Resource
    DataLayerFieldTypeService dataLayerFieldTypeService;

    public JsonResult<Page<DbManage>> pageDataSort(Page<DbManage> pager, String searchStr) {
        LambdaQueryWrapper<DbManage> queryWrapper = new LambdaQueryWrapper<DbManage>();
        if(searchStr!=null && !"".equals(searchStr)){
            queryWrapper.like(DbManage::getName,searchStr);
        }
        queryWrapper.orderByDesc(DbManage::getCreateTime);
        return JsonResult.success(dbManageMapper.selectPage(pager, queryWrapper));
    }


    public JsonResult datasetList(Long dbId) {
        DbManage dbManage = dbManageMapper.selectById(dbId);
        try {
            Class.forName(dbManage.getDbVersion());
            Connection conn= DriverManager.getConnection(dbManage.getExampleUrl(),dbManage.getUsername(),dbManage.getPassword());
            logger.info("db连接成功");
            Statement stmt=conn.createStatement();
            String query = "select *  from GDB_ITEMTYPES where NAME='Feature Dataset'";
            ResultSet rs = stmt.executeQuery(query);
            String UUID_TYPE = "";
            while (rs.next()){
                UUID_TYPE = rs.getString("UUID");
            }
            String query2 = "select * from GDB_ITEMS where TYPE='"+UUID_TYPE+"'";
            ResultSet rs2 = stmt.executeQuery(query2);
            HashMap<String ,List<String>> datasetMap = new HashMap<String ,List<String>>();
            List<String> datasetNameList = new ArrayList<String>();
            while (rs2.next()){
                String datasetName = rs2.getString("NAME");
                datasetNameList.add(datasetName);
            }
            datasetMap.put("dataset",datasetNameList);
            stmt.close();
            rs.close();
            rs2.close();
            conn.close();
            return JsonResult.success(datasetMap);
        }catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public JsonResult queryLayersByDatasetname(String datasetName,String db_id) {
        DbManage dbManage = dbManageMapper.selectById(Long.valueOf(db_id));
        try{
            Class.forName(dbManage.getDbVersion());
            Connection conn= DriverManager.getConnection(dbManage.getExampleUrl(),dbManage.getUsername(),dbManage.getPassword());
            logger.info("db连接成功");
            Statement stmt=conn.createStatement();
            datasetName = "%\\"+datasetName+"\\%";
            String query = "select * from GDB_ITEMS where path like '"+datasetName+"'";
            ResultSet rs = stmt.executeQuery(query);
            HashMap<String ,List<String>> pathMap = new HashMap<String ,List<String>>();
            List<String> pathList = new ArrayList<String>();
            while (rs.next()){
                String path = rs.getString("path");
                path = path.split("\\\\")[2];
                path = path.split("\\.")[1];
                pathList.add(path);
            }
            pathMap.put("layerPath",pathList);
            stmt.close();
            rs.close();
            conn.close();
            return JsonResult.success(pathMap);
        }catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public JsonResult importLayerData(String db_id, String layerName,Long dataStandardId) throws Exception{
        DataLayer dataLayer1 = null;
        List<DataLayer> dataLayers = new ArrayList<>();
        if(layerName.split(",").length > 1){
            List<String> layerNameList = Arrays.asList(layerName.split(","));
            dataLayers = this.dataLayerMapper.selectList(new LambdaQueryWrapper<DataLayer>().in(DataLayer::getName, layerNameList).eq(DataLayer::getDataStandardId, dataStandardId));
        }else {
            dataLayer1 = this.dataLayerMapper.selectOne(new LambdaQueryWrapper<DataLayer>().eq(DataLayer::getName, layerName).eq(DataLayer::getDataStandardId, dataStandardId));
        }

        if(Objects.nonNull(dataLayer1) || dataLayers.size() > 0){
            return JsonResult.error("图层名称已存在!");
        }
        int count = layerName.split(",").length;
        int insert_process = 0;
        for(int i=0;i<count;i++){
            this.importData(db_id,layerName.split(",")[i],dataStandardId);
            insert_process++;
            logger.info("批量总写入进度："+insert_process+"/"+count);
        }
        return JsonResult.success("写入成功");
    }

    private void importData(String db_id, String layerName,Long dataStandardId) throws Exception {
        DbManage dbManage = dbManageMapper.selectById(Long.valueOf(db_id));
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        Connection conn = null;
        Class.forName(dbManage.getDbVersion());
        conn= DriverManager.getConnection(dbManage.getExampleUrl(),dbManage.getUsername(),dbManage.getPassword());
        // // 关闭自动提交
        // conn.setAutoCommit(false);
        logger.info("db连接成功");
        int count = 0;
        Statement stmt=conn.createStatement();
        String query = "select column_name,data_type,data_length,nullable from user_tab_columns where REGEXP_LIKE(table_name,'"+layerName+"','i') order by column_id asc";

        String query3 = "select count(*) as count from (select column_name,data_type,data_length,nullable from user_tab_columns where REGEXP_LIKE(table_name,'"+layerName+"','i') order by column_id asc) c";
        ResultSet rs3 = stmt.executeQuery(query3);
        while (rs3.next()){
            count = rs3.getInt("count");
        }
        //查询图层类型
        String query2 = "select GEOMETRY_TYPE from GEOMETRY_COLUMNS where REGEXP_LIKE(F_TABLE_NAME,'"+layerName+"','i')";
        ResultSet rs2 = stmt.executeQuery(query2);
        Long dataLayerTypeId = 0L;
        while (rs2.next()){
            dataLayerTypeId = Long.valueOf(rs2.getInt("geometry_type"));
        }
        //写入图层
        DataLayer dataLayer = new DataLayer();
        dataLayer.setName(layerName);
        dataLayer.setShowName(layerName);
        dataLayer.setCreateTime(LocalDateTime.now());
        dataLayer.setDataStandardId(dataStandardId);
        dataLayer.setLayerTypeId(dataLayerTypeId);
        dataLayer.setOperator(username);
        int insert = this.dataLayerMapper.insert(dataLayer);
        int insert_process = 0;
        ResultSet rs = stmt.executeQuery(query);
        while (rs.next()){
            String columnName = rs.getString("column_name");
            String dataType = rs.getString("data_type");
            String dataLength = rs.getString("data_length");
            String nullable = rs.getString("nullable");
            logger.info(columnName+" "+dataType+" "+dataLength+" "+nullable);
            if(insert > 0){
                //写入图层字段
                DataLayerField dataLayerField = new DataLayerField();
                dataLayerField.setFieldName(columnName);
                dataLayerField.setShowName(columnName);
                Long dataTypeId = 5L;
                if(columnName.equals("OBJECTID")){
                    dataTypeId = FieldType.OID.typeid();
                }
                if(dataType.equals("NVARCHAR2")){
                    dataTypeId = FieldType.STRING.typeid();
                }
                if(dataType.equals("NUMBER")){
                    dataTypeId = FieldType.INTEGER.typeid();
                }
                if(dataType.equals("ST_GEOMETRY")){
                    dataTypeId = FieldType.GEOMETRY.typeid();
                }
                dataLayerField.setFieldTypeId(dataTypeId);
                dataLayerField.setDataLayerId(this.dataLayerMapper.selectOne(new LambdaQueryWrapper<DataLayer>().eq(DataLayer::getName, layerName)).getId());
                dataLayerField.setDataStandardId(dataStandardId);
                this.dataLayerFieldMapper.insert(dataLayerField);
                insert_process++;
                logger.info("写入进度:"+insert_process+"/"+count);
            }
        }
        if(insert_process != count){
            conn.rollback();
            stmt.close();
            rs.close();
            rs2.close();
            rs3.close();
            conn.close();
            throw new RuntimeException("写入失败");
        }
        stmt.close();
        rs.close();
        rs2.close();
        rs3.close();
        conn.close();
    }


    public JsonResult importLayerDataFromService(MetadataTablestructuresAllDto dto) throws Exception {

        List<MetadataTablestructuresDto> metadataTablestructuresList = dto.getMetadataTablestructuresList();
        List<MetadataTablestructurefieldsDto> metadataTablestructurefieldsDtoList = dto.getMetadataTablestructurefieldsDtoList();

        if(Objects.isNull(metadataTablestructuresList) || Objects.isNull(metadataTablestructurefieldsDtoList) || metadataTablestructuresList.isEmpty() || metadataTablestructurefieldsDtoList.isEmpty()){
            return JsonResult.error("数据为空");
        }
        //todo 先做成单图层导入  先手动关联id
        // if(metadataTablestructuresList.size() != metadataTablestructurefieldsDtoList.size()){
        //     return JsonResult.error("图层数量和字段集合数量不一致,请检查数据");
        // }
        List<String> layerNameList = metadataTablestructuresList.stream().map(MetadataTablestructuresDto::getName).toList();
        List<MetadataTablestructures> dataLayers = this.metadataTablestructuresMapper.selectList(new LambdaQueryWrapper<MetadataTablestructures>().in(MetadataTablestructures::getName, layerNameList));
        if(Objects.nonNull(dataLayers) && !dataLayers.isEmpty()){
            return JsonResult.error("数据中存在重名图层,请修正");
        }
        // List<DataLayerFieldType> dataLayerFieldTypeList = this.dataLayerFieldTypeService.list();
        // List<HashMap<String,Long>> dataLayerFieldTypeMap = new ArrayList<>();
        // dataLayerFieldTypeList.forEach(item -> {
        //     HashMap<String,Long> map = new HashMap<>();
        //     map.put(item.getName(),item.getId());
        //     dataLayerFieldTypeMap.add(map);
        // });
        List<MetadataTablestructures> dataLayerList = BeanUtil.copyToList(metadataTablestructuresList, MetadataTablestructures.class);
        List<MetadataTablestructurefields> fieldList = BeanUtil.copyToList(metadataTablestructurefieldsDtoList, MetadataTablestructurefields.class);
        dataLayerList.forEach(item -> {
            item.setShapetype(Short.valueOf("4"));
            item.setTabletype(null);
            item.setDisplayname(item.getName());
        });

        boolean b = metadataTablestructuresService.saveBatch(dataLayerList);
        //todo 先做成单图层导入  先手动关联id
        fieldList.forEach(item -> {
            item.setTablestructureid(dataLayerList.get(0).getTablestructureid());
            String upperCase = item.getFieldtypeStr().substring(13).toUpperCase();
            if(upperCase.equals("DATE")) upperCase = "DATETIME";
            Long l = this.dataLayerFieldTypeService.selectByName(upperCase);
            item.setFieldtype(l);
            item.setLength(Long.valueOf("0"));
            item.setPrecision(Long.valueOf("0"));
            item.setScale(Long.valueOf("0"));
            if(item.getName().contains("OBJECTID")) item.setIsnullable(false);
            item.setStatisticscategory(null);
            item.setIsvisiable(false);
        });
        boolean b1 = metadataTablestructurefieldsService.saveBatch(fieldList);

        if(!b || !b1)
            throw new Exception("数据导入失败");
        else
            return JsonResult.success();
    }

}
