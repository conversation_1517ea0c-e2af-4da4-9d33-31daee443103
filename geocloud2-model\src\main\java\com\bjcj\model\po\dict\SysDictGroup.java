package com.bjcj.model.po.dict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


/**
 *@Author：qinyi
 *@Date：2024/2/1  17:36
*/
/**
    * 字典值分组表
    */
@Schema(description="字典值分组表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_dict_group")
public class SysDictGroup implements Serializable {
    /**
     * 字典值组id
     */
    @TableId(value = "group_id", type = IdType.ASSIGN_ID)
    @Schema(description="字典值组id")
    private Long groupId;

    /**
     * 组名称
     */
    @TableField(value = "group_name")
    @Schema(description="组名称")
    @Size(max = 100,message = "组名称max length should less than 100")
    @NotBlank(message = "组名称is not blank")
    private String groupName;

    @TableField(value = "create_time")
    @Schema(description="")
    private Date createTime;

    @TableField(value = "update_time")
    @Schema(description="")
    private Date updateTime;

    @TableField(value = "operater")
    @Schema(description="")
    @Size(max = 30,message = "max length should less than 30")
    private String operater;

    /**
     * 字典id
     */
    @TableField(value = "dict_id")
    @Schema(description="字典id")
    @NotNull(message = "字典idis not null")
    private Long dictId;

    private static final long serialVersionUID = 1L;
}