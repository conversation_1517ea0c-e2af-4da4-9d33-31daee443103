<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.safetyManage.SysRoleFuncMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.safetyManage.SysRoleFunc">
    <!--@mbg.generated-->
    <!--@Table public.sys_role_func-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="func_id" jdbcType="BIGINT" property="funcId" />
    <result column="role_id" jdbcType="BIGINT" property="roleId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, func_id, role_id,from_role_name
  </sql>

  <insert id="insertBatch">
    insert into sys_role_func (
    <include refid="Base_Column_List" />
    ) values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.funcId,jdbcType=BIGINT}, #{item.roleId,jdbcType=BIGINT}, #{item.fromRoleName,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <delete id="deleteBatch">
    delete from sys_role_func where role_id=#{roleId} and func_id in
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </delete>

  <select id="selectRoleNameByFuncId" resultType="java.lang.String">
    select string_agg(r.role_name,',') from sys_role_func rc
    left join sys_role r on rc.role_id=r.id
    where func_id=#{id}
  </select>
</mapper>