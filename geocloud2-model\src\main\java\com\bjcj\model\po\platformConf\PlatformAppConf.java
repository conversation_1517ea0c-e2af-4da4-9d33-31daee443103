package com.bjcj.model.po.platformConf;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/1/24  17:48
*/
/**
    * 平台应用配置表
    */
@Schema(description="平台应用配置表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "platform_app_conf")
public class PlatformAppConf implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 应用名称
     */
    @TableField(value = "app_name")
    @Schema(description="应用名称")
    @Size(max = 50,message = "应用名称max length should less than 50")
    private String appName;

    /**
     * 显示名称
     */
    @TableField(value = "show_name")
    @Schema(description="显示名称")
    @Size(max = 50,message = "显示名称max length should less than 50")
    @NotBlank(message = "显示名称is not blank")
    private String showName;

    /**
     * 系统标题
     */
    @TableField(value = "system_title")
    @Schema(description="系统标题")
    @Size(max = 50,message = "系统标题max length should less than 50")
    @NotBlank(message = "系统标题is not blank")
    private String systemTitle;

    /**
     * 地址
     */
    @TableField(value = "url")
    @Schema(description="地址")
    @Size(max = 255,message = "地址max length should less than 255")
    @NotBlank(message = "地址is not blank")
    private String url;

    /**
     * 登录主题
     */
    @TableField(value = "login_theme")
    @Schema(description="登录主题")
    @Size(max = 100,message = "登录主题max length should less than 100")
    private String loginTheme;

    /**
     * 平台菜单id
     */
    @TableField(value = "platform_menu_id")
    @Schema(description="平台菜单id")
    private Long platformMenuId;

    /**
     * 描述
     */
    @TableField(value = "remark")
    @Schema(description="描述")
    @Size(max = 255,message = "描述max length should less than 255")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @Schema(description="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 最后操作人
     */
    @TableField(value = "operater")
    @Schema(description="最后操作人")
    @Size(max = 30,message = "最后操作人max length should less than 30")
    private String operater;

    /**
     * 系统配置
     */
    @TableField(value = "system_config_json")
    @Schema(description="系统配置")
    private String systemConfigJson;

    /**
     * 系统配置解释json
     */
    @TableField(value = "system_config_json_schema")
    @Schema(description="系统配置解释json")
    private String systemConfigJsonSchema;

    /**
     * theme_conf_json
     */
    @TableField(value = "theme_conf_json")
    @Schema(description="主题配置json")
    private String themeConfJson;

    /**
     * theme_conf_json
     */
    @TableField(value = "theme_conf_json_schema")
    @Schema(description="主题配置json解释")
    private String themeConfJsonSchema;

    @TableField(exist = false)
    @Schema(description="默认专题方案id")
    private Long specialPlanId;

    @TableField(exist = false)
    @Schema(description="默认字段方案id")
    private Long fieldPlanId;


    private static final long serialVersionUID = 1L;
}