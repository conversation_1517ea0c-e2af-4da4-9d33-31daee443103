package com.bjcj.web.ore;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bjcj.common.utils.support.Create;
import com.bjcj.common.utils.support.Update;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.ore.OreWatchInfo;
import com.bjcj.service.ore.OreWatchInfoService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-12-04 09:31
 */
@Tag(name = "矿山监管汇总", description = "矿山监管汇总")
@RestController
@RequestMapping("/ore")
public class OreController {

    @Resource
    OreWatchInfoService oreWatchInfoService;

    @Operation(summary = "查询数据")
    @ApiOperationSupport(order = 1)
    @GetMapping
    public JsonResult list(String status, String name, String miningWay) {
        List<OreWatchInfo> list = oreWatchInfoService.list(
                Wrappers.<OreWatchInfo>lambdaQuery()
                        .like(StrUtil.isNotBlank(name), OreWatchInfo::getOreName, name).or()
                        .like(StrUtil.isNotBlank(name), OreWatchInfo::getSubOreName, name).or()
                        .like(StrUtil.isNotBlank(name), OreWatchInfo::getOreNumber, name)
                        .eq(StrUtil.isNotBlank(status), OreWatchInfo::getStatus, status)
                        .eq(StrUtil.isNotBlank(miningWay), OreWatchInfo::getMiningWay, miningWay)
        );
        return JsonResult.success(list);
    }

    @Operation(summary = "状态数据")
    @ApiOperationSupport(order = 2)
    @GetMapping("/statusData")
    public JsonResult statusData() {
        List<String> list = oreWatchInfoService.list().stream().map(OreWatchInfo::getStatus).distinct().toList();
        return JsonResult.success(list);
    }

    @Operation(summary = "采矿方式数据")
    @ApiOperationSupport(order = 3)
    @GetMapping("/miningWayData")
    public JsonResult miningWayData() {
        List<String> list = oreWatchInfoService.list().stream().map(OreWatchInfo::getMiningWay).distinct().toList();
        return JsonResult.success(list);
    }

    @Operation(summary = "添加数据")
    @ApiOperationSupport(order = 4)
    @PostMapping
    public JsonResult create(@RequestBody @Validated(value = Create.class) OreWatchInfo oreWatchInfo) {
        oreWatchInfoService.save(oreWatchInfo);
        return JsonResult.success();
    }

    @Operation(summary = "编辑数据")
    @ApiOperationSupport(order = 5)
    @PutMapping
    public JsonResult update(@RequestBody @Validated(value = Update.class) OreWatchInfo oreWatchInfo) {
        oreWatchInfoService.updateById(oreWatchInfo);
        return JsonResult.success();
    }


}
