package com.bjcj.service.naturalresources.function;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.categories.ResourceServicesMapper;
import com.bjcj.mapper.naturalresources.function.FunctionProviderMapper;
import com.bjcj.mapper.naturalresources.function.FunctionServeMapper;
import com.bjcj.mapper.naturalresources.function.RequestParamsMapper;
import com.bjcj.mapper.naturalresources.function.RequestResultMapper;
import com.bjcj.mapper.safetyManage.SafetyRoleMapper;
import com.bjcj.mapper.safetyManage.SafetyUserRoleMapper;
import com.bjcj.mapper.safetyManage.SysRoleFuncMapper;
import com.bjcj.mapper.safetyManage.UserMapper;
import com.bjcj.model.po.naturalresources.categories.ResourceServices;
import com.bjcj.model.po.naturalresources.function.FunctionProvider;
import com.bjcj.model.po.naturalresources.function.FunctionServe;
import com.bjcj.model.po.naturalresources.function.RequestParams;
import com.bjcj.model.po.naturalresources.function.RequestResult;
import com.bjcj.model.po.safetyManage.SafetyRole;
import com.bjcj.model.po.safetyManage.SafetyUserRole;
import com.bjcj.model.po.safetyManage.SysRoleFunc;
import com.bjcj.model.po.safetyManage.User;
import com.bjcj.model.vo.naturalresources.categories.CategoriesParamVo;
import com.bjcj.model.vo.naturalresources.function.FunctionServeAuthVo;
import com.bjcj.model.vo.naturalresources.function.FunctionServeVo;
import com.bjcj.model.vo.naturalresources.function.FunctionVo;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/11/30 15:13 周四
 */
@Service
public class FunctionServeService extends ServiceImpl<FunctionServeMapper, FunctionServe> {

    @Resource
    private FunctionServeMapper functionServeMapper;

    @Resource
    private FunctionProviderMapper functionProviderMapper;

    @Resource
    private RequestParamsMapper requestParamsMapper;

    @Resource
    private RequestResultMapper requestResultMapper;

    @Resource
    private SysRoleFuncMapper sysRoleFuncMapper;
    @Resource
    private SafetyUserRoleMapper safetyUserRoleMapper;
    @Resource
    private UserMapper userMapper;
    @Resource
    private SafetyRoleMapper safetyRoleMapper;

    @Resource
    private ResourceServicesMapper resourceServicesMapper;

    public JsonResult lists(String id, String name, int current, int size){
        Page<FunctionServe> page = new Page<>(current, size);
        LambdaQueryWrapper<FunctionServe> wrapper = new LambdaQueryWrapper();
        wrapper.eq(FunctionServe::getDel, 0)
                .like(FunctionServe::getFwCatalogue, id)
                .like(StringUtils.isNotBlank(name), FunctionServe::getName, name)
                .or()
                .like(StringUtils.isNotBlank(name), FunctionServe::getSname, name);

        wrapper.orderByAsc(FunctionServe::getCreateTime);
        IPage<FunctionServeVo> convertPage = functionServeMapper.selectPage(page, wrapper).convert(result-> BeanUtil.copyProperties(result, FunctionServeVo.class));

        convertPage.getRecords().stream().forEach(item->{
            LambdaQueryWrapper<FunctionProvider> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(FunctionProvider::getParentId, item.getId());
            FunctionProvider functionProvider = functionProviderMapper.selectOne(wrapper1);
            item.setFunctionProvider(functionProvider);

            LambdaQueryWrapper<RequestParams> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(RequestParams::getParentId, item.getId())
                    .eq(RequestParams::getDel, 0);
            List<RequestParams> requestParams = requestParamsMapper.selectList(wrapper2);
            item.setRequestParams(requestParams);

            LambdaQueryWrapper<RequestResult> wrapper3 = new LambdaQueryWrapper<>();
            wrapper3.eq(RequestResult::getParentId, item.getId())
                    .eq(RequestResult::getDel, 0);
            List<RequestResult> requestResult = requestResultMapper.selectList(wrapper3);
            item.setRequestResult(requestResult);

        });
        return JsonResult.success(convertPage);
    }

    public JsonResult listsAuth(CategoriesParamVo categoriesParamVo){
        String name = categoriesParamVo.getName();
        String id = categoriesParamVo.getId();
        int auth = categoriesParamVo.getAuth();
        int current = categoriesParamVo.getCurrent();
        int size = categoriesParamVo.getSize();
        Long roleId = categoriesParamVo.getRoleId();

        Page<Map<String, Object>> page = new Page<>(current, size);

        IPage<FunctionServeAuthVo> list = functionServeMapper.listAuth(name, id, auth, page, roleId);

        return JsonResult.success(list);
    }

    public JsonResult<FunctionVo> findById(Long id){
        FunctionVo functionVo = new FunctionVo();
        FunctionServe functionServe = functionServeMapper.selectById(id);

        LambdaQueryWrapper<FunctionProvider> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(FunctionProvider::getParentId, id);
        FunctionProvider functionProvider = functionProviderMapper.selectOne(wrapper1);

        LambdaQueryWrapper<RequestParams> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(RequestParams::getParentId, id)
                .eq(RequestParams::getDel, 0);
        List<RequestParams> requestParams = requestParamsMapper.selectList(wrapper2);

        LambdaQueryWrapper<RequestResult> wrapper3 = new LambdaQueryWrapper<>();
        wrapper3.eq(RequestResult::getParentId, id)
                .eq(RequestResult::getDel, 0);
        List<RequestResult> requestResult = requestResultMapper.selectList(wrapper3);

        functionVo.setFunctionServe(functionServe);
        functionVo.setFunctionProvider(functionProvider);
        functionVo.setRequestParams(requestParams);
        functionVo.setRequestResult(requestResult);
        return JsonResult.success(functionVo);
    }

    public JsonResult saveAll(FunctionVo functionVo){
        FunctionServe functionServe = functionVo.getFunctionServe();
        FunctionProvider functionProvider = functionVo.getFunctionProvider();
        List<RequestParams> requestParams = functionVo.getRequestParams();
        List<RequestResult> requestResult = functionVo.getRequestResult();
        if(ObjectUtil.isNotEmpty(functionServe)){
            int result = functionServeMapper.insert(functionServe);
            Long parentId = functionServe.getId();
            if(ObjectUtil.isNotEmpty(functionProvider)){
                functionProvider.setParentId(parentId);
                functionProviderMapper.insert(functionProvider);
            }

            if(requestParams.size() > 0){
                requestParams.stream().forEach(item -> {
                    item.setParentId(parentId);
                    requestParamsMapper.insert(item);
                });
            }

            if(requestResult.size() > 0){
                requestResult.stream().forEach(item -> {
                    item.setParentId(parentId);
                    requestResultMapper.insert(item);
                });
            }

            return JsonResult.success();
        }

        return JsonResult.error();
    }

    public JsonResult updateAll(FunctionVo functionVo){
        FunctionServe functionServe = functionVo.getFunctionServe();
        FunctionProvider functionProvider = functionVo.getFunctionProvider();
        List<RequestParams> requestParams = functionVo.getRequestParams();
        List<RequestResult> requestResult = functionVo.getRequestResult();
        if(ObjectUtil.isNotEmpty(functionServe)){
            int result = functionServeMapper.updateById(functionServe);

            if(ObjectUtil.isNotEmpty(functionProvider)){
                functionProviderMapper.updateById(functionProvider);
            }

            if(requestParams.size() > 0){
                requestParams.stream().forEach(item -> {
                    if(ObjectUtil.isEmpty(item.getId())){
                        item.setParentId(functionServe.getId());
                        requestParamsMapper.insert(item);
                    }else{
                        requestParamsMapper.updateById(item);
                    }

                });
            }

            if(requestResult.size() > 0){
                requestResult.stream().forEach(item -> {
                    if(ObjectUtil.isEmpty(item.getId())){
                        item.setParentId(functionServe.getId());
                        requestResultMapper.insert(item);
                    }else {
                        requestResultMapper.updateById(item);
                    }
                });
            }

            return JsonResult.success();
        }
        return JsonResult.success();
    }

    public JsonResult del(List<Long> ids){

        LambdaUpdateWrapper<FunctionServe> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(FunctionServe::getDel, 1)
                .in(FunctionServe::getId, ids);
        int result = functionServeMapper.update(wrapper);
        if(result < 1){
            return JsonResult.error();
        }

        return JsonResult.success();
    }

    public JsonResult uptStatus(Long id, Boolean status){
        LambdaUpdateWrapper<FunctionServe> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(FunctionServe::getStatus, status)
                .eq(FunctionServe::getId, id);
        int result = functionServeMapper.update(wrapper);
        return JsonResult.success(result);
    }

    public JsonResult uptAuth(Long id, int auth){
        LambdaUpdateWrapper<FunctionServe> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(FunctionServe::getAuth, auth)
                .eq(FunctionServe::getId, id);
        int result = functionServeMapper.update(wrapper);
        return JsonResult.success(result);
    }

    public JsonResult updateRequestParams(RequestParams requestParams){
        int result = requestParamsMapper.updateById(requestParams);
        if(result < 1){
            return JsonResult.error();
        }
        return JsonResult.success();
    }

    public JsonResult delRequestParams(Long id){
        LambdaUpdateWrapper<RequestParams> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(RequestParams::getDel, 1)
                .eq(RequestParams::getId, id);
        int result = requestParamsMapper.update(wrapper);
        if(result != 1){
            return JsonResult.error();
        }
        return JsonResult.success();
    }

    public JsonResult updateRequestResult(RequestResult requestResult){
        int result = requestResultMapper.updateById(requestResult);
        if(result != 1){
            return JsonResult.error();
        }
        return JsonResult.success();
    }

    public JsonResult delRequestResult(Long id){
        LambdaUpdateWrapper<RequestResult> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(RequestResult::getDel, 1)
                .eq(RequestResult::getId, id);
        int result = requestResultMapper.update(wrapper);
        if(result != 1){
            return JsonResult.error();
        }
        return JsonResult.success();
    }

    public JsonResult<Page<ResourceServices>> pageDataSort(Page<ResourceServices> pager, String searchStr, String resourcecatalogid) {
        List<ResourceServices> resourceServicesList = this.resourceServicesMapper.selectPageList((pager.getCurrent() - 1) * pager.getSize(),pager.getSize(),searchStr,resourcecatalogid);
        Long count = this.resourceServicesMapper.selectListCount(searchStr,resourcecatalogid);
        Page<ResourceServices> page = new Page<>();
        page.setRecords(resourceServicesList);
        page.setTotal(count);
        return JsonResult.success(page);
    }

    public JsonResult<Page<User>> queryAuthUser(Page<User> pager, String resourceServiceId) {
        LambdaQueryWrapper<SysRoleFunc> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleFunc::getFuncId, resourceServiceId);
        List<SysRoleFunc> sysRoleFuncList = sysRoleFuncMapper.selectList(wrapper);
        if(sysRoleFuncList.isEmpty() || Objects.isNull(sysRoleFuncList)){
            return JsonResult.success(new Page<User>());
        }
        List<Long> roleIds = sysRoleFuncList.stream().map(SysRoleFunc::getRoleId).toList();
        List<Long> userids = this.safetyUserRoleMapper.selectUserByRoleids(roleIds);
        LambdaQueryWrapper<User> wapper = new LambdaQueryWrapper<User>();
        wapper.in(User::getId,userids);
        Page<User> userPage = userMapper.selectPage(pager, wapper);
        userPage.getRecords().stream().forEachOrdered( user -> {
            List<SafetyRole> roleList = this.safetyRoleMapper.selectIdInByUserId(user.getId());
            user.setRoleList(roleList);
        });
        return JsonResult.success(userPage);
    }

    public JsonResult<Page<ResourceServices>> serviceAuthList(Page<ResourceServices> pager, Long userId, String searchStr, String resourceCatalogsId) {
        List<Long> roleIds = this.safetyUserRoleMapper.selectList(new LambdaQueryWrapper<SafetyUserRole>().eq(SafetyUserRole::getUserId,userId)).stream().map(SafetyUserRole::getRoleId).toList();
        if(Objects.isNull(roleIds) || roleIds.isEmpty()){
            return JsonResult.error("该人员未拥有角色");
        }
        List<String> funcIds = this.sysRoleFuncMapper.selectList(new LambdaQueryWrapper<SysRoleFunc>().in(SysRoleFunc::getRoleId,roleIds)).stream().map(SysRoleFunc::getFuncId).toList();
        if(funcIds.isEmpty()){
            return JsonResult.success(new Page<ResourceServices>());
        }else{
            LambdaQueryWrapper<ResourceServices> wrapper = new LambdaQueryWrapper<ResourceServices>();
            if(Objects.nonNull(searchStr) && !"".equals(searchStr)){
                wrapper.and(wp -> wp.like(ResourceServices::getName,searchStr).or().like(ResourceServices::getDisplayname,searchStr));
            }
            if(Objects.nonNull(resourceCatalogsId) &&!"".equals(resourceCatalogsId)){
                wrapper.eq(ResourceServices::getResourcecatalogid,resourceCatalogsId);
            }
            wrapper.in(ResourceServices::getId,funcIds).orderByDesc(ResourceServices::getRegisterdate);
            Page<ResourceServices> page = this.resourceServicesMapper.selectPage(pager, wrapper);
            page.getRecords().stream().forEachOrdered(data -> {
                data.setAuthFromRoleName(this.sysRoleFuncMapper.selectRoleNameByFuncId(data.getId()));
            });
            return JsonResult.success(page);
        }
     }
}
