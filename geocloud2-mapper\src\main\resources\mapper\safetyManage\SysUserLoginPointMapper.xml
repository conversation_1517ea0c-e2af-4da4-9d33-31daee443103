<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.safetyManage.SysUserLoginPointMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.safetyManage.SysUserLoginPoint">
    <!--@Table public.sys_user_login_point-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="login_time" jdbcType="TIMESTAMP" property="loginTime" />
    <result column="invalid_time" jdbcType="TIMESTAMP" property="invalidTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, username, login_time, invalid_time
  </sql>
</mapper>