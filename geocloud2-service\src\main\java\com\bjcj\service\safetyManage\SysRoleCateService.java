package com.bjcj.service.safetyManage;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.safetyManage.SysRoleCateMapper;
import com.bjcj.model.dto.safetyManage.SysRoleCateDto;
import com.bjcj.model.po.safetyManage.SysRoleCate;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 *@Author：qinyi
 *@Date：2023/12/13  15:09
*/
@Service
public class SysRoleCateService extends ServiceImpl<SysRoleCateMapper, SysRoleCate> {

    @Resource
    SysRoleCateMapper sysRoleCateMapper;

    @Resource
    CheckIsAdmin checkIsAdmin;

    public JsonResult saveRoleCate(SysRoleCateDto dto) {
        //获取当前登录用户角色
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        String fromRoleName = checkIsAdmin.getUserRoleName(userId);
        Long roleId = dto.getRoleId();
        if(!dto.getAddIdList().isEmpty()){
            List<SysRoleCate> list = new ArrayList<SysRoleCate>();
            dto.getAddIdList().forEach(cateId -> {
                SysRoleCate sysRoleCate =  new SysRoleCate(){{setCateId(cateId);setRoleId(roleId);setFromRoleName(fromRoleName);}};
                list.add(sysRoleCate);
            });
            sysRoleCateMapper.insertBatch(list);
        }
        if(!dto.getDelIdList().isEmpty()){
            sysRoleCateMapper.deleteBatch(dto.getDelIdList(),roleId);
        }
        return JsonResult.success();
    }
}
