package com.bjcj.web.datamodel;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.datamodel.SpdpSavedataDto;
import com.bjcj.model.po.datamodel.SpdpSavedata;
import com.bjcj.service.datamodel.SpdpSavedataService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
* 保存数据(public.spdp_savedata)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/spdp_savedata")
@Tag(name = "数据保存")
@Validated
public class SpdpSavedataController {
    /**
    * 服务对象
    */
    @Resource
    private SpdpSavedataService spdpSavedataService;

    @SaCheckPermission("sys:read")
    @GetMapping("/list")
    @Operation(summary = "数据保存列表", description = "数据保存列表")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "searchStr", description = "searchStr", required = false)
    })
    public JsonResult<List<SpdpSavedata>> list(@RequestParam(value = "searchStr",required = false) String searchStr){
        if(StringUtils.isNotBlank(searchStr)){
            return JsonResult.success(this.spdpSavedataService.list(new LambdaQueryWrapper<SpdpSavedata>().and(i -> i.like(SpdpSavedata::getName, searchStr).or().like(SpdpSavedata::getDisplayname, searchStr))));
        }
        return JsonResult.success(this.spdpSavedataService.list());
    }

    @OperaLog(operaModule = "删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del/{saveid}")
    @Operation(summary = "删除", description = "根据id删除")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "delsjbc")
    public JsonResult del(@PathVariable("saveid") String saveid){
        return JsonResult.success(this.spdpSavedataService.removeById(saveid));
    }

    @OperaLog(operaModule = "批量删除",operaType = OperaLogConstant.DELETE,operaDesc = "批量删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delBatch")
    @Operation(summary = "批量删除", description = "根据id批量删除")
    @ApiOperationSupport(order = 3)
    @Parameters({
            @Parameter(name = "saveids", description = "ids逗号拼接", required = true)
    })
    @RequestLock(prefix = "delBatchsjbc")
    public JsonResult delBatch(@RequestParam("saveids") String saveids){
        if(saveids.contains(",")){
            //ids转list
            List<String> ids = List.of(saveids.split(","));
            return JsonResult.success(this.spdpSavedataService.removeByIds(ids));
        }else{
            return JsonResult.error("ids格式错误,必须选择一条以上的数据");
        }
    }

    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "注册/编辑", description = "注册/编辑")
    @ApiOperationSupport(order = 4)
    @RequestLock(prefix = "addOrEditsjbc")
    public JsonResult addOrEditsjbc(@Validated @RequestBody SpdpSavedataDto dto){
        SpdpSavedata spdpSavedata = BeanUtil.copyProperties(dto, SpdpSavedata.class);
        return this.spdpSavedataService.saveOrUpdate(spdpSavedata) ? JsonResult.success() : JsonResult.error();
    }



}
