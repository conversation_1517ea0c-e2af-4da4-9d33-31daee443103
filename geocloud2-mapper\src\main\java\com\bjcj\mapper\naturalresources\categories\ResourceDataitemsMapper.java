package com.bjcj.mapper.naturalresources.categories;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.naturalresources.categories.ResourceDataitems;
import com.bjcj.model.po.naturalresources.categories.ResourceServices;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/4/24  14:21
*/
public interface ResourceDataitemsMapper extends BaseMapper<ResourceDataitems> {
    List<ResourceServices> selectPageList(@Param("current") Long current, @Param("size") Long size, @Param("searchStr") String searchStr, @Param("resourceCatalogId") String resourceCatalogId);

    Long selectListCount(@Param("searchStr")  String searchStr,@Param("resourceCatalogId")  String resourceCatalogId);

    void updateReviewStatus(@Param("resId") String resId,@Param("status") int status);

    void updateReviewStatus2(@Param("resId") String resId,@Param("status") int status);
}