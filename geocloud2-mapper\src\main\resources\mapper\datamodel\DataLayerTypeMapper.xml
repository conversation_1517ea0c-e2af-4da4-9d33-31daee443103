<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.DataLayerTypeMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.DataLayerType">
    <!--@mbg.generated-->
    <!--@Table public.data_layer_type-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, type_name
  </sql>
</mapper>