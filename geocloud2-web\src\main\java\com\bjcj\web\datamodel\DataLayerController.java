package com.bjcj.web.datamodel;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.datamodel.*;
import com.bjcj.model.po.datamodel.*;
import com.bjcj.service.datamodel.*;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
* 图层表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/dataLayer")
@Tag(name = "图层")
@Validated
public class DataLayerController {
    /**
    * 服务对象
    */
    @Resource
    private DataLayerService dataLayerService;

    @Resource
    private DataLayerTypeService dataLayerTypeService;

    @Resource
    private DataLayerFieldTypeService dataLayerFieldTypeService;

    @Resource
    private DataLayerFieldStatisticsTypeService dataLayerFieldStatisticsTypeService;

    @Resource
    private DataLayerFieldService dateLayerFieldService;

    @Resource
    private DbManageService dbManageService;

    @Resource
    private MetadataTablestructuresService metadataTablestructuresService;

    @Resource
    private MetadataTablestructurefieldsService metadataTablestructurefieldsService;


    @SaCheckPermission("sys:read")
    @Operation(summary = "列表", description = "带搜索框模糊查询(可搜名称/显示名称)")
    @Parameters({
            @Parameter(name = "searchStr", description = "名称/显示名称搜索", required = false),
            @Parameter(name = "dataStandardId", description = "数据标准id", required = true)
    })
    @ApiOperationSupport(order = 1)
    @GetMapping("/list")
    public JsonResult<List<DataLayer>> list(@RequestParam(value = "searchStr",required = false) String searchStr,
                                            @RequestParam("dataStandardId") Long dataStandardId){
        LambdaQueryWrapper<DataLayer> queryWrapper = new LambdaQueryWrapper<DataLayer>();
        queryWrapper.eq(DataLayer::getDataStandardId,dataStandardId);
        if(searchStr!=null && !"".equals(searchStr)){
            queryWrapper.and(i->i.like(DataLayer::getName,searchStr).or().like(DataLayer::getShowName,searchStr));
        }
        queryWrapper.orderByDesc(DataLayer::getCreateTime);
        List<DataLayer> list = dataLayerService.list(queryWrapper);
        list.stream().forEachOrdered(item->{
            item.setLayerType(dataLayerTypeService.getById(item.getLayerTypeId()).getTypeName());
        });
        return JsonResult.success(list);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "1.列表", description = "带搜索框模糊查询(可搜名称/显示名称)")
    @Parameters({
            @Parameter(name = "searchStr", description = "名称/显示名称搜索", required = false),
            @Parameter(name = "dataStandardId", description = "数据标准id", required = true)
    })
    @ApiOperationSupport(order = 11)
    @GetMapping("/list2")
    public JsonResult<List<MetadataTablestructures>> list2(@RequestParam(value = "searchStr",required = false) String searchStr,
                                            @RequestParam("dataStandardId") String dataStandardId){
        LambdaQueryWrapper<MetadataTablestructures> queryWrapper = new LambdaQueryWrapper<MetadataTablestructures>();
        queryWrapper.eq(MetadataTablestructures::getDatastandardid,dataStandardId);
        if(searchStr!=null && !"".equals(searchStr)){
            queryWrapper.and(i->i.like(MetadataTablestructures::getName,searchStr).or().like(MetadataTablestructures::getDisplayname,searchStr));
        }
        queryWrapper.orderByAsc(MetadataTablestructures::getDisplayorder);
        List<MetadataTablestructures> list = metadataTablestructuresService.list(queryWrapper);
        return JsonResult.success(list);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "查询all图层类型", description = "全部")
    @ApiOperationSupport(order = 2)
    @GetMapping("/typeList")
    public JsonResult<List<DataLayerType>> typeList(){
        return JsonResult.success(dataLayerTypeService.list());
    }

    @OperaLog(operaModule = "2.图层-新增/编辑",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "新增/编辑")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit2")
    @Operation(summary = "2.新增/编辑图层", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 13)
    @RequestLock(prefix = "addOrEdit2")
    public JsonResult add2(@Validated @RequestBody MetadataTablestructuresDto dto){
        return this.metadataTablestructuresService.saveData(dto);
    }

    @OperaLog(operaModule = "图层-新增/编辑",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "新增/编辑")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑图层", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 3)
    @RequestLock(prefix = "addOrEdit")
    public JsonResult add(@Validated @RequestBody DataLayerDto dto){
        return this.dataLayerService.saveData(dto);
    }

    @OperaLog(operaModule = "图层-删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del")
    @Operation(summary = "删除", description = "根据id删除")
    @Parameters({
            @Parameter(name = "id", description = "id", required = true)
    })
    @ApiOperationSupport(order = 4)
    public JsonResult del(@RequestParam("id") Long id){
        return this.dataLayerService.removeById(id) ? JsonResult.success() : JsonResult.error();
    }

    @OperaLog(operaModule = "3.图层-删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del2")
    @Operation(summary = "3.删除", description = "根据id删除")
    @Parameters({
            @Parameter(name = "tablestructureid", description = "tablestructureid", required = true)
    })
    @ApiOperationSupport(order = 14)
    public JsonResult del2(@RequestParam("tablestructureid") String tablestructureid){
        return this.metadataTablestructuresService.removeById(tablestructureid) ? JsonResult.success() : JsonResult.error();
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "查询图层字段类型", description = "全部")
    @ApiOperationSupport(order = 5)
    @GetMapping("/fieldTypeList")
    public JsonResult<List<DataLayerFieldType>> fieldTypeList(){
        return JsonResult.success(dataLayerFieldTypeService.list());
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "查询图层字段统计分类", description = "全部")
    @ApiOperationSupport(order = 6)
    @GetMapping("/fieldStatisticsTypeList")
    public JsonResult<List<DataLayerFieldStatisticsType>> fieldStatisticsTypeList(){
        return JsonResult.success(dataLayerFieldStatisticsTypeService.list());
    }

    @OperaLog(operaModule = "图层字段-新增/编辑",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "新增/编辑")
    @SaCheckPermission("sys:write")
    @PostMapping("/fieldAddOrEdit")
    @Operation(summary = "新增/编辑图层字段", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 7)
    @RequestLock(prefix = "fieldAddOrEdit")
    public JsonResult fieldAddOrEdit(@Validated @RequestBody DataLayerFieldDto dto){
        return this.dateLayerFieldService.saveData(dto);
    }

    @OperaLog(operaModule = "4.图层字段-新增/编辑",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "新增/编辑")
    @SaCheckPermission("sys:write")
    @PostMapping("/fieldAddOrEdit2")
    @Operation(summary = "4.新增/编辑图层字段", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 17)
    @RequestLock(prefix = "fieldAddOrEdit2")
    public JsonResult fieldAddOrEdit2(@Validated @RequestBody MetadataTablestructurefieldsDto dto){
        Optional<MetadataTablestructures> tableStructureOptional = this.metadataTablestructuresService.getOneOpt(new LambdaQueryWrapper<MetadataTablestructures>().eq(MetadataTablestructures::getTablestructureid,dto.getTablestructureid()));
        if (!tableStructureOptional.isPresent()) {
            return JsonResult.build(500, String.format("不存在标识为'%s'的表结构", dto.getTablestructureid()));
        }else{
            MetadataTablestructurefields field = BeanUtil.copyProperties(dto, MetadataTablestructurefields.class);
            field.setFieldtype(this.dataLayerFieldTypeService.selectByName(field.getFieldtypeStr()));
            if(Objects.isNull(this.dataLayerFieldTypeService.selectByName(field.getFieldtypeStr()))) field.setFieldtype(dto.getFieldtype());
            field.setLength(Long.valueOf("0"));
            field.setPrecision(Long.valueOf("0"));
            field.setScale(Long.valueOf("0"));
            if(field.getName().contains("OBJECTID")) field.setIsnullable(false);
            field.setStatisticscategory(null);
            field.setIsvisiable(false);
            if(Objects.isNull(dto.getFieldid()))
                return this.metadataTablestructurefieldsService.save(field) ? JsonResult.success() : JsonResult.error();
            else
                return this.metadataTablestructurefieldsService.updateById(field) ? JsonResult.success() : JsonResult.error();
        }
    }

    @OperaLog(operaModule = "图层字段-删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/fieldDel")
    @Operation(summary = "删除", description = "根据id删除")
    @Parameters({
            @Parameter(name = "id", description = "id", required = true)
    })
    @ApiOperationSupport(order = 8)
    public JsonResult fieldDel(@RequestParam("id") Long id){
        return this.dateLayerFieldService.removeById(id)? JsonResult.success() : JsonResult.error();
    }

    @OperaLog(operaModule = "5.图层字段-删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/fieldDel2")
    @Operation(summary = "5.删除", description = "根据id删除")
    @Parameters({
            @Parameter(name = "fieldid", description = "fieldid", required = true)
    })
    @ApiOperationSupport(order = 18)
    public JsonResult fieldDel2(@RequestParam("fieldid") String fieldid){
        return this.metadataTablestructurefieldsService.removeById(fieldid)? JsonResult.success() : JsonResult.error();
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "图层字段列表", description = "带搜索框模糊查询(可搜名称/显示名称)")
    @Parameters({
            @Parameter(name = "searchStr", description = "名称/显示名称搜索", required = false),
            @Parameter(name = "dataLayerId", description = "图层id", required = true)
    })
    @ApiOperationSupport(order = 9)
    @GetMapping("/fieldList")
    public JsonResult<List<DataLayerField>> fieldList(@RequestParam(value = "searchStr",required = false) String searchStr,
                                                      @RequestParam("dataLayerId") Long dataLayerId){
        LambdaQueryWrapper<DataLayerField> queryWrapper = new LambdaQueryWrapper<DataLayerField>(){{
            eq(DataLayerField::getDataLayerId,dataLayerId);
            if(searchStr!=null &&!"".equals(searchStr)) {
                and(i -> i.like(DataLayerField::getFieldName, searchStr).or().like(DataLayerField::getShowName, searchStr));
            }
            orderByAsc(DataLayerField::getShowSort);
        }};
        List<DataLayerField> list = dateLayerFieldService.list(queryWrapper);
        list.parallelStream().forEachOrdered(item -> {
            item.setFieldType(dataLayerFieldTypeService.getById(item.getFieldTypeId()).getName());
            String statisticsType = dataLayerFieldStatisticsTypeService.getById(item.getStatisticsTypeId()) == null? "" : dataLayerFieldStatisticsTypeService.getById(item.getStatisticsTypeId()).getStatisticsType();
            item.setStatisticsType(statisticsType);
        });
        return JsonResult.success(list);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "6.图层字段列表", description = "带搜索框模糊查询(可搜名称/显示名称)")
    @Parameters({
            @Parameter(name = "searchStr", description = "名称/显示名称搜索", required = false),
            @Parameter(name = "tablestructureid", description = "图层id", required = true)
    })
    @ApiOperationSupport(order = 19)
    @GetMapping("/fieldList2")
    public JsonResult<List<MetadataTablestructurefields>> fieldList2(@RequestParam(value = "searchStr",required = false) String searchStr,
                                                      @RequestParam("tablestructureid") String tablestructureid){
        LambdaQueryWrapper<MetadataTablestructurefields> queryWrapper = new LambdaQueryWrapper<MetadataTablestructurefields>(){{
            eq(MetadataTablestructurefields::getTablestructureid,tablestructureid);
            if(searchStr!=null &&!"".equals(searchStr)) {
                and(i -> i.like(MetadataTablestructurefields::getName, searchStr).or().like(MetadataTablestructurefields::getDisplayname, searchStr));
            }
            orderByAsc(MetadataTablestructurefields::getDisplayorder);
        }};
        List<MetadataTablestructurefields> list = metadataTablestructurefieldsService.list(queryWrapper);
        return JsonResult.success(list);
    }


    // @OperaLog(operaModule = "从服务输入图层数据(批量写入)",operaType = OperaLogConstant.CREATE,operaDesc = "从服务输入图层数据(批量写入)")
    // @SaCheckPermission("sys:write")
    // @PostMapping("/importLayerDataFromService")
    // @Operation(summary = "从服务输入图层数据(批量写入)", description = "从服务输入图层数据(批量写入)")
    // @ApiOperationSupport(order = 10)
    // @Transactional(rollbackFor = Exception.class)
    // @RequestLock(prefix = "importLayerDataFromService")
    // public JsonResult importLayerDataFromService(@RequestBody List<MetadataTablestructuresDto> metadataTablestructuresList,@RequestBody List<MetadataTablestructurefieldsDto> metadataTablestructurefieldsDtoList) throws Exception {
    //         return this.dbManageService.importLayerDataFromService(metadataTablestructuresList,metadataTablestructurefieldsDtoList);
    // }


    @OperaLog(operaModule = "从服务输入图层数据(批量写入)",operaType = OperaLogConstant.CREATE,operaDesc = "从服务输入图层数据(批量写入)")
    @SaCheckPermission("sys:write")
    @PostMapping("/importLayerDataFromService")
    @Operation(summary = "从服务输入图层数据(批量写入)", description = "从服务输入图层数据(批量写入)")
    @ApiOperationSupport(order = 10)
    @Transactional(rollbackFor = Exception.class)
    @RequestLock(prefix = "importLayerDataFromService")
    public JsonResult importLayerDataFromService(@RequestBody MetadataTablestructuresAllDto dto) throws Exception {
        return this.dbManageService.importLayerDataFromService(dto);
    }

}
