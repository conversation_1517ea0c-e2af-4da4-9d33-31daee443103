package com.bjcj.web.safetyManage;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.common.utils.domain.Ztree;
import com.bjcj.model.dto.safetyManage.*;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlan;
import com.bjcj.model.po.safetyManage.*;
import com.bjcj.service.platformConf.PlatformAppConfService;
import com.bjcj.service.platformManage.specialPlan.SpecialPlanService;
import com.bjcj.service.safetyManage.*;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
* 角色表(sys_role)表控制层
*
* <AUTHOR>
* @date 2023.11.29
*/
@RestController
@RequestMapping("/role")
@Tag(name = "角色管理")
@Validated
public class SafetyRoleController {
    /**
    * 服务对象
    */
    @Resource
    private SafetyRoleService safetyRoleService;

    @Resource
    MenuService menuService;

    @Resource
    RoleMenuService roleMenuService;

    @Resource
    RolePermsService rolesService;

    @Resource
    SysPermissionService sysPermissionService;

    @Resource
    SysRoleProdService sysRoleProdService;

    @Resource
    SysRoleFuncService sysRoleFuncService;

    @Resource
    SysRoleCateService sysRoleCateService;

    @Resource
    SysRoleBusinessService sysRoleBusinessService;

    @Resource
    SpecialPlanService specialPlanService;

    @Resource
    PlatformAppConfService platformAppConfService;

    @OperaLog(operaModule = "角色管理-新增/编辑角色",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "新增/编辑角色")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑角色", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "addOrEdit")
    public JsonResult addOrEdit(@Validated @RequestBody SafetyRoleDto dto) {
        return safetyRoleService.saveData(dto);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/page")
    @Operation(summary = "角色列表分页", description = "带搜索框模糊查询(可搜名称和类型)")
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "searchStr", description = "名称搜索", required = false)
    })
    @ApiOperationSupport(order = 1)
    public JsonResult<Page<SafetyRole>> page(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam(value = "searchStr",required = false) String searchStr
    ){
        Page<SafetyRole> pager = new Page<>(page, pageSize);
        return safetyRoleService.pageDataSort(pager,searchStr);
    }

    @OperaLog(operaModule = "角色管理-删除角色信息",operaType = OperaLogConstant.DELETE,operaDesc = "删除角色信息")
    @SaCheckPermission("sys:write")
    @DeleteMapping("/del")
    @Operation(summary = "删除角色信息", description = "根据id删除")
    @Parameters({
            @Parameter(name = "id", description = "角色id", required = true)
    })
    @Transactional(rollbackFor = Exception.class)
    @ApiOperationSupport(order = 3)
    public JsonResult del(@RequestParam("id") Long id){
        return this.safetyRoleService.delData(id);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/allInstitutionList")
    @Operation(summary = "展示所有机构", description = "展示所有机构")
    @ApiOperationSupport(order = 4)
    public JsonResult<List<SafetyInstitution>> allInstitutionList(){
        return this.safetyRoleService.allInstitutionList();
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/queryDeptByInstitutionOrParentId")
    @Operation(summary = "查询部门", description = "根据机构id查询顶层部门或者根据父id查询子部门")
    @Parameters({
            @Parameter(name = "institutionId", description = "机构id", required = false),
            @Parameter(name = "deptId", description = "父部门id", required = false)
    })
    @ApiOperationSupport(order = 5)
    public JsonResult<List<SafetyDept>> queryDeptByInstitutionOrParentId(
            @RequestParam(value = "institutionId",required = false) Long institutionId,
            @RequestParam(value = "deptId",required = false) Long deptId){
        return this.safetyRoleService.queryTopDeptByInstitution(institutionId,deptId);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("queryUsersByDeptId")
    @Operation(summary = "查询用户", description = "根据部门id查询其下用户")
    @Parameters({
            @Parameter(name = "deptId", description = "部门id", required = true)
    })
    @ApiOperationSupport(order = 6)
    public JsonResult<List<User>> queryUsersByDeptId(@RequestParam("deptId") Long deptId){
        return this.safetyRoleService.queryUsersByDeptId(deptId);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("queryExistingUserRole")
    @Operation(summary = "查询角色下现有的用户", description = "查询角色下现有的用户")
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "roleId", description = "角色id", required = true)
    })
    @ApiOperationSupport(order = 7)
    public JsonResult<Page<SafetyUserRole>> queryExistingUserRole(@RequestParam("roleId") Long roleId,
                                                                  @RequestParam("current") Integer page,
                                                                  @RequestParam("size") Integer pageSize){
        Page<SafetyUserRole> pager = new Page<>(page, pageSize);
        return this.safetyRoleService.queryExistingUserRole(pager,roleId);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("searchUser")
    @Operation(summary = "搜索用户", description = "根据用户名或真实姓名模糊查询用户")
    @Parameters({
            @Parameter(name = "searchStr", description = "用户名或真实姓名", required = true)
    })
    @ApiOperationSupport(order = 8)
    public JsonResult<List<User>> searchUser(@RequestParam("searchStr") String searchStr){
        return this.safetyRoleService.searchUser(searchStr);
    }

    @OperaLog(operaModule = "角色管理-保存角色用户",operaType = OperaLogConstant.UPDATE,operaDesc = "保存角色用户")
    @SaCheckPermission("sys:write")
    @PatchMapping("saveRoleUsers")
    @Operation(summary = "保存角色用户", description = "保存角色用户")
    @Parameters({
            @Parameter(name = "roleId", description = "角色id", required = true),
            @Parameter(name = "userIds", description = "用户ids逗号分隔", required = true)
    })
    @Transactional(rollbackFor = Exception.class)
    @ApiOperationSupport(order = 9)
    @RequestLock(prefix = "saveRoleUsers")
    public JsonResult saveRoleUsers(@RequestParam("roleId") Long roleId, @RequestParam("userIds") String userIds){
        return this.safetyRoleService.saveRoleUsers(roleId,userIds);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/roleMenuTreeData")
    @Operation(summary = "角色菜单树", description = "回显加载角色菜单列表树")
    @Parameters({
            @Parameter(name = "roleId", description = "角色id", required = true)
    })
    @ApiOperationSupport(order = 10)
    public JsonResult<List<Ztree>> roleMenuTreeData(@RequestParam("roleId") Long roleId){
        return this.menuService.roleMenuTreeData(roleId);
    }

    @OperaLog(operaModule = "角色管理-保存角色菜单及操作权限",operaType = OperaLogConstant.UPDATE,operaDesc = "保存角色菜单及操作权限")
    @SaCheckPermission("sys:write")
    @PatchMapping("saveRoleMenu")
    @Operation(summary = "保存角色菜单及操作权限", description = "保存角色菜单及操作权限")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperationSupport(order = 11)
    @RequestLock(prefix = "saveRoleMenu")
    public JsonResult saveRoleMenu(@Valid @RequestBody RoleMenuPermsDto dto){
        return this.roleMenuService.saveRoleMenu(dto);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("getPermsList")
    @Operation(summary = "获取权限列表", description = "获取权限列表")
    @ApiOperationSupport(order = 12)
    public JsonResult<List<SysPermission>> getPermsList(){
        return this.sysPermissionService.getPermsList();
    }

    @SaCheckPermission("sys:read")
    @GetMapping("getRoleMenuAndPerms")
    @Operation(summary = "获取角色菜单及操作权限", description = "获取角色菜单及操作权限")
    @Parameters({
            @Parameter(name = "roleId", description = "角色id", required = true),
            @Parameter(name = "specialPlanId", description = "专题id", required = false)
    })
    @ApiOperationSupport(order = 13)
    public JsonResult<RoleMenuPermsDto> getRoleMenuAndPerms(@RequestParam("roleId") Long roleId,
                                                            @RequestParam(value = "specialPlanId",required = false) Long specialPlanId){
        return this.roleMenuService.getRoleMenuAndPerms(roleId,specialPlanId);
    }

    @OperaLog(operaModule = "角色管理-数据产品授权给角色",operaType = OperaLogConstant.CREATE,operaDesc = "数据产品授权给角色")
    @SaCheckPermission("sys:write")
    @PostMapping("saveRoleProducts")
    @Operation(summary = "保存数据产品角色授权信息", description = "保存数据产品角色授权信息")
    @ApiOperationSupport(order = 14)
    @Transactional(rollbackFor = Exception.class)
    @RequestLock(prefix = "saveRoleProducts")
    public JsonResult saveRoleProducts(@Valid @RequestBody RoleProductsDto dto){
        return this.sysRoleProdService.saveRoleProducts(dto);
    }

    @OperaLog(operaModule = "角色管理-应用服务授权给角色",operaType = OperaLogConstant.CREATE,operaDesc = "应用服务授权给角色")
    @SaCheckPermission("sys:write")
    @PostMapping("saveRoleFunctions")
    @Operation(summary = "保存应用服务角色授权信息", description = "保存应用服务角色授权信息")
    @ApiOperationSupport(order = 15)
    @Transactional(rollbackFor = Exception.class)
    @RequestLock(prefix = "saveRoleFunctions")
    public JsonResult saveRoleFunctions(@Valid @RequestBody RoleFunctionsDto dto){
        return this.sysRoleFuncService.saveRoleFunctions(dto);
    }

    @OperaLog(operaModule = "角色管理-数据资源授权给角色",operaType = OperaLogConstant.CREATE,operaDesc = "数据资源授权给角色")
    @SaCheckPermission("sys:write")
    @PostMapping("saveRoleCate")
    @Operation(summary = "保存数据资源角色授权信息", description = "保存数据资源角色授权信息")
    @ApiOperationSupport(order = 16)
    @Transactional(rollbackFor = Exception.class)
    @RequestLock(prefix = "saveRoleCate")
    public JsonResult saveRoleCate(@Valid @RequestBody SysRoleCateDto dto){
        return this.sysRoleCateService.saveRoleCate(dto);
    }

    @OperaLog(operaModule = "角色管理-业务数据授权给角色",operaType = OperaLogConstant.CREATE,operaDesc = "业务数据授权给角色")
    @SaCheckPermission("sys:write")
    @PostMapping("saveRoleBusiness")
    @Operation(summary = "保存业务数据角色授权信息", description = "保存业务数据角色授权信息")
    @ApiOperationSupport(order = 17)
    @Transactional(rollbackFor = Exception.class)
    @RequestLock(prefix = "saveRoleBusiness")
    public JsonResult saveRoleBusiness(@Valid @RequestBody SysRoleBusinessDto dto){
        return this.sysRoleBusinessService.saveRoleBusiness(dto);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "专题列表", description = "专题列表")
    @ApiOperationSupport(order = 18)
    @GetMapping("/specialPlanList")
    public JsonResult<List<SpecialPlan>> specialPlanList(){
        List<SpecialPlan> list = this.specialPlanService.list();
        list.parallelStream().forEachOrdered(item -> {
            item.setPlatformAppConfShowName(this.platformAppConfService.getById(item.getPlatformAppConfId()).getShowName());
        });
        return JsonResult.success(list);
    }

}