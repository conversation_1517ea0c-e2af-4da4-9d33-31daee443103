package com.bjcj.model.dto.mobile;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *  内容管理配置表
 **/
@Schema(description = "内容管理配置表Dto")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContentConfigDto implements Serializable {

    @Schema(description = "主键")
    private String id;//主键

    @Schema(description = "内容名称")
    private String name;//内容名称

    @Schema(description = "数据源")
    private String dataSource;//数据源

    @Schema(description = "数据表名称")
    private String tableName;//数据表名称

    @Schema(description = "数据表主键字段名称")
    private String pk;//数据表主键字段名称

    @Schema(description = "数据表标题字段名称")
    private String title;//数据表标题字段名称

    @Schema(description = "数据表创建人字段名称")
    private String createUser;//数据表创建人字段名称

    @Schema(description = "数据表创建部门字段名称")
    private String createDept;//数据表创建部门字段名称

    @Schema(description = "是否支持附件")
    private Boolean isAttached;//是否支持附件

    @Schema(description = "数据表创建日期字段名称")
    private String createDate;//数据表创建日期字段名称

    @Schema(description = "数据表结束日期字段名称")
    private String endDate;//数据表结束日期字段名称

    @Schema(description = "数据表内容字段名称")
    private String content;//数据表内容字段名称

    @Schema(description = "是否需要登录")
    private Boolean isLogined;//是否需要登录

    @Schema(description = "备注")
    private String remark;//备注

    @Schema(description = "是否显示查询输入框及按钮")
    private Boolean isQuery; //是否显示查询输入框及按钮

    @Schema(description = "是否显示列表图片内容")
    private Boolean isPicShow; //是否显示列表图片内容

    @Schema(description = "是否显示已读状态")
    private Boolean isReadState; //是否显示已读状态

    @Schema(description = "查询条件")
    private String condition; //查询条件

    @Schema(description = "查询类型")
    private String searchType; //查询类型

    @Schema(description = "是否显示评论信息")
    private Boolean isComment; //是否显示评论信息

    @Schema(description = "是否显示分享按钮")
    private Boolean isShare; //是否显示分享按钮

    @Schema(description = "设备类型（手机或平板）")
    private String deviceType; //设备类型（手机或平板）

    @Schema (description = "是否需要查询子类型数据")
    private Boolean isTypeSearch; //是否需要查询子类型数据

    @Schema(description = "页面模板主题风格")
    private String templateStyle; //页面模板主题风格

    @Schema(description = "页面模板主页风格")
    private Integer templeId; //页面模板主页风格

    @Schema(description = "已读关联名称")
    private String readName; //已读关联名称

    @Schema(description = "查询语句条件sql语句")
    private String sqlCondition; //查询语句条件sql语句

    @Schema(description = "是否sql语句查询")
    private Boolean isStatement; //是否sql语句查询

    @Schema(description = "是否阅读标记")
    private Boolean isReadMark; //是否阅读标记

    @Schema(description = "已读关联的配置ID")
    private String readCofigId; //已读关联的配置ID

    @Schema(description = "查询所有的未读和已读的sql语句信息")
    private String querySql; //查询所有的未读和已读的sql语句信息

    @Schema(description = "排序字段")
    private String sortField; //排序字段

    @Schema(description = "是否升序降序")
    private String isSortAsc; //是否升序降序(1:升序 0:降序)

    @Schema(description = "表单配置底部表单按钮样式配置")
    private String footerType; //表单配置底部表单按钮样式配置

    @Schema(description = "是否有上传图片控件表单配置中")
    private String isUploadPic; //是否有上传图片控件表单配置中

    @Schema(description = "内容配置类型（list为列表组件配置）")
    private String type; //内容配置类型（list为列表组件配置）

    @Schema(description = "分页条数")
    private String pageNumber; //分页条数

    @Schema(description = "上传图标的ID，关联sys_mobile_ATTACH表")
    private String icon; //上传图标的ID，关联sys_mobile_ATTACH表

    @Schema(description = "租户ID")
    private String tenantId;


}
