<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.safetyManage.UserMapper">
    <resultMap id="BaseResultMap" type="com.bjcj.model.po.safetyManage.User">
        <!--@mbg.generated-->
        <!--@Table public."sys_user"-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="nick_name" jdbcType="VARCHAR" property="nickName"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="register_user" jdbcType="VARCHAR" property="registerUser"/>
        <result column="institution_id" jdbcType="BIGINT" property="institutionId"/>
        <result column="dept_id" jdbcType="BIGINT" property="deptId"/>
        <result column="show_sort" jdbcType="INTEGER" property="showSort"/>
        <result column="register_time" jdbcType="VARCHAR" property="registerTime"/>
        <result column="active_status" jdbcType="INTEGER" property="activeStatus"/>
        <result column="role_ids" jdbcType="VARCHAR" property="roleIds"/>
        <result column="access_permissions_ids" jdbcType="VARCHAR" property="accessPermissionsIds"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
        <result column="operater" jdbcType="VARCHAR" property="operater"/>
        <result column="telephone" jdbcType="VARCHAR" property="telephone"/>
        <result column="office_phone" jdbcType="VARCHAR" property="officePhone"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="family_phone" jdbcType="VARCHAR" property="familyPhone"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, nick_name, username, "password", register_user, institution_id, dept_id, show_sort,
        register_time, active_status, role_ids, access_permissions_ids, create_time, update_time,
        operater, telephone, office_phone, email, family_phone
    </sql>

    <select id="selectUserRole" resultType="string">
        select sr.role_type
        from sys_role sr
        left join sys_user_role sur on sur.role_id = sr.id
        where sur.user_id = #{id}
    </select>

    <update id="modifyPassword">
        update sys_user set password=#{encryption} where id=#{userId}
    </update>

    <select id="selectUserRoleName" resultType="java.lang.String">
        select string_agg(sr.role_name,',') as role_name
        from sys_role sr
        left join sys_user_role sur on sur.role_id = sr.id
        where sur.user_id = #{userId}
    </select>


    <!-- 根据当前登录用户ID获取对应用户权限 -->
    <select id="queryUserPermissions" resultType="java.lang.String">
        SELECT
            DISTINCT M.perms
        FROM
            sys_menu AS M
            INNER JOIN sys_role_menu sr ON sr.menu_id = M.ID
            INNER JOIN sys_user_role ur ON ur.role_id = sr.role_id
        WHERE
            ur.user_id = #{userId}
    </select>
</mapper>