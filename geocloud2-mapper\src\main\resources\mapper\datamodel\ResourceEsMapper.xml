<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.ResourceEsMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.ResourceEs">
    <!--@mbg.generated-->
    <!--@Table public.resource_es-->
    <result column="id" jdbcType="CHAR" property="id" />
    <result column="tableid" jdbcType="CHAR" property="tableid" />
    <result column="topicname" jdbcType="VARCHAR" property="topicname" />
    <result column="updatetime" jdbcType="TIMESTAMP" property="updatetime" />
    <result column="status" jdbcType="NUMERIC" property="status" />
    <result column="samplingfrequency" jdbcType="VARCHAR" property="samplingfrequency" />
    <result column="samplingtime" jdbcType="NUMERIC" property="samplingtime" />
    <result column="isdeleteexitindex" jdbcType="BOOLEAN" property="isdeleteexitindex" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tableid, topicname, updatetime, "status", samplingfrequency, samplingtime, isdeleteexitindex
  </sql>
</mapper>