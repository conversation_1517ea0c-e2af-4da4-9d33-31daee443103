package com.bjcj.service.naturalresources.categories;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.cloudportal.UserCollectedMapper;
import com.bjcj.mapper.naturalresources.categories.*;
import com.bjcj.mapper.naturalresources.resReview.UserServrReviewMapper;
import com.bjcj.mapper.platformManage.specialPlan.SpecialPlanDataMapper;
import com.bjcj.mapper.platformManage.specialPlan.SpecialPlanMapper;
import com.bjcj.mapper.safetyManage.SafetyUserRoleMapper;
import com.bjcj.mapper.safetyManage.SysRoleCateMapper;
import com.bjcj.model.po.cloudportal.UserCollected2;
import com.bjcj.model.po.naturalresources.categories.*;
import com.bjcj.model.po.naturalresources.resReview.UserServrReview;
import com.bjcj.model.po.safetyManage.SafetyUserRole;
import com.bjcj.model.po.safetyManage.SysRoleCate;
import com.bjcj.model.vo.naturalresources.categories.CategoriesParamVo;
import com.bjcj.model.vo.naturalresources.categories.CategoriesServiceVo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/11/29 10:07 周三
 */
@Service
public class CategoriesServiceService extends ServiceImpl<CategoriesServiceMapper, CategoriesService> {

    @Resource
    private CategoriesServiceMapper categoriesServiceMapper;

    @Resource
    private CategoriesService2Mapper categoriesService2Mapper;

    @Resource
    private CategoriesDataInfoMapper categoriesDataInfoMapper;

    @Resource
    private UserCollectedMapper userCollectedMapper;

    @Resource
    private UserServrReviewMapper userServrReviewMapper;

    @Resource
    SafetyUserRoleMapper safetyUserRoleMapper;

    @Resource
    SysRoleCateMapper sysRoleCateMapper;

    @Resource
    SpecialPlanMapper specialPlanMapper;

    @Resource
    SpecialPlanDataMapper specialPlanDataMapper;

    @Resource
    ResourceTagsMapper resourceTagsMapper;

    @Resource
    ResourceServicesMapper resourceServicesMapper;

    @Resource
    ResourceServices2Mapper resourceServices2Mapper;

    public JsonResult<IPage<CategoriesServiceVo>> lists(CategoriesParamVo categoriesParamVo){
        String name = categoriesParamVo.getName();
        String id = categoriesParamVo.getId();
        int auth = categoriesParamVo.getAuth();
        int current = categoriesParamVo.getCurrent();
        int size = categoriesParamVo.getSize();
        Long roleId = categoriesParamVo.getRoleId();
        Page<CategoriesServiceVo> page = new Page<>(current, size);

        List<Long> ids = new ArrayList<>();

        LambdaQueryWrapper<CategoriesDataInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(id),CategoriesDataInfo::getCatalogue, id)
                .eq(CategoriesDataInfo::getDel, 0)
                .ne(CategoriesDataInfo::getAuthItype,2);  //非公开的数据
        List<CategoriesDataInfo> infolist = categoriesDataInfoMapper.selectList(wrapper);
        if(infolist.size() > 0){
            infolist.stream().forEach(item -> {
                ids.add(item.getId());
            });
        }

        IPage<CategoriesServiceVo> list = categoriesServiceMapper.lists(name,id,ids,auth,roleId,page);
        return JsonResult.success(list);
    }

    public JsonResult<List<ResourceServices>> findById(String id){
        LambdaQueryWrapper<ResourceServices> wrapper = new LambdaQueryWrapper<ResourceServices>();
        wrapper.eq(ResourceServices::getDataitemid, id);
        List<ResourceServices> list = resourceServicesMapper.selectList(wrapper);
        return JsonResult.success(list);
    }

    public JsonResult del(String id){
        return this.resourceServicesMapper.deleteById(id) > 0 ? JsonResult.success() : JsonResult.error();
    }

    public JsonResult uptStatus(String id, int status){
        LambdaUpdateWrapper<ResourceServices> wrapper = new LambdaUpdateWrapper<ResourceServices>();
        wrapper.set(ResourceServices::getStatus, status)
                .eq(ResourceServices::getId, id);
        int result = resourceServicesMapper.update(wrapper);
        if(result < 1){
            return JsonResult.error();
        }
        return JsonResult.success();
    }

    public JsonResult updShow(String id, Boolean show){
        LambdaUpdateWrapper<ResourceServices> wrapper = new LambdaUpdateWrapper<ResourceServices>();
        wrapper.set(ResourceServices::getIsvisiable, show)
                .eq(ResourceServices::getId, id);
        int result = resourceServicesMapper.update(wrapper);
        if(result < 1){
            return JsonResult.error();
        }
        return JsonResult.success();
    }

    public JsonResult<Page<ResourceServices3>> hotData(Page<ResourceServices3> pager, Integer days) {
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        //计算当前日期到前days天的日期
        LocalDateTime nowDay = LocalDateTime.now();
        LocalDateTime betweenDay;
        if(Objects.nonNull(days)) betweenDay = nowDay.minusDays(days);
        else betweenDay = nowDay.minusDays(365*80);
        LambdaQueryWrapper<ResourceServices3> wrapper = new LambdaQueryWrapper<ResourceServices3>();
        wrapper.between(ResourceServices3::getRegisterdate, betweenDay, nowDay)
                .eq(ResourceServices3::getStatus,1)
                .orderByDesc(ResourceServices3::getAccesscount);
        Page<ResourceServices3> page = this.resourceServices2Mapper.selectPage(pager, wrapper);
        //写入是否收藏和当前数据权限信息
        page.getRecords().parallelStream().forEachOrdered(cataservice -> {
            UserCollected2 userCollected = this.userCollectedMapper.selectOneData(userId, cataservice.getId());
            cataservice.setIsCollect(userCollected != null);
            UserServrReview userServrReview = this.userServrReviewMapper.selectByUserIdAndResId(userId, cataservice.getId());
            //查询用户是否超管  //todo 超管查看所有数据的权限也去掉,现在专题里都需要申请
            // Boolean admin = checkIsAdmin.checkIsAdmin(principal.getId());
            // if(admin) cataservice.setHasPermission("2");
            // else{

            if(Objects.isNull(userServrReview)) cataservice.setHasPermission("0");
            else{
                if(userServrReview.getReviewStatus() == 0) cataservice.setHasPermission("3");
                if(userServrReview.getReviewStatus() == 1) cataservice.setHasPermission("1");
                if(userServrReview.getReviewStatus() == 2) cataservice.setHasPermission("0");
            }

            //如果父级datainfo中的类型是公开  设置为2  无需申请
            //安全0 公开1 私有2
            int authItype = cataservice.getAuthoritytype();
            if(authItype == 1) cataservice.setHasPermission("2");
            // }
        });
        //再查询当前登录用户的角色,去主动授权的表中按角色查询哪些数据是已经被授权的,然后权限标识字段再重新覆盖一下
        List<Long> roleIds = new ArrayList<>();
        List<SafetyUserRole> userRoles = this.safetyUserRoleMapper.selectList(new LambdaQueryWrapper<SafetyUserRole>().eq(SafetyUserRole::getUserId, userId));
        if(!userRoles.isEmpty()){
            roleIds.addAll(userRoles.stream().map(item -> item.getRoleId()).toList());
            List<SysRoleCate> sysRoleCateList = this.sysRoleCateMapper.selectList(new LambdaQueryWrapper<SysRoleCate>().in(SysRoleCate::getRoleId, roleIds));
            List<String> cataIds = sysRoleCateList.stream().map(SysRoleCate::getCateId).toList();
            page.getRecords().parallelStream().forEachOrdered(cataservice -> {
                if(cataIds.contains(cataservice.getId())){
                    cataservice.setHasPermission("1");
                }
            });
        }
        page.getRecords().forEach(cataservice -> {
            cataservice.setUrl("涉密数据,无权查看");
        });
        return JsonResult.success(page);
    }

    public JsonResult<Page<ResourceServices3>> newData(Page<ResourceServices3> pager, Integer days) {
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        //计算当前日期到前days天的日期
        LocalDateTime nowDay = LocalDateTime.now();
        LocalDateTime betweenDay;
        if(Objects.nonNull(days)) betweenDay = nowDay.minusDays(days);
        else betweenDay = nowDay.minusDays(365*80);
        LambdaQueryWrapper<ResourceServices3> wrapper = new LambdaQueryWrapper<ResourceServices3>();
        wrapper.between(ResourceServices3::getRegisterdate, betweenDay, nowDay)
                .eq(ResourceServices3::getStatus,1)
                .orderByDesc(ResourceServices3::getRegisterdate);
        Page<ResourceServices3> page = this.resourceServices2Mapper.selectPage(pager, wrapper);
        //写入是否收藏和当前数据权限信息
        page.getRecords().parallelStream().forEachOrdered(cataservice -> {
            UserCollected2 userCollected = this.userCollectedMapper.selectOneData(userId, cataservice.getId());
            cataservice.setIsCollect(userCollected != null);
            UserServrReview userServrReview = this.userServrReviewMapper.selectByUserIdAndResId(userId, cataservice.getId());
            //查询用户是否超管  //todo 超管查看所有数据的权限也去掉,现在专题里都需要申请
            // Boolean admin = checkIsAdmin.checkIsAdmin(principal.getId());
            // if(admin) cataservice.setHasPermission("2");
            // else{

            if(Objects.isNull(userServrReview)) cataservice.setHasPermission("0");
            else{
                if(userServrReview.getReviewStatus() == 0) cataservice.setHasPermission("3");
                if(userServrReview.getReviewStatus() == 1) cataservice.setHasPermission("1");
                if(userServrReview.getReviewStatus() == 2) cataservice.setHasPermission("0");
            }

            //如果父级datainfo中的类型是公开  设置为2  无需申请
            //安全0 公开1 私有2
            int authItype = cataservice.getAuthoritytype();
            if(authItype == 1) cataservice.setHasPermission("2");
            // }
        });
        //再查询当前登录用户的角色,去主动授权的表中按角色查询哪些数据是已经被授权的,然后权限标识字段再重新覆盖一下
        List<Long> roleIds = new ArrayList<>();
        List<SafetyUserRole> userRoles = this.safetyUserRoleMapper.selectList(new LambdaQueryWrapper<SafetyUserRole>().eq(SafetyUserRole::getUserId, userId));
        if(!userRoles.isEmpty()){
            roleIds.addAll(userRoles.stream().map(SafetyUserRole::getRoleId).toList());
            List<SysRoleCate> sysRoleCateList = this.sysRoleCateMapper.selectList(new LambdaQueryWrapper<SysRoleCate>().in(SysRoleCate::getRoleId, roleIds));
            List<String> cataIds = sysRoleCateList.stream().map(SysRoleCate::getCateId).toList();
            page.getRecords().parallelStream().forEachOrdered(cataservice -> {
                if(cataIds.contains(cataservice.getId())){
                    cataservice.setHasPermission("1");
                }
            });
        }
        page.getRecords().forEach(cata -> {
            cata.setUrl("涉密数据,无权查看");
        });
        return JsonResult.success(page);
    }

    public JsonResult<String> allLabels() {
        List<ResourceServices> serviceList = this.resourceServicesMapper.selectList(new LambdaQueryWrapper<ResourceServices>()
                .eq(ResourceServices::getIsvisiable,true));
        List<String> serviceIds = serviceList.stream().map(item -> item.getId().trim()).toList();
        List<ResourceTags> resourceTags = this.resourceTagsMapper.selectList(new LambdaQueryWrapper<ResourceTags>().in(ResourceTags::getResourceid, serviceIds));
        List<String> tagNameList = resourceTags.stream().map(item -> item.getName()).toList();
        String result = String.join(",", tagNameList);
        return JsonResult.build(200,"成功",result);
    }
}
