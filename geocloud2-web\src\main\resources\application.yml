server:
    port: 8012
    servlet:
        context-path: /geocloud2

spring:
    application:
        name: geocloud2
    profiles:
        active: local
    boot:
        admin:
            ## admin 客户端连接服务端
            client:
                # 为了显示客户端的ip否则是以主机名显示的，这样需要添加hosts影射。
                instance:
                    service-host-type: ip
                url: http://localhost:8010/admin/
                username: admin
                password: cj888

management:
    endpoints:
        web:
            exposure:
                include: "*"
    endpoint:
        health:
            show-details: ALWAYS




# springdoc-openapi项目配置
springdoc:
    api-docs:
        enabled: true
    swagger-ui:
        operationsSorter: method
        enabled: true
        tagsSorter: alpha
    group-configs:
        -   group: 'default'
            paths-to-match: '/**'
            packages-to-scan: com.bjcj.web
knife4j:
    enable: true
    # 开启生产环境屏蔽
    production: false
    setting:
        language: zh_cn
        swagger-model-name: 实体类列表
        enable-open-api: false
        enable-version: true


