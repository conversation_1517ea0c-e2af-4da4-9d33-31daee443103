package com.bjcj.service.datamodel;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.datamodel.MetadataTablestructuresMapper;
import com.bjcj.model.dto.datamodel.MetadataTablestructuresDto;
import com.bjcj.model.po.datamodel.MetadataTablestructures;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 *@Author：qinyi
 *@Date：2024/4/19  14:53
*/
@Service
public class MetadataTablestructuresService extends ServiceImpl<MetadataTablestructuresMapper, MetadataTablestructures> {

    @Resource
    MetadataTablestructuresMapper mapper;

    public JsonResult saveData(MetadataTablestructuresDto dto) {
        MetadataTablestructures metadataTablestructures = BeanUtil.copyProperties(dto, MetadataTablestructures.class);
        if(Objects.isNull(metadataTablestructures.getTablestructureid())){
            //验证名称或显示名称重复数据
            LambdaQueryWrapper<MetadataTablestructures> queryWrapper = new LambdaQueryWrapper<MetadataTablestructures>();
            queryWrapper.eq(MetadataTablestructures::getDatastandardid,metadataTablestructures.getDatastandardid());
            queryWrapper.and(i->i.eq(MetadataTablestructures::getName,metadataTablestructures.getName()).or().eq(MetadataTablestructures::getDisplayname,metadataTablestructures.getDisplayname()));
            List<MetadataTablestructures> list = this.mapper.selectList(queryWrapper);
            if(!list.isEmpty()){
                return JsonResult.error("名称或显示名称重复");
            }
            this.mapper.insert(metadataTablestructures);
        }else{
            this.mapper.updateById(metadataTablestructures);
        }
        return JsonResult.success();
    }
}
