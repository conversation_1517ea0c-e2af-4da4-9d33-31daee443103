<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.DataLayerFieldMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.DataLayerField">
    <!--@mbg.generated-->
    <!--@Table public.data_layer_field-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
    <result column="standard_name" jdbcType="VARCHAR" property="standardName" />
    <result column="field_type_id" jdbcType="BIGINT" property="fieldTypeId" />
    <result column="field_length" jdbcType="INTEGER" property="fieldLength" />
    <result column="field_accuracy" jdbcType="INTEGER" property="fieldAccuracy" />
    <result column="decimal_places" jdbcType="INTEGER" property="decimalPlaces" />
    <result column="is_must" jdbcType="BOOLEAN" property="isMust" />
    <result column="is_null" jdbcType="BOOLEAN" property="isNull" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="statistics_type_id" jdbcType="BIGINT" property="statisticsTypeId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="data_layer_id" jdbcType="BIGINT" property="dataLayerId" />
    <result column="data_standard_id" jdbcType="BIGINT" property="dataStandardId" />
    <result column="show_sort" jdbcType="INTEGER" property="showSort" />
    <result column="is_sort" jdbcType="BOOLEAN" property="isSort" />
    <result column="is_show" jdbcType="BOOLEAN" property="isShow" />
  </resultMap>
</mapper>