package com.bjcj.model.po.naturalresources.categories;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName CategoriesDataInfo
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/11/28 9:31
 */
@Schema(description="数据项信息")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "categories_data_info")
public class CategoriesDataInfo implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="", hidden = true)
    private Long id;

    @NotBlank(message = "数据目录名称不能为空")
    @TableField(value = "name")
    @Schema(description="数据目录名称")
    private String name;

    @NotBlank(message = "数据目录标识不能为空")
    @TableField(value = "catalogue")
    @Schema(description="资源目录")
    private String catalogue;

    @NotBlank(message = "数据目录标识不能为空")
    @TableField(value = "district")
    @Schema(description="行政区")
    private String district;

    @TableField(value = "year")
    @Schema(description="年份")
    private String year;

    @NotBlank(message = "比例尺不能为空")
    @TableField(value = "scale")
    @Schema(description="比例尺")
    private String scale;

    @NotBlank(message = "坐标系类型不能为空")
    @TableField(value = "coordinate")
    @Schema(description="坐标系类型")
    private String coordinate;

    @TableField(value = "district_bs")
    @Schema(description="省市县标识")
    private String districtBs;

    @TableField(value = "data_itype")
    @Schema(description="数据类型(1矢量数据2栅格数据)")
    private int dataItype;

    @TableField(value = "auth_itype")
    @Schema(description="权限类型（1私有2公开3安全）")
    private int authItype;

    @TableField(value = "show")
    @Schema(description="是否显示（0显示1不显示）")
    private int show;

    @TableField(value = "status")
    @Schema(description="状态（0不运行1运行）")
    private Boolean status;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description="数据类型(1矢量数据2栅格数据)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "del")
    @Schema(description="删除标识（0未删除1已删除）")
    private int del;

    @TableField(value = "publish_institution_name")
    @Schema(description="发布单位")
    private String publishInstitutionName;

    @TableField(value = "review_status")
    @Schema(description="注册审核状态")
    private Integer registerReviewStatus;

    @TableField(value = "operater")
    @Schema(description="最后操作人")
    private String operater;

}
