package com.bjcj.common.utils;

/**
 * <AUTHOR>
 * @date 2020/3/28 17:32
 */
public enum Code {

    /**
     * 参数类型不匹配
     */
    ARGUMENT_TYPE_MISMATCH(104),

    /**
     * 成功
     */
    SUCCESS(200),

    /**
     * 参数错误
     */
    BAD_REQUEST(400),

    /**
     * 找不到资源
     */
    NOT_FOUND(404),

    /**
     * 权限不足
     */
    SHIRO_ERROR(407),

    /**
     * 错误
     */
    ERROR(500),

    /**
     * 请求方式不支持
     */
    REQ_METHOD_NOT_SUPPORT(501),

    /**
     * 未知错误
     */
    UNKNOWN_EXCEPTION(600),

    /**
     * 添加失败
     */
    ADD_ERROR(2000),

    /**
     * 更新失败
     */
    UPDATE_ERROR(2001),

    /**
     * 删除失败
     */
    DELETE_ERROR(2002),

    /**
     * 查找失败
     */
    GET_ERROR(2003),

    /**
     * 导入失败
     */
    IMPORT_ERROR(2004),

    /**
     * 用户名或密码错误
     */
    USER_PWD_ERROR(1000),

    /**
     * 用户不存在
     */
    USER_NOT_ERROR(1001),

    /**
     * 登录超时，请重新登录
     */
    LOGIN_TIME_OUT(1002),

    /**
     * 用户未登录，请进行登录
     */
    USER_NOT_LOGIN(1003),

    /**
     * 账号锁定
     */
    USER_LOCK(1004),

    /**
     * 非法令牌
     */
    ILLEGAL_TOKEN(5000),

    /**
     * 其他客户端登录
     */
    OTHER_CLIENT_LOGIN(5001),

    /**
     * 令牌过期
     */
    TOKEN_EXPIRED(5002),

    ;

    private final int value;

    Code(int value) {
        this.value = value;
    }

    public int value() {
        return this.value;
    }

}
