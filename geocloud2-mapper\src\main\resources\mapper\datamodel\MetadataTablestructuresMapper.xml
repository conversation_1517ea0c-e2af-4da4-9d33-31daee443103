<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.MetadataTablestructuresMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.MetadataTablestructures">
    <!--@mbg.generated-->
    <!--@Table public.metadata_tablestructures-->
    <id column="tablestructureid" jdbcType="CHAR" property="tablestructureid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="shapetype" jdbcType="NUMERIC" property="shapetype" />
    <result column="tabletype" jdbcType="NUMERIC" property="tabletype" />
    <result column="displayname" jdbcType="VARCHAR" property="displayname" />
    <result column="displayfieldid" jdbcType="CHAR" property="displayfieldid" />
    <result column="datastandardid" jdbcType="CHAR" property="datastandardid" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="required" jdbcType="BOOLEAN" property="required" />
    <result column="displayorder" jdbcType="NUMERIC" property="displayorder" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    tablestructureid, "name", description, shapetype, tabletype, displayname, displayfieldid, 
    datastandardid, code, required, displayorder
  </sql>
</mapper>