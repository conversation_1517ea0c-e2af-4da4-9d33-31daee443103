package com.bjcj.web.naturalresources.categories;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.naturalresources.categories.ResourceDataitems;
import com.bjcj.model.vo.naturalresources.categories.CategoriesDataInfoVo;
import com.bjcj.model.vo.naturalresources.categories.CategoriesVo;
import com.bjcj.model.vo.naturalresources.categories.CategoriesVo2;
import com.bjcj.service.naturalresources.categories.CategoriesDataInfoService;
import com.bjcj.service.naturalresources.categories.ResourceDataitemsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/11/28 10:06 周二
 * @update author qinyi
 * @date 2024-4-26
 */
@Tag(name = "数据服务管理(数据项信息)", description = "数据服务管理(数据项信息)")
@RestController
@Slf4j
@RequestMapping(value = "/categories_dataInfo")
public class CategoriesDataInfoController {

    @Resource
    private CategoriesDataInfoService categoriesDataInfoService;

    @Resource
    private ResourceDataitemsService resourceDataitemsService;


    @SaCheckPermission("sys:read")
    @Operation(summary = "数据项信息列表")
    @PostMapping(value = "/treeList")
    public JsonResult<IPage<CategoriesDataInfoVo>> treeList(@RequestParam int index, String name, String id, int current, int size){

        return categoriesDataInfoService.treeList(index,name,id,current,size);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "1.数据项信息列表")
    @PostMapping(value = "/treeList2")
    public JsonResult<IPage<ResourceDataitems>> treeList2(@RequestParam int index, String name, String id, int current, int size){
        return resourceDataitemsService.treeList(index,name,id,current,size);
    }

    @SaCheckPermission("sys:read")
    @OperaLog(
            operaModule = "根据id查询数据项信息及数据服务、物理数据、要素服务",
            operaType = OperaLogConstant.LOOK,
            operaDesc = "根据id查询数据项信息及数据服务、物理数据、要素服务"
    )

    @Operation(summary = "根据id查询数据项信息及数据服务、物理数据、要素服务")
    @GetMapping(value = "/findById/{id}")
    public JsonResult<CategoriesVo> findById(@PathVariable Long id){

        return categoriesDataInfoService.findById(id);
    }

    @SaCheckPermission("sys:read")
    @OperaLog(
            operaModule = "根据id查询数据项信息及数据服务、物理数据、要素服务",
            operaType = OperaLogConstant.LOOK,
            operaDesc = "根据id查询数据项信息及数据服务、物理数据、要素服务"
    )

    @Operation(summary = "2.根据id查询数据项信息及数据服务、物理数据、要素服务")
    @GetMapping(value = "/findById2/{id}")
    public JsonResult<ResourceDataitems> findById2(@PathVariable String id){
        return resourceDataitemsService.findById(id);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "注册数据信息",
            operaType = OperaLogConstant.CREATE,
            operaDesc = "注册数据信息"
    )
    @Operation(summary = "注册数据信息")
    @PostMapping(value = "/save")
    public JsonResult save(@RequestBody CategoriesVo categoriesVo){

        return categoriesDataInfoService.saveData(categoriesVo);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "注册数据信息",
            operaType = OperaLogConstant.CREATE,
            operaDesc = "注册数据信息"
    )
    @Operation(summary = "3.注册数据信息")
    @PostMapping(value = "/save2")
    @Transactional(rollbackFor = Exception.class)
    public JsonResult save2(@RequestBody CategoriesVo2 categoriesVo2){
        return resourceDataitemsService.saveData(categoriesVo2);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "修改数据信息",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "修改数据信息"
    )
    @Operation(summary = "修改数据信息")
    @PutMapping(value = "/updates")
    @RequestLock(prefix = "categories_dataInfo_updates")
    public JsonResult updates(@RequestBody @Validated CategoriesVo categoriesVo){

        return categoriesDataInfoService.updates(categoriesVo);
    }

    @SaCheckPermission("sys:write")
    @Operation(summary = "4.修改数据信息")
    @PutMapping(value = "/updates2")
    @RequestLock(prefix = "categories_dataInfo_updates2")
    @Transactional(rollbackFor = Exception.class)
    public JsonResult updates2(@RequestBody @Validated CategoriesVo2 categoriesVo2){

        return resourceDataitemsService.updates(categoriesVo2);
    }


    @SaCheckPermission("sys:del")
    @OperaLog(
            operaModule = "删除数据项信息",
            operaType = OperaLogConstant.DELETE,
            operaDesc = "删除数据项信息"
    )
    @Operation(summary = "删除数据项信息")
    @DeleteMapping(value = "/deleteById/{id}")
    public JsonResult deleteById(@PathVariable Long id){

        return categoriesDataInfoService.deleteById(id);
    }

    @SaCheckPermission("sys:del")
    @OperaLog(
            operaModule = "删除数据项信息",
            operaType = OperaLogConstant.DELETE,
            operaDesc = "删除数据项信息"
    )
    @Operation(summary = "5.删除数据项信息")
    @DeleteMapping(value = "/deleteById2/{id}")
    @Transactional(rollbackFor = Exception.class)
    public JsonResult deleteById2(@PathVariable String id){
        return this.resourceDataitemsService.deleteById(id);
    }


    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "状态修改",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "状态修改"
    )
    @Operation(summary = "状态修改")
    @PostMapping(value = "/uptStatus")
    public JsonResult uptStatus(@RequestParam Long id, Boolean status){

        return categoriesDataInfoService.uptStatus(id, status);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "状态修改",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "状态修改"
    )
    @Operation(summary = "6.状态修改")
    @PostMapping(value = "/uptStatus2")
    @RequestLock(prefix = "categories_dataInfo_uptStatus2")
    public JsonResult uptStatus2(@RequestParam String id, Boolean status){
        return resourceDataitemsService.uptStatus(id, status);
    }


    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "是否显示",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "是否显示"
    )
    @Operation(summary = "是否显示")
    @PostMapping(value = "/uptShow")
    public JsonResult uptShow(@RequestParam Long id, int show){

        return categoriesDataInfoService.uptShow(id, show);
    }


    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "是否显示",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "是否显示"
    )
    @Operation(summary = "7.是否显示")
    @PostMapping(value = "/uptShow2")
    public JsonResult uptShow2(@RequestParam String id, Boolean isvisiable){

        return resourceDataitemsService.uptShow(id, isvisiable);
    }

}
