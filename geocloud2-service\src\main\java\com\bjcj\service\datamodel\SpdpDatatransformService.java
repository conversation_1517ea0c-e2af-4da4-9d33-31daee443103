package com.bjcj.service.datamodel;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.mapper.datamodel.SpdpDatatransformMapper;
import com.bjcj.model.po.datamodel.SpdpDatatransform;

/**
 *@Author：qinyi
 *@Date：2024/7/1  14:52
*/
@Service
public class SpdpDatatransformService extends ServiceImpl<SpdpDatatransformMapper, SpdpDatatransform> {

}
