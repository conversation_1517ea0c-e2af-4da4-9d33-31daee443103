package com.bjcj.model.po.eswPlatform;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024-8-14  14:33
* 动画导航表
*/
@Schema(description="动画导航")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "flight_routes")
public class FlightRoutes implements Serializable {
    /**
     * 飞行路线标识
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(description="飞行路线标识")
    @Size(max = 40,message = "飞行路线标识最大长度要小于 40")
    private String id;

    /**
     * 名称
     */
    @TableField(value = "name")
    @Schema(description="名称")
    @Size(max = 60,message = "名称最大长度要小于 60")
    @NotBlank(message = "名称不能为空")
    private String name;

    /**
     * 分析参数配置用json保存
     */
    @TableField(value = "content")
    @Schema(description="分析参数配置用json保存")
    @NotBlank(message = "分析参数配置用json保存不能为空")
    private String content;

    /**
     * 模型类型
     */
    @TableField(value = "type")
    @Schema(description="模型类型")
    @Size(max = 255,message = "模型类型最大长度要小于 255")
    @NotBlank(message = "模型类型不能为空")
    private String type;

    /**
     * 飞行速度
     */
    @TableField(value = "speed")
    @Schema(description="飞行速度")
    @Size(max = 255,message = "飞行速度最大长度要小于 255")
    @NotBlank(message = "飞行速度不能为空")
    private String speed;

    /**
     * 视角
     */
    @TableField(value = "perspective")
    @Schema(description="视角")
    @Size(max = 255,message = "视角最大长度要小于 255")
    private String perspective;

    @TableField(value = "special_plan_id")
    @Schema(description="")
    @NotNull(message = "不能为null")
    private Long specialPlanId;

    //create_time
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}