package com.bjcj.model.po.platformManage.modulePlan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/2/27  15:21
*/
@Schema(description="组件配置方案表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "module_plan")
public class ModulePlan implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 组件名称
     */
    @TableField(value = "module_name")
    @Schema(description="组件名称")
    @Size(max = 100,message = "组件名称max length should less than 100")
    @NotBlank(message = "组件名称is not blank")
    private String moduleName;

    /**
     * 显示名称
     */
    @TableField(value = "show_name")
    @Schema(description="显示名称")
    @Size(max = 100,message = "显示名称max length should less than 100")
    @NotBlank(message = "显示名称is not blank")
    private String showName;

    /**
     * 是否延迟0否,1是默认0
     */
    @TableField(value = "is_lag")
    @Schema(description="是否延迟0否,1是默认0")
    private String isLag;

    /**
     * 显示顺序
     */
    @TableField(value = "show_sort")
    @Schema(description="显示顺序")
    private Short showSort;

    /**
     * 组件类型
     */
    @TableField(value = "module_type")
    @Schema(description="组件类型")
    @Size(max = 50,message = "组件类型max length should less than 50")
    private String moduleType;

    /**
     * 组件路径
     */
    @TableField(value = "module_src")
    @Schema(description="组件路径")
    @Size(max = 255,message = "组件路径max length should less than 255")
    private String moduleSrc;

    /**
     * 所属上级id
     */
    @TableField(value = "parent_id")
    @Schema(description="所属上级id")
    private Long parentId;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "update_time",fill = FieldFill.UPDATE)
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 最后操作人
     */
    @TableField(value = "operator")
    @Schema(description="最后操作人")
    @Size(max = 20,message = "最后操作人max length should less than 20")
    @NotBlank(message = "最后操作人is not blank")
    private String operator;

    /**
     * 元数据json
     */
    @TableField(value = "conf_json")
    @Schema(description="元数据json")
    private String confJson;

    /**
     * 元数据解释json
     */
    @TableField(value = "conf_json_schema")
    @Schema(description="元数据解释json")
    private String confJsonSchema;

    @TableField(value = "platform_app_conf_id")
    @Schema(description="平台应用配置id")
    private Long platformAppConfId;

    @TableField(exist = false)
    @Schema(description="子集")
    private List<ModulePlan> children;

    private static final long serialVersionUID = 1L;
}