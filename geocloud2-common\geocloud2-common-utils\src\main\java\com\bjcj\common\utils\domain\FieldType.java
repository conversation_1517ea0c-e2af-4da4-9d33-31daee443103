package com.bjcj.common.utils.domain;

/**
 * @Author：qinyi
 * @Date：2024/1/9 9:56
 */
public enum FieldType {

    /**
     * 图层字段枚举类
     */
    SMALLINTEGER(1L),
    INTEGER(2L),
    SINGLE(3L),
    DOUBLE(4L),
    STRING(5L),
    DATETIME(6L),
    OID(7L),
    GEOMETRY(8L),
    BLOB(9L),
    RASTER(10L),
    GUID(11L),
    GLOBALID(12L),
    XML(13L);

    private final Long typeid;

    FieldType(Long typeid) {
        this.typeid = typeid;
    }

    public Long typeid() {
        return typeid;
    }
}
