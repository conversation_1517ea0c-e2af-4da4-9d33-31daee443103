package com.bjcj.mapper.mobile;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TableMapper {

    @Select("select TABLE_NAME from user_tables")
    List<String> getDataTableNameListOra();

    @Select("select table_name from information_schema.tables where table_schema=#{schema}")
    List<String> getDataTableNameListMysql(@Param("schema") String schema);

    @Select("SELECT table_name FROM information_schema.tables WHERE table_schema = #{schema}")
    List<String> getDataTableNameListPgsql(@Param("schema") String schema);

    @Select("select COLUMN_NAME from USER_TAB_COLUMNS t where TABLE_NAME = #{tableName}")
    List<String> getDataTableColumnListOra(@Param("tableName") String tableName);

    @Select("select COLUMN_NAME from information_schema.COLUMNS  where table_name = #{tableName}")
    List<String> getDataTableColumnListMysql(@Param("tableName") String tableName);

    @Select("select COLUMN_NAME from information_schema.COLUMNS  where table_name = #{tableName}")
    List<String> getDataTableColumnListPgsql(@Param("tableName") String tableName);
}
