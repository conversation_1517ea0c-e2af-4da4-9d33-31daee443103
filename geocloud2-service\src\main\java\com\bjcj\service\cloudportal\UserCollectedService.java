package com.bjcj.service.cloudportal;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.cloudportal.UserCollectedMapper;
import com.bjcj.model.dto.cloudportal.CollectDto;
import com.bjcj.model.po.cloudportal.UserCollected;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 *@Author：qinyi
 *@Date：2024/1/15  9:55
*/
@Service
public class UserCollectedService extends ServiceImpl<UserCollectedMapper, UserCollected> {

    @Resource
    UserCollectedMapper userCollectedMapper;

    public JsonResult collectOrDel(CollectDto dto) {
        UserCollected userCollected =  BeanUtil.copyProperties(dto, UserCollected.class);
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        //收藏
        if (userCollected.getId() == null) {
            //查询是否重复收藏
            if (userCollectedMapper.selectCountByUserIdAndResId(userId,userCollected.getResId()) > 0) {
                return JsonResult.error("您已经收藏过该资源");
            }
            userCollected.setUserId(userId);
            userCollected.setCreateTime(LocalDateTime.now());
            userCollectedMapper.insert(userCollected);
            return JsonResult.success();
        } else {
            userCollectedMapper.deleteById(userCollected.getId());
            return JsonResult.success();
        }
    }

    public JsonResult<Page<UserCollected>> pageData(Page<UserCollected> pager, String searchStr,String resouceType) {
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        List<UserCollected> list = this.userCollectedMapper.selectPageData((pager.getCurrent() - 1) * pager.getSize(),pager.getSize(),searchStr,resouceType,userId);
        int count = this.userCollectedMapper.selectPageDataCount((pager.getCurrent() - 1) * pager.getSize(),pager.getSize(),searchStr,resouceType,userId);
        Page<UserCollected> page = new Page<>();
        page.setRecords(list);
        page.setTotal(count);
        return JsonResult.success(page);
    }
}
