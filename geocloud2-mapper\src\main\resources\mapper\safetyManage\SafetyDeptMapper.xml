<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.safetyManage.SafetyDeptMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.safetyManage.SafetyDept">
    <!--@mbg.generated-->
    <!--@Table public.sys_dept-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="show_sort" jdbcType="INTEGER" property="showSort" />
    <result column="parent_institution_id" jdbcType="BIGINT" property="parentInstitutionId" />
    <result column="parent_dept_id" jdbcType="BIGINT" property="parentDeptId" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
    <result column="operater" jdbcType="VARCHAR" property="operater" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, dept_name, show_sort, parent_institution_id, parent_dept_id, create_time, update_time, 
    operater
  </sql>
</mapper>