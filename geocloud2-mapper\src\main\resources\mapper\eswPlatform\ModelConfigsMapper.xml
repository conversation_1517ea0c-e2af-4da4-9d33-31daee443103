<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.eswPlatform.ModelConfigsMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.eswPlatform.ModelConfigs">
    <!--@mbg.generated-->
    <!--@Table public.model_configs-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="thumbnail_path" jdbcType="VARCHAR" property="thumbnailPath" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="special_plan_id" jdbcType="BIGINT" property="specialPlanId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", "path", description, thumbnail_path, "type", special_plan_id
  </sql>
</mapper>