package com.bjcj.web.platformConf;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.platformConf.GisResConfDto;
import com.bjcj.model.po.platformConf.GisResConf;
import com.bjcj.service.platformConf.GisResConfService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
* GIS server网站配置表(public.gis_res_conf)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/gis_res_conf")
@Validated
@Tag(name = "9-GISServer网站配置")
public class GisResConfController {
    /**
    * 服务对象
    */
    @Resource
    private GisResConfService gisResConfService;

    @SaCheckPermission("sys:read")
    @Operation(summary = "列表", description = "列表")
    @ApiOperationSupport(order = 1)
    @GetMapping("/page")
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "searchStr", description = "名称搜索", required = false)
    })
    public JsonResult<Page<GisResConf>> page(@RequestParam("current") Integer page,
                                             @RequestParam("size") Integer pageSize,
                                             @RequestParam(value = "searchStr",required = false) String searchStr){
        Page<GisResConf> pager = new Page<>(page, pageSize);
        return this.gisResConfService.pageDataSort(pager, searchStr);
    }

    @OperaLog(operaModule = "新增/编辑",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "新增/编辑")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "addOrEdit")
    public JsonResult addOrEdit(@Validated @RequestBody GisResConfDto dto){
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        GisResConf gisResConf = BeanUtil.copyProperties(dto, GisResConf.class);
        gisResConf.setOperator(username);
        return this.gisResConfService.saveOrUpdate(gisResConf) ? JsonResult.success() : JsonResult.error();
    }

    @OperaLog(operaModule = "删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除", description = "删除")
    @ApiOperationSupport(order = 3)
    public JsonResult delete(@PathVariable("id") Long id){
        return this.gisResConfService.removeById(id) ? JsonResult.success() : JsonResult.error();
    }
}