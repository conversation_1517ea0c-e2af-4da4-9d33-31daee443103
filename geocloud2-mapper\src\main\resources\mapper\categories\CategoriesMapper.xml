<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.naturalresources.categories.CategoriesServiceMapper">

  <select id="lists" resultType="com.bjcj.model.vo.naturalresources.categories.CategoriesServiceVo">
    select c.id,c.name,c.sname,c.fwitype,s.cate_id,s.role_id from categories_service c
    left join sys_role_cate s on s.cate_id = c.id and s.role_id = #{roleId}
    where c.del = 0 and c.status=true
    <if test="name!=null and name!='' ">
      and c.name like CONCAT('%', #{name}, '%') or c.sname like CONCAT('%', #{name}, '%')
    </if>
    <if test="ids!=null and ids.size > 0">
      and c.parent_id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
          #{item}
        </foreach>
    </if>
    <if test="ids.size == 0 and id!=''">
      and c.parent_id is null
    </if>
    <if test="auth==1">
    and s.cate_id is not null
    </if>
    order by c.fwsort
  </select>

  <select id="selectServicePage" resultType="com.bjcj.model.po.naturalresources.categories.CategoriesService">
    select cs.* from categories_service cs
    left join categories_data_info cdi on cs.parent_id=cdi.id
    where 1=1
    <if test="searchStr!=null and searchStr!=''">
      and (cs.name like CONCAT('%', #{searchStr}, '%') or cs.sname like CONCAT('%', #{searchStr}, '%') )
    </if>
    <if test="categoriesDataId!=null and categoriesDataId!=''">
      and cdi.catalogue=#{categoriesDataId}
    </if>
    and cs.id in
    <foreach item="item" index="index" collection="cateIds" open="(" separator="," close=")">
      #{item}
    </foreach>
    and cs.del=0 and cs.status=true
    order by cs.create_time desc
    LIMIT #{size} OFFSET #{current}
  </select>

  <select id="selectServicePageCount" resultType="java.lang.Long">
    select count(*) from (
      select cs.* from categories_service cs
      left join categories_data_info cdi on cs.parent_id=cdi.id
      where 1=1
      <if test="searchStr!=null and searchStr!=''">
        and (cs.name like CONCAT('%', #{searchStr}, '%') or cs.sname like CONCAT('%', #{searchStr}, '%') )
      </if>
      <if test="categoriesDataId!=null and categoriesDataId!=''">
        and cdi.catalogue=#{categoriesDataId}
      </if>
      and cs.id in
      <foreach item="item" index="index" collection="cateIds" open="(" separator="," close=")">
        #{item}
      </foreach>
      and cs.del=0 and cs.status=true
    ) c
  </select>

  <select id="selectServicePage2" resultType="com.bjcj.model.po.naturalresources.categories.CategoriesService2">
    select cs.*,cdi.scale,cdi.publish_institution_name from categories_service cs left join categories_data_info cdi on cs.parent_id=cdi.id
    where cs.status = true
      <if test="datainfoids!=null and datainfoids.size>0">
        and cs.parent_id in
        <foreach item="item" index="index" collection="datainfoids" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="startYear!=null and startYear!='' and endYear!=null and endYear!=''">
        and ( cdi.year BETWEEN #{startYear} and #{endYear})
      </if>
      <if test="scale!=null and scale!=''">
        and cdi.scale = #{scale}
      </if>
      <if test="scale!=null and scale!=''">
        and cdi.scale = #{scale}
      </if>
      <if test="publishInstitutionName!=null and publishInstitutionName!=''">
        and cdi.publish_institution_name=#{publishInstitutionName}
      </if>
      and cs.del=0 and cdi.del=0 and cdi.review_status=1 and cdi.status=true
      <if test="ordersql!=null and ordersql!=''">
        ${ordersql}
      </if>
    LIMIT #{size} OFFSET #{current}
  </select>

  <select id="selectServicePage2Count" resultType="int">
    select count(*) from (
    select * from categories_service cs left join categories_data_info cdi on cs.parent_id=cdi.id
    where 1=1
    <if test="datainfoids!=null and datainfoids.size>0">
      and cs.parent_id in
      <foreach item="item" index="index" collection="datainfoids" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="startYear!=null and startYear!='' and endYear!=null and endYear!=''">
      and ( cdi.year BETWEEN #{startYear} and #{endYear})
    </if>
    <if test="scale!=null and scale!=''">
      and cdi.scale = #{scale}
    </if>
    <if test="scale!=null and scale!=''">
      and cdi.scale = #{scale}
    </if>
    <if test="publishInstitutionName!=null and publishInstitutionName!=''">
      and cdi.publish_institution_name=#{publishInstitutionName}
    </if>
    and cs.del=0 and cdi.del=0 and cdi.review_status=1 and cdi.status=true
    ) c
  </select>

  <select id="selectServiceCountByIdIn" resultType="java.lang.Integer">
    select count(*) from (
    select * from categories_service
    where del=0 and status=true
    <if test="datainfoids!=null and datainfoids.size>0">
      and parent_id in
      <foreach item="item" index="index" collection="datainfoids" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    ) c
  </select>

  <select id="selectServicePage3" resultType="com.bjcj.model.po.naturalresources.categories.CategoriesService2">
    select cs.*,cdi.scale,cdi.publish_institution_name from categories_service cs left join categories_data_info cdi on cs.parent_id=cdi.id
    where cs.status = true and cs.tags like CONCAT('%', #{label}, '%')
    <if test="startYear!=null and startYear!='' and endYear!=null and endYear!=''">
      and ( cdi.year BETWEEN #{startYear} and #{endYear})
    </if>
    <if test="scale!=null and scale!=''">
      and cdi.scale = #{scale}
    </if>
    <if test="scale!=null and scale!=''">
      and cdi.scale = #{scale}
    </if>
    <if test="publishInstitutionName!=null and publishInstitutionName!=''">
      and cdi.publish_institution_name=#{publishInstitutionName}
    </if>
    and cs.del=0 and cdi.del=0 and cdi.review_status=1 and cdi.status=true
    <if test="ordersql!=null and ordersql!=''">
      ${ordersql}
    </if>
    LIMIT #{size} OFFSET #{current}
  </select>

  <select id="selectServicePage3Count" resultType="int">
    select count(*) from (
    select * from categories_service cs left join categories_data_info cdi on cs.parent_id=cdi.id
    where cs.tags like CONCAT('%', #{label}, '%')
    <if test="startYear!=null and startYear!='' and endYear!=null and endYear!=''">
      and ( cdi.year BETWEEN #{startYear} and #{endYear})
    </if>
    <if test="scale!=null and scale!=''">
      and cdi.scale = #{scale}
    </if>
    <if test="scale!=null and scale!=''">
      and cdi.scale = #{scale}
    </if>
    <if test="publishInstitutionName!=null and publishInstitutionName!=''">
      and cdi.publish_institution_name=#{publishInstitutionName}
    </if>
    and cs.del=0 and cdi.del=0 and cdi.review_status=1 and cdi.status=true
    ) c
  </select>
</mapper>