package com.bjcj.common.database.handler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
/**
 * @description 布尔类型转换器(转布尔为整形)
 * @methodFullName NumericToBooleanTypeHandler.
 *
 * @return
 * <AUTHOR>
 * @create 2025/6/30
 **/
public class NumericToBooleanTypeHandler extends BaseTypeHandler<Boolean> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Boolean parameter, JdbcType jdbcType) throws SQLException {
        // 将 Boolean 转换为 1 或 0
        ps.setInt(i, parameter ? 1 : 0);
    }

    @Override
    public Boolean getNullableResult(ResultSet rs, String columnName) throws SQLException {
        // 将 numeric 转换为 Boolean
        return rs.getInt(columnName) == 1;
    }

    @Override
    public Boolean getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        // 将 numeric 转换为 Boolean
        return rs.getInt(columnIndex) == 1;
    }

    @Override
    public Boolean getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        // 将 numeric 转换为 Boolean
        return cs.getInt(columnIndex) == 1;
    }
}
