package com.bjcj.model.po.mobile;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *  模板数据
 **/
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("sys_mobile_CONFIGPAGE")
public class PdaConfigPage {

    @TableId("OBJECTID")
    @Schema(description = "内容管理模板页面类型id")
    private String objectId;

    @TableField("CONFIGID")
    @Schema(description = "内容管理配置id")
    private String configId;

    @TableField("TYPENAME")
    @Schema(description = "类型名称")
    private String typeName;

    @TableField("DETAILPAGEURL")
    @Schema(description = "详情页URL")
    private String detailPageUrl;

    @TableField("TABNAME")
    @Schema(description = "显示名称")
    private String tabName;

    @TableField("APPCODE")
    @Schema(description = "app编码")
    private String appCode;

    @TableField("TENANTID")
    @Schema(description = "租户id")
    private String tenantId;


}
