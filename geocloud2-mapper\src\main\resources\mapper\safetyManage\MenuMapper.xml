<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.safetyManage.MenuMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.safetyManage.Menu">
    <!--@mbg.generated-->
    <!--@Table sys_menu-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="menu_name" jdbcType="VARCHAR" property="menuName" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="show_sort" jdbcType="INTEGER" property="showSort" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="target" jdbcType="VARCHAR" property="target" />
    <result column="menu_type" jdbcType="VARCHAR" property="menuType" />
    <result column="visible" jdbcType="VARCHAR" property="visible" />
    <result column="is_refresh" jdbcType="VARCHAR" property="isRefresh" />
    <result column="perms" jdbcType="VARCHAR" property="perms" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
    <result column="operater" jdbcType="VARCHAR" property="operater" />
    <result column="platform_app_conf_id" jdbcType="BIGINT" property="platformAppConfId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, menu_name, parent_id, show_sort, url, target, menu_type, visible, is_refresh,
    perms, icon, create_time, update_time, operater, platform_app_conf_id
  </sql>

  <select id="checkMenuNameUnique" resultMap="BaseResultMap">
    select * from sys_menu
    where menu_name=#{menuName} and parent_id = #{parentId} limit 1
  </select>

  <select id="checkMenuNameUnique2" resultMap="BaseResultMap">
    select * from sys_menu
    where menu_name=#{menuName} and parent_id = #{parentId} and platform_app_conf_id=#{appid} limit 1
  </select>

  <select id="selectCountMenuByParentId" resultType="Integer">
    select count(1) from sys_menu where parent_id=#{menuId}
  </select>

  <select id="selectMenuListByUserId" resultMap="BaseResultMap">
    select distinct m.*
    from sys_menu m
    left join sys_role_menu rm on m.id = rm.menu_id
    left join sys_user_role ur on rm.role_id = ur.role_id
    left join sys_role ro on ur.role_id = ro.id
    where ur.user_id = #{userId}
    and m.platform_app_conf_id is null
    <if test="menuName != null and menuName != ''">
      AND m.menu_name like concat('%', #{menuName}, '%')
    </if>
    order by m.parent_id, m.show_sort
  </select>

  <select id="selectMenuListByUserIdSpecialPlan" resultMap="BaseResultMap">
    select distinct m.*
    from sys_menu m
    left join sys_role_menu rm on m.id = rm.menu_id
    left join sys_user_role ur on rm.role_id = ur.role_id
    left join sys_role ro on ur.role_id = ro.id
    where ur.user_id = #{userId}
    and m.platform_app_conf_id=#{platFormAppConfId} and m.visible='0'
    order by m.parent_id, m.show_sort
  </select>

  <select id="selectMenuAll" resultMap="BaseResultMap">
    select * from sys_menu where platform_app_conf_id is null order by parent_id, show_sort
  </select>

  <select id="selectMenuAllByUserId" resultMap="BaseResultMap">
    select distinct m.*
    from sys_menu m
    left join sys_role_menu rm on m.id = rm.menu_id
    left join sys_user_role ur on rm.role_id = ur.role_id
    left join sys_role ro on ur.role_id = ro.id
    where ur.user_id = #{userId}
    and m.platform_app_conf_id is null
    order by m.parent_id, m.show_sort
  </select>

  <select id="selectMenuTree" resultType="java.lang.String">
    select m.*
    from sys_menu m
    left join sys_role_menu rm on m.id = rm.menu_id
    where rm.role_id = #{roleId}
    order by m.parent_id, m.show_sort
  </select>
</mapper>