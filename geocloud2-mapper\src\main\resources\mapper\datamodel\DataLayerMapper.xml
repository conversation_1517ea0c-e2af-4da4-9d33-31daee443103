<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.DataLayerMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.DataLayer">
    <!--@mbg.generated-->
    <!--@Table public.data_layer-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="is_must" jdbcType="BOOLEAN" property="isMust" />
    <result column="layer_type_id" jdbcType="BIGINT" property="layerTypeId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="data_standard_id" jdbcType="BIGINT" property="dataStandardId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", show_name, code, is_must, layer_type_id, remark, data_standard_id, create_time, 
    update_time, "operator"
  </sql>
</mapper>