package com.bjcj.model.dto.dict;

import com.bjcj.common.utils.annotation.RequestKeyParam;
import com.bjcj.model.dto.specialPlan.SpecialPlanDataDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/2/1  10:13
*/
/**
    * 字典数据表
    */
@Schema(description="字典数据表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SysDictDataDto implements Serializable {
    /**
     * 字典编码
     */
    @Schema(description="字典编码")
    @NotNull(message = "id不能为空",groups = {SpecialPlanDataDto.EditGroup.class})
    @RequestKeyParam(name = "dictCode")
    private Long dictCode;

    /**
     * 字典排序
     */
    @Schema(description="字典排序")
    @NotNull(message = "字典排序不能为空",groups = {SpecialPlanDataDto.EditGroup.class,SpecialPlanDataDto.AddGroup.class})
    private Integer dictSort;

    /**
     * 字典标签
     */
    @Schema(description="字典标签")
    @Size(max = 100,message = "字典标签max length should less than 100")
    @NotBlank(message = "字典标签不能为空",groups = {SpecialPlanDataDto.EditGroup.class,SpecialPlanDataDto.AddGroup.class})
    private String dictLabel;

    /**
     * 字典键值
     */
    @Schema(description="字典键值")
    @Size(max = 100,message = "字典键值max length should less than 100")
    @NotBlank(message = "字典键值不能为空",groups = {SpecialPlanDataDto.EditGroup.class,SpecialPlanDataDto.AddGroup.class})
    private String dictValue;

    /**
     * 字典类型
     */
    @Schema(description="字典类型")
    @Size(max = 100,message = "字典类型max length should less than 100")
    @NotBlank(message = "字典类型不能为空",groups = {SpecialPlanDataDto.EditGroup.class,SpecialPlanDataDto.AddGroup.class})
    private String dictType;

    /**
     * 样式属性（其他样式扩展）
     */
    @Schema(description="样式属性（其他样式扩展）")
    @Size(max = 100,message = "样式属性（其他样式扩展）max length should less than 100")
    private String cssClass;

    /**
     * 表格回显样式
     */
    @Schema(description="表格回显样式")
    @Size(max = 100,message = "表格回显样式max length should less than 100")
    private String listClass;

    /**
     * 是否默认（Y是 N否）
     */
    @Schema(description="是否默认（Y是 N否）")
    @NotBlank(message = "是否默认（Y是 N否）不能为空",groups = {SpecialPlanDataDto.EditGroup.class,SpecialPlanDataDto.AddGroup.class})
    private String isDefault;

    /**
     * 状态（0正常 1停用）
     */
    @Schema(description="状态（0正常 1停用）")
    @NotBlank(message = "状态（0正常 1停用）不能为空",groups = {SpecialPlanDataDto.EditGroup.class,SpecialPlanDataDto.AddGroup.class})
    private String status;

    /**
     * 创建时间
     */
    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 最后操作人
     */
    @Schema(description="最后操作人")
    @Size(max = 30,message = "最后操作人max length should less than 30")
    private String operater;

    /**
     * 父级id
     */
    @Schema(description="父级id")
    @NotNull(message = "父级id不能为空",groups = {SpecialPlanDataDto.EditGroup.class,SpecialPlanDataDto.AddGroup.class})
    private Long pid;

    /**
     * 组id
     */
    @Schema(description="组名称")
    private String groupName;

    /**
     * 备注
     */
    @Schema(description="备注")
    @Size(max = 500,message = "备注max length should less than 500")
    private String remark;

    // @Schema(description="所属字典id")
    // @NotNull(message = "字典id不能为空",groups = {SpecialPlanDataDto.EditGroup.class,SpecialPlanDataDto.AddGroup.class})
    // private Long dictId;

    private static final long serialVersionUID = 1L;

    public interface AddGroup{
    }
    public interface EditGroup{
    }
}