<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.platformManage.analysisPlan.AnalysisPlanFieldStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.platformManage.analysisPlan.AnalysisPlanFieldStatistics">
    <!--@mbg.generated-->
    <!--@Table public.analysis_plan_field_statistics-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
    <result column="field_width" jdbcType="VARCHAR" property="fieldWidth" />
    <result column="mapping_method" jdbcType="VARCHAR" property="mappingMethod" />
    <result column="mapping_param" jdbcType="VARCHAR" property="mappingParam" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="detail_type" jdbcType="CHAR" property="detailType" />
    <result column="analysis_plan_id" jdbcType="BIGINT" property="analysisPlanId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, field_name, show_name, field_width, mapping_method, mapping_param, "source", 
    detail_type, analysis_plan_id
  </sql>
</mapper>