package com.bjcj.model.po.naturalresources.categories;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/12/1 14:08 周五
 */
@Schema(description="数据服务元数据")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "categories_meta")
public class CategoriesMeta implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="", hidden = true)
    private Long id;

    @TableField(value = "parent_id")
    @Schema(description="父级id")
    private Long parentId;

    @NotBlank(message = "提供方姓名不能为空")
    @TableField(value = "provider_name")
    @Schema(description="提供方姓名")
    private String providerName;

    @NotBlank(message = "提供方编码不能为空")
    @TableField(value = "provider_code")
    @Schema(description="提供方编码")
    private String providerCode;

    @TableField(value = "provide_type")
    @Schema(description="提供方类型")
    private String provideType;

    @TableField(value = "service_web")
    @Schema(description="数据应用层")
    private String serviceWeb;

    @NotBlank(message = "联系人不能为空")
    @TableField(value = "sname")
    @Schema(description="联系人")
    private String sname;

    @NotBlank(message = "联系人电话不能为空")
    @TableField(value = "sphone")
    @Schema(description="联系方式")
    private String sphone;

    @NotBlank(message = "提供方地址不能为空")
    @TableField(value = "provide_address")
    @Schema(description="提供方地址")
    private String provideAddress;

    @TableField(value = "service_form")
    @Schema(description="数据格式")
    private String serviceForm;

    @TableField(value = "service_type")
    @Schema(description="数据类型")
    private String serviceType;

    @TableField(value = "wl_type")
    @Schema(description="网络类型")
    private String wlType;

    @TableField(value = "service_capacity")
    @Schema(description="数据存储容量")
    private String serviceCapacity;

    @TableField(value = "service_time")
    @Schema(description="数据生产时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime serviceTime;

    @TableField(value = "service_itype")
    @Schema(description="数据资源状态")
    private String serviceItype;

    @TableField(value = "service_zy")
    @Schema(description="数据资源摘要")
    private String serviceZy;

    @TableField(value = "service_content")
    @Schema(description="数据资源内容")
    private String serviceContent;

    @TableField(value = "scale")
    @Schema(description="比例尺")
    private String scale;

    @NotBlank(message = "坐标系不能为空")
    @TableField(value = "coordinate")
    @Schema(description="坐标系统")
    private String coordinate;

    @NotBlank(message = "东边经度不能为空")
    @TableField(value = "dlongitude")
    @Schema(description="东边经度")
    private String dlongitude;

    @NotBlank(message = "南边经度不能为空")
    @TableField(value = "nlongitude")
    @Schema(description="南边经度")
    private String nlongitude;

    @NotBlank(message = "西边经度不能为空")
    @TableField(value = "xlongitude")
    @Schema(description="西边经度")
    private String xlongitude;

    @NotBlank(message = "北边经度不能为空")
    @TableField(value = "blongitude")
    @Schema(description="北边经度")
    private String blongitude;

    @TableField(value = "cycle")
    @Schema(description="更新周期")
    private String cycle;

    @TableField(value = "fb_time")
    @Schema(description="发布时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fbTime;

    @TableField(value = "zt_time")
    @Schema(description="停用时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime ztTime;

    @TableField(value = "gx_time")
    @Schema(description="更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gxTime;

    @TableField(value = "zbf")
    @Schema(description="数据资源主办方")
    private String zbf;

    @TableField(value = "bgf")
    @Schema(description="数据资源保管方")
    private String bgf;

    @TableField(value = "scf")
    @Schema(description="数据资源生产方")
    private String scf;

    @TableField(value = "dire_name")
    @Schema(description="关联目录名称")
    private String direName;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}
