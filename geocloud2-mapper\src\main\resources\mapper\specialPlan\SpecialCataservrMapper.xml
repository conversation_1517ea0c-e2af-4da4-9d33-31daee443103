<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.platformManage.specialPlan.SpecialCataservrMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.platformManage.specialPlan.SpecialCataservr">
    <!--@mbg.generated-->
    <!--@Table public.special_cataservr-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="special_plan_data_id" jdbcType="BIGINT" property="specialPlanDataId" />
    <result column="cata_service_id" jdbcType="BIGINT" property="cataServiceId" />
    <result column="service_name" jdbcType="VARCHAR" property="serviceName" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, special_plan_data_id, cata_service_id, service_name, show_name
  </sql>
</mapper>