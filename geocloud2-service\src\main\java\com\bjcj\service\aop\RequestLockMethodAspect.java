package com.bjcj.service.aop;

import com.bjcj.common.utils.annotation.RequestKeyGenerator;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.types.Expiration;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;

/**
 * @Author：qinyi
 * @Date：2024/4/10 11:38
 * @description 请求锁切面处理器
 */
@Aspect
@Configuration
public class RequestLockMethodAspect {
    private final StringRedisTemplate stringRedisTemplate;
    private final RequestKeyGenerator requestKeyGenerator;


    @Autowired
    public RequestLockMethodAspect(StringRedisTemplate stringRedisTemplate, RequestKeyGenerator requestKeyGenerator) {
        this.requestKeyGenerator = requestKeyGenerator;
        this.stringRedisTemplate = stringRedisTemplate;
    }


    /**
     * 实现使用 Redis 分布式锁来防止重复提交，并且允许并发请求。
     * 在方法执行前会尝试获取锁，如果获取成功则执行目标方法，否则直接返回“您的操作太快了，请稍后重试”。在方法执行完成后会释放锁。
     * 实现中使用了 Redis 的 SETNX 命令来实现分布式锁，尝试设置一个键的值，如果键不存在则设置成功，表示获取到了锁。
     * 如果键已经存在，则设置失败，表示已经有其他请求正在处理中，直接返回“您的操作太快了，请稍后重试”。
     * @param joinPoint
     * @return
     */
    @Around("execution(public * * (..)) && @annotation(com.bjcj.common.utils.annotation.RequestLock)")
    public Object interceptor(ProceedingJoinPoint joinPoint) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        RequestLock requestLock = method.getAnnotation(RequestLock.class);
        if (StringUtils.isEmpty(requestLock.prefix())) {
//            throw new RuntimeException("重复提交前缀不能为空");
            return JsonResult.error("重复提交前缀不能为空");
        }
        //获取自定义key
        final String lockKey = requestKeyGenerator.getLockKey(joinPoint);
        final Boolean success = stringRedisTemplate.execute(
                (RedisCallback<Boolean>) connection -> connection.set(lockKey.getBytes(), new byte[0], Expiration.from(requestLock.expire(), requestLock.timeUnit())
                        , RedisStringCommands.SetOption.SET_IF_ABSENT));
        if (!success) {
//            throw new RuntimeException("您的操作太快了,请稍后重试");
            return JsonResult.error("您的操作太快了,请稍后重试");
        }
        try {
            return joinPoint.proceed();
        } catch (Throwable throwable) {
//            throw new RuntimeException("系统异常");
            System.out.println(throwable.getMessage().toString());
            return JsonResult.error("系统异常");
        }
    }

}
