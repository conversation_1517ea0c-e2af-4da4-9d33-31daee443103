package com.bjcj.model.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/12/27 15:01 周三
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description="有道api请求对象")
public class YdReq {

    @NotBlank
    @Schema(description = "待翻译文本")
    private String q;

    @NotBlank
    @Schema(description = "源语言")
    private String from;

    @NotBlank
    @Schema(description = "目标语言")
    private String to;

    @NotBlank
    @Schema(description = "应用ID")
    private String appKey;

    @NotBlank
    @Schema(description = "UUID")
    private String salt;

    @NotBlank
    @Schema(description = "签名")
    private String sign;

    @NotBlank
    @Schema(description = "签名类型")
    private String signType = "v3";

    @NotBlank
    @Schema(description = "当前UTC时间戳(秒)")
    private String curtime;

    // 以下为可选参数 ----------------------------------

    @Schema(description = "翻译结果音频格式，支持mp3")
    private String ext;

    @Schema(description = "翻译结果发音选择")
    private String voice;

    @Schema(description = "是否严格按照指定from和to进行翻译：true/false")
    private String strict;

    @Schema(description = "用户上传的词典")
    private String vocabId;

    @Schema(description = "领域化翻译")
    private String domain = "computers";

    @Schema(description = "拒绝领域化翻译降级-当领域化翻译失败时改为通用翻译")
    private String rejectFallback = "true";

}
