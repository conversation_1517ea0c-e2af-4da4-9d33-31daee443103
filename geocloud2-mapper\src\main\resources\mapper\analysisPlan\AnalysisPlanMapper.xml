<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.platformManage.analysisPlan.AnalysisPlanMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.platformManage.analysisPlan.AnalysisPlan">
    <!--@mbg.generated-->
    <!--@Table public.analysis_plan-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="plan_name" jdbcType="VARCHAR" property="planName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="platform_app_conf_id" jdbcType="BIGINT" property="platformAppConfId" />
    <result column="plan_type" jdbcType="VARCHAR" property="planType" />
    <result column="fx_data_layer_id" jdbcType="BIGINT" property="fxDataLayerId" />
    <result column="bfx_data_layer_id" jdbcType="BIGINT" property="bfxDataLayerId" />
  </resultMap>
</mapper>