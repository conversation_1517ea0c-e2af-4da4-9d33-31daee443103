<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.safetyManage.SafetyRoleMapper">
    <resultMap id="BaseResultMap" type="com.bjcj.model.po.safetyManage.SafetyRole">
        <!--@mbg.generated-->
        <!--@Table public.sys_role-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="role_name" jdbcType="VARCHAR" property="roleName"/>
        <result column="role_type" jdbcType="VARCHAR" property="roleType"/>
        <result column="show_sort" jdbcType="VARCHAR" property="showSort"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, role_name, role_type, show_sort, remark
    </sql>

    <select id="selectIdInByUserId" resultMap="BaseResultMap">
        select *
        from sys_role
        where id in (select role_id from sys_user_role where user_id = #{id})
    </select>

    <select id="getRoleByUserId" resultMap="BaseResultMap">
        select sr.*
        from sys_role sr
        left join sys_user_role sur on sr.id = sur.role_id
        <where>
            <if test="userId != null">
                sur.user_id = #{userId}
            </if>
        </where>
    </select>

</mapper>