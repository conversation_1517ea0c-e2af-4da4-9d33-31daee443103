<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.platformManage.fieldPlan.FieldPlanMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.platformManage.fieldPlan.FieldPlan">
    <!--@mbg.generated-->
    <!--@Table public.field_plan-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operater" jdbcType="VARCHAR" property="operater" />
    <result column="platform_app_conf_id" jdbcType="BIGINT" property="platformAppConfId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, field_name, remark, create_time, update_time, operater, platform_app_conf_id
  </sql>
</mapper>