package com.bjcj.model.po.datamodel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/5/29  11:18
*/
/**
    * 分析中的组件类型
    */
@Schema(description="分析中的组件类型")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "spdp_componenttype")
public class SpdpComponenttype implements Serializable {
    @TableId(value = "componenttypeid", type = IdType.ASSIGN_UUID)
    @Size(max = 36,message = "max length should less than 36")
    private String componenttypeid;

    @TableField(value = "name")
    @Schema(description="名称")
    @Size(max = 60,message = "max length should less than 60")
    private String name;

    @TableField(value = "description")
    @Schema(description="")
    @Size(max = 500,message = "max length should less than 500")
    private String description;

    /**
     * 类别
     */
    @TableField(value = "category")
    @Schema(description="类别")
    @Size(max = 100,message = "类别max length should less than 100")
    private String category;

    /**
     * 组件类型，一般可以用IOC里面的key表示
     */
    @TableField(value = "typeidentify")
    @Schema(description="组件类型，一般可以用IOC里面的key表示")
    @Size(max = 100,message = "组件类型，一般可以用IOC里面的key表示max length should less than 100")
    private String typeidentify;

    /**
     * 组件类型的元数据，用JSON表示
     */
    @TableField(value = "metadata")
    @Schema(description="组件类型的元数据，用JSON表示")
    @Size(max = 4000,message = "组件类型的元数据，用JSON表示max length should less than 4000")
    private String metadata;

    /**
     * 所属模块
     */
    @TableField(value = "modulename")
    @Schema(description="所属模块")
    @Size(max = 60,message = "所属模块max length should less than 60")
    private String modulename;

    /**
     * 编辑参数的url
     */
    @TableField(value = "paramediturl")
    @Schema(description="编辑参数的url")
    @Size(max = 100,message = "编辑参数的urlmax length should less than 100")
    private String paramediturl;

    /**
     * 支持模式：1表示仅支持传统、2表示仅支持大数据、3表示两者都支持
     */
    @TableField(value = "supportmode")
    @Schema(description="支持模式：1表示仅支持传统、2表示仅支持大数据、3表示两者都支持")
    private Short supportmode;

    private static final long serialVersionUID = 1L;
}