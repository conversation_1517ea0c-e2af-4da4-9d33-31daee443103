<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.safetyManage.SafetyXzqCodeMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.safetyManage.SafetyXzqCode">
    <!--@mbg.generated-->
    <!--@Table public.sys_xzq_code-->
    <result column="xzq_name" jdbcType="VARCHAR" property="xzqName" />
    <result column="xzq_level" jdbcType="VARCHAR" property="xzqLevel" />
    <result column="xzq_code" jdbcType="VARCHAR" property="xzqCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    xzq_name, xzq_level, xzq_code
  </sql>
</mapper>