package com.bjcj.service.mobile;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.mobile.ContentTbFieldMapper;
import com.bjcj.model.dto.mobile.ContentTbFieldDto;
import com.bjcj.model.po.mobile.ContentTbField;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-07-01-10:48
 */
@Service
public class ContentTbFieldService extends ServiceImpl<ContentTbFieldMapper, ContentTbField> {

   /**
    * @description 保存列表字段
    * <AUTHOR>
    * @create 2025/7/2
    **/
    public JsonResult saveContentTbField(ContentTbFieldDto dto) {
        ContentTbField entity = new ContentTbField();
        BeanUtil.copyProperties(dto, entity);
        return JsonResult.success(this.save(entity));
    }

   /**
    * @description 更新列表字段
    * <AUTHOR>
    * @create 2025/7/2
    **/
    public JsonResult updateContentTbField(ContentTbFieldDto dto) {
        ContentTbField entity = new ContentTbField();
        BeanUtil.copyProperties(dto, entity);
        return JsonResult.success(this.updateById(entity));
    }

   /**
    * @description 删除列表字段
    * <AUTHOR>
    * @create 2025/7/2
    **/
    public JsonResult removeContentTbField(String id) {
        return JsonResult.success(this.removeById(id));
    }

    /**
     * @description 批量删除列表字段
     * <AUTHOR>
     * @create 2025/7/2
     **/
    public JsonResult removeContentTbFieldByIds(String deleteids) {
        if(deleteids.contains(",")){
            //ids转list
            List<String> ids = List.of(deleteids.split(","));
            return JsonResult.success(this.removeByIds(ids));
        }else{
            return JsonResult.error("ids格式错误,必须选择一条以上的数据");
        }
    }
}
