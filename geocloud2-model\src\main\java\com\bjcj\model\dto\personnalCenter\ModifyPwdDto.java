package com.bjcj.model.dto.personnalCenter;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author：qinyi
 * @Date：2023/12/21 14:41
 */
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ModifyPwdDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="新密码")
    @NotBlank(message = "密码不能为空")
    private String password;

}
