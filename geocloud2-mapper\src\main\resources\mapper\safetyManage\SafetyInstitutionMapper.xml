<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.safetyManage.SafetyInstitutionMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.safetyManage.SafetyInstitution">
    <!--@mbg.generated-->
    <!--@Table sys_institution-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="institution_name" jdbcType="VARCHAR" property="institutionName" />
    <result column="level_id" jdbcType="BIGINT" property="levelId" />
    <result column="institution_short_name" jdbcType="VARCHAR" property="institutionShortName" />
    <result column="show_sort" jdbcType="SMALLINT" property="showSort" />
    <result column="xzq_code" jdbcType="VARCHAR" property="xzqCode" />
    <result column="fax_num" jdbcType="VARCHAR" property="faxNum" />
    <result column="phone_num" jdbcType="VARCHAR" property="phoneNum" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
    <result column="operater" jdbcType="VARCHAR" property="operater" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, institution_name, level_id, institution_short_name, show_sort, xzq_code, fax_num, 
    phone_num, create_time, update_time, operater
  </sql>

  <select id="selectNameByUserId" resultType="java.lang.String">
    select si.institution_name from sys_institution si left join sys_user u on u.institution_id=si.id where u.id=#{userId}
    </select>
</mapper>