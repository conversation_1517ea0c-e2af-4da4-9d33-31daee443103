package com.bjcj.web.naturalresources.categories;

import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.naturalresources.categories.CategoriesMeta;
import com.bjcj.service.naturalresources.categories.CategoriesMetaService;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2023/12/1 15:23 周五
 */
@Tag(name = "数据服务管理(元数据)", description = "数据服务管理(元数据)")
@ApiSupport(order = 55)
@RestController
@Slf4j
@RequestMapping(value = "/categories_meta")
public class CategoriesMetaController {

    @Resource
    private CategoriesMetaService categoriesMetaService;

    /*@Operation(summary = "数据服务元数据列表")
    @GetMapping(value = "/lists")
    public JsonResult lists(int current, int size){

        return categoriesMetaService.lists(current, size);
    }

    @Operation(summary = "注册数据服务元数据")
    @PostMapping(value = "/save")
    public JsonResult save(@RequestBody @Validated CategoriesMeta categoriesMeta){

        return JsonResult.success(categoriesMetaService.save(categoriesMeta));
    }*/

    @Operation(summary = "修改数据服务元数据")
    @PutMapping(value = "/update")
    public JsonResult update(@RequestBody @Validated CategoriesMeta categoriesMeta){

        return JsonResult.success(categoriesMetaService.saveOrUpdate(categoriesMeta));
    }

    @Operation(summary = "导出")
    @GetMapping(value = "/export")
    public JsonResult exportJson(){

        return categoriesMetaService.exportJson();
    }

    @Operation(summary = "导入")
    @PostMapping(value = "/import")
    public JsonResult importJson(@RequestParam("file") MultipartFile file){

        return categoriesMetaService.importJson(file);
    }

}
