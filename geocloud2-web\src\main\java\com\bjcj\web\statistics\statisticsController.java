package com.bjcj.web.statistics;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.statistics.IpCountStatisticsDto;
import com.bjcj.model.dto.statistics.ResourceServiceStatisticsDto;
import com.bjcj.model.dto.statistics.UserStatisticsDto;
import com.bjcj.model.po.naturalresources.categories.ResourceServices;
import com.bjcj.service.naturalresources.categories.ResourceServicesService;
import com.bjcj.service.safetyManage.UserService;
import com.bjcj.service.system.SysOperaLogService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author：qinyi
 * @Date：2024-8-8 09:03
 */
@RestController
@RequestMapping("/statistics")
@Tag(name = "统计/统计")
@Validated
public class statisticsController {

    @Resource
    ResourceServicesService resourceServicesService;

    @Resource
    SysOperaLogService sysOperaLogService;

    @Resource
    UserService userService;


    @SaCheckPermission("sys:read")
    @GetMapping("/resourceServiceStatistics")
    @Operation(summary = "数据服务统计", description = "数据服务统计")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "resourcecategory", description = "资源类别。1应用服务；2应用；3文档；4数据；5数据产品；6数据服务；7数据库；8服务器", required = true)
    })
    public JsonResult<ResourceServiceStatisticsDto> resourceServiceStatistics(@RequestParam("resourcecategory") Integer resourcecategory){
        //build dto
        ResourceServiceStatisticsDto dto = ResourceServiceStatisticsDto.builder()
                .resourceServiceCount(resourceServicesService.count(new LambdaQueryWrapper<ResourceServices>().eq(ResourceServices::getResourcecategory,resourcecategory)))
                .resourceServiceNoReviewCount(resourceServicesService.count(new LambdaQueryWrapper<ResourceServices>().eq(ResourceServices::getResourcecategory,resourcecategory).eq(ResourceServices::getStatus,0)))
                .resourceServiceReviewNotCount(resourceServicesService.count(new LambdaQueryWrapper<ResourceServices>().eq(ResourceServices::getResourcecategory,resourcecategory).eq(ResourceServices::getStatus,2)))
                .resourceServiceRuningCount(resourceServicesService.count(new LambdaQueryWrapper<ResourceServices>().eq(ResourceServices::getResourcecategory,resourcecategory).eq(ResourceServices::getStatus,1)))
                .resourceServiceWHZCount(resourceServicesService.count(new LambdaQueryWrapper<ResourceServices>().eq(ResourceServices::getResourcecategory,resourcecategory).eq(ResourceServices::getStatus,3)))
                .build();
        return JsonResult.success(dto);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/userCount")
    @Operation(summary = "用户数量统计", description = "用户数量统计")
    @ApiOperationSupport(order = 2)
    public JsonResult<UserStatisticsDto> userCount(){
        Long todayUseUserCount = sysOperaLogService.selectTodayUserCount();
        UserStatisticsDto dto = UserStatisticsDto.builder()
                .todayUseUserCount(todayUseUserCount)
                .userCount(userService.count())
                .build();
        return JsonResult.success(dto);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/ipcount")
    @Operation(summary = "IP数量统计", description = "IP数量统计")
    @ApiOperationSupport(order = 3)
    public JsonResult<IpCountStatisticsDto> ipcount(){
        Long todayIpCount = sysOperaLogService.selecttodayIpCount();
        Long ipCount = sysOperaLogService.selectAllUserCount();
        IpCountStatisticsDto dto = IpCountStatisticsDto.builder()
                .ipCount(ipCount)
                .todayIpCount(todayIpCount)
                .build();
        return JsonResult.success(dto);
    }

}