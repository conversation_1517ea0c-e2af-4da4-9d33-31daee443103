package com.bjcj.mapper.naturalresources.function;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.model.po.naturalresources.function.FunctionServe;
import com.bjcj.model.vo.naturalresources.function.FunctionServeAuthVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/30 15:12 周四
 */
public interface FunctionServeMapper extends BaseMapper<FunctionServe> {

    @Select("<script>"+
            "select c.id,c.name,c.displayname,s.func_id,s.role_id from resource_services c "+
            " left join sys_role_func s on s.func_id = c.id and s.role_id = #{roleId} "+
            " where c.resourcecategory=1  "+
            " <if test=\"name!=null and name!=''\">"+
            " and c.name like CONCAT('%', #{name}, '%') or c.displayname like CONCAT('%', #{name}, '%') "+
            " </if>"+
            " <if test=\"id!=null and id!=''\">"+
            " and c.resourcecatalogid like CONCAT('%', #{id}, '%') "+
            " </if>"+
            " <if test=\"auth==1\">"+
            " and s.func_id is not null "+
            " </if>"+
            "</script>"
    )
    IPage<FunctionServeAuthVo> listAuth(String name, String id, int auth, @Param("page") Page<Map<String, Object>> page, Long roleId);


    List<FunctionServe> selectPageList(@Param("current") Long current, @Param("size") Long size, @Param("searchStr") String searchStr,@Param("functionDataId")  String functionDataId);

    Long selectListCount( @Param("searchStr") String searchStr,@Param("functionDataId")  String functionDataId);
}
