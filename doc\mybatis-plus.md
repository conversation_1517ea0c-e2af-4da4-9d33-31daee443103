### mybatis_plus 使用注意事项

#### insert

```txt
userMapper.insert(
        User.builder()
                .username("zs")
                .password("123456")
                .nickname("zs")
                .sex("1")
                .email("<EMAIL>")
                .build()
);
List<User> users = userMapper.selectList(null);
```

#### delete

```txt
userMapper.delete(Wrappers.<User>query().lambda().eq(User::getId, 1726902183487291393L));
```

#### update

```txt
userMapper.updateById(
        User.builder()
                .id(1726902183487291393L)
                .username("")
                .password("")
                .nickname("")
                .sex("")
                .email("")
                .build()
);
```

#### select

```txt
List<User> users = userMapper.selectList(null);
        for (User user : users) {
            System.out.println(user);
        }
```