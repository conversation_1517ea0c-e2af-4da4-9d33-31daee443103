package com.bjcj.model.dto.specialPlan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/1/26  10:18
*/

/**
    * 专题服务关联表
    */
@Schema(description="专题服务关联表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "special_cataservr")
public class SpecialCataservrDto implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 专题id
     */
    @TableField(value = "special_plan_data_id")
    @Schema(description="专题id")
    @NotNull(message = "专题idis not null")
    private Long specialPlanDataId;

    /**
     * 服务id
     */
    @TableField(value = "cata_service_id")
    @Schema(description="服务id")
    @NotNull(message = "服务idis not null")
    private Long cataServiceId;

    @TableField(value = "service_name")
    @Schema(description="")
    @Size(max = 100,message = "max length should less than 100")
    private String serviceName;

    @TableField(value = "show_name")
    @Schema(description="")
    @Size(max = 100,message = "max length should less than 100")
    private String showName;

    private static final long serialVersionUID = 1L;
}