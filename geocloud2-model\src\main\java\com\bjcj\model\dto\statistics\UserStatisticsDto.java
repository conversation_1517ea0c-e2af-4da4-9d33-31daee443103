package com.bjcj.model.dto.statistics;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author：qinyi
 * @Date：2024-8-8 09:34
 */
@Schema(description="用户统计")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserStatisticsDto implements Serializable {
    @Schema(description="总用户数量")
    private Long userCount;

    @Schema(description="今日活跃用户数量")
    private Long todayUseUserCount;

}
