package com.bjcj.web.safetyManage;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.safetyManage.RoleMenuPermsDto;
import com.bjcj.model.po.naturalresources.categories.ResourceServices;
import com.bjcj.model.po.naturalresources.products.ProductsData;
import com.bjcj.model.po.safetyManage.User;
import com.bjcj.service.naturalresources.categories.CategoriesDataInfoService;
import com.bjcj.service.naturalresources.function.FunctionServeService;
import com.bjcj.service.naturalresources.products.ProductsDataService;
import com.bjcj.service.safetyManage.MenuService;
import com.bjcj.service.safetyManage.UserService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author：qinyi
 * @Date：2023/12/26 8:44
 */
@RestController
@RequestMapping("authorizationInfo")
@Tag(name = "授权信息")
@Validated
public class AuthorizationInfoController {

    @Resource
    private CategoriesDataInfoService categoriesDataInfoService;

    @Resource
    private FunctionServeService functionServeService;

    @Resource
    private ProductsDataService productsDataService;

    @Resource
    private UserService userService;

    @Resource
    private MenuService menuService;


    @OperaLog(operaModule = "授权信息-数据服务查询",operaType = OperaLogConstant.LOOK,operaDesc = "数据服务查询")
    @SaCheckPermission("sys:read")
    @GetMapping("/pageCata")
    @Operation(summary = "数据服务列表分页", description = "带搜索框模糊查询(服务名或显示名),按数据类别查询")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "searchStr", description = "服务名称或服务显示名", required = false),
            @Parameter(name = "resourceDataItemId", description = "数据服务目录id", required = false)
    })
    public JsonResult<Page<ResourceServices>> pageCata(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam(value = "searchStr",required = false) String searchStr,
            @RequestParam(value = "resourceCatalogId",required = false) String resourceCatalogId
    ) {
        Page<ResourceServices> pager = new Page<>(page, pageSize);
        return this.categoriesDataInfoService.pageDataSort(pager,searchStr,resourceCatalogId);
    }

    @OperaLog(operaModule = "授权信息-应用服务查询",operaType = OperaLogConstant.LOOK,operaDesc = "应用服务查询")
    @SaCheckPermission("sys:read")
    @GetMapping("/pageFunc")
    @Operation(summary = "应用服务列表分页", description = "带搜索框模糊查询(服务名或显示名),按数据类别查询")
    @ApiOperationSupport(order = 3)
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "searchStr", description = "姓名和用户名搜索", required = false),
            @Parameter(name = "resourcecatalogid", description = "资源目录id", required = false)
    })
    public JsonResult<Page<ResourceServices>> pageFunc(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam(value = "searchStr",required = false) String searchStr,
            @RequestParam(value = "resourcecatalogid",required = false) String resourcecatalogid
    ){
        Page<ResourceServices> pager = new Page<>(page, pageSize);
        return this.functionServeService.pageDataSort(pager,searchStr,resourcecatalogid);
    }


    @OperaLog(operaModule = "授权信息-数据产品查询",operaType = OperaLogConstant.LOOK,operaDesc = "数据产品查询")
    @SaCheckPermission("sys:read")
    @GetMapping("/pageProd")
    @Operation(summary = "数据产品列表分页", description = "带搜索框模糊查询(服务名或显示名),按服务目录查询")
    @ApiOperationSupport(order = 5)
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "searchStr", description = "姓名和用户名搜索", required = false),
            @Parameter(name = "productsId", description = "数据产品目录id", required = false)
    })
    public JsonResult<Page<ProductsData>> pageProd(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam(value = "searchStr",required = false) String searchStr,
            @RequestParam(value = "productsId",required = false) String productsId
    ){
        Page<ProductsData> pager = new Page<ProductsData>(page, pageSize);
        return this.productsDataService.pageDataSort(pager,searchStr,productsId);
    }



    @OperaLog(operaModule = "授权信息-数据服务查询授权人员",operaType = OperaLogConstant.LOOK,operaDesc = "数据服务查询授权人员")
    @SaCheckPermission("sys:read")
    @GetMapping("/queryAuthUserCata")
    @Operation(summary = "数据服务查询授权人员", description = "数据服务查询授权人员")
    @ApiOperationSupport(order = 2)
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "resourceServicesId", description = "数据服务id", required = true)
    })
    public JsonResult<Page<User>> queryAuthUserCata(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam(value = "resourceServicesId",required = true) String resourceServicesId
    ) {
        Page<User> pager = new Page<>(page, pageSize);
        return this.categoriesDataInfoService.queryAuthUser(pager,resourceServicesId);
    }

    @OperaLog(operaModule = "授权信息-应用服务查询授权人员",operaType = OperaLogConstant.LOOK,operaDesc = "应用服务查询授权人员")
    @SaCheckPermission("sys:read")
    @GetMapping("/queryAuthUserFunc")
    @Operation(summary = "应用服务查询授权人员", description = "应用服务查询授权人员")
    @ApiOperationSupport(order = 4)
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "resourceServiceId", description = "服务id", required = true)
    })
    public JsonResult<Page<User>> queryAuthUserFunc(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam(value = "resourceServiceId",required = true) String resourceServiceId
    ) {
        Page<User> pager = new Page<>(page, pageSize);
        return this.functionServeService.queryAuthUser(pager,resourceServiceId);
    }

    @OperaLog(operaModule = "授权信息-数据产品查询授权人员",operaType = OperaLogConstant.LOOK,operaDesc = "数据产品查询授权人员")
    @SaCheckPermission("sys:read")
    @GetMapping("/queryAuthUserProd")
    @Operation(summary = "数据产品查询授权人员", description = "数据产品查询授权人员")
    @ApiOperationSupport(order = 6)
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "productsDataId", description = "数据产品id", required = true)
    })
    public JsonResult<Page<User>> queryAuthUserProd(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam(value = "productsDataId",required = true) Long productsDataId
    ) {
        Page<User> pager = new Page<>(page, pageSize);
        return this.productsDataService.queryAuthUser(pager,productsDataId);
    }

    @OperaLog(operaModule = "人查授权-数据产品查询",operaType = OperaLogConstant.LOOK,operaDesc = "人员查询数据产品权限")
    @SaCheckPermission("sys:read")
    @GetMapping("/queryAuthCataByUser")
    @Operation(summary = "人员查询数据服务权限", description = "人员查询数据服务权限")
    @ApiOperationSupport(order = 7)
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "userId", description = "用户id", required = true),
            @Parameter(name = "searchStr", description = "服务名称或服务显示名", required = false),
            @Parameter(name = "resourceCatalogsId", description = "数据服务目录id", required = false)
    })
    public JsonResult<Page<ResourceServices>> queryAuthCataByUser(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam(value = "userId",required = true) Long userId,
            @RequestParam(value = "searchStr",required = false) String searchStr,
            @RequestParam(value = "resourceCatalogsId",required = false) String resourceCatalogsId
    ) {
        Page<ResourceServices> pager = new Page<>(page, pageSize);
        return this.categoriesDataInfoService.serviceAuthList(pager,userId,searchStr,resourceCatalogsId);
    }

    @OperaLog(operaModule = "人查授权-应用服务查询",operaType = OperaLogConstant.LOOK,operaDesc = "人员查询应用服务权限")
    @SaCheckPermission("sys:read")
    @GetMapping("/queryAuthFuncByUser")
    @Operation(summary = "人员查询应用服务权限", description = "人员查询应用服务权限")
    @ApiOperationSupport(order = 8)
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "userId", description = "用户id", required = true),
            @Parameter(name = "searchStr", description = "服务名称或服务显示名", required = false),
            @Parameter(name = "resourceCatalogsId", description = "应用服务目录id", required = false)
    })
    public JsonResult<Page<ResourceServices>> queryAuthFuncByUser(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam(value = "userId",required = true) Long userId,
            @RequestParam(value = "searchStr",required = false) String searchStr,
            @RequestParam(value = "resourceCatalogsId",required = false) String resourceCatalogsId
    ) {
        Page<ResourceServices> pager = new Page<>(page, pageSize);
        return this.functionServeService.serviceAuthList(pager,userId,searchStr,resourceCatalogsId);
    }


    @OperaLog(operaModule = "人查授权-数据产品查询",operaType = OperaLogConstant.LOOK,operaDesc = "人员查询数据产品权限")
    @SaCheckPermission("sys:read")
    @GetMapping("/queryAuthProdByUser")
    @Operation(summary = "人员查询数据产品权限", description = "人员查询数据产品权限")
    @ApiOperationSupport(order = 9)
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "userId", description = "用户id", required = true),
            @Parameter(name = "searchStr", description = "服务名称或服务显示名", required = false),
            @Parameter(name = "productsId", description = "数据产品目录id", required = false)
    })
    public JsonResult<Page<ProductsData>> queryAuthProdByUser(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam(value = "userId",required = true) Long userId,
            @RequestParam(value = "searchStr",required = false) String searchStr,
            @RequestParam(value = "productsId",required = false) String productsId
    ) {
        Page<ProductsData> pager = new Page<>(page, pageSize);
        return this.productsDataService.serviceAuthList(pager,userId,searchStr,productsId);
    }

    @OperaLog(operaModule = "菜单授权查询(菜单查人)",operaType = OperaLogConstant.LOOK,operaDesc = "菜单授权查询(菜单查人)")
    @SaCheckPermission("sys:read")
    @GetMapping("/queryAuthUserByMenuId")
    @Operation(summary = "菜单授权查询(菜单查人)", description = "菜单授权查询(菜单查人)")
    @ApiOperationSupport(order = 10)
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "menuId", description = "菜单id", required = true),
            @Parameter(name = "searchStr", description = "用户名称", required = false)
    })
    public JsonResult<Page<User>> queryAuthUserByMenuId(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam("menuId") Long menuId,
            @RequestParam(value = "searchStr",required = false) String searchStr
    ) {
        Page<User> pager = new Page<>(page, pageSize);
        return this.userService.queryAuthUserByMenuId(pager,menuId,searchStr);
    }

    @OperaLog(operaModule = "人查菜单权限",operaType = OperaLogConstant.LOOK,operaDesc = "人查菜单权限")
    @SaCheckPermission("sys:read")
    @GetMapping("/queryMenuAuthByUserId")
    @Operation(summary = "人查菜单权限", description = "人查菜单权限")
    @ApiOperationSupport(order = 11)
    @Parameters({
            @Parameter(name = "userId", description = "用户id", required = true),
            @Parameter(name = "specialPlanId", description = "专题id", required = false)
    })
    public JsonResult<RoleMenuPermsDto> queryMenuAuthByUserId(@RequestParam("userId") Long userId,
                                                              @RequestParam(value = "specialPlanId",required = false) Long specialPlanId){
        return this.menuService.queryMenuAuthByUserId(userId,specialPlanId);
    }





}