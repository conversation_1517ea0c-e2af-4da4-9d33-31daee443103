package com.bjcj.model.vo.specialPlan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/1/26  10:17
*/
@Schema(description="专题目录树")
@Data
public class DirTreeVo implements Serializable {
    @Schema(description="id")
    private String id;

    @Schema(description="专题方案id")
    private Long specialPlanId;

    @Schema(description="目录名称")
    private String dirName;

    @Schema(description="显示名称")
    private String showName;

    @Schema(description="父节点id")
    private String parentId;

    @Schema(description="子目录")
    private List<DirTreeVo> children;

}