package com.bjcj.web.security;

import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bjcj.common.core.exception.CaptchaException;
import com.bjcj.common.core.redis.RedisHandler;
import com.bjcj.common.core.redis.RedisKeyConstant;
import com.bjcj.common.utils.AESFixedEncryption;
import com.bjcj.common.utils.SoMap;
import com.bjcj.common.utils.UserAgentUtil;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.common.utils.properties.GeoCloud2Properties;
import com.bjcj.model.dto.LoginDto;
import com.bjcj.model.po.safetyManage.User;
import com.bjcj.model.po.system.SysLoginLog;
import com.bjcj.service.safetyManage.UserService;
import com.bjcj.service.system.SysLoginLogService;
import com.ejlchina.okhttps.OkHttps;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2024/8/19 16:52 周一
 */
@RestController
@RequestMapping
@Tag(name = "01登录")
@Slf4j
public class LoginController {

    @Resource
    RedisHandler redisHandler;

    @Resource
    GeoCloud2Properties geoCloud2Properties;

    @Resource
    UserService userService;

    @Resource
    SysLoginLogService sysLoginLogService;

    @Operation(summary = "登录")
    @ApiOperationSupport(order = 2)
    @PostMapping("/login")
    public JsonResult doLogin(HttpServletRequest request, @RequestBody LoginDto loginDto) throws Exception {

        if (geoCloud2Properties.isCheckCaptcha()) {
            // 验证码校验
            String text = redisHandler.get(RedisKeyConstant.CAPTCHA + loginDto.getKey());
            if (!StrUtil.equalsIgnoreCase(text, loginDto.getCaptcha())) {

                // 记录日志
                recordLog(request, loginDto.getUsername(), null, "login", false, "验证码输入错误");
                throw new CaptchaException("验证码输入错误！");
            }
        }

        // 查询用户
        User sysUser = userService.getOne(
                Wrappers.<User>lambdaQuery()
                        .eq(User::getUsername, loginDto.getUsername())
                        .eq(User::getPassword, AESFixedEncryption.encrypt(loginDto.getPassword()))
        );
        if (ObjUtil.isNotEmpty(sysUser)) {

            // 登录
            StpUtil.login(sysUser.getId());

            // 保存用户信息
            // StpUtil.getSession().set("user", sysUser);

            // 记录日志
            recordLog(request, loginDto.getUsername(), sysUser.getId(), "login", true, null);

            return JsonResult.success(StpUtil.getTokenInfo());
        } else {
            // 记录日志
            recordLog(request, loginDto.getUsername(), null, "login", false, "账号或者密码不正确");
            return JsonResult.error("账号或者密码不正确");
        }
    }

//    @Operation(summary = "登出")
//    @ApiOperationSupport(order = 3)
//    @GetMapping("/logout")
//    public JsonResult logout(HttpServletRequest request, @RequestParam String username) {
//        // 记录日志
//        recordLog(request, username, StpUtil.getLoginIdAsLong(), "logout", true, null);
//        StpUtil.logout();
//        return JsonResult.success();
//    }

    @Operation(summary = "查询是否登录")
    @ApiOperationSupport(order = 4)
    @GetMapping("/isLogin")
    public JsonResult isLogin() {
        return JsonResult.success("是否登录：" + StpUtil.isLogin());
    }

    @Operation(summary = "获取token信息")
    @ApiOperationSupport(order = 5)
    @GetMapping("/tokenInfo")
    public JsonResult tokenInfo() {
        return JsonResult.success(StpUtil.getTokenInfo());
    }

    @Operation(summary = "code到sso换取token")
    @ApiOperationSupport(order = 6)
    @GetMapping("/codeToToken")
    public JsonResult codeToToken(@RequestParam(value = "code",required = true) String code) {
        try {
            String str = OkHttps.sync(geoCloud2Properties.getServerurl() + "/oauth2/token")
                    .addBodyPara("grant_type", "authorization_code")
                    .addBodyPara("code", code)
                    .addBodyPara("client_id", geoCloud2Properties.getClientId())
                    .addBodyPara("client_secret", geoCloud2Properties.getClientSecret())
                    .post()
                    .getBody()
                    .toString();
            SoMap so = SoMap.getSoMap().setJsonString(str);
            System.out.println("返回结果: " + new ObjectMapper().writeValueAsString(so));
            // code不等于200  代表请求失败
            if (so.getInt("code") != 200) {
                return JsonResult.error(so.getString("msg"));
            }
            //获取userid进行登录
            String access_token = so.getString("access_token");
            String str2 = OkHttps.sync(geoCloud2Properties.getServerurl() + "/oauth2/userinfo?access_token=" + access_token).get().getBody().toString();
            SoMap so2 = SoMap.getSoMap().setJsonString(str2);
            // code不等于200  代表请求失败
            if (so2.getInt("code") != 200) {
                return JsonResult.error(so.getString("msg"));
            }
            Object data = so2.get("data");
                JSONObject json = JSONUtil.parseObj(data);
            Long userId = json.getLong("id");
            StpUtil.login(userId);
            // StpUtil.getSession().set("user", userService.getById(userId));
            HashMap<String, Object> map = new HashMap<>();
            map.put("authInfo", so);
            map.put("userInfo", so2.get("data"));
            return JsonResult.success(map);
        } catch (Exception e) {
            log.error("code获取token错误: {}", e.getMessage());
            return JsonResult.build(401,"登录失败!");
        }
    }

    @Operation(summary = "登出")
    @ApiOperationSupport(order = 7)
    @RequestMapping("/logout")
    public SaResult logout() {
        String userId =  StpUtil.getLoginId().toString();
        String result = OkHttps.sync(geoCloud2Properties.getServerurl() + "/logout?userId=" + userId)
                .get()
                .getBody()
                .toString();
        System.out.println("sso logout返回结果: " + result);
        StpUtil.logout();
        // 获取当前会话是否已经登录，返回true=已登录，false=未登录
        boolean login = StpUtil.isLogin();
        System.out.println("是否登录：" + login);
        return SaResult.ok("注销成功");
    }


    private void recordLog(HttpServletRequest request, String username, Long userId,
                           String loginOrOut, boolean status, String failMsg) {
        // 记录日志
        sysLoginLogService.save(
                SysLoginLog.builder()
                        .username(username)
                        .userId(userId)
                        .createTime(LocalDateTime.now())
                        .ip(JakartaServletUtil.getClientIP(request))
                        .device(UserAgentUtil.getDevice(request))
                        .loginOrOut(loginOrOut)
                        .status(status)
                        .failedMsg(failMsg)
                        .build()
        );
    }


}
