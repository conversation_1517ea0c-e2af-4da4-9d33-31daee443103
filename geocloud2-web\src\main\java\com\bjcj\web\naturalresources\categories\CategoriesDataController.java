package com.bjcj.web.naturalresources.categories;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.naturalresources.categories.CategoriesData;
import com.bjcj.model.po.naturalresources.categories.ResourceCatalogs;
import com.bjcj.model.vo.naturalresources.categories.CategoriesDataVo;
import com.bjcj.service.naturalresources.categories.CategoriesDataService;
import com.bjcj.service.naturalresources.categories.ResourceCatalogsService;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @ClassName CategoriesDataController
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/11/27 9:35
 */
@Tag(name = "数据服务管理（数据资源分类）", description = "数据服务管理（数据资源分类）")
@ApiSupport(order = 50, author = "50")
@RestController
@Slf4j
@RequestMapping(value = "/categories_data")
public class CategoriesDataController {

    @Resource
    private CategoriesDataService dataService;

    @Resource
    private ResourceCatalogsService resourcesCatalogsService;

    @SaCheckPermission("sys:read")
    @Operation(summary = "数据资源分类树形")
    @GetMapping(value = "/treeList")
    public JsonResult<List<CategoriesDataVo>> treeList(){

        return dataService.treeList();
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "1.数据资源分类树形")
    @GetMapping(value = "/treeList2")
    @Parameters({
            @Parameter(name = "resourcecategory", description = "树分类标识", required = true)
    })
    public JsonResult<List<ResourceCatalogs>> treeList2(@RequestParam("resourcecategory") Integer resourcecategory){
        return resourcesCatalogsService.treeList(resourcecategory);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "数据服务列表")
    @GetMapping(value = "/lists")
    @Parameters({
            @Parameter(name = "name", description = "分类名称", required = false),
            @Parameter(name = "code", description = "编码", required = false)
    })
    public JsonResult<List<CategoriesDataVo>> lists(@RequestParam(value = "name",required = false) String name,
                                                     @RequestParam(value = "code",required = false) String code){

        return dataService.lists(name,code);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "2.数据服务列表")
    @GetMapping(value = "/lists2")
    @Parameters({
            @Parameter(name = "name", description = "分类名称", required = false),
            @Parameter(name = "code", description = "编码", required = false),
            @Parameter(name = "resourcecategory", description = "目录类型", required = true)
    })
    public JsonResult<List<ResourceCatalogs>> lists2(@RequestParam(value = "name",required = false) String name,
                                                    @RequestParam(value = "code",required = false) String code,
                                                     @RequestParam(value = "resourcecategory",required = true) Integer resourcecategory){

        return resourcesCatalogsService.lists(name,code,resourcecategory);
    }

    /*@GetMapping(value = "/findById/{id}")
    public JsonResult<CategoriesData> findById(@PathVariable Long id){
        return dataService.findById(id);
    }*/

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "添加数据资源分类",
            operaType = OperaLogConstant.CREATE,
            operaDesc = "添加数据资源分类"
    )
    @Operation(summary = "添加数据资源分类")
    @PostMapping(value = "/save")
    @RequestLock(prefix = "save")
    public JsonResult save(@RequestBody @Validated CategoriesData categoriesData){

        return JsonResult.success(dataService.save(categoriesData));
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "添加数据资源分类",
            operaType = OperaLogConstant.CREATE,
            operaDesc = "添加数据资源分类"
    )
    @Operation(summary = "3.添加数据资源分类")
    @PostMapping(value = "/save2")
    @RequestLock(prefix = "save2")
    public JsonResult save2(@RequestBody @Validated ResourceCatalogs resourceCatalogs){

        return JsonResult.success(resourcesCatalogsService.save(resourceCatalogs));
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "修改数据资源分类",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "修改数据资源分类"
    )
    @Operation(summary = "修改数据资源分类")
    @PutMapping(value = "/update")
    @RequestLock(prefix = "update")
    public JsonResult update(@RequestBody @Validated CategoriesData categoriesData){

        return JsonResult.success(dataService.saveOrUpdate(categoriesData));
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "修改数据资源分类",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "修改数据资源分类"
    )
    @Operation(summary = "4.修改数据资源分类")
    @PutMapping(value = "/update2")
    @RequestLock(prefix = "update2")
    public JsonResult update2(@RequestBody @Validated ResourceCatalogs resourceCatalogs){

        return JsonResult.success(resourcesCatalogsService.saveOrUpdate(resourceCatalogs));
    }

    @SaCheckPermission("sys:del")
    @OperaLog(
            operaModule = "删除数据资源分类",
            operaType = OperaLogConstant.DELETE,
            operaDesc = "删除数据资源分类"
    )
    @Operation(summary = "删除数据资源分类")
    @DeleteMapping(value = "/del/{id}")
    public JsonResult del(@PathVariable Long id){

        return dataService.del(id);
    }

    @SaCheckPermission("sys:del")
    @OperaLog(
            operaModule = "删除数据资源分类",
            operaType = OperaLogConstant.DELETE,
            operaDesc = "删除数据资源分类"
    )
    @Operation(summary = "5.删除数据资源分类")
    @DeleteMapping(value = "/del2/{id}")
    public JsonResult del2(@PathVariable String id){

        return resourcesCatalogsService.del(id);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "修改显示状态",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "修改显示状态"
    )
    @Operation(summary = "修改显示状态")
    @PostMapping(value = "/uptStatus")
    public JsonResult uptStatus(Long id, Boolean status){

        return dataService.uptStatus(id,status);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "修改显示状态",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "修改显示状态"
    )
    @Operation(summary = "6.修改显示状态")
    @PostMapping(value = "/uptStatus2")
    public JsonResult uptStatus2(String id, Boolean isvisiable){

        return resourcesCatalogsService.uptStatus(id,isvisiable);
    }

    @Operation(summary = "导出")
    @GetMapping(value = "/export")
    public JsonResult exportJson(){

        return dataService.exportJson();
    }

    @Operation(summary = "导入")
    @PostMapping(value = "/import")
    public JsonResult importJson(@RequestParam("file") MultipartFile file){

        return dataService.importJson(file);
    }

}
