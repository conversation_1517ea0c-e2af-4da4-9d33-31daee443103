<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.eswPlatform.PlotAllocationInfoMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.eswPlatform.PlotAllocationInfo">
    <!--@mbg.generated-->
    <!--@Table public.plot_allocation_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="color_cause" jdbcType="VARCHAR" property="colorCause" />
    <result column="altitude" jdbcType="VARCHAR" property="altitude" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="parentid" jdbcType="VARCHAR" property="parentid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", content, color_cause, altitude, "type", parentid
  </sql>
</mapper>