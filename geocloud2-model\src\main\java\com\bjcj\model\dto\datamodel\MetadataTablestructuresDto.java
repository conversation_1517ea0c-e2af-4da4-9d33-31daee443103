package com.bjcj.model.dto.datamodel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/4/19  14:53
*/

/**
    * 表结构
    */
@Schema(description="图层表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MetadataTablestructuresDto implements Serializable {
    /**
     * 标识
     */
    @Schema(description="主键")
    private String tablestructureid;

    /**
     * 表结构的名称
     */
    @Schema(description="表结构的名称")
    private String name;

    /**
     * 说明
     */
    @Schema(description="说明")
    private String description;

    /**
     * 要素类的Shape的类型，如：1表示点，2表示多点，3表示线，4表示面
     */
    @Schema(description="要素类的Shape的类型，如：1表示点，2表示多点，3表示线，4表示面")
    private Short shapetype;

    /**
     * 表的类型，例如：0，表示注记类；1，表示要素类，2，表示镶嵌数据集，3，表示注记要素类，4，表示属性表，5，表示栅格数据集，6，表示栅格目录
     */
    @Schema(description="表的类型，例如：0，表示注记类；1，表示要素类，2，表示镶嵌数据集，3，表示注记要素类，4，表示属性表，5，表示栅格数据集，6，表示栅格目录")
    private Short tabletype;

    /**
     * 显示名称
     */
    @Schema(description="显示名称")
    private String displayname;

    /**
     * 显示字段ID
     */
    @Schema(description="显示字段ID")
    private String displayfieldid;

    /**
     * 数据标准的ID
     */
    @Schema(description="数据标准的ID")
    private String datastandardid;

    /**
     * 编码
     */
    @Schema(description="编码")
    private String code;

    /**
     * 是否必须
     */
    @Schema(description="是否必须")
    private Boolean required;

    /**
     * 显示顺序
     */
    @Schema(description="显示顺序")
    private Short displayorder;

}