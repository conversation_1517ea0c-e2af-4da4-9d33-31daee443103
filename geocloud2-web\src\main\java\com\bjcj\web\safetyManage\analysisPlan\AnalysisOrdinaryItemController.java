package com.bjcj.web.safetyManage.analysisPlan;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.analysisPlan.AnalysisAndOrdinaryItemDto;
import com.bjcj.service.platformManage.analysisPlan.AnalysisOrdinaryItemService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
* 分析项表(analysis_ordinary_item)表控制层
* <AUTHOR>
*/
@RestController
@RequestMapping("/analysis_ordinary_item")
@Tag(name = "普通分析")
@Validated
public class AnalysisOrdinaryItemController {
    /**
    * 服务对象
    */
    @Resource
    private AnalysisOrdinaryItemService analysisOrdinaryItemService;

    @OperaLog(operaModule = "新增普通分析",operaType = OperaLogConstant.CREATE,operaDesc = "新增普通分析")
    @SaCheckPermission("sys:write")
    @PostMapping("/add")
    @Operation(summary = "新增普通分析", description = "新增普通分析")
    @ApiOperationSupport(order = 2)
    @Transactional(rollbackFor = Exception.class)
    @RequestLock(prefix = "add_analysis_ordinary_item")
    public JsonResult add(@Validated @RequestBody AnalysisAndOrdinaryItemDto dto){
        return this.analysisOrdinaryItemService.add(dto);
    }


    @OperaLog(operaModule = "编辑普通分析",operaType = OperaLogConstant.UPDATE,operaDesc = "编辑普通分析")
    @SaCheckPermission("sys:write")
    @PostMapping("/edit")
    @Operation(summary = "编辑普通分析", description = "编辑普通分析")
    @ApiOperationSupport(order = 3)
    @Transactional(rollbackFor = Exception.class)
    @RequestLock(prefix = "edit_analysis_ordinary_item")
    public JsonResult edit(@Validated @RequestBody AnalysisAndOrdinaryItemDto dto){
        return this.analysisOrdinaryItemService.edit(dto);
    }

    @GetMapping("/detail")
    @Operation(summary = "普通分析详情", description = "普通分析详情")
    @SaCheckPermission("sys:read")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "id", description = "分析方案id", required = true)
    })
    public JsonResult detail(@RequestParam("id") Long id){
        return this.analysisOrdinaryItemService.detail(id);
    }

    @OperaLog(operaModule = "删除普通分析",operaType = OperaLogConstant.DELETE,operaDesc = "删除普通分析")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del/{id}")
    @Operation(summary = "删除普通分析", description = "删除普通分析")
    @ApiOperationSupport(order = 4)
    @Transactional(rollbackFor = Exception.class)
    @RequestLock(prefix = "del_analysis_ordinary_item")
    public JsonResult del(@PathVariable("id") Long id){
        return this.analysisOrdinaryItemService.del(id);
    }


    @OperaLog(operaModule = "根据普通分析方案id查分析项",operaType = OperaLogConstant.LOOK,operaDesc = "根据普通分析方案id查分析项")
    @SaCheckPermission("sys:read")
    @GetMapping("/analysisOrdinaryItemlist")
    @Operation(summary = "根据普通分析方案id查分析项", description = "根据普通分析方案id查分析项")
    @ApiOperationSupport(order = 5)
    @Parameters({
            @Parameter(name = "id", description = "分析方案id", required = true)
    })
    public JsonResult analysisOrdinaryItemlist(@RequestParam("id") Long id){
        return this.analysisOrdinaryItemService.analysisOrdinaryItemlist(id);
    }
}
