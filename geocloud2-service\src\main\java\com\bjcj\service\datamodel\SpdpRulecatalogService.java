package com.bjcj.service.datamodel;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.datamodel.SpdpRuleMapper;
import com.bjcj.mapper.datamodel.SpdpRulecatalogMapper;
import com.bjcj.mapper.datamodel.SpdpRuleitemMapper;
import com.bjcj.model.dto.datamodel.RuleDto;
import com.bjcj.model.dto.datamodel.RuleItemDto;
import com.bjcj.model.po.datamodel.SpdpRule;
import com.bjcj.model.po.datamodel.SpdpRulecatalog;
import com.bjcj.model.po.datamodel.SpdpRuleitem;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 *@Author：qinyi
 *@Date：2024/7/2  9:07
*/
@Service
public class SpdpRulecatalogService extends ServiceImpl<SpdpRulecatalogMapper, SpdpRulecatalog> {

    @Resource
    SpdpRulecatalogMapper spdpRulecatalogMapper;
    @Resource
    SpdpRuleMapper spdpRuleMapper;

    @Resource
    SpdpRuleService spdpRuleService;

    @Resource
    SpdpRuleitemMapper spspRuleitemMapper;

    @Resource
    SpdpRuleitemService spspRuleitemsService;


    public JsonResult addRuleCatalog(SpdpRulecatalog ruleCatalog, List<RuleDto> ruleDtos) {
        SpdpRulecatalog rCatalog = this.spdpRulecatalogMapper.selectOne(new LambdaQueryWrapper<SpdpRulecatalog>().eq(SpdpRulecatalog::getName, ruleCatalog.getName()));
        if (rCatalog != null) {
            return JsonResult.error("名称为" + rCatalog.getName() + "的数据检查已存在");
        }else{
            this.spdpRulecatalogMapper.insert(ruleCatalog);
            if (ruleDtos != null && ruleDtos.size() > 0) {
                Iterator var6 = ruleDtos.iterator();
                label36:
                while(true) {
                    RuleDto ruleDto;
                    SpdpRule rule;
                    do {
                        do {
                            if (!var6.hasNext()) {
                                break label36;
                            }

                            ruleDto = (RuleDto)var6.next();
                            rule = new SpdpRule();
                            rule.setRulecatalogid(ruleCatalog.getRulecatalogid());
                            rule.setName(ruleDto.getName());
                            rule.setDisplayname(ruleDto.getDisplayname());
                            rule.setErrorlevel(ruleDto.getErrorlevel());
                            rule.setIsneed(ruleDto.getIsneed());
                            rule.setRuleitemcompositetype(ruleDto.getRuleitemcompositetype());
                            rule.setRulecode(ruleDto.getRulecode());
                            this.spdpRuleMapper.insert(rule);
                        } while(ruleDto.getRuleItemDtos() == null);
                    } while(ruleDto.getRuleItemDtos().size() <= 0);

                    List<SpdpRuleitem> ruleItems = new ArrayList();
                    Iterator var10 = ruleDto.getRuleItemDtos().iterator();

                    while(var10.hasNext()) {
                        RuleItemDto ruleItemDto = (RuleItemDto)var10.next();
                        SpdpRuleitem ruleItem = new SpdpRuleitem();
                        ruleItem.setName(ruleItemDto.getName());
                        ruleItem.setDisplayname(ruleItemDto.getDisplayname());
                        ruleItem.setDescription(ruleItemDto.getDescription());
                        ruleItem.setValidatorconfigdatatext(ruleItemDto.getValidatorconfigdatatext());
                        ruleItem.setValidatorkey(ruleItemDto.getValidatorkey());
                        ruleItem.setRuleid(rule.getRuleid());
                        ruleItems.add(ruleItem);
                    }

                    this.spspRuleitemsService.saveBatch(ruleItems);
                }
            }
            return JsonResult.success();
        }
    }

    public JsonResult editRuleCatalog(SpdpRulecatalog ruleCatalog, List<RuleDto> ruleDtos) {
        SpdpRulecatalog rCatalog = this.spdpRulecatalogMapper.selectOne(new LambdaQueryWrapper<SpdpRulecatalog>().eq(SpdpRulecatalog::getName, ruleCatalog.getName()));
        // if (rCatalog != null && !rCatalog.getRulecatalogid().equals(ruleCatalog.getRulecatalogid())) {
        //     return JsonResult.error("名称为" + rCatalog.getName() + "的数据检查已存在");
        // }else{
        this.spdpRulecatalogMapper.updateById(ruleCatalog);
            // this.spdpRulecatalogMapper.insert(ruleCatalog);
            List<SpdpRule> rules = this.spdpRuleMapper.selectList(new LambdaQueryWrapper<SpdpRule>().eq(SpdpRule::getRulecatalogid, ruleCatalog.getRulecatalogid()));
            Iterator var7;
            if (rules != null && rules.size() > 0) {
                var7 = rules.iterator();

                while(var7.hasNext()) {
                    SpdpRule rule = (SpdpRule)var7.next();
                    List<SpdpRuleitem> ruleItems = this.spspRuleitemMapper.selectList(new LambdaQueryWrapper<SpdpRuleitem>().eq(SpdpRuleitem::getRuleid,rule.getRuleid()));        //getByRuleId(rule.getId());
                    if (ruleItems != null && ruleItems.size() > 0) {
                        this.spspRuleitemsService.removeBatchByIds(ruleItems);//deleteInBatch(ruleItems);
                    }
                }
            }

            this.spdpRuleService.removeBatchByIds(rules);
            if (ruleDtos != null && ruleDtos.size() > 0) {
                var7 = ruleDtos.iterator();

                label48:
                while(true) {
                    RuleDto ruleDto;
                    SpdpRule rule;
                    do {
                        do {
                            if (!var7.hasNext()) {
                                break label48;
                            }

                            ruleDto = (RuleDto)var7.next();
                            rule = new SpdpRule();
                            rule.setRulecatalogid(ruleCatalog.getRulecatalogid());
                            rule.setName(ruleDto.getName());
                            rule.setDisplayname(ruleDto.getDisplayname());
                            rule.setErrorlevel(ruleDto.getErrorlevel());
                            rule.setIsneed(ruleDto.getIsneed());
                            rule.setRuleitemcompositetype(ruleDto.getRuleitemcompositetype());
                            rule.setRulecode(ruleDto.getRulecode());
                            this.spdpRuleMapper.insert(rule);
                        } while(ruleDto.getRuleItemDtos() == null);
                    } while(ruleDto.getRuleItemDtos().size() <= 0);

                    List<SpdpRuleitem> ruleItems = new ArrayList();
                    Iterator var11 = ruleDto.getRuleItemDtos().iterator();

                    while(var11.hasNext()) {
                        RuleItemDto ruleItemDto = (RuleItemDto)var11.next();
                        SpdpRuleitem ruleItem = new SpdpRuleitem();
                        ruleItem.setName(ruleItemDto.getName());
                        ruleItem.setDisplayname(ruleItemDto.getDisplayname());
                        ruleItem.setDescription(ruleItemDto.getDescription());
                        ruleItem.setValidatorconfigdatatext(ruleItemDto.getValidatorconfigdatatext());
                        ruleItem.setValidatorkey(ruleItemDto.getValidatorkey());
                        ruleItem.setRuleid(rule.getRuleid());
                        ruleItems.add(ruleItem);
                    }

                    this.spspRuleitemsService.saveBatch(ruleItems);
                }
            }

            return JsonResult.success();
        // }
    }

    public JsonResult deleteById(String ruleCatalogId) {
        List<SpdpRule> rules = this.spdpRuleMapper.selectList(new LambdaQueryWrapper<SpdpRule>().eq(SpdpRule::getRulecatalogid, ruleCatalogId));//getByRuleCatalogId(ruleCatalogId);
        Iterator var4 = rules.iterator();

        while(var4.hasNext()) {
            SpdpRule rule = (SpdpRule)var4.next();
            List<SpdpRuleitem> ruleItems = this.spspRuleitemMapper.selectList(new LambdaQueryWrapper<SpdpRuleitem>().eq(SpdpRuleitem::getRuleid,rule.getRuleid()));//.getByRuleId(rule.getId());
            this.spspRuleitemsService.removeBatchByIds(ruleItems);
        }

        this.spdpRuleService.removeBatchByIds(rules);
        this.spdpRulecatalogMapper.deleteById(ruleCatalogId);
        return JsonResult.success();
    }

    public JsonResult deleteByIds(List<String> ids) {
        List<SpdpRulecatalog> ruleCatalogs = this.listByIds(ids);
        if (ruleCatalogs.size() == 0) {
            // result.setStatus(0);
            // result.setMessage("无法根据ids获取数据检查，请确认数据库是否存在指定id的数据检查");
            // return result;
            return JsonResult.error("无法根据ids获取数据检查，请确认数据库是否存在指定id的数据检查");
        } else {
            Iterator var4 = ids.iterator();

            while(var4.hasNext()) {
                String id = (String)var4.next();
                List<SpdpRule> rules = this.spdpRuleMapper.selectList(new LambdaQueryWrapper<SpdpRule>().eq(SpdpRule::getRulecatalogid, id));//getByRuleCatalogId(id);
                Iterator var7 = rules.iterator();

                while(var7.hasNext()) {
                    SpdpRule rule = (SpdpRule)var7.next();
                    List<SpdpRuleitem> ruleItems = this.spspRuleitemMapper.selectList(new LambdaQueryWrapper<SpdpRuleitem>().eq(SpdpRuleitem::getRuleid,rule.getRuleid()));//ruleItemDao.getByRuleId(rule.getId());
                    this.spspRuleitemsService.removeBatchByIds(ruleItems);
                }

                this.spdpRuleService.removeBatchByIds(rules);
            }

            this.removeBatchByIds(ruleCatalogs);
            return JsonResult.success();
        }
    }
}
