package com.bjcj.service.naturalresources.categories;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.categories.ResourceCatalogsMapper;
import com.bjcj.model.po.naturalresources.categories.ResourceCatalogs;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 *@Author：qinyi
 *@Date：2024/4/23  16:04
*/
@Service
public class ResourceCatalogsService extends ServiceImpl<ResourceCatalogsMapper, ResourceCatalogs> {

    @Resource
    private ResourceCatalogsMapper resourceCatalogsMapper;

    public JsonResult<List<ResourceCatalogs>> treeList(Integer resourcecategory) {
        LambdaQueryWrapper<ResourceCatalogs> wrapper = new LambdaQueryWrapper<ResourceCatalogs>();
        wrapper.isNull(ResourceCatalogs::getParentid)
                .eq(ResourceCatalogs::getIsvisiable,true)
                .eq(ResourceCatalogs::getResourcecategory, resourcecategory)
                .orderByAsc(ResourceCatalogs::getDisplayorder);

        //查询根级
        List<ResourceCatalogs> list = resourceCatalogsMapper.selectList(wrapper);
        list.forEach(item -> {
            getChildren(item,1);
        });
        return JsonResult.success(list);
    }

    public JsonResult<List<ResourceCatalogs>> treeList2() {
        LambdaQueryWrapper<ResourceCatalogs> wrapper = new LambdaQueryWrapper<ResourceCatalogs>();
        wrapper.isNull(ResourceCatalogs::getParentid)
                .eq(ResourceCatalogs::getIsvisiable,true)
                .orderByAsc(ResourceCatalogs::getDisplayorder);

        //查询根级
        List<ResourceCatalogs> list = resourceCatalogsMapper.selectList(wrapper);
        list.forEach(item -> {
            getChildren(item,1);
        });
        return JsonResult.success(list);
    }


    private void getChildren(ResourceCatalogs item, int index){
        LambdaQueryWrapper<ResourceCatalogs> wrapper = new LambdaQueryWrapper<ResourceCatalogs>();
        wrapper.eq(ResourceCatalogs::getParentid, item.getId())
                .orderByAsc(ResourceCatalogs::getDisplayorder);
        if(index == 1){
            wrapper.eq(ResourceCatalogs::getIsvisiable, true);
        }

        //根据parentId查询
        List<ResourceCatalogs> list = resourceCatalogsMapper.selectList(wrapper);

        //写入到children
        item.setChildren(list);

        //如果children不为空，继续往下找
        if (!CollectionUtils.isEmpty(list)) {
            list.parallelStream().forEach(items->{getChildren(items,index);});
        }
    }

    public JsonResult<List<ResourceCatalogs>> lists(String name,String code,Integer resourcecategory) {

        //查询根级
        List<ResourceCatalogs> list = resourceCatalogsMapper.selectList(
                        Wrappers.<ResourceCatalogs>query().lambda()
                                .isNull(ResourceCatalogs::getParentid).eq(ResourceCatalogs::getResourcecategory,resourcecategory)
                                .eq(StringUtils.isNotBlank(name), ResourceCatalogs::getName, name)
                                .eq(StringUtils.isNotBlank(code), ResourceCatalogs::getCode, code)
                                .orderByAsc(ResourceCatalogs::getDisplayorder)
                );
        list.parallelStream().forEach(item->{getChildren(item,2);});
        // for(int i=0;i<list.size();i++){
        //     List<CategoriesDataInfo> categoriesDataInfos = this.categoriesDataInfoMapper.selectList(new LambdaQueryWrapper<CategoriesDataInfo>().eq(CategoriesDataInfo::getCatalogue, String.valueOf(list.get(i).getId())));
        //     if(categoriesDataInfos.size() > 0){
        //         list.remove(list.get(i));
        //         i--;
        //     }
        // }
        return JsonResult.success(list);

    }

    public JsonResult del(String id) {
        if(!this.resourceCatalogsMapper.selectList(new LambdaQueryWrapper<ResourceCatalogs>().eq(ResourceCatalogs::getParentid, id)).isEmpty()){
            return JsonResult.error("该目录下还有子目录，请先删除子目录");
        }
        return this.resourceCatalogsMapper.deleteById(id) > 0 ? JsonResult.success() : JsonResult.error();
    }

    public JsonResult uptStatus(String id, Boolean isvisiable) {
        LambdaUpdateWrapper<ResourceCatalogs> wrapper = new LambdaUpdateWrapper<ResourceCatalogs>();
        wrapper.set(ResourceCatalogs::getIsvisiable, isvisiable)
                .eq(ResourceCatalogs::getId, id);
        return resourceCatalogsMapper.update(wrapper) > 0 ? JsonResult.success() : JsonResult.error();
    }

}
