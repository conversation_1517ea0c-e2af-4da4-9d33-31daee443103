package com.bjcj.common.core.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import com.bjcj.common.core.converter.LocalDateTimeConverter;
import com.bjcj.common.utils.properties.GeoCloud2Properties;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 全局注册
 * <AUTHOR>
 * @date 2023/12/19 16:38 周二
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Resource
    GeoCloud2Properties geoCloud2Properties;

    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverter(new LocalDateTimeConverter());
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/file/**")
                .addResourceLocations("file:" + geoCloud2Properties.getFile().getUpload());
        registry.addResourceHandler("/log/**")
                .addResourceLocations("file:" + geoCloud2Properties.getFile().getLog());
    }
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，打开注解式鉴权功能
        registry.addInterceptor(new SaInterceptor()).addPathPatterns("/**");
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                //是否发送Cookie
                .allowCredentials(true)
                //放行哪些原始域
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "options")
                .allowedHeaders("*")
                .exposedHeaders("*");
    }


}
