package com.bjcj.model.po.platformManage.specialPlan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/1/26  10:18
*/
/**
    * 专题展示方案数据
    */
@Schema(description="专题展示方案数据")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "special_plan_data")
public class SpecialPlanData implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 专题名称
     */
    @TableField(value = "special_name")
    @Schema(description="专题名称")
    @Size(max = 100,message = "专题名称max length should less than 100")
    @NotBlank(message = "专题名称is not blank")
    private String specialName;

    /**
     * 显示名称
     */
    @TableField(value = "show_name")
    @Schema(description="显示名称")
    @Size(max = 100,message = "显示名称max length should less than 100")
    private String showName;

    /**
     * 专题id
     */
    @TableField(value = "special_plan_id")
    @Schema(description="专题id")
    @NotNull(message = "专题idis not null")
    private Long specialPlanId;

    /**
     * 专题目录id
     */
    @TableField(value = "special_plan_dir_id")
    @Schema(description="专题目录id")
    private String specialPlanDirId;

    /**
     * 显示顺序
     */
    @TableField(value = "show_sort")
    @Schema(description="显示顺序")
    @NotNull(message = "显示顺序is not null")
    private Integer showSort;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "update_time",fill = FieldFill.UPDATE)
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField(value = "operater")
    @Schema(description="")
    @Size(max = 30,message = "max length should less than 30")
    private String operater;

    /**
     * 是否显示
     */
    @TableField(value = "is_show")
    @Schema(description="是否显示")
    @NotNull(message = "是否显示is not null")
    private Boolean isShow;

    /**
     * 是否展开
     */
    @TableField(value = "is_expand")
    @Schema(description="是否展开")
    @NotNull(message = "是否展开is not null")
    private Boolean isExpand;

    /**
     * 是否初始化加载
     */
    @TableField(value = "is_init_load")
    @Schema(description="是否初始化加载")
    @NotNull(message = "是否初始化加载is not null")
    private Boolean isInitLoad;

    @TableField(value = "cata_data_info_id")
    @Schema(description="数据服务id")
    private String cataDataInfoId;

    @TableField(value = "cata_service_id")
    @Schema(description="数据服务地图服务id")
    private String cataServiceId;

    private static final long serialVersionUID = 1L;
}