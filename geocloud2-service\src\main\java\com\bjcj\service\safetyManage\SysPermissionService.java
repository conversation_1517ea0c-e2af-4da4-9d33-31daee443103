package com.bjcj.service.safetyManage;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.safetyManage.SysPermissionMapper;
import com.bjcj.model.po.safetyManage.SysPermission;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *@Author：qinyi
 *@Date：2023/12/5  9:36
*/
@Service
public class SysPermissionService extends ServiceImpl<SysPermissionMapper, SysPermission> {

    @Resource
    SysPermissionMapper sysPermissionMapper;

    public JsonResult<List<SysPermission>> getPermsList() {
        return JsonResult.success(sysPermissionMapper.selectAll());
    }
}
