package com.bjcj.web.system;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.system.SysErrorLog;
import com.bjcj.model.po.system.SysLoginLog;
import com.bjcj.model.po.system.SysOperaLog;
import com.bjcj.model.qo.SysErrorLogQo;
import com.bjcj.model.qo.SysLoginLogQo;
import com.bjcj.model.qo.SysOperaLogQo;
import com.bjcj.service.system.SysErrorLogService;
import com.bjcj.service.system.SysLoginLogService;
import com.bjcj.service.system.SysOperaLogService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/12/19 14:34 周二
 */
@Tag(name = "日志管理", description = "日志管理")
@ApiSupport(order = 4, author = "guow")
@RestController
@RequestMapping("/log")
@Slf4j
public class LogController {

    @Resource
    SysOperaLogService sysOperaLogService;

    @Resource
    SysLoginLogService sysLoginLogService;

    @Resource
    SysErrorLogService sysErrorLogService;

    @Operation(summary = "登录日志列表（带搜索）", description = "登录日志列表（带搜索）")
    @ApiOperationSupport(order = 1)
    @GetMapping("/entry")
    public JsonResult<Page<SysLoginLog>> loginLogList(@Validated @ParameterObject SysLoginLogQo sysLoginLogQo) {
        log.debug("sysLoginLogQo: {}", sysLoginLogQo);
        return sysLoginLogService.loginLogList(sysLoginLogQo);
    }

    @Operation(summary = "操作日志列表（带搜索）", description = "操作日志列表（带搜索）")
    @ApiOperationSupport(order = 2)
    @GetMapping("/opera")
    public JsonResult<Page<SysOperaLog>> operaLogList(@Validated @ParameterObject SysOperaLogQo sysOperaLogQo) {
        log.debug("sysOperaLogQo: {}", sysOperaLogQo);
        return sysOperaLogService.operaLogList(sysOperaLogQo);
    }

    @Operation(summary = "错误日志列表（带搜索）", description = "错误日志列表（带搜索）")
    @ApiOperationSupport(order = 3)
    @GetMapping("/error")
    public JsonResult<Page<SysErrorLog>> errorLogList(@Validated @ParameterObject SysErrorLogQo sysErrorLogQo) {
        log.debug("sysErrorLogQo: {}", sysErrorLogQo);
        return sysErrorLogService.errorLogList(sysErrorLogQo);
    }

}
