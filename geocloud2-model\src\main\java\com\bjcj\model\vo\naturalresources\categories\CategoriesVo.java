package com.bjcj.model.vo.naturalresources.categories;

import com.bjcj.model.po.naturalresources.categories.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/6 10:00 周三
 */
@Data
public class CategoriesVo implements Serializable {

    private CategoriesDataInfo categoriesDataInfo;

    private List<CategoriesFactor> categoriesFactor;

    private List<CategoriesPhysics> categoriesPhysics;

    private List<CategoriesService> categoriesService;

    private CategoriesMeta categoriesMeta;

}
