<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.naturalresources.fileResManage.FileInfoMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.naturalresources.fileResManage.FileInfo">
    <!--@mbg.generated-->
    <!--@Table public.file_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="file_set_info_id" jdbcType="BIGINT" property="fileSetInfoId" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="file_size" jdbcType="VARCHAR" property="fileSize" />
    <result column="file_type" jdbcType="VARCHAR" property="fileType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, file_set_info_id, file_name, file_url, file_size, file_type, create_time, update_time, 
    "operator"
  </sql>

  <delete id="deleteByPid">
    delete from file_info where file_set_info_id=#{pid}
    </delete>
</mapper>