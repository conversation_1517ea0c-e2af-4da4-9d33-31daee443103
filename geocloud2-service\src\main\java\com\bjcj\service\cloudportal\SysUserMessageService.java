package com.bjcj.service.cloudportal;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.cloudportal.SysUserMessageMapper;
import com.bjcj.model.dto.resReview.ResRegisterLogDto;
import com.bjcj.model.po.cloudportal.SysUserMessage;
import com.bjcj.model.po.naturalresources.resReview.UserServrReview;
import com.bjcj.model.po.safetyManage.User;
import com.bjcj.service.safetyManage.UserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 *@Author：qinyi
 *@Date：2024/7/29  9:50
*/
@Service
@Slf4j
public class SysUserMessageService extends ServiceImpl<SysUserMessageMapper, SysUserMessage> {

    @Resource
    SysUserMessageMapper sysUserMessageMapper;

    @Resource
    UserService userService;

    public Boolean saveReview(ResRegisterLogDto dto,Long sendUserId){
        Long receiveUserId = this.userService.getOne(new LambdaQueryWrapper<User>().eq(User::getUsername, dto.getPublishUserName())).getId();
        SysUserMessage sysUserMessage = SysUserMessage.builder()
                .receiveUserId(receiveUserId)
                .sendUserId(sendUserId)
                .title("资源注册审核")
                .content("您提交的"+dto.getResType()+":"+dto.getResName()+"已审核"+dto.getReviewRemark())
                .build();
        int insert = this.sysUserMessageMapper.insert(sysUserMessage);
        if (insert>0) return true;
        return false;

    }

    public Boolean saveReviewSysh(UserServrReview dto, Long sendUserId){
        SysUserMessage sysUserMessage = SysUserMessage.builder()
                .receiveUserId(dto.getUserId())
                .sendUserId(sendUserId)
                .title("资源使用审核")
                .content("您提交的"+dto.getServiceType()+":"+dto.getTitle()+"已审核"+dto.getReviewRemark())
                .build();
        int insert = this.sysUserMessageMapper.insert(sysUserMessage);
        if (insert>0) return true;
        return false;
    }

    public JsonResult noReadMsgCount() {
        String tokenValue = StpUtil.getTokenValue();
        log.error("tokenValue: " + tokenValue);

        JWT jwt = JWTUtil.parseToken(tokenValue);
        log.error("jwt: " + jwt);

        Long userId = Long.parseLong(jwt.getPayload("userId").toString());
        log.error("userId: " + userId);
        return JsonResult.success(
                sysUserMessageMapper.selectCount(
                        new LambdaQueryWrapper<SysUserMessage>()
                                .eq(SysUserMessage::getReceiveUserId, userId)
                                .eq(SysUserMessage::getIsRead, false)
                )
        );
    }
}
