package com.bjcj.mapper.safetyManage;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.safetyManage.SysRoleFunc;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 *@Author：qinyi
 *@Date：2023/12/13  15:09
*/
public interface SysRoleFuncMapper extends BaseMapper<SysRoleFunc> {
    Integer insertBatch(@Param("list") List<SysRoleFunc> list);

    boolean deleteBatch(@Param("list") List<String> idlist,@Param("roleId") Long roleId);

    String selectRoleNameByFuncId(@Param("id") String id);
}