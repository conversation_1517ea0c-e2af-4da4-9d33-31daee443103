package com.bjcj.model.dto.searchConf;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/3/6  11:40
*/

/**
    * 专题查询配置表
    */
@Schema(description="专题查询配置表")
@Data
public class SpecialPlanSearchConfDto implements Serializable {

    /**
     * mapserver服务id
     */
    @Schema(description="mapserver服务id列表")
    @NotNull(message = "mapserver服务idis not null")
    private List<String> resourceServicesIdList;

    /**
     * 专题id
     */
    @Schema(description="专题id")
    @NotNull(message = "专题idis not null")
    private Long specialPlanId;

    private static final long serialVersionUID = 1L;
}