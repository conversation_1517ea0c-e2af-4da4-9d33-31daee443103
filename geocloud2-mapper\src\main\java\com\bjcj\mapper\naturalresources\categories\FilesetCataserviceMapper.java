package com.bjcj.mapper.naturalresources.categories;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.naturalresources.categories.FilesetCataservice;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/1/16  16:02
*/
public interface FilesetCataserviceMapper extends BaseMapper<FilesetCataservice> {
    List<FilesetCataservice> selectByCataServiceId(@Param("cataServiceId") String cataServiceId);

    void deleteBatch(@Param("idList") List<Long> idList,@Param("resourceServicesId") String resourceServicesId);
}