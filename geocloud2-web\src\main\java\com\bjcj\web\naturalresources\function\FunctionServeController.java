package com.bjcj.web.naturalresources.function;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.naturalresources.categories.ResourceServices;
import com.bjcj.model.vo.naturalresources.categories.CategoriesParamVo;
import com.bjcj.model.vo.naturalresources.function.FunctionVo;
import com.bjcj.service.naturalresources.categories.ResourceServicesService;
import com.bjcj.service.naturalresources.function.FunctionServeService;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/30 15:14 周四
 */
@Tag(name = "功能服务管理(服务信息)",description = "功能服务管理(服务信息)")
@ApiSupport(order = 57)
@RestController
@Slf4j
@RequestMapping(value = "/function_serve")
public class FunctionServeController {

    @Resource
    private FunctionServeService functionServeService;

    @Resource
    private ResourceServicesService resourceServicesService;

    @SaCheckPermission("sys:read")
    @Operation(summary = "服务信息列表")
    @PostMapping(value = "/lists")
    public JsonResult lists(@RequestParam String id, String name, int current, int size){

        return functionServeService.lists(id,name,current,size);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "安全管理角色授权应用服务列表")
    @PostMapping(value = "/listsAuth")
    public JsonResult listsAuth(@RequestBody CategoriesParamVo categoriesParamVo){

        return functionServeService.listsAuth(categoriesParamVo);
    }

    @SaCheckPermission("sys:read")
    @OperaLog(
            operaModule = "根据id查询服务信息详情",
            operaType = OperaLogConstant.LOOK,
            operaDesc = "根据id查询服务信息详情"
    )
    @Operation(summary = "根据id查询服务信息详情")
    @GetMapping(value = "/findById/{id}")
    public JsonResult<FunctionVo> findById(@PathVariable Long id){

        return functionServeService.findById(id);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "服务信息注册",
            operaType = OperaLogConstant.CREATE,
            operaDesc = "服务信息注册"
    )
    @Operation(summary = "服务信息注册")
    @PostMapping(value = "/saveAll")
    public JsonResult saveAll(@RequestBody @Validated FunctionVo functionVo){

        return functionServeService.saveAll(functionVo);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "修改服务信息",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "修改服务信息"
    )
    @Operation(summary = "修改服务信息")
    @PutMapping(value = "/updateAll")
    public JsonResult updateAll(@RequestBody @Validated FunctionVo functionVo){

        return functionServeService.updateAll(functionVo);
    }

    @SaCheckPermission("sys:del")
    @OperaLog(
            operaModule = "删除服务信息",
            operaType = OperaLogConstant.DELETE,
            operaDesc = "删除服务信息"
    )
    @Operation(summary = "删除服务信息")
    @DeleteMapping(value = "/del")
    public JsonResult del(@RequestParam List<Long> id){

        return functionServeService.del(id);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "修改服务信息运行状态",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "修改服务信息运行状态"
    )
    @Operation(summary = "修改服务信息运行状态")
    @PostMapping(value = "/uptStatus")
    public JsonResult uptStatus(@RequestParam Long id, Boolean status){

        return functionServeService.uptStatus(id, status);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "修改服务信息权限",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "修改服务信息权限"
    )
    @Operation(summary = "修改服务信息权限")
    @PostMapping(value = "/uptAuth")
    public JsonResult uptAuth(@RequestParam Long id, int auth){

        return functionServeService.uptAuth(id, auth);
    }

    /*@Operation(summary = "修改参数说明")
    @PutMapping(value = "/updateRequestParams")
    public JsonResult updateRequestParams(@RequestBody @Validated RequestParams requestParams){

        return functionServeService.updateRequestParams(requestParams);
    }

    @Operation(summary = "删除参数说明")
    @GetMapping(value = "/delRequestParams/{id}")
    public JsonResult delRequestParams(@PathVariable Long id){

        return functionServeService.delRequestParams(id);
    }

    @Operation(summary = "修改返回参数")
    @PutMapping(value = "/updateRequestResult")
    public JsonResult updateRequestResult(@RequestBody @Validated RequestResult requestResult){

        return functionServeService.updateRequestResult(requestResult);
    }

    @Operation(summary = "删除返回参数")
    @GetMapping(value = "/delRequestResult/{id}")
    public JsonResult delRequestResult(@PathVariable Long id){

        return functionServeService.delRequestResult(id);
    }*/


    @SaCheckPermission("sys:read")
    @Operation(summary = "1.功能服务列表")
    @GetMapping(value = "/list")
    @Parameters({
            @Parameter(name = "resourcecatalogid", description = "左侧树id", required = true),
            @Parameter(name = "searchStr", description = "搜索名称(displayname)", required = false),
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true)
    })
    public JsonResult<Page<ResourceServices>> list(@RequestParam("resourcecatalogid") String resourcecatalogid,
                                                   @RequestParam(value = "searchStr",required = false) String searchStr,
                                                   @RequestParam("current") Integer page,
                                                   @RequestParam("size") Integer pageSize){
        Page<ResourceServices> pager = new Page<>(page, pageSize);
        return resourceServicesService.listData(pager,searchStr,resourcecatalogid);
    }

    @OperaLog(operaModule = "注册/修改功能服务",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "注册/修改功能服务")
    @SaCheckPermission("sys:write")
    @PostMapping("/saveOrUpdate")
    @Operation(summary = "2.注册/修改功能服务", description = "注册/修改功能服务")
    @RequestLock(prefix = "saveOrUpdateGnfw")
    @Transactional(rollbackFor = Exception.class)
    public JsonResult saveOrUpdate(@RequestBody @Validated ResourceServices resourceServices){
        resourceServices.setResourcecategory(Short.valueOf("1"));
        if(StringUtils.isNotBlank(resourceServices.getUrl()) && resourceServices.getUrl().contains("/arcgis")){
            resourceServices.setTransferurl(resourceServices.getUrl().split("/arcgis")[1]);
        }else{
            return JsonResult.error("url格式有误");
        }
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        resourceServices.setRegisterman(username);
        resourceServices.setRegisterdate(LocalDateTime.now());
        // return resourceServicesService.saveOrUpdate(resourceServices) ? JsonResult.success() : JsonResult.error();
        return this.resourceServicesService.saveOrUpdateData(resourceServices);
    }

    @OperaLog(operaModule = "删除功能服务"
            ,operaType = OperaLogConstant.DELETE,operaDesc = "删除功能服务")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del2")
    @Operation(summary = "3.删除功能服务", description = "删除功能服务")
    @Parameters({
            @Parameter(name = "id", description = "主键id", required = true)
    })
    public JsonResult del2(@RequestParam("id") String id){
        return resourceServicesService.removeById(id) ? JsonResult.success() : JsonResult.error();
    }

    @OperaLog(operaModule = "批量删除功能服务",operaType = OperaLogConstant.DELETE,operaDesc = "删除功能服务")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delBatch")
    @Operation(summary = "4.删除功能服务", description = "删除功能服务")
    @Parameters({
            @Parameter(name = "ids", description = "主键ids逗号分隔", required = true)
    })
    public JsonResult delBatch(@RequestParam("ids") String ids){
        if(!ids.contains(",")){
            return JsonResult.error("批量删除数据须大于等于两条");
        }
        List<String> idList = Arrays.asList(ids.split(","));
        return resourceServicesService.removeBatchByIds(idList) ? JsonResult.success() : JsonResult.error();
    }


    @SaCheckPermission("sys:read")
    @Operation(summary = "5.功能服务详情")
    @GetMapping(value = "/detail")
    @Parameters({
            @Parameter(name = "id", description = "功能服务id", required = true)
    })
    public JsonResult<ResourceServices> detail(@RequestParam("id") String id){
        return resourceServicesService.detail(id);
    }




}
