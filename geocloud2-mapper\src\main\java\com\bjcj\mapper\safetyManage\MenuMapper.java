package com.bjcj.mapper.safetyManage;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.safetyManage.Menu;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 *@Author：qinyi
 *@Date：2023/12/4  9:19
*/
public interface MenuMapper extends BaseMapper<Menu> {
    Menu checkMenuNameUnique(@Param("menuName") String menuName, @Param("parentId") Long parentId);

    Menu checkMenuNameUnique2(@Param("menuName") String menuName, @Param("parentId") Long parentId,@Param("appid") Long appid);

    int selectCountMenuByParentId(Long parentId);

    List<Menu> selectMenuListByUserId(@Param("menuName") String menuName,@Param("userId") Long userId);

    List<Menu> selectMenuListByUserIdSpecialPlan(@Param("platFormAppConfId") Long platFormAppConfId,@Param("userId") Long userId);

    List<Menu> selectMenuAll();

    List<Menu> selectMenuAllByUserId(@Param("userId") Long userId);

    List<String> selectMenuTree(@Param("roleId") Long roleId);
}