package com.bjcj.service.safetyManage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.safetyManage.RoleMenuMapper;
import com.bjcj.mapper.safetyManage.RolePermsMapper;
import com.bjcj.mapper.safetyManage.SysPermissionMapper;
import com.bjcj.model.dto.safetyManage.RoleMenuPermsDto;
import com.bjcj.model.dto.safetyManage.SpecialPlanAndMenuIds;
import com.bjcj.model.po.safetyManage.RoleMenu;
import com.bjcj.model.po.safetyManage.RolePerms;
import com.bjcj.model.po.safetyManage.SysPermission;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *@Author：qinyi
 *@Date：2023/12/4  10:48
*/
@Service
public class RoleMenuService extends ServiceImpl<RoleMenuMapper, RoleMenu> {

    @Resource
    RoleMenuMapper roleMenuMapper;

    @Resource
    RolePermsMapper rolePermsMapper;

    @Resource
    SysPermissionMapper sysPermissionMapper;

    @Resource
    RolePermsService rolePermsService;

    public JsonResult saveRoleMenu(RoleMenuPermsDto dto) {
        Long roleId = dto.getRoleId();
        String menuIds = dto.getMenuIds();
        String permsIds = dto.getPermsIds();
        List<SysPermission> sysPermissionList = this.sysPermissionMapper.selectList(null);
        Long permId = null;
        for(SysPermission item : sysPermissionList){
            if("sys:read".equals(item.getCode())){
                permId = item.getId();
            }
        }
        //操作权限不能为空,至少有一个读的权限
        if (Objects.isNull(permsIds) || "".equals(permsIds) || !Arrays.stream(permsIds.split(",")).toList().contains(permId.toString())) {
            return JsonResult.error("权限至少设置读!");
        }

        if (Objects.nonNull(menuIds) && !"".equals(menuIds)) {

            // 删除角色与菜单关联  重新插入最新关联数据
            LambdaQueryWrapper<RoleMenu> queryWrapper = new LambdaQueryWrapper<RoleMenu>();
            queryWrapper.eq(RoleMenu::getRoleId, roleId);
            this.roleMenuMapper.delete(queryWrapper);

            List<RoleMenu> roleMenuList = new ArrayList<RoleMenu>();
            String[] menuIdArr = menuIds.split(",");
            for (String menuId : menuIdArr) {
                RoleMenu roleMenu = new RoleMenu();
                roleMenu.setRoleId(roleId);
                roleMenu.setMenuId(Long.valueOf(menuId));
                // if(Objects.nonNull(dto.getSpecialPlanId())) roleMenu.setSpecialPlanId(dto.getSpecialPlanId());
                roleMenuList.add(roleMenu);
            }
            // this.roleMenuMapper.insertBatch(roleMenuList);
            this.saveBatch(roleMenuList);
        }

        //保存专题的菜单数据集
        List<SpecialPlanAndMenuIds> specialPlansAndMenuIds = dto.getSpecialPlansAndMenuIds();
        if(Objects.nonNull(specialPlansAndMenuIds) && !specialPlansAndMenuIds.isEmpty()){

            specialPlansAndMenuIds.forEach(item -> {

                //删除专题菜单旧数据重新插入
                this.roleMenuMapper.deleteByRoleAndSpecialPlanId(roleId,item.getSpecialPlanId());

                if (Objects.nonNull(item.getMenuIds()) && !"".equals(item.getMenuIds())) {

                    List<RoleMenu> roleMenuList = new ArrayList<RoleMenu>();
                    String[] menuIdArr = item.getMenuIds().split(",");
                    for (String menuId : menuIdArr) {
                        RoleMenu roleMenu = new RoleMenu();
                        roleMenu.setRoleId(roleId);
                        roleMenu.setMenuId(Long.valueOf(menuId));
                        roleMenu.setSpecialPlanId(item.getSpecialPlanId());
                        roleMenuList.add(roleMenu);
                    }
                    // this.roleMenuMapper.insertBatch(roleMenuList);
                    this.saveBatch(roleMenuList);
                }
            });
        }


        LambdaQueryWrapper<RolePerms> queryWrapper1 = new LambdaQueryWrapper<RolePerms>();
        queryWrapper1.eq(RolePerms::getRoleId, roleId);
        this.rolePermsMapper.delete(queryWrapper1);
        if (Objects.nonNull(permsIds) && !"".equals(permsIds)) {
            List<RolePerms> rolePermsList = new ArrayList<RolePerms>();
            String[] permsIdArr = permsIds.split(",");
            for (String permsId : permsIdArr) {
                RolePerms rolePerms = new RolePerms();
                rolePerms.setRoleId(roleId);
                rolePerms.setPermsId(Long.valueOf(permsId));
                // if(Objects.nonNull(dto.getSpecialPlanId())) rolePerms.setSpecialPlanId(dto.getSpecialPlanId());
                rolePermsList.add(rolePerms);
            }
            // this.rolePermsMapper.insertBatch(rolePermsList);
            this.rolePermsService.saveBatch(rolePermsList);
        }
        return JsonResult.success();
    }

    public JsonResult<RoleMenuPermsDto> getRoleMenuAndPerms(Long roleId,Long specialPlanId) {
        RoleMenuPermsDto dto = new RoleMenuPermsDto();
        dto.setRoleId(roleId);
        //查询角色关联的菜单和权限
        if (Objects.nonNull(roleId)) {
            List<RoleMenu> roleMenuList = this.roleMenuMapper.selectList(new LambdaQueryWrapper<RoleMenu>().eq(RoleMenu::getRoleId, roleId)
                    .isNull(RoleMenu::getSpecialPlanId));
                    // .eq(Objects.nonNull(specialPlanId), RoleMenu::getSpecialPlanId, specialPlanId));
            List<RolePerms> rolePermsList = this.rolePermsMapper.selectList(new LambdaQueryWrapper<RolePerms>().eq(RolePerms::getRoleId, roleId));
                    // .eq(Objects.nonNull(specialPlanId), RolePerms::getSpecialPlanId, specialPlanId));
            dto.setMenuIds(roleMenuList.stream().map(roleMenu -> String.valueOf(roleMenu.getMenuId())).collect(Collectors.joining(",")));
            dto.setPermsIds(rolePermsList.stream().map(rolePerms -> String.valueOf(rolePerms.getPermsId())).collect(Collectors.joining(",")));
        }
        if(Objects.nonNull(specialPlanId)){
            List<RoleMenu> roleMenuList = this.roleMenuMapper.selectList(new LambdaQueryWrapper<RoleMenu>().eq(RoleMenu::getRoleId, roleId)
                    .eq(RoleMenu::getSpecialPlanId, specialPlanId));
            SpecialPlanAndMenuIds s = new SpecialPlanAndMenuIds();
            s.setMenuIds(roleMenuList.stream().map(roleMenu -> String.valueOf(roleMenu.getMenuId())).collect(Collectors.joining(",")));
            s.setSpecialPlanId(specialPlanId);
            List<SpecialPlanAndMenuIds> sList = new ArrayList<>();
            sList.add(s);
            dto.setSpecialPlansAndMenuIds(sList);
        }
        return JsonResult.success(dto);
    }
}
