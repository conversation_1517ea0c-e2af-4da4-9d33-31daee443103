package com.bjcj.service.naturalresources.resReview;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.categories.CategoriesDataInfoMapper;
import com.bjcj.mapper.naturalresources.categories.ResourceDataitemsMapper;
import com.bjcj.mapper.naturalresources.categories.ResourceServicesMapper;
import com.bjcj.mapper.naturalresources.resReview.ResRegisterLogMapper;
import com.bjcj.model.dto.resReview.ResRegisterLogDto;
import com.bjcj.model.po.naturalresources.categories.ResourceDataitems;
import com.bjcj.model.po.naturalresources.categories.ResourceServices;
import com.bjcj.model.po.naturalresources.resReview.ResRegisterLog;
import com.bjcj.service.cloudportal.SysUserMessageService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *@Author：qinyi
 *@Date：2024/1/22  9:43
*/
@Service
public class ResRegisterLogService extends ServiceImpl<ResRegisterLogMapper, ResRegisterLog> {

    @Resource
    private ResRegisterLogMapper resRegisterLogMapper;

    @Resource
    private CategoriesDataInfoMapper categoriesDataInfoMapper;

    @Resource
    private ResourceDataitemsMapper resourceDataitemsMapper;

    @Resource
    private ResourceServicesMapper resourceServicesMapper;

    @Resource
    private SysUserMessageService sysUserMessageService;


    public JsonResult<Page> queryPage(Page<ResRegisterLog> pager, String searchStr, String reviewStatus, String serviceType, String startTime, String endTime, Boolean orderAsc, String orderColumn, Integer page, Integer pageSize) {
        Timestamp s = null;
        Timestamp e = null;
        if(startTime!=null && !"".equals(startTime) && endTime!=null && !"".equals(endTime)){
            startTime = startTime+" 00:00:00";
            endTime = endTime+" 23:59:59";
            s = Timestamp.valueOf(startTime);
            e = Timestamp.valueOf(endTime);
        }
        if(reviewStatus.equals("待审核")){
            if("1".equals(serviceType)){
                //功能服务
                Page<ResourceServices> pager2 = new Page<>(page, pageSize);
                LambdaQueryWrapper<ResourceServices> wrapper = new LambdaQueryWrapper<ResourceServices>();
                wrapper.eq(ResourceServices::getStatus,0).eq(ResourceServices::getResourcecategory,1)
                        .like(StringUtils.isNotBlank(searchStr), ResourceServices::getDisplayname, searchStr)
                        .between(Objects.nonNull(startTime), ResourceServices::getRegisterdate, s, e);

                List<OrderItem> orderList = new ArrayList<>();
                OrderItem orderItem = new OrderItem() {{
                    setAsc(orderAsc);
                    setColumn(orderColumn);
                }};
                orderList.add(orderItem);
                pager2.setOrders(orderList);
                Page categoriesServicePage = this.resourceServicesMapper.selectPage(pager2, wrapper);
                //数据转换 返回统一的格式
                List<ResRegisterLog> result = new ArrayList<>();
                categoriesServicePage.getRecords().forEach(item -> {
                    ResourceServices items = BeanUtil.copyProperties(item,ResourceServices.class);
                    ResRegisterLog log = new ResRegisterLog();
                    log.setPublishTime(items.getRegisterdate());
                    log.setResName(items.getName());
                    log.setPublishUserName(items.getRegisterman());
                    log.setReviewStatus(null);
                    log.setResId(items.getId());
                    log.setResType("应用服务");
                    result.add(log);
                });
                categoriesServicePage.setRecords(result);
                return JsonResult.success(categoriesServicePage);
            }else {
                LambdaQueryWrapper<ResourceDataitems> wrapper = new LambdaQueryWrapper<ResourceDataitems>();
                wrapper.eq(ResourceDataitems::getStatus, 0)
                        .like(StringUtils.isNotBlank(searchStr), ResourceDataitems::getDisplayname, searchStr)
                        .between(Objects.nonNull(startTime), ResourceDataitems::getRegisterdate, s, e);
                Page<ResourceDataitems> pager2 = new Page<>(page, pageSize);
                List<OrderItem> orderList = new ArrayList<>();
                OrderItem orderItem = new OrderItem() {{
                    setAsc(orderAsc);
                    setColumn(orderColumn);
                }};
                orderList.add(orderItem);
                pager2.setOrders(orderList);
                Page categoriesDataInfoPage = this.resourceDataitemsMapper.selectPage(pager2, wrapper);
                //数据转换 返回统一的格式
                List<ResRegisterLog> result = new ArrayList<>();
                categoriesDataInfoPage.getRecords().forEach(item -> {
                    ResourceDataitems items = BeanUtil.copyProperties(item, ResourceDataitems.class);
                    ResRegisterLog log = new ResRegisterLog();
                    log.setPublishTime(items.getRegisterdate());
                    log.setResName(items.getName());
                    log.setPublishUserName(items.getRegisterman());
                    log.setReviewStatus(null);
                    log.setResId(items.getId());
                    log.setResType("数据资源");
                    result.add(log);
                });
                categoriesDataInfoPage.setRecords(result);
                return JsonResult.success(categoriesDataInfoPage);
            }
        }else{  //已审核
            LambdaQueryWrapper<ResRegisterLog> wrapper = new LambdaQueryWrapper<ResRegisterLog>();
            wrapper.like(StringUtils.isNotBlank(searchStr), ResRegisterLog::getResName, searchStr)
                    .between(Objects.nonNull(startTime), ResRegisterLog::getPublishTime, s, e);
            if("1".equals(serviceType)){
                wrapper.eq(ResRegisterLog::getResType,"应用服务");
            }else {
                wrapper.eq(ResRegisterLog::getResType,"数据资源");
            }
            Page<ResRegisterLog> resRegisterLogPage = resRegisterLogMapper.selectPage(pager, wrapper);
            return JsonResult.success(resRegisterLogPage);
        }
    }

    public JsonResult saveData(ResRegisterLogDto dto) {
        int status = 0;
        if(dto.getReviewStatus()) status=1;
        else status=2;
        //添加审核记录并改变主表审核状态
        if(dto.getResType().equals("应用服务")){
            this.resourceDataitemsMapper.updateReviewStatus2(dto.getResId(),status);
        }else{
            this.resourceDataitemsMapper.updateReviewStatus(dto.getResId(),status);
            this.resourceServicesMapper.update(new LambdaUpdateWrapper<ResourceServices>().eq(ResourceServices::getDataitemid,dto.getResId()).set(ResourceServices::getStatus,status));
        }
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        ResRegisterLog resRegisterLog = BeanUtil.copyProperties(dto,ResRegisterLog.class);
        if(this.resRegisterLogMapper.exists(new LambdaQueryWrapper<ResRegisterLog>().eq(ResRegisterLog::getResId,dto.getResId()).eq(ResRegisterLog::getReviewStatus,true)))
            return JsonResult.error("该资源已审核,请勿重复提交");
        resRegisterLog.setOperater(username);
        resRegisterLog.setReviewTime(LocalDateTime.now());
        sysUserMessageService.saveReview(dto,userId);
        return JsonResult.success(this.resRegisterLogMapper.insert(resRegisterLog));
    }
}
