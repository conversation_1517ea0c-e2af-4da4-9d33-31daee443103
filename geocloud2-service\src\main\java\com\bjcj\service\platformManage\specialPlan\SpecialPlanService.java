package com.bjcj.service.platformManage.specialPlan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.platformManage.specialPlan.SpecialPlanDataMapper;
import com.bjcj.mapper.platformManage.specialPlan.SpecialPlanDirMapper;
import com.bjcj.mapper.platformManage.specialPlan.SpecialPlanMapper;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlan;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlanData;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlanDir;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *@Author：qinyi
 *@Date：2024/1/19  9:09
*/
@Service
public class SpecialPlanService extends ServiceImpl<SpecialPlanMapper, SpecialPlan> {

    @Resource
    SpecialPlanMapper specialPlanMapper;

    @Resource
    SpecialPlanDirMapper specialPlanDirMapper;

    @Resource
    SpecialPlanDataMapper specialPlanDataMapper;

    @Resource
    SpecialPlanDirService specialPlanDirService;

    public JsonResult exportTo(Long fromPlatformAppConfId, Long toPlatformAppConfId) {
        SpecialPlan oldPlan = this.specialPlanMapper.selectOne(new LambdaQueryWrapper<SpecialPlan>().eq(SpecialPlan::getPlatformAppConfId, fromPlatformAppConfId));
        SpecialPlan newPlan = this.specialPlanMapper.selectOne(new LambdaQueryWrapper<SpecialPlan>().eq(SpecialPlan::getPlatformAppConfId, toPlatformAppConfId));
        if(Objects.isNull(newPlan)) JsonResult.error("目标平台应用不存在");
        Long oldPlanId = oldPlan.getId();
        Long newPlanId = newPlan.getId();

        //先删除原数据
        List<String> newPlanDirIdList = this.specialPlanDirMapper.selectList(new LambdaQueryWrapper<SpecialPlanDir>()
                .eq(SpecialPlanDir::getSpecialPlanId, newPlanId)).stream().map(item -> item.getId()).toList();
        newPlanDirIdList.forEach(item -> {
            specialPlanDirService.delDataById(item, newPlanId);
        });


        List<SpecialPlanDir> oldPlanDirList = this.specialPlanDirMapper.selectList(new LambdaQueryWrapper<SpecialPlanDir>()
                .eq(SpecialPlanDir::getSpecialPlanId, oldPlanId));
        if(oldPlanDirList.isEmpty())
            return JsonResult.error("源平台应用目录为空");
        List<SpecialPlanDir> finalDirList = oldPlanDirList;
        List<SpecialPlanDir> collect = oldPlanDirList.stream()
                .filter(item -> Objects.equals(item.getParentId(), "0"))
                .map(item -> item.setChildren(getChild(item.getId(), finalDirList)))
                .sorted(Comparator.comparingInt(dir -> (dir.getShowSort() == null ? 0 : dir.getShowSort())))
                .toList();
        String parent_dirid = null;
        this.insertDirTree(parent_dirid,collect,newPlanId, oldPlanId);
        return JsonResult.success("导入成功!");
    }

    private List<SpecialPlanDir> getChild(String id, List<SpecialPlanDir> dirList){
        return dirList.stream()
                .filter(item -> Objects.equals(item.getParentId(), id))
                .map(item -> item.setChildren(getChild(item.getId(), dirList)))
                .sorted(Comparator.comparingInt(dir -> (dir.getShowSort() == null ? 0 : dir.getShowSort())))
                .collect(Collectors.toList());
    }

    private void insertDirTree(String parent_dirid, List<SpecialPlanDir> collect, Long newPlanId , Long oldPlanId){
        collect.forEach(dir -> {
            String oldDirId = dir.getId();
            dir.setId(null);
            dir.setSpecialPlanId(newPlanId);
            dir.setParentId(parent_dirid);
            this.specialPlanDirMapper.insert(dir);
            //插入data数据
            List<SpecialPlanData> specialPlanDataList = this.specialPlanDataMapper.selectList(new LambdaQueryWrapper<SpecialPlanData>()
                    .eq(SpecialPlanData::getSpecialPlanDirId, oldDirId)
                    .eq(SpecialPlanData::getSpecialPlanId, oldPlanId));
            specialPlanDataList.forEach(data -> {
                data.setId(null);
                data.setSpecialPlanId(newPlanId);
                data.setSpecialPlanDirId(dir.getId());
                this.specialPlanDataMapper.insert(data);
            });
            if(!dir.getChildren().isEmpty())
                this.insertDirTree(dir.getId(),dir.getChildren(),newPlanId,oldPlanId);
        });
    }
}
