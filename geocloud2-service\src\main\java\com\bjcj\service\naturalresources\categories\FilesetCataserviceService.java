package com.bjcj.service.naturalresources.categories;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.categories.FilesetCataserviceMapper;
import com.bjcj.mapper.naturalresources.fileResManage.FileSetInfoMapper;
import com.bjcj.model.po.naturalresources.categories.FilesetCataservice;
import com.bjcj.model.po.naturalresources.fileResManage.FileSetInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 *@Author：qinyi
 *@Date：2024/1/16  16:02
*/
@Service
public class FilesetCataserviceService extends ServiceImpl<FilesetCataserviceMapper, FilesetCataservice> {

    @Resource
    FilesetCataserviceMapper filesetCataserviceMapper;

    @Resource
    FileSetInfoMapper fileSetInfoMapper;

    public JsonResult<List<FileSetInfo>> exitsFileList(String resourceServicesId) {
        List<Long> ids = this.filesetCataserviceMapper.selectByCataServiceId(resourceServicesId).stream().map(FilesetCataservice::getFileSetInfoId).toList();
        if(ids.isEmpty()) return JsonResult.success(new ArrayList<FileSetInfo>());
        return JsonResult.success(this.fileSetInfoMapper.selectList(new LambdaQueryWrapper<FileSetInfo>().in(!ids.isEmpty(),FileSetInfo::getId, ids)));
    }

    public JsonResult delBatch(String ids, String resourceServicesId) {
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        this.filesetCataserviceMapper.deleteBatch(idList,resourceServicesId);
        return JsonResult.success();
    }
}
