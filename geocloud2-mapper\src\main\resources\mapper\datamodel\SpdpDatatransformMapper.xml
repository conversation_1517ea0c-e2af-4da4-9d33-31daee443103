<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.SpdpDatatransformMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.SpdpDatatransform">
    <!--@mbg.generated-->
    <!--@Table public.spdp_datatransform-->
    <result column="datatransformid" jdbcType="CHAR" property="datatransformid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="displayname" jdbcType="VARCHAR" property="displayname" />
    <result column="datatransformmodelid" jdbcType="VARCHAR" property="datatransformmodelid" />
    <result column="params" jdbcType="VARCHAR" property="params" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    datatransformid, "name", displayname, datatransformmodelid, params
  </sql>
</mapper>