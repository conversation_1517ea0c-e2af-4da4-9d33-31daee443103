package com.bjcj.model.vo.naturalresources.function;

import com.bjcj.model.po.naturalresources.function.FunctionProvider;
import com.bjcj.model.po.naturalresources.function.FunctionServe;
import com.bjcj.model.po.naturalresources.function.RequestParams;
import com.bjcj.model.po.naturalresources.function.RequestResult;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/8 9:13 周五
 */
@Data
public class FunctionVo implements Serializable {

    private FunctionServe functionServe;

    private FunctionProvider functionProvider;

    private List<RequestParams> requestParams;

    private List<RequestResult> requestResult;

}
