package com.bjcj.web.system;

import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.common.utils.FileUtil;
import com.bjcj.common.utils.domain.FileInfo;
import com.bjcj.common.utils.enums.FileTypeEnum;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/6 16:01
 */
@Tag(name = "文件管理", description = "文件管理")
@ApiSupport(order = 3, author = "guow")
@RestController
@RequestMapping("/file")
@Slf4j
public class FileController {

    @Operation(summary = "上传文件", description = "上传文件")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "file", description = "文件流", required = true),
            @Parameter(name = "fileType", description = "文件类型", required = true)
    })
    @PostMapping("/upload")
    public JsonResult<FileInfo> uploadFile(@RequestParam("file") MultipartFile file, FileTypeEnum fileType) {
        FileInfo fileInfo = FileUtil.uploadFile(file, fileType);
        return JsonResult.success(fileInfo);
    }

    @Operation(summary = "多文件上传", description = "多文件上传")
    @ApiOperationSupport(order = 2)
    @Parameters({
            @Parameter(name = "files", description = "文件流", required = true),
            @Parameter(name = "fileType", description = "文件类型", required = true)
    })
    @PostMapping("/uploads")
    public JsonResult<List<FileInfo>> uploadFiles(@RequestParam("files") MultipartFile[] files, FileTypeEnum fileType) {
        List<FileInfo> fileInfo = FileUtil.uploadFiles(files, fileType);
        return JsonResult.success(fileInfo);
    }

    @Operation(summary = "下载文件", description = "下载文件")
    @ApiOperationSupport(order = 3)
    @Parameter(name = "url", description = "文件路径", required = true)
    @GetMapping("/download")
    public ResponseEntity<InputStreamResource> downloadFile(@RequestParam("url") String url) throws Exception {
        return FileUtil.downloadFile(url);
    }

}
