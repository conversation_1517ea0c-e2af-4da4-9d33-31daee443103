<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.eswPlatform.FlightRoutesMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.eswPlatform.FlightRoutes">
    <!--@mbg.generated-->
    <!--@Table public.flight_routes-->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="speed" jdbcType="VARCHAR" property="speed" />
    <result column="perspective" jdbcType="VARCHAR" property="perspective" />
    <result column="special_plan_id" jdbcType="BIGINT" property="specialPlanId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", content, "type", speed, perspective, special_plan_id
  </sql>
</mapper>