package com.bjcj.model.po.platformManage.analysisPlan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/2/21  17:36
 * mybatisflex 示例bean
*/
@Schema(description="分析展示方案表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "analysis_plan")
public class AnalysisPlan implements Serializable {
    /**
     * 雪花id
     * @Column(value = "isDeleted", isLogicDelete = true)  逻辑删除
     * @Column(value = "updatedTime", onInsertValue = "now()", onUpdateValue = "now()")  onInsertValue 自动填充时间
     */
    // @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 名称
     */
    @TableField(value = "plan_name")
    @Schema(description="名称")
    @Size(max = 100,message = "名称max length should less than 100")
    @NotBlank(message = "名称is not blank")
    private String planName;

    /**
     * 描述
     */
    @TableField(value = "remark")
    @Schema(description="描述")
    @Size(max = 255,message = "描述max length should less than 255")
    private String remark;

    /**
     * 配置json信息
     */
    @TableField(value = "json_content")
    @Schema(description="配置json信息")
    private String jsonContent;

    @TableField(value = "json_schema")
    @Schema(description="配置json信息描述")
    private String jsonSchema;

    @TableField(value = "json_plan")
    @Schema(description="自定义方案")
    private String jsonPlan;

    /**
     * 平台应用配置id
     */
    @TableField(value = "platform_app_conf_id")
    @Schema(description="平台应用配置id")
    @NotNull(message = "平台应用配置idis not null")
    private Long platformAppConfId;

    /**
     * 方案类型:自定义,重叠分析,规划分析,基本农田
     */
    @TableField(value = "plan_type")
    @Schema(description="方案类型:自定义,重叠分析,规划分析,基本农田")
    @Size(max = 20,message = "方案类型:自定义,重叠分析,规划分析,基本农田max length should less than 20")
    @NotBlank(message = "方案类型:自定义,重叠分析,规划分析,基本农田is not blank")
    private String planType;

    /**
     * 分析图层id
     */
    @TableField(value = "fx_data_layer_id")
    @Schema(description="分析图层id")
    private Long fxDataLayerId;

    /**
     * 被分析图层id
     */
    @TableField(value = "bfx_data_layer_id")
    @Schema(description="被分析图层id")
    private Long bfxDataLayerId;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "update_time",fill = FieldFill.UPDATE)
    @Schema(description="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField(value = "operater")
    @Schema(description="")
    @Size(max = 30,message = "max length should less than 30")
    private String operater;

    @TableField(exist = false)
    @Schema(description="分析图层名称")
    private String fxLayerName;

    @TableField(exist = false)
    @Schema(description="被分析图层名称")
    private String bfxLayerName;

    @TableField(exist = false)
    @Schema(description="字段明细配置")
    private List<AnalysisPlanFieldDetail> analysisPlanFieldDetailDtos;

    @TableField(exist = false)
    @Schema(description="字段统计配置")
    private List<AnalysisPlanFieldStatistics> analysisPlanFieldStatisticsDtos;

    @TableField(exist = false)
    @Schema(description="分析项列表")
    private List<AnalysisOrdinaryItem> analysisOrdinaryItemList;


    private static final long serialVersionUID = 1L;
}