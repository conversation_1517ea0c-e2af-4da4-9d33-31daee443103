package com.bjcj.model.po.naturalresources.function;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/11/30 15:00 周四
 */
@Schema(description="功能服务信息")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "function_serve")
public class FunctionServe implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="", hidden = true)
    private Long id;

    @NotBlank
    @TableField(value = "name")
    @Schema(description="服务注册名")
    private String name;

    @NotBlank
    @TableField(value = "sname")
    @Schema(description="服务显示名")
    private String sname;

    @NotBlank
    @TableField(value = "fw_catalogue")
    @Schema(description="服务目录")
    private String fwCatalogue;

    @TableField(value = "auth")
    @Schema(description="权限")
    private int auth;

    @NotBlank
    @TableField(value = "reg_address")
    @Schema(description="注册地址")
    private String regAddress;

    @NotBlank
    @TableField(value = "fw_itype")
    @Schema(description="服务类型")
    private String fwItype;

    @TableField(value = "fw_tag")
    @Schema(description="服务标签")
    private String fwTag;

    @TableField(value = "imgurl")
    @Schema(description="缩略图")
    private String imgurl;

    @TableField(value = "description")
    @Schema(description="描述")
    private String description;

    @TableField(value = "reg_name")
    @Schema(description="注册人")
    private String regName;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description="注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "status")
    @Schema(description="运行状态")
    private Boolean status;

    @TableField(value = "del")
    @Schema(description="删除状态（0未删除，1已删除）")
    private int del;

    @TableField(exist = false)
    @Schema(description="被授予的角色")
    private String authFromRoleName;

}
