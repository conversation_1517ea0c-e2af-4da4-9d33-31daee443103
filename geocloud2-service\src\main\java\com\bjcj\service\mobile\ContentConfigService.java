package com.bjcj.service.mobile;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.database.config.DatasourceMapConfig;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.mobile.ContentConfigMapper;
import com.bjcj.mapper.mobile.ContentTbFieldMapper;
import com.bjcj.mapper.mobile.TableMapper;
import com.bjcj.model.dto.mobile.ContentConfigDto;
import com.bjcj.model.po.mobile.ContentConfig;
import com.bjcj.model.po.mobile.ContentTbField;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class ContentConfigService extends ServiceImpl<ContentConfigMapper, ContentConfig> {

    // 数据库数据源映射
    @Resource
    private DatasourceMapConfig mapConfig;

    @Resource
    private TableMapper tableMapper;

    @Resource
    private ContentConfigMapper contentConfigMapper;

    @Resource
    private ContentTbFieldMapper contentTbFieldMapper;


    /**
     * @description 根据数据源获取表名
     * <AUTHOR>
     * @create 2025/7/2
     **/
    @DS("#ds")
    public List<String> findTableNameByKey(String ds) {
        Map<String, String> maps = mapConfig.getDatasource().get(ds);
        String url = maps.get("url");
        if (maps.get("driver-class-name").equals("oracle.jdbc.driver.OracleDriver")) {
            return tableMapper.getDataTableNameListOra();
        } else if (maps.get("driver-class-name").equals("com.mysql.cj.jdbc.Driver")) {
            String[] urlArr = url.split("\\?");
            String[] pathArr = urlArr[0].split("/");
            return tableMapper.getDataTableNameListMysql(pathArr[3]);
        } else if (maps.get("driver-class-name").equals("org.postgresql.Driver")) {
            return tableMapper.getDataTableNameListPgsql("public");
        }
        return null;
    }

    /**
     * @description 根据数据源和表名获取表中所有字段名
     * <AUTHOR>
     * @create 2025/7/2
     **/
    @DS("#ds")
    public List<String> findTableColumnByTable(String ds, String tableName) {
        Map<String, String> maps = mapConfig.getDatasource().get(ds);
        if (maps.get("driver-class-name").equals("oracle.jdbc.driver.OracleDriver")) {
            return tableMapper.getDataTableColumnListOra(tableName);
        } else if (maps.get("driver-class-name").equals("com.mysql.cj.jdbc.Driver")) {
            return tableMapper.getDataTableColumnListMysql(tableName);
        } else if (maps.get("driver-class-name").equals("org.postgresql.Driver")) {
            return tableMapper.getDataTableColumnListPgsql(tableName);
        }
        return null;
    }

    
    /**
     * @description 分页查询配置数据和列表字段
     * <AUTHOR>
     * @create 2025/7/2
     **/
    public Object pageData(Integer page, Integer pageSize, String contentTbId) {
        // 根据contentId判断 配置数据查询还是列表字段查询
        if (StringUtils.isNotEmpty(contentTbId)) {
            Page<ContentTbField> pager = new Page<>(page, pageSize);
            LambdaQueryWrapper<ContentTbField> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ContentTbField::getConfigid, contentTbId);
            Page<ContentTbField> pages = contentTbFieldMapper.selectPage(pager, wrapper);
            return JsonResult.success(pages);
        } else {
            Page<ContentConfig> pager = new Page<>(page, pageSize);
            LambdaQueryWrapper<ContentConfig> wrapper = new LambdaQueryWrapper<>();
            Page<ContentConfig> pages = contentConfigMapper.selectPage(pager, wrapper);
            return JsonResult.success(pages);
        }
    }

    /**
     * @description 获取数据源
     * <AUTHOR>
     * @create 2025/7/1
     **/
    public JsonResult getDataSource() {
        Map<String, Map<String, String>> dataSourceMap = mapConfig.getDatasource();
        return JsonResult.success(new ArrayList<>(dataSourceMap.keySet()));
    }


    /**
     * @description 保存配置数据
     * <AUTHOR>
     * @create 2025/7/1
     **/
    public JsonResult saveContentConfig(ContentConfigDto contentConfigDto) {
        ContentConfig contentConfig = new ContentConfig();
        BeanUtil.copyProperties(contentConfigDto, contentConfig);
        return JsonResult.success(this.save(contentConfig));
    }

    /**
     * @description 修改配置数据
     * <AUTHOR>
     * @create 2025/7/1
     **/
    public JsonResult updateContentConfig(ContentConfigDto contentConfigDto) {
        ContentConfig contentConfig = new ContentConfig();
        BeanUtil.copyProperties(contentConfigDto, contentConfig);
        return JsonResult.success(this.updateById(contentConfig));
    }
    
    /**
     * @description 按照id删除模块配置
     * <AUTHOR>
     * @create 2025/7/1
     **/
    public JsonResult removeContentConfig(String id) {
        return JsonResult.success(this.removeById(id));
    }

    /**
     * @description 批量删除模块配置
     * <AUTHOR>
     * @create 2025/7/1
     **/
    public JsonResult removeContentConfigByIds(String deleteids) {
        if(deleteids.contains(",")){
            //ids转list
            List<String> ids = List.of(deleteids.split(","));
            return JsonResult.success(this.removeByIds(ids));
        }else{
            return JsonResult.error("ids格式错误,必须选择一条以上的数据");
        }
    }

    
    /**
     * @description 获取dataSource的所有表名
     * <AUTHOR>
     * @create 2025/7/2
     **/
    public JsonResult getTableName(String dbName) {
        return JsonResult.success(findTableNameByKey(dbName));
    }

    /**
     * @description 获取table的所有列名
     * <AUTHOR>
     * @create 2025/7/2
     **/
    public JsonResult getTableColumn(String ds, String tableName) {
        return JsonResult.success(findTableColumnByTable(ds, tableName));
    }


    /**
     * @description 获取所有配置数据
     * <AUTHOR>
     * @create 2025/7/2
     **/
    public JsonResult getContentConfigList() {
        return JsonResult.success(this.list());
    }
}
