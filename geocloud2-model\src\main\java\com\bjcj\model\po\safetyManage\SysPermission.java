package com.bjcj.model.po.safetyManage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2023/12/5  9:36
*/
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_permission")
public class SysPermission implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    @NotNull(message = "不能为null")
    private Long id;

    /**
     * 权限代码
     */
    @TableField(value = "code")
    @Schema(description="权限代码")
    @Size(max = 255,message = "权限代码最大长度要小于 255")
    private String code;

    /**
     * 权限名称
     */
    @TableField(value = "\"name\"")
    @Schema(description="权限名称")
    @Size(max = 255,message = "权限名称最大长度要小于 255")
    private String name;

    private static final long serialVersionUID = 1L;
}