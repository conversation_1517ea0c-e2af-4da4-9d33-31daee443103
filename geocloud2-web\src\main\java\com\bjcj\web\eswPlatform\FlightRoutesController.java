package com.bjcj.web.eswPlatform;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.eswPlatform.FlightRoutes;
import com.bjcj.service.eswPlatform.FlightRoutesService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* 动画导航表(public.flight_routes)表控制层
* <AUTHOR>
*/
@RestController
@RequestMapping("/flight_routes")
@Validated
@Tag(name = "动画导航")
public class FlightRoutesController {
    /**
    * 服务对象
    */
    @Resource
    private FlightRoutesService flightRoutesService;

    @SaCheckPermission("sys:read")
    @GetMapping("/list")
    @Operation(summary = "列表", description = "列表")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "special_plan_id", description = "专题id", required = true)
    })
    public JsonResult<List<FlightRoutes>> list(@RequestParam(value = "special_plan_id",required = true) Long special_plan_id) {
        return JsonResult.success(this.flightRoutesService.list(new LambdaQueryWrapper<FlightRoutes>().eq(FlightRoutes::getSpecialPlanId, special_plan_id).orderByDesc(FlightRoutes::getCreateTime)));
    }

    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑", description = "新增/编辑")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "addOrEditDhdh")
    public JsonResult addOrEdit(@Validated @RequestBody FlightRoutes po){
        return this.flightRoutesService.saveOrUpdate(po) ? JsonResult.success() : JsonResult.error();
    }

    @OperaLog(operaModule = "删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del/{id}")
    @Operation(summary = "删除", description = "根据id删除")
    @ApiOperationSupport(order = 3)
    @RequestLock(prefix = "delDhdh")
    public JsonResult del(@PathVariable("id") String id){
        return this.flightRoutesService.removeById(id) ? JsonResult.success() : JsonResult.error();
    }

    @OperaLog(operaModule = "批量删除",operaType = OperaLogConstant.DELETE,operaDesc = "批量删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delBatch")
    @Operation(summary = "批量删除", description = "根据id批量删除")
    @ApiOperationSupport(order = 4)
    @Parameters({
            @Parameter(name = "ids", description = "ids逗号拼接", required = true)
    })
    @RequestLock(prefix = "delBatchDhdh")
    public JsonResult delBatch(@RequestParam("ids") String ids){
        if(ids.contains(",")){
            //ids转list
            List<String> idss = List.of(ids.split(","));
            return JsonResult.success(this.flightRoutesService.removeByIds(idss));
        }else{
            return JsonResult.error("ids格式错误,必须选择一条以上的数据");
        }
    }

}
