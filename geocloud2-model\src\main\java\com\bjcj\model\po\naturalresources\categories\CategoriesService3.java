package com.bjcj.model.po.naturalresources.categories;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/11/28 11:39 周二
 */
@Schema(description="数据服务")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "categories_service")
public class CategoriesService3 implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="", hidden = true)
    private Long id;

    @TableField(value = "parent_id")
    @Schema(description="父级id")
    private Long parentId;

    @NotBlank
    @TableField(value = "name")
    @Schema(description="注册名称")
    private String name;

    @NotBlank
    @TableField(value = "fwbs")
    @Schema(description="服务标识")
    private String fwbs;

    @NotBlank
    @TableField(value = "sname")
    @Schema(description="显示名称")
    private String sname;

    @NotBlank
    @TableField(value = "address")
    @Schema(description="注册地址")
    private String address;

    @NotBlank
    @TableField(value = "fwitype")
    @Schema(description="服务类型")
    private String fwitype;

    @TableField(value = "fwgroup")
    @Schema(description="服务分组")
    private String fwgroup;

    @NotNull
    @TableField(value = "show")
    @Schema(description="是否显示0不1是")
    private int show;

    @TableField(value = "status")
    @Schema(description="状态")
    private Boolean status;

    @TableField(value = "fwsort")
    @Schema(description="服务排序")
    private int fwsort;

    @TableField(value = "tags")
    @Schema(description="标签")
    private String tags;

    @TableField(value = "imgurl")
    @Schema(description="缩略图")
    private String imgurl;

    @TableField(value = "remark")
    @Schema(description="描述")
    private String remark;

    @TableField(value = "fwcs")
    @Schema(description="服务参数")
    private String fwcs;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "del")
    @Schema(description="删除状态（0未删除，1已删除）")
    private int del;

    @TableField(exist = false)
    @Schema(description="1私有2公开3安全")
    private int authItype;

    @TableField(exist = false)
    @Schema(description="被授予的角色")
    private String authFromRoleName;


    @TableField(value = "views_count")
    @Schema(description="浏览次数")
    private int viewsCount;

    @TableField(exist = false)
    @Schema(description="0待审核,1通过,2拒绝")
    private String reviewStatus;

    @TableField(exist = false)
    @Schema(description="是否收藏")
    private Boolean isCollect;


    @TableField(exist = false)
    @Schema(description="是否有权限(0.无权限,1.已有权限,2.无需申请,3.申请待审批)")
    private String hasPermission;


    @TableField(value = "use_count")
    @Schema(description="使用次数")
    private int useCount;


    @TableField(value = "last_use_time", fill = FieldFill.UPDATE)
    @Schema(description="最后使用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUseTime;

    @TableField(value = "group_id")
    @Schema(description="服务分组id")
    private Long groupId;
}
