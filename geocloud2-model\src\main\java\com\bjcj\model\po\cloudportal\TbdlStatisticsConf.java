package com.bjcj.model.po.cloudportal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/7/16  9:51
*/
/**
    * 图斑地类统计配置
    */
@Schema(description="图斑地类统计配置")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "tbdl_statistics_conf")
public class TbdlStatisticsConf implements Serializable {
    @TableField(value = "workspaceid")
    @Schema(description="")
    @Size(max = 50,message = "max length should less than 50")
    @NotBlank(message = "is not blank")
    private String workspaceid;

    @TableField(value = "table_name")
    @Schema(description="")
    @Size(max = 100,message = "max length should less than 100")
    @NotBlank(message = "is not blank")
    private String tableName;

    @TableField(value = "xzq_code")
    @Schema(description="")
    @Size(max = 100,message = "max length should less than 100")
    private String xzqCode;

    @TableField(value = "special_plan_id")
    @Schema(description="")
    @NotNull(message = "is not blank")
    private Long specialPlanId;

    @TableField(value = "resource_service_id")
    @Schema(description="")
    @Size(max = 100,message = "max length should less than 100")
    @NotBlank(message = "is not blank")
    private String resourceServiceId;

    //resource_service_name
    @TableField(value = "resource_service_name")
    @Schema(description="")
    @Size(max = 100,message = "max length should less than 100")
    @NotBlank(message = "is not blank")
    private String resourceServiceName;

    // xzq_id
    @TableField(value = "xzq_id")
    @Schema(description="")
    private Long xzqId;

    private static final long serialVersionUID = 1L;
}