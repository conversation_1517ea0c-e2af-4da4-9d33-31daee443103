package com.bjcj.model.po.eswPlatform;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024-8-21  10:17
*/
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "scheme_models")
public class SchemeModels implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Size(max = 40,message = "最大长度要小于 40")
    private String id;

    /**
     * 名称
     */
    @TableField(value = "name")
    @Schema(description="名称")
    @Size(max = 255,message = "名称最大长度要小于 255")
    private String name;

    /**
     * 方案Id
     */
    @TableField(value = "schemeid")
    @Schema(description="方案Id")
    @Size(max = 40,message = "方案Id最大长度要小于 40")
    @NotBlank(message = "方案Id不能为空")
    private String schemeid;

    /**
     * 坐标信息
     */
    @TableField(value = "content")
    @Schema(description="坐标信息")
    private String content;

    /**
     * 模型文件Url
     */
    @TableField(value = "modelurl")
    @Schema(description="模型文件Url")
    private String modelurl;

    /**
     * 类型
     */
    @TableField(value = "type")
    @Schema(description="类型")
    @Size(max = 255,message = "类型最大长度要小于 255")
    private String type;

    private static final long serialVersionUID = 1L;
}