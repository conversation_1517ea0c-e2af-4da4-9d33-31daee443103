package com.bjcj.model.po.naturalresources.categories;

import com.baomidou.mybatisplus.annotation.*;
import com.bjcj.common.utils.mapperType.JsonbTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/4/25  9:19
*/
/**
    * 资源项
    */
@Schema(description="地图服务")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "resource_services")
public class ResourceServices implements Serializable {
    /**
     * 标识
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(description="标识")
    @Size(max = 36,message = "标识max length should less than 36")
    private String id;

    /**
     * 名称
     */
    @TableField(value = "name")
    @Schema(description="名称")
    @Size(max = 60,message = "名称max length should less than 60")
    @NotBlank(message = "名称is not blank")
    private String name;

    /**
     * 别名
     */
    @TableField(value = "displayname")
    @Schema(description="别名")
    @Size(max = 60,message = "别名max length should less than 60")
    private String displayname;

    /**
     * 描述
     */
    @TableField(value = "description")
    @Schema(description="描述")
    @Size(max = 2000,message = "描述max length should less than 2000")
    private String description;

    /**
     * 原始地址
     */
    @TableField(value = "url")
    @Schema(description="原始地址")
    @Size(max = 500,message = "原始地址max length should less than 500")
    @NotBlank(message = "原始地址is not blank")
    private String url;

    /**
     * 权限类型
     */
    @TableField(value = "authoritytype")
    @Schema(description="权限类型")
    @NotNull(message = "权限类型is not null")
    private Short authoritytype;

    /**
     * 状态，0表示待审核，1表示运行中，2表示未通过，3维护中，4处理中，5表示处理失败
     */
    @TableField(value = "status")
    @Schema(description="状态，0表示待审核，1表示运行中，2表示未通过，3维护中，4处理中，5表示处理失败")
    @NotNull(message = "状态，0表示待审核，1表示运行中，2表示未通过，3维护中，4处理中，5表示处理失败is not null")
    private Short status;

    /**
     * 转发地址
     */
    @TableField(value = "transferurl")
    @Schema(description="转发地址")
    @Size(max = 500,message = "转发地址max length should less than 500")
    private String transferurl;

    /**
     * 注册人登录名
     */
    @TableField(value = "registerman")
    @Schema(description="注册人登录名")
    @Size(max = 100,message = "注册人登录名max length should less than 100")
    private String registerman;

    /**
     * 注册日期
     */
    @TableField(value = "registerdate", fill = FieldFill.INSERT)
    @Schema(description="注册日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registerdate;

    /**
     * 修改人登录名
     */
    @TableField(value = "repairman")
    @Schema(description="修改人登录名")
    @Size(max = 100,message = "修改人登录名max length should less than 100")
    private String repairman;

    /**
     * 修改日期
     */
    @TableField(value = "repairdate", fill = FieldFill.UPDATE)
    @Schema(description="修改日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime repairdate;

    /**
     * 版本号
     */
    @TableField(value = "version")
    @Schema(description="版本号")
    private Integer version;

    /**
     * 资源目录id，resource_catalogs的id
     */
    @TableField(value = "resourcecatalogid")
    @Schema(description="资源目录id，resource_catalogs的id")
    @Size(max = 36,message = "资源目录id，resource_catalogs的idmax length should less than 36")
    private String resourcecatalogid;

    /**
     * 服务引擎id
     */
    @TableField(value = "serviceclusterid")
    @Schema(description="服务引擎id")
    @Size(max = 36,message = "服务引擎idmax length should less than 36")
    private String serviceclusterid;

    /**
     * 缩略图url
     */
    @TableField(value = "thumbnailurl")
    @Schema(description="缩略图url")
    @Size(max = 500,message = "缩略图urlmax length should less than 500")
    private String thumbnailurl;

    /**
     * 注册类型 1 系统注册,2 门户注册
     */
    @TableField(value = "registertype")
    @Schema(description="注册类型 1 系统注册,2 门户注册")
    private Short registertype;

    /**
     * 服务缓存类型 1 Dynamic,2 Tiled
     */
    @TableField(value = "cachetype")
    @Schema(description="服务缓存类型 1 Dynamic,2 Tiled")
    private Short cachetype;

    /**
     * 资源类型（小类）如应用服务和数据服务对应的小类有MapServer、WMSServer等；数据产品对应的小类有服务、应用、图件、文档等
     */
    @TableField(value = "resourcetype")
    @Schema(description="资源类型（小类）如应用服务和数据服务对应的小类有MapServer、WMSServer等；数据产品对应的小类有服务、应用、图件、文档等")
    @Size(max = 60,message = "资源类型（小类）如应用服务和数据服务对应的小类有MapServer、WMSServer等；数据产品对应的小类有服务、应用、图件、文档等max length should less than 60")
    @NotBlank(message = "资源类型（小类）如应用服务和数据服务对应的小类有MapServer、WMSServer等；数据产品对应的小类有服务、应用、图件、文档等is not blank")
    private String resourcetype;

    /**
     * 请求类型 1 不需要token,2 需要token
     */
    @TableField(value = "requesttype")
    @Schema(description="请求类型 1 不需要token,2 需要token")
    private Short requesttype;

    /**
     * 评价等级 1-5级
     */
    @TableField(value = "evaluationlevel")
    @Schema(description="评价等级 1-5级")
    private BigDecimal evaluationlevel;

    /**
     * 服务访问量
     */
    @TableField(value = "accesscount")
    @Schema(description="服务访问量")
    private BigDecimal accesscount;

    /**
     * 资源类别。1应用服务；2应用；3文档；4数据；5数据产品；6数据服务；7数据库；8服务器
     */
    @TableField(value = "resourcecategory")
    @Schema(description="资源类别。1应用服务；2应用；3文档；4数据；5数据产品；6数据服务；7数据库；8服务器")
    @NotNull(message = "资源类别。1应用服务；2应用；3文档；4数据；5数据产品；6数据服务；7数据库；8服务器is not null")
    private Short resourcecategory;

    /**
     * 注册人所在的组织机构标识
     */
    @TableField(value = "organizationid")
    @Schema(description="注册人所在的组织机构标识")
    @Size(max = 40,message = "注册人所在的组织机构标识max length should less than 40")
    private String organizationid;

    /**
     * 数据分组标识
     */
    @TableField(value = "dataitemid")
    @Schema(description="数据分组标识")
    @Size(max = 36,message = "数据分组标识max length should less than 36")
    private String dataitemid;

    /**
     * 浏览资源时候默认浏览的对象
     */
    @TableField(value = "isdefaultbrowser")
    @Schema(description="浏览资源时候默认浏览的对象")
    private Short isdefaultbrowser;

    /**
     * 其他参数
     */
    @TableField(value = "params")
    @Schema(description="其他参数")
    @Size(max = 4000,message = "其他参数max length should less than 4000")
    private String params;

    /**
     * 切片图层最大比例尺，这个值一般用作静态和动态图层动态切换来用，所以这个比例尺值一般需要比实际最大比例尺小
     */
    @TableField(value = "titlemaxscale")
    @Schema(description="切片图层最大比例尺，这个值一般用作静态和动态图层动态切换来用，所以这个比例尺值一般需要比实际最大比例尺小")
    private BigDecimal titlemaxscale;

    /**
     * 是否按行政区过滤 0 否,1 是
     */
    @TableField(value = "isfilter")
    @Schema(description="是否按行政区过滤 0 否,1 是")
    private Boolean isfilter;

    /**
     * 显示顺序
     */
    @TableField(value = "displayorder")
    @Schema(description="显示顺序")
    private Short displayorder;

    /**
     * 元数据字段
     */
    @TableField(value = "metadata", typeHandler = JsonbTypeHandler.class)
    @Schema(description="元数据字段")
    private Object metadata;

    /**
     * 是否显示
     */
    @TableField(value = "isvisiable")
    @Schema(description="是否显示")
    private Boolean isvisiable;

    /**
     * 服务组标识
     */
    @TableField(value = "servicegroupid")
    @Schema(description="服务组标识")
    @Size(max = 36,message = "服务组标识max length should less than 36")
    private String servicegroupid;

    /**
     * 行政区代码
     */
    @TableField(value = "districtcode")
    @Schema(description="行政区代码")
    @Size(max = 20,message = "行政区代码max length should less than 20")
    private String districtcode;

    /**
     * 行政区名称
     */
    @TableField(value = "districtname")
    @Schema(description="行政区名称")
    @Size(max = 100,message = "行政区名称max length should less than 100")
    private String districtname;

    /**
     * 服务标识
     */
    @TableField(value = "servicetag")
    @Schema(description="服务标识")
    @Size(max = 60,message = "服务标识max length should less than 60")
    private String servicetag;

    @TableField(exist = false)
    private String groupName;

    @TableField(exist = false)
    @Schema(description="服务上的标签信息")
    private String resourceTags;

    @TableField(exist = false)
    @Schema(description="被授予的角色")
    private String authFromRoleName;


    @TableField(value = "last_use_time")
    @Schema(description="最后使用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUseTime;

    @TableField(value = "map_type")
    @Schema(description="门户自用业务类型")
    private String mapType;


    private static final long serialVersionUID = 1L;
}