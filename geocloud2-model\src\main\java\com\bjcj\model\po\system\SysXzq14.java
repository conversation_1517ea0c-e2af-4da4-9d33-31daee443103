package com.bjcj.model.po.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bjcj.common.utils.support.Create;
import com.bjcj.common.utils.support.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 山西省行政区代码表
 * <AUTHOR>
 * @date 2023/12/8 17:35 周五
 */
@Schema(description="山西省行政区代码表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_xzq14")
public class SysXzq14 {

    @NotNull(message = "id不允许为空", groups = Update.class)
    @Null(message = "id必须为空", groups = Create.class)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    @NotBlank(message = "code不允许为空")
    @TableField(value = "code")
    @Schema(description="")
    private String code;

    @NotBlank(message = "name不允许为空")
    @TableField(value = "\"name\"")
    @Schema(description="")
    private String name;

    @TableField(value = "rural_code")
    @Schema(description="城乡分类代码")
    private String ruralCode;

    @NotNull(message = "level不允许为空")
    @Positive(message = "level必须大于0")
    @TableField(value = "\"level\"")
    @Schema(description="级别")
    private Short level;

    @NotNull(message = "pId不允许为空")
    @TableField(value = "p_id")
    @Schema(description="父id")
    private Long pId;

    @NotNull(message = "sort不允许为空")
    @Positive(message = "sort必须大于0")
    @TableField(value = "sort")
    @Schema(description="排序")
    private Short sort;
}