package com.bjcj.service.naturalresources.fileResManage;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.compress.CompressUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.aspose.cad.CodePages;
import com.aspose.cad.Image;
import com.aspose.cad.LoadOptions;
import com.aspose.cad.imageoptions.DxfOptions;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.common.utils.FileUtil;
import com.bjcj.common.utils.ShapeFileUtil;
import com.bjcj.common.utils.Txt2GeoJsonUtil;
import com.bjcj.common.utils.domain.FileInfo;
import com.bjcj.common.utils.enums.FileTypeEnum;
import com.bjcj.common.utils.properties.GeoCloud2Properties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.gdal.gdal.gdal;
import org.gdal.ogr.*;
import org.gdal.osr.SpatialReference;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 *@Author：qinyi
 *@Date：2024/1/16  11:04
*/
@Service
@Slf4j
public class FileInfosService {

    @Resource
    GeoCloud2Properties geoCloud2Properties;


    public JsonResult shpZip2GeoJson(MultipartFile file) throws Exception {

        String filename = file.getOriginalFilename();
        if (StrUtil.isBlank(filename)) {
            return JsonResult.error("文件名不正确");
        }
        String suffix = filename.substring(filename.lastIndexOf(".") + 1);
        if (!StrUtil.equalsIgnoreCase(suffix, "zip")) {
            return JsonResult.error("文件格式不正确");
        }

        StopWatch stopWatch = new StopWatch("shp压缩包解压转geojson");

        // 先上传
        stopWatch.start("上传文件");
        FileInfo fileInfo = FileUtil.uploadFile(file, FileTypeEnum.PACKAGE);
        stopWatch.stop();

        // 解压路径
        stopWatch.start("解压文件");
        String extractor = fileInfo.getServerUrl().substring(0, fileInfo.getServerUrl().lastIndexOf("."));

        // 解压
        CompressUtil.createExtractor(CharsetUtil.CHARSET_UTF_8,
                        suffix,
                        new File(fileInfo.getServerUrl()))
                .extract(new File(extractor));
        stopWatch.stop();

        // 解析成geojson
        stopWatch.start("解析成geojson");
        JSONObject jsonObject = ShapeFileUtil.shpZipToGeoJson(new File(extractor));
        stopWatch.stop();
        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
        return JsonResult.success(jsonObject);
    }


    public JsonResult txt2GeoJsonByGuo(MultipartFile file) throws IOException {

        // 获取后缀名
        String fileName = file.getOriginalFilename();
        String suffixName = fileName.substring(fileName.lastIndexOf("."));
        if (!StrUtil.equals(suffixName, ".txt")) {
            return JsonResult.error("文件格式错误");
        }

        // 文件上传
        FileInfo fileInfo = FileUtil.uploadFile(file, FileTypeEnum.TXT);

        return Txt2GeoJsonUtil.txt2GeoJson(fileInfo.getServerUrl(), geoCloud2Properties.getZone());
    }


    public JsonResult cad2GeoJsonByGuo(MultipartFile file) throws IOException {

        // 获取文件后缀名
        String suffixName = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        String[] suffixNames = {"dwg", "dxf"};
        if (!ArrayUtil.contains(suffixNames, suffixName.toLowerCase())){
            return JsonResult.error("文件格式不正确");
        }

        // 文件名
        String fileName = file.getOriginalFilename().substring(0, file.getOriginalFilename().lastIndexOf("."));

        // 文件上传
        FileInfo fileInfo = FileUtil.uploadFile(file, FileTypeEnum.CAD);

        // 1,dwg转dxf
        String res;
        String dwgPath = fileInfo.getServerUrl(), dxfPath = fileInfo.getServerUrl();
        if (suffixName.toLowerCase().equals("dwg")) {
            dxfPath = StrUtil.replaceIgnoreCase(dwgPath, ".dwg", ".dxf");
            res = dwg2dxf(dwgPath, dxfPath);
            if (res != null) {
                return JsonResult.error(res);
            }
        }

        // 2,dxf转换成json
        String jsonPath = StrUtil.replaceIgnoreCase(dxfPath, ".dxf", "转换前.geojson");
        res = dxfToJson(dxfPath, jsonPath);
        if (res != null) {
            return JsonResult.error(res);
        }

        // 3,坐标转换
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String newJsonPath = StrUtil.replaceIgnoreCase(jsonPath, "转换前.geojson", "") + ".geojson";
        res = geoJsonCov(jsonPath, newJsonPath, fileName);
        if (res != null) {
            return JsonResult.error(res);
        }
        stopWatch.stop();
        log.info("dwg坐标转换耗时：{}", stopWatch.getTotalTimeMillis());

        // 解除文件占用
        // gdal.GDALDestroyDriverManager();

        // 读取newJsonPath
        String newJsonContent = cn.hutool.core.io.FileUtil.readString(newJsonPath, "UTF-8");
        return JsonResult.success(JSON.parseObject(newJsonContent));
    }

    public String dwg2dxf(String dwgPath, String outFile) {
        File dwgFile = new File(dwgPath);
        if (dwgFile.exists()) {
            if (!StrUtil.endWithIgnoreCase(dwgFile.getName(), ".dwg")) {
                return "文件格式错误";
            }
            LoadOptions loadOptions = new LoadOptions();
            loadOptions.setSpecifiedEncoding(CodePages.SimpChinese);
            Image cadImage = Image.load(dwgFile.getAbsolutePath(), loadOptions);
            DxfOptions dxfOptions = new DxfOptions();
            cadImage.save(outFile, dxfOptions);
            cadImage.close();
            return null;
        } else {
            return "dwg文件不存在," + dwgPath;
        }
    }

    public String dxfToJson(String dxfPath, String geoJsonPath) {
        ogr.RegisterAll();
        // 支持中文路径
        gdal.SetConfigOption("GDAL_FILENAME_IS_UTF8", "YES");
        gdal.SetConfigOption("DXF_ENCODING", "UTF-8");

        // 读取文件转化为DataSource
        DataSource ds = ogr.Open(dxfPath, 0);
        if (ds == null) {
            return "打开文件失败," + dxfPath;
        }
        // 获取geojson
        Driver geojsonDriver = ogr.GetDriverByName("GeoJSON");
        DataSource geojsonDataSource = geojsonDriver.CopyDataSource(ds, geoJsonPath);
        geojsonDataSource.delete();
        ds.delete();
        return null;
    }


    /**
     * <h2>坐标转换</h2>
     */
    public String geoJsonCov(String geoJsonPath, String outputJson, String dwgName) {
        ogr.RegisterAll();
        gdal.SetConfigOption("GDAL_FILENAME_IS_UTF8", "YES");
        gdal.SetConfigOption("DXF_ENCODING", "UTF-8");
        DataSource ds2 = ogr.Open(geoJsonPath, 0);
        log.info("----------坐标转换开始-----------");
        // 读取第一个图层，这个图层不是cad中的layer
        Layer oLayer = ds2.GetLayerByIndex(0);
        if (oLayer == null) {
            log.error("从dwg的geojson中获取layer 获取失败:{}", geoJsonPath);
            ds2.delete();
            return "从dwg的geojson中获取layer 获取失败";
        }

        // 新创建一个图层geojson
        Driver geojsonDriver = ogr.GetDriverByName("GeoJSON");
        SpatialReference epsg2000 = new SpatialReference("");
        epsg2000.ImportFromEPSG(4490);
        DataSource dataSource = geojsonDriver.CreateDataSource(outputJson, null);
        Layer geojsonLayer = dataSource.CreateLayer(dwgName, null);

        Feature feature;
        String geometryStr;
        String newGeometryStr;
        while ((feature = oLayer.GetNextFeature()) != null) {
            // 获取空间属性
            Geometry geometry = feature.GetGeometryRef();
            // 解决自相交问题
            // geometry = geometry.Buffer(0.0);
            // System.out.println(geometry.ExportToJson());
            geometryStr = geometry.ExportToJson();
            if (geometryStr == null) {
                continue;
            }
            newGeometryStr = String.valueOf(ShapeFileUtil.paseCoordinates(geometryStr));
            Geometry newGeometry = Geometry.CreateFromJson(newGeometryStr);
            // 传给新建的矢量图层
            Feature dstFeature = feature.Clone();
            dstFeature.SetGeometry(newGeometry);
            geojsonLayer.CreateFeature(dstFeature);
        }
        geojsonDriver.delete();
        ds2.delete();
        dataSource.delete();
        return null;
    }


    public JsonResult shp2GeoJson(MultipartFile file) throws IOException {

        // 文件上传
        FileInfo fileInfo = FileUtil.uploadFile(file, FileTypeEnum.FILE);
        // 解析成geojson
        JSONObject geojson = ShapeFileUtil.shpToGeoJson(fileInfo.getServerUrl());
        return JsonResult.success(geojson);
    }

}
