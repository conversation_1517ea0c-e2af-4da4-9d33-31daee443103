<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.dict.SysDictDataMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.dict.SysDictData">
    <!--@mbg.generated-->
    <!--@Table public.sys_dict_data-->
    <id column="dict_code" jdbcType="BIGINT" property="dictCode" />
    <result column="dict_sort" jdbcType="INTEGER" property="dictSort" />
    <result column="dict_label" jdbcType="VARCHAR" property="dictLabel" />
    <result column="dict_value" jdbcType="VARCHAR" property="dictValue" />
    <result column="dict_type" jdbcType="VARCHAR" property="dictType" />
    <result column="css_class" jdbcType="VARCHAR" property="cssClass" />
    <result column="list_class" jdbcType="VARCHAR" property="listClass" />
    <result column="is_default" jdbcType="CHAR" property="isDefault" />
    <result column="status" jdbcType="CHAR" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operater" jdbcType="VARCHAR" property="operater" />
    <result column="pid" jdbcType="BIGINT" property="pid" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, 
    "status", create_time, update_time, operater, pid, group_id, remark
  </sql>

  <select id="selectMaxSortValue" resultType="int">
    select max(dict_sort)+1 from sys_dict_data where dict_type=#{dictType} and pid=#{pid}
    </select>
</mapper>