package com.bjcj.mapper.safetyManage;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.safetyManage.SafetyUserRole;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 *@Author：qinyi
 *@Date：2023/11/29  11:37
*/
public interface SafetyUserRoleMapper extends BaseMapper<SafetyUserRole> {
    List<SafetyUserRole> selectListByIdsLeftJoinUser(@Param("current") Long current,@Param("size") Long size,@Param("roleId") Long roleId);

    List<SafetyUserRole> queryExistingUserRoles(Long userId);

    void deleteByUserId(Long userId);

    Long selectListByIdsLeftJoinUserCount(Long roleId);

    List<Long> selectUserByRoleids(@Param("roleIds") List<Long> roleIds);

    List<Long> queryUserRoleId(@Param("userId") Long userId);
}