package com.bjcj.model.vo.naturalresources.categories;

import com.bjcj.model.po.naturalresources.categories.ResourceServices;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/4/24  14:21
*/
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResourceDataitemsVo2 implements Serializable {
    /**
     * 标识
     */
    @Schema(description="标识")
    @Size(max = 36,message = "标识max length should less than 36")
    private String id;

    /**
     * 名称
     */
    @Schema(description="名称")
    @Size(max = 60,message = "名称max length should less than 60")
    private String name;

    /**
     * 显示名称
     */
    @Schema(description="显示名称")
    @Size(max = 60,message = "显示名称max length should less than 60")
    private String displayname;

    /**
     * 描述
     */
    @Schema(description="描述")
    @Size(max = 2000,message = "描述max length should less than 2000")
    private String description;

    /**
     * 年份
     */
    @Schema(description="年份")
    private Short year;

    /**
     * 比例尺分母
     */
    @Schema(description="比例尺分母")
    private Integer scale;

    /**
     * 行政区编码
     */
    @Schema(description="行政区编码")
    @Size(max = 20,message = "行政区编码max length should less than 20")
    private String districtcode;

    /**
     * 行政区名称
     */
    @Schema(description="行政区名称")
    @Size(max = 100,message = "行政区名称max length should less than 100")
    private String districtname;

    /**
     * 访问量
     */
    @Schema(description="访问量")
    private BigDecimal accesscount;

    /**
     * 组织机构标识
     */
    @Schema(description="组织机构标识")
    @Size(max = 40,message = "组织机构标识max length should less than 40")
    private String organizationid;

    /**
     * 资源目录标识
     */
    @Schema(description="资源目录标识")
    @Size(max = 36,message = "资源目录标识max length should less than 36")
    private String resourcecatalogid;

    /**
     * 缩略图url
     */
    @Schema(description="缩略图url")
    @Size(max = 500,message = "缩略图urlmax length should less than 500")
    private String thumbnailurl;

    /**
     * 状态，0表示待审核，1表示运行中，2表示未通过，3表示维护中，4表示处理中，5表示处理失败
     */
    @Schema(description="状态，0表示待审核，1表示运行中，2表示未通过，3表示维护中，4表示处理中，5表示处理失败")
    private Short status;

    /**
     * 数据时间
     */
    @Schema(description="数据时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime datatime;

    /**
     * 数据时间的精度，0表示年，1表示月，2表示日，3表示小时，4表示分，5表示秒
     */
    @Schema(description="数据时间的精度，0表示年，1表示月，2表示日，3表示小时，4表示分，5表示秒")
    private Short datatimeprecision;

    /**
     * 数据秘密等级
     */
    @Schema(description="数据秘密等级")
    @Size(max = 10,message = "数据秘密等级max length should less than 10")
    private String secretlevel;

    /**
     * 注册人ID
     */
    @Schema(description="注册人ID")
    @Size(max = 100,message = "注册人IDmax length should less than 100")
    private String registerman;

    /**
     * 注册时间
     */
    @Schema(description="注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registerdate;

    /**
     * 编码
     */
    @Schema(description="编码")
    @Size(max = 100,message = "编码max length should less than 100")
    private String code;

    /**
     * 大数据是否入池 0 否,1 是
     */
    @Schema(description="大数据是否入池 0 否,1 是")
    private Boolean isbigdataimport;

    /**
     * 坐标系类型标识
     */
    @Schema(description="坐标系类型标识")
    @Size(max = 10,message = "坐标系类型标识max length should less than 10")
    private String cs;

    /**
     * 省市县标识
     */
    @Schema(description="省市县标识")
    @Size(max = 10,message = "省市县标识max length should less than 10")
    private String levelsign;

    /**
     * 修改人ID
     */
    @Schema(description="修改人ID")
    @Size(max = 100,message = "修改人IDmax length should less than 100")
    private String repairman;

    /**
     * 修改日期
     */
    @Schema(description="修改日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime repairdate;

    /**
     * 显示顺序
     */
    @Schema(description="显示顺序")
    private Short displayorder;

    /**
     * 数据类型：1表示矢量数据，2表示栅格数据。默认为1
     */
    @Schema(description="数据类型：1表示矢量数据，2表示栅格数据。默认为1")
    @NotNull(message = "数据类型：1表示矢量数据，2表示栅格数据。默认为1is not null")
    private Short datatype;

    /**
     * 数据检查类型：0表示不检查，1表示只需传统空间检查，2表示只需大数据入库检查，4表示传统和大入库数据都需要检查。默认为0
     */
    @Schema(description="数据检查类型：0表示不检查，1表示只需传统空间检查，2表示只需大数据入库检查，4表示传统和大入库数据都需要检查。默认为0")
    @NotNull(message = "数据检查类型：0表示不检查，1表示只需传统空间检查，2表示只需大数据入库检查，4表示传统和大入库数据都需要检查。默认为0is not null")
    private Short validationtype;

    /**
     * 元数据字段
     */
    @Schema(description="元数据字段(json)")
    private Object metadata;

    /**
     * 权限类型
     */
    @Schema(description="权限类型")
    private Short authoritytype;

    /**
     * 是否显示
     */
    @Schema(description="是否显示")
    private Boolean isvisiable;

    private List<ResourceServices> resourceServices;

    private static final long serialVersionUID = 1L;
}