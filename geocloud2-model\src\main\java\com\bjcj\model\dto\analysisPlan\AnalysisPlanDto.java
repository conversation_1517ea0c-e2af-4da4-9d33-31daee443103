package com.bjcj.model.dto.analysisPlan;

import com.bjcj.common.utils.annotation.RequestKeyParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/2/21  17:36
 * mybatisflex 示例bean
*/
@Schema(description="分析展示方案表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AnalysisPlanDto implements Serializable {
    @Schema(description="")
    private Long id;

    /**
     * 名称
     */
    @Schema(description="名称")
    @Size(max = 100,message = "名称max length should less than 100")
    @NotBlank(message = "名称is not blank")
    @RequestKeyParam(name = "planName")
    private String planName;

    /**
     * 描述
     */
    @Schema(description="描述")
    @Size(max = 255,message = "描述max length should less than 255")
    private String remark;

    /**
     * 配置json信息
     */
    @Schema(description="配置json信息")
    private String jsonContent;

    @Schema(description="配置json信息描述")
    private String jsonSchema;

    @Schema(description="自定义方案")
    private String jsonPlan;

    /**
     * 平台应用配置id
     */
    @Schema(description="平台应用配置id")
    @NotNull(message = "平台应用配置idis not null")
    private Long platformAppConfId;

    /**
     * 方案类型:自定义,重叠分析,规划分析,基本农田
     */
    @Schema(description="方案类型:自定义,重叠分析,规划分析,基本农田")
    @Size(max = 20,message = "方案类型:自定义,重叠分析,规划分析,基本农田max length should less than 20")
    @NotBlank(message = "方案类型:自定义,重叠分析,规划分析,基本农田is not blank")
    private String planType;

    /**
     * 分析图层id
     */
    @Schema(description="分析图层id")
    private Long fxDataLayerId;

    /**
     * 被分析图层id
     */
    @Schema(description="被分析图层id")
    private Long bfxDataLayerId;

    @Schema(description="字段明细配置")
    private List<AnalysisPlanFieldDetailDto> analysisPlanFieldDetailDtos;

    @Schema(description="字段统计配置")
    private List<AnalysisPlanFieldStatisticsDto> analysisPlanFieldStatisticsDtos;

    private static final long serialVersionUID = 1L;
}