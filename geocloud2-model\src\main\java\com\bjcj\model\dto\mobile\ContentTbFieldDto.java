package com.bjcj.model.dto.mobile;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *  内容管理配置表字段内容
 **/

@Schema(description = "内容管理配置表字段内容Dto")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("sys_mobile_CONTENTTBFIELD")
public class ContentTbFieldDto implements Serializable {

    @Schema(description = "主键")
    private String fieldId; //主键

    @Schema(description = "英文名称")
    private String fieldName; //英文名称

    @Schema(description = "中文名称")
    private String aliasName; //中文名称

    @Schema(description = "描述")
    private String description; //描述

    @Schema(description = "字段类型")
    private String fieldType; //字段类型

    @Schema(description = "显示顺序")
    private Integer displayOrder; //显示顺序

    @Schema(description = "是否显示")
    private String isdisplay; //是否显示

    @Schema(description = "长度")
    private Integer length; //长度

    @Schema(description = "精度")
    private String precision; //精度

    @Schema(description = "小数位数，用于浮点数字段")
    private String scale; //小数位数，用于浮点型字段

    @Schema(description = "显示名称")
    private String displayname; //显示名称(字段标识符号)

    @Schema(description = "配置表的外键")
    private String configid; //配置表的外键

    @Schema(description = "租户ID")
    private String tenantId; //租户ID

    @Schema(description = "表单域类型")
    private String ftype; //表单域类型

    @Schema(description = "表单类型外键")
    private String typeId; //表单类型外键

    @Schema(description = "是否必填")
    private String isRequired; //是否必填

    @Schema(description = "下拉框的值设置")
    private String dropData; //主要用于下拉框的值设置

    @Schema(description = "是否作为表单头")
    private String isHead; //表单字段之间是否添加分隔标题

    @Schema(description = "分隔标题名称")
    private String headSeparName; //分隔标题名称

    @Schema(description = "默认值字段")
    private String defaultValue; //默认值字段

    @Schema(description = "是否显示绘制红线数据")
    private String isDrawLine; //是否显示绘制红线数据

    @Schema(description = "是否显示拍照内容")
    private String isShowPhotos; //是否显示拍照内容

    @Schema(description = "显示拍照标题名称说明")
    private String photoName; //显示拍照标题名称说明

    @Schema(description = "拍照位置位于某个字段上面还是下面")
    private String photoPosition; //拍照位置位于某个字段上面还是下面

    @Schema(description = "拍照标识符编号名称")
    private String photoId; //拍照标识符编号名称

    @Schema(description = "数据源")
    private String dbName; //数据源

    @Schema(description = "表名")
    private String tableName; //表名

    @Schema(description = "字段名")
    private String isParam;//是否需要传递到详情页面的参数

}
