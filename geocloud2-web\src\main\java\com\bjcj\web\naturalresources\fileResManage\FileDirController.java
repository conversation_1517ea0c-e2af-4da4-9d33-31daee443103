package com.bjcj.web.naturalresources.fileResManage;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.fileResManage.FileDirDto;
import com.bjcj.model.po.naturalresources.fileResManage.FileDir;
import com.bjcj.service.naturalresources.fileResManage.FileDirService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


/**
* 文件目录(public.file_dir)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/fileDirResManage")
@Tag(name = "文件资源管理")
@Validated
public class FileDirController {
    /**
    * 服务对象
    */
    @Resource
    private FileDirService fileDirService;

    @OperaLog(operaModule = "文件资源目录-新增/编辑",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "文件资源目录-新增/编辑")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "addOrEdit")
    public JsonResult addOrEdit(@Validated @RequestBody FileDirDto dto){
        FileDir one = this.fileDirService.getOne(new LambdaQueryWrapper<FileDir>().eq(FileDir::getFileDirName, dto.getFileDirName()));
        if(Objects.nonNull(one)){
            return JsonResult.error("文件资源目录名称重复");
        }
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        FileDir fileDir = BeanUtil.copyProperties(dto, FileDir.class);
        fileDir.setOperater(username);
        if(Objects.isNull(fileDir.getId()))
            fileDir.setCreateTime(LocalDateTime.now());
        else
            fileDir.setUpdateTime(LocalDateTime.now());
        return this.fileDirService.saveOrUpdate(fileDir) ? JsonResult.success():JsonResult.error();
    }

    @OperaLog(operaModule = "文件资源目录-删除",operaType = OperaLogConstant.DELETE,operaDesc = "文件资源目录-删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del")
    @Operation(summary = "删除", description = "根据id删除")
    @Parameters({
            @Parameter(name = "id", description = "id", required = true)
    })
    @ApiOperationSupport(order = 3)
    public JsonResult del(@RequestParam("id") Long id){
        return this.fileDirService.removeById(id) ? JsonResult.success():JsonResult.error();
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "列表树", description = "列表树")
    @ApiOperationSupport(order = 4)
    @GetMapping("/tree")
    public JsonResult<List<FileDir>> tree() {
        return fileDirService.lists();
    }

    // @SaCheckPermission("sys:read")
    // @Operation(summary = "列表", description = "查询,分页")
    // @ApiOperationSupport(order = 1)
    // @GetMapping("/list")
    // @Parameters({
    //         @Parameter(name = "current", description = "页码", required = true),
    //         @Parameter(name = "size", description = "每页条数", required = true),
    //         @Parameter(name = "searchStr", description = "名称搜索", required = false)
    // })
    // public JsonResult<Page<FileDir>> list(
    //         @RequestParam("current") Integer page,
    //         @RequestParam("size") Integer pageSize,
    //         @RequestParam(value = "searchStr",required = false) String searchStr){
    //     Page<FileDir> pager = new Page<>(page, pageSize);
    //     return fileDirService.pageData(pager,searchStr);
    // }

}
