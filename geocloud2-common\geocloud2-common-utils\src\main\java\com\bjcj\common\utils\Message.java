package com.bjcj.common.utils;

/**
 * <AUTHOR>
 * @date 2020/3/28 17:32
 */
public enum Message {

    /**
     * 操作成功
     */
    SUCCESS("操作成功"),

    ERROR("操作失败"),

    PARAM_ERROR("参数错误！"),

    LOGIN_SUCCESS(" 登陆成功"),

    LOGOUT_SUCCESS(" 安全退出"),

    REGISTER_SUCCESS(" 注册成功"),

    USER_EXIST("用户已存在！"),

    USER_NOT_EXIST("用户不存在或禁用"),

    PASSWORD_ERROR("密码错误"),

    SERVER_ERROR("服务器异常"),

    COURSE_EXIST("课程已经村在"),

    AUTH_ERROR("权限不足"),

    ARGUMENT_TYPE_MISMATCH("参数类型不匹配"),

    REQ_METHOD_NOT_SUPPORT("请求方式不支持"),

    UNKNOWN_EXCEPTION("未知错误"),

    SHIRO_ERROR("权限不足"),

    /**
     * 添加失败
     */
    ADD_ERROR("添加失败"),

    /**
     * 更新失败
     */
    UPDATE_ERROR("更新失败"),

    /**
     * 删除失败
     */
    DELETE_ERROR("删除失败"),

    /**
     * 查找失败
     */
    GET_ERROR("查询失败，数据可能不存在"),

    /**
     * 导入失败
     */
    IMPORT_ERROR("导入失败"),

    /**
     * 用户名或密码错误
     */
    USER_PWD_ERROR("用户名或密码错误"),

    /**
     * 用户不存在
     */
    USER_NOT_ERROR("用户不存在"),

    /**
     * 登录超时，请重新登录
     */
    LOGIN_TIME_OUT("登录超时，请重新登录"),

    /**
     * 用户未登录，请进行登录
     */
    USER_NOT_LOGIN("用户未登录，请进行登录"),

    /**
     * 账号锁定
     */
    USER_LOCK("账号锁定中"),

    /**
     * 非法令牌
     */
    ILLEGAL_TOKEN("非法令牌"),

    /**
     * 其他客户端登录
     */
    OTHER_CLIENT_LOGIN("其他客户端登录"),

    /**
     * 令牌过期
     */
    TOKEN_EXPIRED("令牌过期"),

    /**
     * 菜单名字已存在
     */
    MENU_TITLE_EXIST("菜单名字已存在"),

    /**
     * 菜单名字已存在
     */
    FACULTY_TITLE_EXIST("院系名字已存在"),

    /**
     * 此版本已存在
     */
    VERSION_EXIST("此版本已存在"),

    /**
     * 已是最新版本
     */
    VERSION_LATEST("已是最新版本"),

    /**
     * 考试已开始，不允许修改
     */
    EXAM_START("考试已开始，不允许修改"),

    EXAM_UP("考试已发布，不允许修改"),

    ;

    private final String message;

    Message(String message) {
        this.message = message;
    }

    public String message() {
        return message;
    }

}
