package com.bjcj.model.dto.mobile;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PdaSudoconfigDto implements Serializable {

    private String pid;

    private String name;

    private String remark;

    private String imgPath;

    private String isDisplay;

    private String menuname;

    private String phoneType;

    private String sysName;

    private String className;

    private String titleImageUrl;

    private String isApproval;

    private String mobilePhone;

    private String width;

    private String height;

    private String numFileds;

    private String key;

    private String columnSize;

    private String isSystem;

    private Integer currentPage;   // 页码
    private Integer pageSize;  // 每页条数

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

}
