package com.bjcj.model.dto.datamodel;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/7/1  9:04
*/

/**
    * 保存数据
    */
@Schema(description="保存数据")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "spdp_savedata")
public class SpdpSavedataDto implements Serializable {
    @Schema(description="")
    private String saveid;

    @TableField(value = "name")
    @Schema(description="")
    @Size(max = 60,message = "max length should less than 60")
    @NotBlank(message = "名称不能为空")
    private String name;

    @Schema(description="")
    @Size(max = 100,message = "max length should less than 100")
    @NotBlank(message = "显示名称不能为空")
    private String displayname;

    @Schema(description="")
    @Size(max = 100,message = "max length should less than 100")
    private String savemodelid;

    @TableField(value = "params")
    @Schema(description="")
    @Size(max = 4000,message = "max length should less than 4000")
    private String params;

    private static final long serialVersionUID = 1L;
}