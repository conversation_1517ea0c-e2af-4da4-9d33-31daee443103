package com.bjcj.web.datamodel;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.datamodel.DbManageDto;
import com.bjcj.model.dto.datamodel.MetadataWorkspacesDto;
import com.bjcj.model.po.datamodel.DbManage;
import com.bjcj.model.po.datamodel.MetadataWorkspaces;
import com.bjcj.service.datamodel.DbManageService;
import com.bjcj.service.datamodel.MetadataWorkspacesService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Objects;


/**
* (public.db_manage)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/dbManage")
@Tag(name = "数据库管理")
@Validated
public class DbManageController {
    /**
    * 服务对象
    */
    @Resource
    private DbManageService dbManageService;

    @Resource
    private MetadataWorkspacesService metadataWorkspacesService;

    @OperaLog(operaModule = "数据库管理-新增/编辑",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "新增/编辑")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑数据库", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "addOrEdit")
    public JsonResult addOrEdit(@Validated @RequestBody DbManageDto dto) {
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        DbManage dbManage = BeanUtil.copyProperties(dto, DbManage.class);
        dbManage.setOperator(username);
        if(Objects.isNull(dbManage.getId())){
            dbManage.setCreateTime(LocalDateTime.now());
        }else{
            dbManage.setUpdateTime(LocalDateTime.now());
        }
        return dbManageService.saveOrUpdate(dbManage) ? JsonResult.success():JsonResult.error();
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/page")
    @Operation(summary = "列表分页", description = "带搜索框模糊查询(可搜名称)")
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "searchStr", description = "名称搜索", required = false)
    })
    @ApiOperationSupport(order = 1)
    public JsonResult<Page<DbManage>> page(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam(value = "searchStr",required = false) String searchStr) {
        Page<DbManage> pager = new Page<>(page, pageSize);
        return this.dbManageService.pageDataSort(pager,searchStr);
    }

    @OperaLog(operaModule = "数据库管理-删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del")
    @Operation(summary = "删除", description = "根据id删除")
    @Parameters({
            @Parameter(name = "id", description = "id", required = true)
    })
    @ApiOperationSupport(order = 3)
    public JsonResult del(@RequestParam("id") Long id) {
        return dbManageService.removeById(id)? JsonResult.success():JsonResult.error();
    }

    @OperaLog(operaModule = "数据集列表",operaType = OperaLogConstant.CREATE,operaDesc = "数据集列表")
    @SaCheckPermission("sys:read")
    @GetMapping("/datasetList")
    @Operation(summary = "数据集列表", description = "数据集列表")
    @ApiOperationSupport(order = 11)
    @Parameters({
            @Parameter(name = "db_id", description = "数据库id", required = true)
    })
    public JsonResult datasetList(@RequestParam("db_id") Long db_id){
        return this.dbManageService.datasetList(db_id);
    }

    @OperaLog(operaModule = "通过数据集获取图层",operaType = OperaLogConstant.CREATE,operaDesc = "通过数据集获取图层")
    @SaCheckPermission("sys:read")
    @GetMapping("/queryLayersByDatasetname")
    @Operation(summary = "通过数据集获取图层", description = "通过数据集获取图层")
    @ApiOperationSupport(order = 12)
    @Parameters({
            @Parameter(name = "datasetName", description = "数据集名称", required = true),
            @Parameter(name = "db_id", description = "数据库id", required = true)
    })
    public JsonResult queryLayersByDatasetname(@RequestParam("datasetName") String datasetName,
                                               @RequestParam("db_id") String db_id){
        return this.dbManageService.queryLayersByDatasetname(datasetName,db_id);
    }

    @OperaLog(operaModule = "从db输入图层数据",operaType = OperaLogConstant.CREATE,operaDesc = "从db通过图层名称输入图层及字段")
    @SaCheckPermission("sys:write")
    @GetMapping("/importLayerData")
    @Operation(summary = "从db输入图层数据", description = "从db输入图层数据")
    @ApiOperationSupport(order = 13)
    @Parameters({
            @Parameter(name = "dataStandardId", description = "数据标准id", required = true),
            @Parameter(name = "db_id", description = "数据库id", required = true),
            @Parameter(name = "layerName", description = "图层名称(多个需要逗号分隔)", required = true)
    })
    @Transactional(rollbackFor = Exception.class)
    public JsonResult importLayerData(@RequestParam("db_id") String db_id,
                                     @RequestParam("layerName") String layerName,
                                      @RequestParam("dataStandardId") Long dataStandardId) throws Exception {
        return this.dbManageService.importLayerData(db_id,layerName,dataStandardId);
    }



    @OperaLog(operaModule = "数据库管理-新增/编辑",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "新增/编辑")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEditWorkSpace")
    @Operation(summary = "1.新增/编辑数据库", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 4)
    @RequestLock(prefix = "addOrEditWorkSpace")
    public JsonResult addOrEditWorkSpace(@Validated @RequestBody MetadataWorkspacesDto dto) {
        MetadataWorkspaces metadataWorkspaces = BeanUtil.copyProperties(dto, MetadataWorkspaces.class);
        try {
            boolean b = metadataWorkspacesService.saveOrUpdate(metadataWorkspaces);
            return b ? JsonResult.success() : JsonResult.error();
        }catch (Exception e)
            {
                System.out.println(e.getMessage());
                return JsonResult.error(e.getMessage());
            }

    }

    @OperaLog(operaModule = "数据库管理-删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delWorkSpace")
    @Operation(summary = "2.删除", description = "根据id删除")
    @Parameters({
            @Parameter(name = "workspaceid", description = "workspaceid", required = true)
    })
    @ApiOperationSupport(order = 5)
    public JsonResult delWorkSpace(@RequestParam("workspaceid") String workspaceid) {
        return metadataWorkspacesService.removeById(workspaceid)? JsonResult.success():JsonResult.error();
    }


    @SaCheckPermission("sys:read")
    @GetMapping("/workSpacePage")
    @Operation(summary = "3.列表分页", description = "带搜索框模糊查询(可搜名称)")
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "searchStr", description = "名称搜索", required = false)
    })
    @ApiOperationSupport(order = 6)
    public JsonResult<Page<MetadataWorkspaces>> workSpacePage(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam(value = "searchStr",required = false) String searchStr) {
        Page<MetadataWorkspaces> pager = new Page<>(page, pageSize);
        return this.metadataWorkspacesService.pageDataSort(pager,searchStr);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/datasetList2")
    @Operation(summary = "4.数据集列表", description = "数据集列表")
    @ApiOperationSupport(order = 7)
    @Parameters({
            @Parameter(name = "workspaceid", description = "数据库id", required = true)
    })
    public JsonResult datasetList2(@RequestParam("workspaceid") String workspaceid){
        return this.metadataWorkspacesService.datasetList(workspaceid);
    }

    @PostMapping("/testlink")
    @Operation(summary = "数据库连接测试", description = "数据库连接测试")
    @ApiOperationSupport(order = 14)
    public JsonResult testLink(@Validated @RequestBody MetadataWorkspacesDto dto){
        return this.metadataWorkspacesService.testLink(dto);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/queryLayersByDatasetname2")
    @Operation(summary = "5.通过数据集获取图层", description = "通过数据集获取图层")
    @ApiOperationSupport(order = 8)
    @Parameters({
            @Parameter(name = "datasetName", description = "数据集名称", required = true),
            @Parameter(name = "workspaceid", description = "数据库id", required = true)
    })
    public JsonResult queryLayersByDatasetname2(@RequestParam("datasetName") String datasetName,
                                               @RequestParam("workspaceid") String workspaceid){
        return this.metadataWorkspacesService.queryLayersByDatasetname(datasetName,workspaceid);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/queryAllTables")
    @Operation(summary = "查询数据库下所有表", description = "查询数据库下所有表")
    @ApiOperationSupport(order = 88)
    @Parameters({
            @Parameter(name = "workspaceid", description = "数据库id", required = true)
    })
    public JsonResult queryAllTables(@RequestParam("workspaceid") String workspaceid){
        return this.metadataWorkspacesService.queryAllTables(workspaceid);
    }


    @OperaLog(operaModule = "从db输入图层数据新",operaType = OperaLogConstant.CREATE,operaDesc = "从db通过图层名称输入图层及字段新")
    @SaCheckPermission("sys:write")
    @GetMapping("/importLayerData2")
    @Operation(summary = "6.从db输入图层数据", description = "从db输入图层数据")
    @ApiOperationSupport(order = 9)
    @Parameters({
            @Parameter(name = "datastandardid", description = "数据标准id", required = true),
            @Parameter(name = "workspaceid", description = "数据库id", required = true),
            @Parameter(name = "layerName", description = "图层名称(多个需要逗号分隔)", required = true)
    })
    @Transactional(rollbackFor = Exception.class)
    public JsonResult importLayerData2(@RequestParam("workspaceid") String workspaceid,
                                      @RequestParam("layerName") String layerName,
                                      @RequestParam("datastandardid") String datastandardid) throws Exception {
        return this.metadataWorkspacesService.importLayerData(workspaceid,layerName,datastandardid);
    }

}