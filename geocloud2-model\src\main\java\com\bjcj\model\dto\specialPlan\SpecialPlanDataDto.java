package com.bjcj.model.dto.specialPlan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
    * 专题展示方案数据
 * <AUTHOR>
 */
@Schema(description="专题展示方案数据")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SpecialPlanDataDto implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    @NotNull(message = "id不能为空",groups = {EditGroup.class})
    private Long id;
    /**
     * 专题名称
     */
    @Schema(description="服务idlist--新增必传")
    @NotNull(message = "服务idlist不能为空",groups = {AddGroup.class})
    private List<String> cataServiceIdList;

    @Schema(description="专题id--新增必传")
    @NotNull(message = "专题id不能为空",groups = {AddGroup.class})
    private Long specialPlanId;

    @Schema(description="显示顺序")
    @NotNull(message = "显示顺序不能为空",groups = {EditGroup.class})
    private Integer showSort;

    @Schema(description="名称")
    @NotNull(message = "名称不能为空",groups = {EditGroup.class})
    @RequestKeyParam(name = "specialName")
    private String specialName;

    @Schema(description="显示名称")
    private String showName;

    @Schema(description="专题目录")
    @Size(max = 100,message = "专题目录max length should less than 100",groups = {AddGroup.class})
    private String specialPlanDirId;

    @Schema(description="是否显示")
    private Boolean isShow;

    @Schema(description="是否展开")
    private Boolean isExpand;

    @Schema(description="是否初始化加载")
    private Boolean isInitLoad;


    public interface AddGroup{
    }
    public interface EditGroup{
    }
    private static final long serialVersionUID = 1L;
}