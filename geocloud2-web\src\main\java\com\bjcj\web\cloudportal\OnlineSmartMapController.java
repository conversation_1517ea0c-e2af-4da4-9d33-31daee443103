package com.bjcj.web.cloudportal;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.cloudportal.OnlineSmartMap;
import com.bjcj.service.cloudportal.OnlineSmartMapService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
* (public.online_smart_map)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/online_smart_map")
@Tag(name = "云门户/在线智图")
@Validated
public class OnlineSmartMapController {
    /**
    * 服务对象
    */
    @Resource
    private OnlineSmartMapService onlineSmartMapService;

    @SaCheckPermission("sys:read")
    @GetMapping("/list")
    @Operation(summary = "列表", description = "列表")
    @ApiOperationSupport(order = 1)
    public JsonResult<List<OnlineSmartMap>> list() {
        return JsonResult.success(this.onlineSmartMapService.list());
    }

    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑", description = "新增/编辑")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "addOrEditzxzt")
    public JsonResult addOrEditzxzt(@Validated @RequestBody OnlineSmartMap onlineSmartMap){
        return this.onlineSmartMapService.saveOrUpdate(onlineSmartMap) ? JsonResult.success() : JsonResult.error();
    }

    @OperaLog(operaModule = "删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del/{id}")
    @Operation(summary = "删除", description = "根据id删除")
    @ApiOperationSupport(order = 3)
    @RequestLock(prefix = "delzxzt")
    public JsonResult del(@PathVariable("id") String id){
        return this.onlineSmartMapService.removeById(id) ? JsonResult.success() : JsonResult.error();
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/detail")
    @Operation(summary = "详情", description = "详情")
    @ApiOperationSupport(order = 4)
    @Parameters({
            @Parameter(name = "id", description = "id", required = true)
    })
    public JsonResult<OnlineSmartMap> list(@RequestParam("id") String id) {
        return JsonResult.success(this.onlineSmartMapService.getById(id));
    }

    @OperaLog(operaModule = "批量删除",operaType = OperaLogConstant.DELETE,operaDesc = "批量删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delBatch")
    @Operation(summary = "批量删除", description = "根据id批量删除")
    @ApiOperationSupport(order = 5)
    @Parameters({
            @Parameter(name = "ids", description = "ids逗号拼接", required = true)
    })
    @RequestLock(prefix = "delBatchzxzt")
    public JsonResult delBatch(@RequestParam("ids") String ids){
        if(ids.contains(",")){
            //ids转list
            List<String> ids2 = List.of(ids.split(","));
            return JsonResult.success(this.onlineSmartMapService.removeByIds(ids2));
        }else{
            return JsonResult.error("ids格式错误,必须选择一条以上的数据");
        }
    }


}
