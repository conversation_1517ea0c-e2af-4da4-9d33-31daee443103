package com.bjcj.model.po.safetyManage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 * @Author：qinyi
 * @Date：2023/11/27  10:31
 */
@Schema(description="用户表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_user")
public class User implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 姓名
     */
    @TableField(value = "nick_name")
    @Schema(description="姓名")
    @Size(max = 50,message = "姓名最大长度要小于 50")
    @NotBlank(message = "姓名不能为空")
    private String nickName;

    /**
     * 用户名
     */
    @TableField(value = "username")
    @Schema(description="用户名")
    @Size(max = 100,message = "用户名最大长度要小于 100")
    @NotBlank(message = "用户名不能为空")
    private String username;

    /**
     * 密码
     */
    @TableField(value = "\"password\"")
    @Schema(description="密码")
    @Size(max = 100,message = "密码最大长度要小于 100")
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 注册人
     */
    @TableField(value = "register_user")
    @Schema(description="注册人")
    @Size(max = 100,message = "注册人最大长度要小于 100")
    private String registerUser;

    /**
     * 机构id
     */
    @TableField(value = "institution_id")
    @Schema(description="机构id")
    @NotNull(message = "机构id不能为null")
    private Long institutionId;

    /**
     * 部门id
     */
    @TableField(value = "dept_id")
    @Schema(description="部门id")
    @NotNull(message = "部门id不能为null")
    private Long deptId;

    /**
     * 显示顺序
     */
    @TableField(value = "show_sort")
    @Schema(description="显示顺序")
    @NotNull(message = "显示顺序不能为null")
    private Integer showSort;

    /**
     * 注册时间
     */
    @TableField(value = "register_time")
    @Schema(description="注册时间")
    @Size(max = 30,message = "注册时间最大长度要小于 30")
    private String registerTime;

    /**
     * 激活状态0待提交1未激活2审核失败3激活
     */
    @TableField(value = "active_status")
    @Schema(description="激活状态0待提交1未激活2审核失败3激活")
    private Integer activeStatus;

    /**
     * 角色ids逗号分隔
     */
    @TableField(value = "role_ids")
    @Schema(description="角色ids逗号分隔")
    @Size(max = 255,message = "角色ids逗号分隔最大长度要小于 255")
    private String roleIds;

    /**
     * 访问权限ids逗号分隔
     */
    @TableField(value = "access_permissions_ids")
    @Schema(description="访问权限ids逗号分隔")
    @Size(max = 255,message = "访问权限ids逗号分隔最大长度要小于 255")
    private String accessPermissionsIds;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description="创建时间")
    @Size(max = 30,message = "创建时间最大长度要小于 30")
    private String createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @Schema(description="更新时间")
    @Size(max = 30,message = "更新时间最大长度要小于 30")
    private String updateTime;

    /**
     * 最后操作人
     */
    @TableField(value = "operater")
    @Schema(description="最后操作人")
    @Size(max = 100,message = "最后操作人最大长度要小于 100")
    private String operater;

    /**
     * 手机号
     */
    @TableField(value = "telephone")
    @Schema(description="手机号")
    @Size(max = 20,message = "手机号最大长度要小于 20")
    private String telephone;

    /**
     * 办公电话
     */
    @TableField(value = "office_phone")
    @Schema(description="办公电话")
    @Size(max = 20,message = "办公电话最大长度要小于 20")
    private String officePhone;

    /**
     * 邮箱
     */
    @TableField(value = "email")
    @Schema(description="邮箱")
    @Size(max = 50,message = "邮箱最大长度要小于 50")
    private String email;

    /**
     * 家庭电话
     */
    @TableField(value = "family_phone")
    @Schema(description="家庭电话")
    @Size(max = 20,message = "家庭电话最大长度要小于 20")
    private String familyPhone;

    @TableField(value = "is_lock")
    @Schema(description="是否锁定-0否1是")
    private Integer isLock;

    @TableField(exist = false)
    private String institution;

    @TableField(exist = false)
    private String deptName;

    /**
     * 密码最后更新时间
     */
    @TableField(value = "pwd_update_date")
    @Schema(description="密码最后更新时间")
    @Size(max = 30,message = "密码最后更新时间最大长度要小于 30")
    private String pwdUpdateDate;


    /**
     * 头像
     */
    @TableField(value = "avatar")
    @Schema(description="头像")
    @Size(max = 255,message = "邮箱最大长度要小于 255")
    private String avatar;

    /**
     * sex
     */
    @TableField(value = "sex")
    @Schema(description="性别")
    @Size(max = 5,message = "邮箱最大长度要小于 5")
    private String sex;

    /**
     * age
     */
    @TableField(value = "age")
    @Schema(description="年龄")
    @Size(max = 5,message = "邮箱最大长度要小于 5")
    private String age;


    /**
     * birthday
     */
    @TableField(value = "birthday")
    @Schema(description="生日")
    @Size(max = 30,message = "邮箱最大长度要小于 30")
    private String birthday;

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    @Schema(description="用户角色")
    private List<SafetyRole> roleList;

    @TableField(exist = false)
    @Schema(description="用户权限")
    private List<SysPermission> permissionList;

}