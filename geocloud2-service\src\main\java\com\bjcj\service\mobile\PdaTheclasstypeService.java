package com.bjcj.service.mobile;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.mapper.mobile.PdaTheclasstypeMapper;
import com.bjcj.model.po.mobile.PdaTheclasstype;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class PdaTheclasstypeService extends ServiceImpl<PdaTheclasstypeMapper, PdaTheclasstype>
    {


    public List<PdaTheclasstype> getEnable() {
        QueryWrapper<PdaTheclasstype> queryWrapper = new QueryWrapper();
        queryWrapper.eq("STATUS","true");
        return list(queryWrapper);
    }
}




