package com.bjcj.service.platformConf;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.platformConf.GisResConfMapper;
import com.bjcj.model.po.platformConf.GisResConf;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 *@Author：qinyi
 *@Date：2024/2/28  16:24
*/
@Service
public class GisResConfService extends ServiceImpl<GisResConfMapper, GisResConf> {

    @Resource
    GisResConfMapper gisResConfMapper;

    public JsonResult<Page<GisResConf>> pageDataSort(Page<GisResConf> pager, String searchStr) {
        LambdaQueryWrapper<GisResConf> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(searchStr),GisResConf::getWebsiteName,searchStr);
        queryWrapper.orderByDesc(GisResConf::getCreateTime);
        return JsonResult.success(this.gisResConfMapper.selectPage(pager,queryWrapper));
    }
}
