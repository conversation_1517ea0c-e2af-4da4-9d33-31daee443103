<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.cloudportal.OnlineSmartMapMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.cloudportal.OnlineSmartMap">
    <!--@mbg.generated-->
    <!--@Table public.online_smart_map-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="json_str" jdbcType="VARCHAR" property="jsonStr" />
    <result column="descstr" jdbcType="VARCHAR" property="descstr" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", json_str, descstr
  </sql>
</mapper>