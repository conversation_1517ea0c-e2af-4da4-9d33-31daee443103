package com.bjcj.model.po.safetyManage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/11/28 14:15 周二
 */
@Schema(description = "用户登录时间点记录")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "public.sys_user_login_point")
public class SysUserLoginPoint {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "")
    private Long id;

    @TableField(value = "user_id")
    @Schema(description = "")
    private Long userId;

    @TableField(value = "username")
    @Schema(description = "")
    private String username;

    @TableField(value = "login_time")
    @Schema(description = "登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime loginTime;

    @TableField(value = "invalid_time")
    @Schema(description = "刷新失效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime invalidTime;
}