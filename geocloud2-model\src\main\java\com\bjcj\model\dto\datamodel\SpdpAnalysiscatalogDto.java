package com.bjcj.model.dto.datamodel;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/5/10  17:30
*/

/**
    * 分析规则目录
    */
@Schema(description="分析规则目录")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SpdpAnalysiscatalogDto implements Serializable {
    /**
     * 标识
     */
    @Schema(description="标识")
    @Size(max = 36,message = "标识max length should less than 36")
    private String id;

    /**
     * 名称
     */
    @Schema(description="名称")
    @Size(max = 60,message = "名称max length should less than 60")
    @NotBlank(message = "名称is not blank")
    private String name;

    /**
     * 显示顺序
     */
    @Schema(description="显示顺序")
    private Short displayorder;

    /**
     * 父级目录ID
     */
    @Schema(description="父级目录ID")
    @Size(max = 36,message = "父级目录IDmax length should less than 36")
    private String parentid;

    /**
     * 描述
     */
    @Schema(description="描述")
    @Size(max = 150,message = "描述max length should less than 150")
    private String description;

    /**
     * 编码
     */
    @Schema(description="编码")
    @Size(max = 50,message = "编码max length should less than 50")
    private String code;

    private static final long serialVersionUID = 1L;
}