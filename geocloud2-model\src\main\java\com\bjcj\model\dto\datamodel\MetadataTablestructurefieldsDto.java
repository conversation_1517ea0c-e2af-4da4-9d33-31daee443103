package com.bjcj.model.dto.datamodel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/4/19  14:54
*/

/**
    * 表结构字段信息表
    */
@Schema(description="图层字段表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MetadataTablestructurefieldsDto implements Serializable {
    /**
     * 字段ID
     */
    @Schema(description="字段ID")
    private String fieldid;

    /**
     * 字段名称
     */
    @Schema(description="字段名称")
    private String name;

    /**
     * 字段编码，可用来存字段标准名称
     */
    @Schema(description="字段编码，可用来存字段标准名称")
    private String code;

    /**
     * 字段说明
     */
    @Schema(description="字段说明")
    private String description;

    /**
     * 字段类型
     */
    @Schema(description="字段类型")
    private Long fieldtype;

    @Schema(description="字段类型字符串")
    private String fieldtypeStr;

    /**
     * 字段长度
     */
    @Schema(description="字段长度")
    private Long length;

    /**
     * 字段精度
     */
    @Schema(description="字段精度")
    private Long precision;

    /**
     * 字段小数位数
     */
    @Schema(description="字段小数位数")
    private Long scale;

    /**
     * 显示名称
     */
    @Schema(description="显示名称")
    private String displayname;

    /**
     * 字段域
     */
    @Schema(description="字段域")
    private String fielddomainid;

    /**
     * 表结构的ID
     */
    @Schema(description="图层表ID")
    private String tablestructureid;

    /**
     * 是否可为空
     */
    @Schema(description="是否可为空")
    private Boolean isnullable;

    /**
     * 是否必须
     */
    @Schema(description="是否必须")
    private Boolean required;

    /**
     * 默认值
     */
    @Schema(description="默认值")
    private String defaultvalue;

    /**
     * 字段的约束表达式
     */
    @Schema(description="字段的约束表达式")
    private String checkexpression;

    /**
     * 数据库字段类型
     */
    @Schema(description="数据库字段类型")
    private String dbfieldtype;

    /**
     * 显示顺序
     */
    @Schema(description="显示顺序")
    private Short displayorder;

    /**
     * 标准名称，用途：有些用户的字段不是标准名称，但是和规范中的名称又是同一个意思
     */
    @Schema(description="标准名称，用途：有些用户的字段不是标准名称，但是和规范中的名称又是同一个意思")
    private String standardname;

    /**
     * 统计分类 ; 0表示属性，1表示维度，2表示指标
     */
    @Schema(description="统计分类 ; 0表示属性，1表示维度，2表示指标")
    private Short statisticscategory;

    /**
     * 维度类型 ; 0表示时间维度，1表示空间维度，2表示业务维度
     */
    @Schema(description="维度类型 ; 0表示时间维度，1表示空间维度，2表示业务维度")
    private Short dimensiontype;

    /**
     * 代表单位
     */
    @Schema(description="代表单位")
    private String representunit;

    /**
     * 时间单位
     */
    @Schema(description="时间单位")
    private String timeunit;

    /**
     * 维度字段映射关系
     */
    @Schema(description="维度字段映射关系")
    private String dimensionfieldmapping;

    /**
     * 是否排序
     */
    @Schema(description="是否排序")
    private Boolean issort;

    /**
     * 排序方式
     */
    @Schema(description="排序方式")
    private String sortord;

    /**
     * 是否显示
     */
    @Schema(description="是否显示")
    private Boolean isvisiable;

    /**
     * 导入维度标识
     */
    @Schema(description="导入维度标识")
    private String dimensionid;

}