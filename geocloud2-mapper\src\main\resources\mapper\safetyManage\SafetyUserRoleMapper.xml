<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.safetyManage.SafetyUserRoleMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.safetyManage.SafetyUserRole">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="role_id" jdbcType="BIGINT" property="roleId" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="institution_name" jdbcType="VARCHAR" property="institutionName" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="role_name" jdbcType="VARCHAR" property="roleName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, user_id, role_id, nick_name,institution_name,dept_name
  </sql>

  <select id="selectListByIdsLeftJoinUser" resultMap="BaseResultMap">
    select ur.id, ur.user_id, ur.role_id, u.nick_name,si.institution_name,sd.dept_name
    from sys_user_role ur
           left join sys_user u on ur.user_id = u.id
           left join sys_institution si on u.institution_id = si.id
           left join sys_dept sd on u.dept_id = sd.id
    where ur.role_id = #{roleId}
    LIMIT #{size} OFFSET #{current}
  </select>

  <select id="queryExistingUserRoles" resultMap="BaseResultMap">
    select ur.id,ur.user_id,ur.role_id,r.role_name from sys_user_role ur left join sys_role r on ur.role_id=r.id where ur.user_id=#{userId}
    </select>

  <delete id="deleteByUserId">
    delete from sys_user_role where user_id=#{userId}
  </delete>

  <select id="selectListByIdsLeftJoinUserCount" resultType="java.lang.Long">
    select count(*) from (
      select ur.id, ur.user_id, ur.role_id, u.nick_name,si.institution_name,sd.dept_name
      from sys_user_role ur
      left join sys_user u on ur.user_id = u.id
      left join sys_institution si on u.institution_id = si.id
      left join sys_dept sd on u.dept_id = sd.id
      where ur.role_id = #{roleId}
      ) u
  </select>

  <select id="selectUserByRoleids" resultType="java.lang.Long">
    select user_id from sys_user_role where role_id in
    <foreach item="item" index="index" collection="roleIds" open="(" close=")" separator=",">
      (#{item})
    </foreach>
  </select>

  <select id="queryUserRoleId" resultType="java.lang.Long">
    select r.id from sys_role r
                       left join sys_user_role ur
                                 on r.id = ur.role_id
    where ur.user_id=#{userId}
    </select>
</mapper>