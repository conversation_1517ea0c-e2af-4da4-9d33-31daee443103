package com.bjcj.model.dto.datamodel;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 *@Author：qinyi
 *@Date：2024/5/13  10:16
*/

/**
    * 重叠分析配置
    */
@Schema(description="重叠分析配置")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SpdpOverlayanalysisDto implements Serializable {
    private String overlayanalysisid;

    @TableField(value = "name")
    @Schema(description="")
    @Size(max = 60,message = "max length should less than 60")
    @NotBlank(message = "is not blank")
    private String name;

    @TableField(value = "displayname")
    @Schema(description="")
    @Size(max = 100,message = "max length should less than 100")
    @NotBlank(message = "is not blank")
    private String displayname;

    @TableField(value = "overlayanalysismodelid")
    @Schema(description="")
    @Size(max = 100,message = "max length should less than 100")
    private String overlayanalysismodelid;

    @TableField(value = "params")
    @Schema(description="")
    private String params;

    @TableField(value = "resourceitemid")
    @Schema(description="")
    @Size(max = 36,message = "max length should less than 36")
    private String resourceitemid;

    /**
     * 输入图层
     */
    @TableField(value = "inputlayers")
    @Schema(description="输入图层")
    @Size(max = 2000,message = "输入图层max length should less than 2000")
    private String inputlayers;

    /**
     * 分析图层
     */
    @TableField(value = "overlaylayers")
    @Schema(description="分析图层")
    @Size(max = 4000,message = "分析图层max length should less than 4000")
    private String overlaylayers;

    /**
     * 组件类型标识
     */
    @TableField(value = "componenttypeid")
    @Schema(description="组件类型标识")
    @Size(max = 36,message = "组件类型标识max length should less than 36")
    private String componenttypeid;

    /**
     * 区域图层标识
     */
    @TableField(value = "regionlayerid")
    @Schema(description="区域图层标识")
    @Size(max = 36,message = "区域图层标识max length should less than 36")
    private String regionlayerid;

    /**
     * 最小面积值
     */
    @TableField(value = "restrictvalue")
    @Schema(description="最小面积值")
    @NotNull(message = "is not blank")
    private BigDecimal restrictvalue;

    /**
     * 面积计算模型标识
     */
    @TableField(value = "areacalculaterid")
    @Schema(description="面积计算模型标识")
    @Size(max = 100,message = "面积计算模型标识max length should less than 100")
    private String areacalculaterid;

    /**
     * 目录ID
     */
    @TableField(value = "catalogid")
    @Schema(description="目录ID")
    @Size(max = 36,message = "目录IDmax length should less than 36")
    private String catalogid;

    /**
     * 分析编码
     */
    @TableField(value = "code")
    @Schema(description="分析编码")
    private Short code;

    private static final long serialVersionUID = 1L;
}