package com.bjcj.model.po.datamodel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/7/2  10:01
*/
/**
    * 规则
    */
@Schema(description="规则")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "spdp_rule")
public class SpdpRule implements Serializable {
    @TableId(value = "ruleid", type = IdType.ASSIGN_UUID)
    @Schema(description="")
    @Size(max = 36,message = "max length should less than 36")
    private String ruleid;

    /**
     * 规则名称
     */
    @TableField(value = "name")
    @Schema(description="规则名称")
    @Size(max = 60,message = "规则名称max length should less than 60")
    private String name;

    /**
     * 别名
     */
    @TableField(value = "displayname")
    @Schema(description="别名")
    @Size(max = 60,message = "别名max length should less than 60")
    private String displayname;

    /**
     * 描述
     */
    @TableField(value = "description")
    @Schema(description="描述")
    @Size(max = 500,message = "描述max length should less than 500")
    private String description;

    /**
     * 错误级别
     */
    @TableField(value = "errorlevel")
    @Schema(description="错误级别")
    private Integer errorlevel;

    /**
     * 是否必须
     */
    @TableField(value = "isneed")
    @Schema(description="是否必须")
    private Boolean isneed;

    /**
     * 规则代码
     */
    @TableField(value = "rulecode")
    @Schema(description="规则代码")
    @Size(max = 50,message = "规则代码max length should less than 50")
    @NotBlank(message = "规则代码is not blank")
    private String rulecode;

    /**
     * 规则项类型
     */
    @TableField(value = "ruleitemcompositetype")
    @Schema(description="规则项类型")
    private Short ruleitemcompositetype;

    /**
     * 所属规则目录ID
     */
    @TableField(value = "rulecatalogid")
    @Schema(description="所属规则目录ID")
    @Size(max = 36,message = "所属规则目录IDmax length should less than 36")
    @NotBlank(message = "所属规则目录IDis not blank")
    private String rulecatalogid;

    @TableField(exist = false)
    @Schema(description="规则下包含的规则项")
    private List<SpdpRuleitem> ruleItemList;

    private static final long serialVersionUID = 1L;
}