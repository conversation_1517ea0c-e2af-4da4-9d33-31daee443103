<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.DataLayerFieldStatisticsTypeMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.DataLayerFieldStatisticsType">
    <!--@mbg.generated-->
    <!--@Table public.data_layer_field_statistics_type-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="statistics_type" jdbcType="VARCHAR" property="statisticsType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, statistics_type
  </sql>
</mapper>