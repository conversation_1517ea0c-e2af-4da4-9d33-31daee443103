package com.bjcj.model.dto.safetyManage;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author：qinyi
 * @Date：2023/12/14 9:54
 */
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SysRoleBusinessDto {
    @Schema(description="角色id")
    @NotNull(message = "角色id不能为空")
    private Long roleId;

    @Schema(description="业务数据新增的数据集(id)")
    @NotNull(message = "addIdList不能为空")
    private List<Long> addIdList;

    @Schema(description="业务数据删除的数据集(id)")
    @NotNull(message = "delIdList不能为空")
    private List<Long> delIdList;

}
