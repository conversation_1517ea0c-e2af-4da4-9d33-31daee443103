<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.naturalresources.fileResManage.FileDirMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.naturalresources.fileResManage.FileDir">
    <!--@mbg.generated-->
    <!--@Table public.file_dir-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="file_dir_name" jdbcType="VARCHAR" property="fileDirName" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="parent_dir_name" jdbcType="VARCHAR" property="parentDirName" />
    <result column="parent_dir_id" jdbcType="BIGINT" property="parentDirId" />
    <result column="show_sort" jdbcType="INTEGER" property="showSort" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operater" jdbcType="VARCHAR" property="operater" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, file_dir_name, code, parent_dir_name, parent_dir_id, show_sort, remark, create_time, 
    update_time, operater
  </sql>
</mapper>