package com.bjcj.web.naturalresources.fileResManage;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.fileResManage.FileInfoDto;
import com.bjcj.model.po.naturalresources.fileResManage.FileInfo;
import com.bjcj.service.naturalresources.fileResManage.FileInfoService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;


/**
* 文件信息表(public.file_info)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/fileInfo")
@Tag(name = "文件信息")
@Validated
public class FileInfoController {
    /**
    * 服务对象
    */
    @Resource
    private FileInfoService fileInfoService;

    @OperaLog(operaModule = "文件资源-删除",operaType = OperaLogConstant.DELETE,operaDesc = "文件资源-删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del")
    @Operation(summary = "删除", description = "根据id删除")
    @Parameters({
            @Parameter(name = "id", description = "id", required = true)
    })
    @ApiOperationSupport(order = 1)
    public JsonResult del(@RequestParam("id") Long id) {
        return this.fileInfoService.removeById(id)? JsonResult.success("删除成功"): JsonResult.error("删除失败");
    }

    @OperaLog(operaModule = "文件资源-编辑",operaType = OperaLogConstant.UPDATE,operaDesc = "文件资源-编辑")
    @SaCheckPermission("sys:write")
    @PostMapping("/edit")
    @Operation(summary = "编辑", description = "编辑")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "edit")
    public JsonResult edit(@Validated @RequestBody FileInfoDto dto) {
        if (dto.getId() == null) {
            return JsonResult.error("id不能为空");
        }
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        FileInfo fileInfo = BeanUtil.copyProperties(dto, FileInfo.class);
        fileInfo.setOperator(username);
        fileInfo.setUpdateTime(LocalDateTime.now());
        return this.fileInfoService.updateById(fileInfo) ? JsonResult.success("编辑成功") : JsonResult.error("编辑失败");
    }




}
