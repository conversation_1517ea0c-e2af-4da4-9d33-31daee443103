package com.bjcj.mapper.safetyManage;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.safetyManage.SafetyInstitution;
import org.apache.ibatis.annotations.Param;


/**
*@Author：qinyi
*@Package：com.bjcj.mapper.safetyManage
*@Project：geocloud2
*@name：SafetyInstitutionMapper
*@Date：2023/11/23  10:43
*@Filename：SafetyInstitutionMapper
*/
public interface SafetyInstitutionMapper extends BaseMapper<SafetyInstitution> {
    String selectNameByUserId(@Param("userId") Long userId);
}