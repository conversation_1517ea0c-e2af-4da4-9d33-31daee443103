package com.bjcj.service.naturalresources.resReview;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.resReview.UserServrReviewMapper;
import com.bjcj.model.dto.resReview.UserServrReviewDto;
import com.bjcj.model.dto.resReview.UserServrReviewDto2;
import com.bjcj.model.po.naturalresources.resReview.UserServrReview;
import com.bjcj.model.po.naturalresources.resReview.UserServrReview2;
import com.bjcj.service.cloudportal.SysUserMessageService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

/**
 *@Author：qinyi
 *@Date：2024/1/17  16:00
*/
@Service
public class UserServrReviewService extends ServiceImpl<UserServrReviewMapper, UserServrReview> {

    @Resource
    private UserServrReviewMapper userServrReviewMapper;

    @Resource
    private SysUserMessageService sysUserMessageService;

    public JsonResult apply(UserServrReviewDto dto) {
        UserServrReview userServrReview = BeanUtil.copyProperties(dto, UserServrReview.class);
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        userServrReview.setCreateTime(LocalDateTime.now());
        userServrReview.setUserId(userId);
        return this.userServrReviewMapper.insert(userServrReview) > 0 ? JsonResult.success() : JsonResult.error();
    }

    public JsonResult review(UserServrReviewDto2 dto) {
        UserServrReview userServrReview = BeanUtil.copyProperties(dto, UserServrReview.class);
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        userServrReview.setReviewTime(LocalDateTime.now());
        userServrReview.setReviewUserId(userId);
        this.sysUserMessageService.saveReviewSysh(userServrReview, userId);
        return this.userServrReviewMapper.updateById(userServrReview) > 0 ? JsonResult.success() : JsonResult.error();
    }

    public JsonResult delBatch(List<Long> idList) {
        return this.userServrReviewMapper.deleteBatchIds(idList) > 0 ? JsonResult.success() : JsonResult.error();
    }

    public JsonResult<Page<UserServrReview>> queryPage(Page<UserServrReview> pager, String searchStr, String reviewStatus, String serviceType, String startTime, String endTime, String orders) {
        if(orders == null || orders.equals("")){
            orders = "desc";
        }
        Timestamp s = null;
        Timestamp e = null;
        if(startTime!=null && !"".equals(startTime) && endTime!=null && !"".equals(endTime)){
            startTime = startTime+" 00:00:00";
            endTime = endTime+" 23:59:59";
            s = Timestamp.valueOf(startTime);
            e = Timestamp.valueOf(endTime);
        }
        int status = 0;
        if(null != reviewStatus && !"".equals(reviewStatus)){
            status = Integer.parseInt(reviewStatus);
        }
        Long current = (pager.getCurrent() - 1) * pager.getSize();
        Long size = pager.getSize();
        List<UserServrReview> pageList = this.userServrReviewMapper.selectPageData(current,size,searchStr,status,serviceType, s,e,orders);
        int count = this.userServrReviewMapper.selectPageDataCount(current,size,searchStr,status,serviceType,s,e,orders);
        Page<UserServrReview> page = new Page<>();
        page.setRecords(pageList);
        page.setTotal(count);
        return JsonResult.success(page);
    }

    public List<UserServrReview2> selectListMap(Long userId, String serverid) {
        return this.userServrReviewMapper.selectListMap(userId, serverid);
    }
}
