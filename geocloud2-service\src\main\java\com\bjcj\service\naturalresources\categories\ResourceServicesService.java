package com.bjcj.service.naturalresources.categories;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.categories.CategoriesServiceGroupMapper;
import com.bjcj.mapper.naturalresources.categories.ResourceDataitemsMapper;
import com.bjcj.mapper.naturalresources.categories.ResourceServicesMapper;
import com.bjcj.mapper.naturalresources.categories.ResourceTagsMapper;
import com.bjcj.mapper.system.SysXzq14Mapper;
import com.bjcj.model.po.naturalresources.categories.ResourceDataitems;
import com.bjcj.model.po.naturalresources.categories.ResourceServices;
import com.bjcj.model.po.naturalresources.categories.ResourceTags;
import com.bjcj.model.vo.naturalresources.categories.CategoriesParamVo;
import com.bjcj.model.vo.naturalresources.categories.ResourceServicesVo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 *@Author：qinyi
 *@Date：2024/4/25  9:19
*/
@Service
public class ResourceServicesService extends ServiceImpl<ResourceServicesMapper, ResourceServices> {

    @Resource
    private ResourceServicesMapper resourceServicesMapper;

    @Resource
    private ResourceDataitemsMapper resourceDataitemsMapper;

    @Resource
    private SysXzq14Mapper sysXzq14Mapper;

    @Resource
    private CategoriesServiceGroupMapper categoriesServiceGroupMapper;

    @Resource
    private ResourceTagsMapper resourceTagsMapper;

    public JsonResult<IPage<ResourceServicesVo>> lists(CategoriesParamVo categoriesParamVo) {
        String name = categoriesParamVo.getName();
        String id = categoriesParamVo.getId();
        int auth = categoriesParamVo.getAuth();
        int current = categoriesParamVo.getCurrent();
        int size = categoriesParamVo.getSize();
        Long roleId = categoriesParamVo.getRoleId();
        Page<ResourceServicesVo> page = new Page<ResourceServicesVo>(current, size);

        List<String> ids = new ArrayList<String>();

        LambdaQueryWrapper<ResourceDataitems> wrapper = new LambdaQueryWrapper<ResourceDataitems>();
        //安全0 公开1 私有2
        wrapper.like(StringUtils.isNotBlank(id),ResourceDataitems::getResourcecatalogid, id)
                .ne(ResourceDataitems::getAuthoritytype,1);  //非公开的数据
        List<ResourceDataitems> itemList = resourceDataitemsMapper.selectList(wrapper);
        if(itemList.size() > 0){
            itemList.stream().forEach(item -> {
                ids.add(item.getId().trim());
            });
            IPage<ResourceServicesVo> list = resourceServicesMapper.lists(name,id,ids,auth,roleId,page);
            return JsonResult.success(list);
        }else{
            IPage<ResourceServicesVo> list = new Page<>();
            return JsonResult.success(list);
        }

    }

    public JsonResult<List<ResourceServices>> findById(String id) {
        LambdaQueryWrapper<ResourceServices> wrapper = new LambdaQueryWrapper<ResourceServices>();
        wrapper.eq(ResourceServices::getDataitemid, id);
        List<ResourceServices> list = resourceServicesMapper.selectList(wrapper);
        list.stream().forEach(item -> {
            if(StringUtils.isNotBlank(item.getServicegroupid())){
                Long groupid = Long.parseLong(item.getServicegroupid());
                item.setGroupName(this.categoriesServiceGroupMapper.selectById(groupid).getShowName());
            }
            List<ResourceTags> tags = this.resourceTagsMapper.selectList(new LambdaQueryWrapper<ResourceTags>().eq(ResourceTags::getResourceid, item.getId()));
            //List转String逗号分隔
            if(!tags.isEmpty()) item.setResourceTags(tags.stream().map(ResourceTags::getName).reduce((a, b) -> a + "," + b).orElse(""));
        });
        return JsonResult.success(list);
    }

    public JsonResult uptStatus(String id, Integer status) {
        LambdaUpdateWrapper<ResourceServices> wrapper = new LambdaUpdateWrapper<ResourceServices>();
        wrapper.set(ResourceServices::getStatus, status)
                .eq(ResourceServices::getId, id);
        return resourceServicesMapper.update(wrapper) > 0 ? JsonResult.success() : JsonResult.error();
    }

    public JsonResult updShow(String id, Boolean show) {
        LambdaUpdateWrapper<ResourceServices> wrapper = new LambdaUpdateWrapper<ResourceServices>();
        wrapper.set(ResourceServices::getIsvisiable, show)
                .eq(ResourceServices::getId, id);
        return resourceServicesMapper.update(wrapper) > 0 ? JsonResult.success() : JsonResult.error();
    }


    public JsonResult updateData(ResourceServices resourceServices) {
        this.resourceServicesMapper.updateById(resourceServices);
        String tags = resourceServices.getResourceTags();
        this.resourceTagsMapper.delete(new LambdaQueryWrapper<ResourceTags>().eq(ResourceTags::getResourceid, resourceServices.getId()));
        if(StringUtils.isNotBlank(tags)){
            List<String> tagList = Arrays.asList(tags.split(","));
            tagList.forEach(item -> {
                ResourceTags tag = new ResourceTags();
                tag.setResourcecategory(6);
                tag.setResourceid(resourceServices.getId());
                tag.setName(item);
                this.resourceTagsMapper.insert(tag);
            });
        }
        return JsonResult.success();
    }

    public JsonResult<Page<ResourceServices>> listData(Page<ResourceServices> pager, String searchStr, String resourcecatalogid) {
        LambdaQueryWrapper<ResourceServices> wrapper = new LambdaQueryWrapper<ResourceServices>();
        wrapper.eq(ResourceServices::getResourcecategory,1).eq(ResourceServices::getResourcecatalogid,resourcecatalogid)
                .like(StringUtils.isNotBlank(searchStr),ResourceServices::getDisplayname,searchStr).orderByAsc(ResourceServices::getDisplayorder);
        Page<ResourceServices> resourceServicesPage = resourceServicesMapper.selectPage(pager, wrapper);
        //写入标签数据,逗号拼接
        resourceServicesPage.getRecords().forEach(item -> {
            List<ResourceTags> tags = this.resourceTagsMapper.selectList(new LambdaQueryWrapper<ResourceTags>().eq(ResourceTags::getResourceid, item.getId()));
            if(!tags.isEmpty()) item.setResourceTags(tags.stream().map(ResourceTags::getName).reduce((a, b) -> a + "," + b).orElse(""));
        });
        return JsonResult.success(resourceServicesPage);
    }

    public JsonResult saveOrUpdateData(ResourceServices resourceServices) {
        boolean b = this.saveOrUpdate(resourceServices);
        this.resourceTagsMapper.delete(new LambdaQueryWrapper<ResourceTags>().eq(ResourceTags::getResourceid, resourceServices.getId()));
        if(StringUtils.isNotBlank(resourceServices.getResourceTags())){
            String[] split = resourceServices.getResourceTags().split(",");
            //split转list
            List<String> tags = Arrays.asList(split);
            tags.forEach(item -> {
                ResourceTags tag = new ResourceTags();
                tag.setResourcecategory(1);
                tag.setResourceid(resourceServices.getId());
                tag.setName(item);
                this.resourceTagsMapper.insert(tag);
            });
        }
        return b ? JsonResult.success() : JsonResult.error();
    }

    public JsonResult<ResourceServices> detail(String id) {
        return JsonResult.success(this.getById(id));
    }

}
