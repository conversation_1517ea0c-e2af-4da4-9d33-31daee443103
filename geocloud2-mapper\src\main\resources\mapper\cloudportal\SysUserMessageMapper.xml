<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.cloudportal.SysUserMessageMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.cloudportal.SysUserMessage">
    <!--@mbg.generated-->
    <!--@Table public.sys_user_message-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="send_user_id" jdbcType="BIGINT" property="sendUserId" />
    <result column="receive_user_id" jdbcType="BIGINT" property="receiveUserId" />
    <result column="is_read" jdbcType="BOOLEAN" property="isRead" />
    <result column="creat_time" jdbcType="TIMESTAMP" property="creatTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, content, send_user_id, receive_user_id, is_read, creat_time, update_time
  </sql>
</mapper>