package com.bjcj.web.naturalresources.products;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.naturalresources.products.ProductsData;
import com.bjcj.model.vo.naturalresources.categories.CategoriesParamVo;
import com.bjcj.model.vo.naturalresources.products.ProductDataVo;
import com.bjcj.service.naturalresources.products.ProductsDataService;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/5 14:08 周二
 */
@Tag(name = "数据产品管理（数据产品信息）",description = "数据产品管理（数据产品信息）")
@ApiSupport(order = 59)
@RestController
@Slf4j
@RequestMapping(value = "/products_data")
public class ProductsDataController {

    @Resource
    private ProductsDataService productsDataService;

    @SaCheckPermission("sys:read")
    @Operation(summary = "数据产品数据列表")
    @GetMapping(value = "/lists")
    public JsonResult lists(@RequestParam String id, String name, int current, int size){

        return productsDataService.lists(id, name, current, size);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "角色权限数据产品数据列表")
    @PostMapping(value = "/listsAuth")
    public JsonResult<IPage<ProductDataVo>> listsAuth(@RequestBody CategoriesParamVo categoriesParamVo){

        return productsDataService.listsAuth(categoriesParamVo);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "数据产品数据注册",
            operaType = OperaLogConstant.CREATE,
            operaDesc = "数据产品数据注册"
    )
    @Operation(summary = "数据产品数据注册")
    @PostMapping(value = "/save")
    public JsonResult save(@RequestBody @Validated ProductsData productsData){
        boolean result = productsDataService.save(productsData);
        if(!result){
            return JsonResult.error();
        }
        return JsonResult.success(productsData.getId());
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "数据产品数据修改",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "数据产品数据修改"
    )
    @Operation(summary = "数据产品数据修改")
    @PutMapping(value = "/update")
    public JsonResult update(@RequestBody @Validated ProductsData productsData){
        boolean result = productsDataService.saveOrUpdate(productsData);
        if(!result){
            return JsonResult.error();
        }
        return JsonResult.success();
    }

    @SaCheckPermission("sys:del")
    @OperaLog(
            operaModule = "数据产品数据删除",
            operaType = OperaLogConstant.DELETE,
            operaDesc = "数据产品数据删除"
    )
    @Operation(summary = "数据产品数据删除")
    @DeleteMapping(value = "/del")
    public JsonResult del(@RequestParam List<Long> id){

        return productsDataService.del(id);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "数据产品状态修改",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "数据产品状态修改"
    )
    @Operation(summary = "数据产品状态修改")
    @PostMapping(value = "/uptStatus")
    public JsonResult uptStatus(@RequestParam Long id, Boolean status){

        return productsDataService.uptStatus(id, status);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "数据产品权限修改",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "数据产品权限修改"
    )
    @Operation(summary = "数据产品权限修改")
    @PostMapping(value = "/uptAuth")
    public JsonResult uptAuth(@RequestParam Long id, int auth){

        return productsDataService.uptAuth(id, auth);
    }

}
