package com.bjcj.model.po.naturalresources.function;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/12/1 9:21 周五
 */
@Schema(description="功能服务信息（元数据）")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "function_provider")
public class FunctionProvider implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="", hidden = true)
    private Long id;

    @TableField(value = "parent_id")
    @Schema(description="父级id")
    private Long parentId;

    @TableField(value = "name")
    @Schema(description="提供者名称")
    private String name;

    @TableField(value = "address")
    @Schema(description="提供者地址")
    private String address;

    @TableField(value = "phone")
    @Schema(description="提供者电话")
    private String phone;

    @TableField(value = "email")
    @Schema(description="提供者邮箱")
    private String email;

    @TableField(value = "request_method")
    @Schema(description="请求方式")
    private String requestMethod;

    @TableField(value = "return_result")
    @Schema(description="返回结果")
    private String returnResult;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description="注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "del")
    @Schema(description="删除状态（0未删除，1已删除）")
    private int del;

}
