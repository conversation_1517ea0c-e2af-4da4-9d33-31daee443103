<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.safetyManage.SysPermissionMapper">
    <resultMap id="BaseResultMap" type="com.bjcj.model.po.safetyManage.SysPermission">
        <!--@mbg.generated-->
        <!--@Table public.sys_permission-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, code, "name"
    </sql>

    <select id="selectAll" resultMap="BaseResultMap">
        select *
        from sys_permission
    </select>

    <select id="findByUserId" parameterType="long" resultType="string">
        select sp.code
        from sys_role_perms srp
        left join sys_permission sp on sp.id = srp.perms_id
        left join sys_user_role sur on sur.role_id = srp.role_id
        where sur.user_id = #{userId}
    </select>

    <select id="getPermissionByUserId" resultMap="BaseResultMap">
        select sp.*
        from sys_user_role sur
        left join sys_role_perms srp on srp.role_id = sur.role_id
        left join sys_permission sp on sp.id = srp.perms_id
        <where>
            <if test="userId != null">
                sur.user_id = #{userId}
            </if>
        </where>
        order by sp.id
    </select>
</mapper>