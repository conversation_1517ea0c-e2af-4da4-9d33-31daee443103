package com.bjcj.model.dto.platformConf;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/2/28  16:24
*/

/**
    * GIS server网站配置表
    */
@Schema(description="GIS server网站配置表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GisResConfDto implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 站点名称
     */
    @TableField(value = "website_name")
    @Schema(description="站点名称")
    @Size(max = 100,message = "站点名称max length should less than 100")
    @NotBlank(message = "站点名称is not blank")
    @RequestKeyParam(name = "websiteName")
    private String websiteName;

    /**
     * 站点基地址url
     */
    @TableField(value = "website_base_url")
    @Schema(description="站点基地址url")
    @Size(max = 255,message = "站点基地址urlmax length should less than 255")
    @NotBlank(message = "站点基地址urlis not blank")
    private String websiteBaseUrl;

    /**
     * 是否WebAdaptor服务器0否1是
     */
    @TableField(value = "webadaptor_server")
    @Schema(description="是否WebAdaptor服务器0否1是")
    @NotBlank(message = "是否WebAdaptor服务器0否1是is not blank")
    private String webadaptorServer;

    /**
     * 站点用户名
     */
    @TableField(value = "website_user_name")
    @Schema(description="站点用户名")
    @Size(max = 50,message = "站点用户名max length should less than 50")
    @NotBlank(message = "站点用户名is not blank")
    private String websiteUserName;

    /**
     * 站点密码
     */
    @TableField(value = "website_password")
    @Schema(description="站点密码")
    @Size(max = 100,message = "站点密码max length should less than 100")
    @NotBlank(message = "站点密码is not blank")
    private String websitePassword;

    /**
     * 缓存路径
     */
    @TableField(value = "cache_url")
    @Schema(description="缓存路径")
    @Size(max = 255,message = "缓存路径max length should less than 255")
    private String cacheUrl;

    /**
     * 站点状态0异常1正常
     */
    @TableField(value = "website_state")
    @Schema(description="站点状态0异常1正常")
    @NotBlank(message = "站点状态0异常1正常is not blank")
    private String websiteState;

    /**
     * 拥有者名称
     */
    @TableField(value = "owner_name")
    @Schema(description="拥有者名称")
    @Size(max = 50,message = "拥有者名称max length should less than 50")
    private String ownerName;

    /**
     * 拥有者单位
     */
    @TableField(value = "owner_unit")
    @Schema(description="拥有者单位")
    @Size(max = 100,message = "拥有者单位max length should less than 100")
    private String ownerUnit;

    /**
     * 拥有者联系方式
     */
    @TableField(value = "owner_phone")
    @Schema(description="拥有者联系方式")
    @Size(max = 11,message = "拥有者联系方式max length should less than 11")
    private String ownerPhone;

    /**
     * 拥有者E-mail
     */
    @TableField(value = "owner_email")
    @Schema(description="拥有者E-mail")
    @Size(max = 50,message = "拥有者E-mailmax length should less than 50")
    private String ownerEmail;

    private static final long serialVersionUID = 1L;
}