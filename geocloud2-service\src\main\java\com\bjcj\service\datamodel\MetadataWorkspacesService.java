package com.bjcj.service.datamodel;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.FieldType;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.datamodel.MetadataTablestructurefieldsMapper;
import com.bjcj.mapper.datamodel.MetadataTablestructuresMapper;
import com.bjcj.mapper.datamodel.MetadataWorkspacesMapper;
import com.bjcj.model.dto.datamodel.MetadataWorkspacesDto;
import com.bjcj.model.po.datamodel.MetadataTablestructurefields;
import com.bjcj.model.po.datamodel.MetadataTablestructures;
import com.bjcj.model.po.datamodel.MetadataWorkspaces;
import jakarta.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.*;

/**
 *@Author：qinyi
 *@Date：2024/4/18  15:29
*/
@Service
public class MetadataWorkspacesService extends ServiceImpl<MetadataWorkspacesMapper, MetadataWorkspaces> {

    Logger logger = LoggerFactory.getLogger(MetadataWorkspacesService.class);

    @Resource
    MetadataWorkspacesMapper metadataWorkspacesMapper;

    @Resource
    MetadataTablestructuresMapper metadataTablestructuresMapper;

    @Resource
    MetadataTablestructurefieldsMapper metadataTablestructurefieldsMapper;

    public JsonResult<Page<MetadataWorkspaces>> pageDataSort(Page<MetadataWorkspaces> pager, String searchStr) {
        return JsonResult.success(this.metadataWorkspacesMapper.selectPage(pager, new LambdaQueryWrapper<MetadataWorkspaces>().like(StringUtils.isNotBlank(searchStr),MetadataWorkspaces::getName, searchStr)));
    }


    public JsonResult testLink(MetadataWorkspacesDto dto) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            String url = dto.getInstance().replace("sde", "jdbc").replace("$", ":thin:@//").replace("/orcl", ":1521/orcl");
            Connection conn = DriverManager.getConnection(url, dto.getUsername(), dto.getPassword());

                return JsonResult.success("连接成功");

        }catch (Exception e) {
            return JsonResult.error(e.getMessage());
        }
    }

    public JsonResult datasetList(String workspaceid) {
        MetadataWorkspaces workSpace = metadataWorkspacesMapper.selectById(workspaceid);
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            // *****************************************
            // sde:oracle$192.168.3.2/orcl
            String url = workSpace.getInstance().replace("sde", "jdbc").replace("$", ":thin:@//").replace("/orcl", ":1521/orcl");
            Connection conn = DriverManager.getConnection(url, workSpace.getUsername(), workSpace.getPassword());
            logger.info("db连接成功");
            Statement stmt = conn.createStatement();
            String query = "select *  from GDB_ITEMTYPES where NAME='Feature Dataset'";
            ResultSet rs = stmt.executeQuery(query);
            String UUID_TYPE = "";
            while (rs.next()) {
                UUID_TYPE = rs.getString("UUID");
            }
            String query2 = "select * from GDB_ITEMS where TYPE='" + UUID_TYPE + "'";
            ResultSet rs2 = stmt.executeQuery(query2);
            HashMap<String, List<String>> datasetMap = new HashMap<String, List<String>>();
            List<String> datasetNameList = new ArrayList<String>();
            while (rs2.next()) {
                String datasetName = rs2.getString("NAME");
                datasetNameList.add(datasetName);
            }
            datasetMap.put("dataset",datasetNameList);
            stmt.close();
            rs.close();
            rs2.close();
            conn.close();
            return JsonResult.success(datasetMap);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public JsonResult queryLayersByDatasetname(String datasetName,String workspaceid) {
        MetadataWorkspaces workSpace = metadataWorkspacesMapper.selectById(workspaceid);
        try{
            Class.forName("oracle.jdbc.driver.OracleDriver");
            //*****************************************
            //sde:oracle$192.168.3.2/orcl
            String url = workSpace.getInstance().replace("sde", "jdbc").replace("$", ":thin:@//").replace("/orcl",":1521/orcl");
            Connection conn= DriverManager.getConnection(url,workSpace.getUsername(),workSpace.getPassword());
            logger.info("db连接成功");
            Statement stmt=conn.createStatement();
            datasetName = "%\\"+datasetName+"\\%";
            String query = "select * from GDB_ITEMS where path like '"+datasetName+"'";
            ResultSet rs = stmt.executeQuery(query);
            HashMap<String ,List<String>> pathMap = new HashMap<String ,List<String>>();
            List<String> pathList = new ArrayList<String>();
            while (rs.next()){
                String path = rs.getString("path");
                path = path.split("\\\\")[2];
                path = path.split("\\.")[1];
                pathList.add(path);
            }
            pathMap.put("layerPath",pathList);
            stmt.close();
            rs.close();
            conn.close();
            return JsonResult.success(pathMap);
        }catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public JsonResult queryAllTables(String workspaceid) {
        MetadataWorkspaces workSpace = metadataWorkspacesMapper.selectById(workspaceid);
        try{
            Class.forName("oracle.jdbc.driver.OracleDriver");
            //*****************************************
            //sde:oracle$192.168.3.2/orcl
            String url = workSpace.getInstance().replace("sde", "jdbc").replace("$", ":thin:@//").replace("/orcl",":1521/orcl");
            Connection conn= DriverManager.getConnection(url,workSpace.getUsername(),workSpace.getPassword());
            logger.info("db连接成功");
            Statement stmt=conn.createStatement();
            String query = "SELECT table_name FROM user_tables";
            ResultSet rs = stmt.executeQuery(query);
            HashMap<String ,List<String>> pathMap = new HashMap<String ,List<String>>();
            List<String> nameList = new ArrayList<String>();
            while (rs.next()){
                String table_name = rs.getString("TABLE_NAME");
                nameList.add(table_name);
            }
            pathMap.put("tableNames",nameList);
            stmt.close();
            rs.close();
            conn.close();
            return JsonResult.success(pathMap);
        }catch (Exception e) {
            throw new RuntimeException(e);
        }
    }



    public JsonResult importLayerData(String workspaceid, String layerName,String datastandardid) throws Exception{
        MetadataTablestructures metadataTablestructures = null;
        List<MetadataTablestructures> layers = new ArrayList<MetadataTablestructures>();
        if(layerName.split(",").length > 1){
            List<String> layerNameList = Arrays.asList(layerName.split(","));
            layers = this.metadataTablestructuresMapper.selectList(new LambdaQueryWrapper<MetadataTablestructures>().in(MetadataTablestructures::getName, layerNameList));
        }else {
            metadataTablestructures = this.metadataTablestructuresMapper.selectOne(new LambdaQueryWrapper<MetadataTablestructures>().eq(MetadataTablestructures::getName, layerName));
        }

        if(Objects.nonNull(metadataTablestructures) || layers.size() > 0){
            return JsonResult.error("图层名称已存在!");
        }
        int count = layerName.split(",").length;
        int insert_process = 0;
        for(int i=0;i<count;i++){
            this.importData(workspaceid,layerName.split(",")[i],datastandardid);
            insert_process++;
            logger.info("批量总写入进度："+insert_process+"/"+count);
        }
        return JsonResult.success("写入成功");
    }


    private void importData(String workspaceid, String layerName,String datastandardid) throws Exception {
        MetadataWorkspaces workSpace = metadataWorkspacesMapper.selectById(workspaceid);
        Connection conn = null;
        Class.forName("oracle.jdbc.driver.OracleDriver");
        //*****************************************
        //sde:oracle$192.168.3.2/orcl
        String url = workSpace.getInstance().replace("sde", "jdbc").replace("$", ":thin:@//").replace("/orcl",":1521/orcl");
        conn= DriverManager.getConnection(url,workSpace.getUsername(),workSpace.getPassword());
        // // 关闭自动提交
        // conn.setAutoCommit(false);
        logger.info("db连接成功");
        int count = 0;
        Statement stmt=conn.createStatement();
        String query = "select column_name,data_type,data_length,nullable from user_tab_columns where REGEXP_LIKE(table_name,'"+layerName+"','i') order by column_id asc";

        String query3 = "select count(*) as count from (select column_name,data_type,data_length,nullable from user_tab_columns where REGEXP_LIKE(table_name,'"+layerName+"','i') order by column_id asc) c";
        ResultSet rs3 = stmt.executeQuery(query3);
        while (rs3.next()){
            count = rs3.getInt("count");
        }
        //查询图层类型
        String query2 = "select GEOMETRY_TYPE from GEOMETRY_COLUMNS where REGEXP_LIKE(F_TABLE_NAME,'"+layerName+"','i')";
        ResultSet rs2 = stmt.executeQuery(query2);
        Integer dataLayerTypeId = 0;
        while (rs2.next()){
            dataLayerTypeId = rs2.getInt("geometry_type");
        }
        //写入图层
        MetadataTablestructures dataLayer = new MetadataTablestructures();
        dataLayer.setName(layerName);
        dataLayer.setDisplayname(layerName);
        // dataLayer.setCreateTime(LocalDateTime.now());
        dataLayer.setDatastandardid(datastandardid);
        dataLayer.setTabletype(dataLayerTypeId);
        dataLayer.setDisplayorder(0);
        // dataLayer.setOperator(principal.getUsername());
        int insert = this.metadataTablestructuresMapper.insert(dataLayer);
        int insert_process = 0;
        ResultSet rs = stmt.executeQuery(query);
        while (rs.next()){
            String columnName = rs.getString("column_name");
            String dataType = rs.getString("data_type");
            String dataLength = rs.getString("data_length");
            String nullable = rs.getString("nullable");
            logger.info(columnName+" "+dataType+" "+dataLength+" "+nullable);
            if(insert > 0){
                //写入图层字段
                MetadataTablestructurefields dataLayerField = new MetadataTablestructurefields();
                dataLayerField.setName(columnName);
                dataLayerField.setDisplayname(columnName);
                Long dataTypeId = 5L;
                if(columnName.equals("OBJECTID")){
                    dataTypeId = FieldType.OID.typeid();
                }
                if(dataType.equals("NVARCHAR2")){
                    dataTypeId = FieldType.STRING.typeid();
                }
                if(dataType.equals("NUMBER")){
                    dataTypeId = FieldType.INTEGER.typeid();
                }
                if(dataType.equals("ST_GEOMETRY")){
                    dataTypeId = FieldType.GEOMETRY.typeid();
                }
                dataLayerField.setFieldtype(dataTypeId);
                dataLayerField.setTablestructureid(this.metadataTablestructuresMapper.selectOne(new LambdaQueryWrapper<MetadataTablestructures>().eq(MetadataTablestructures::getName, layerName)).getTablestructureid());
                // dataLayerField.s(datastandardid);
                dataLayerField.setIsnullable(true);
                dataLayerField.setRequired(false);
                dataLayerField.setDisplayorder(0);
                this.metadataTablestructurefieldsMapper.insert(dataLayerField);
                insert_process++;
                logger.info("写入进度:"+insert_process+"/"+count);
            }
        }
        if(insert_process != count){
            conn.rollback();
            stmt.close();
            rs.close();
            rs2.close();
            rs3.close();
            conn.close();
            throw new RuntimeException("写入失败");
        }
        stmt.close();
        rs.close();
        rs2.close();
        rs3.close();
        conn.close();
    }

}
