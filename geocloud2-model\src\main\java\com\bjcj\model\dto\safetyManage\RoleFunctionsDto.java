package com.bjcj.model.dto.safetyManage;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author：qinyi
 * @Date：2023/12/13 16:40
 */
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RoleFunctionsDto {

    @Schema(description="角色id")
    @NotNull(message = "角色id不能为空")
    private Long roleId;

    @Schema(description="应用服务新增的数据集(id)")
    @NotNull(message = "addIdList不能为空")
    private List<String> addIdList;

    @Schema(description="应用服务删除的数据集(id)")
    @NotNull(message = "delIdList不能为空")
    private List<String> delIdList;

}
