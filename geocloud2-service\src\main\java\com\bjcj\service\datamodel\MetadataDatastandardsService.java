package com.bjcj.service.datamodel;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.datamodel.MetadataDatastandardsMapper;
import com.bjcj.model.dto.datamodel.MetadataDatastandardsDto;
import com.bjcj.model.po.datamodel.MetadataDatastandards;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 *@Author：qinyi
 *@Date：2024/4/19  9:38
*/
@Service
public class MetadataDatastandardsService extends ServiceImpl<MetadataDatastandardsMapper, MetadataDatastandards> {

    @Resource
    MetadataDatastandardsMapper metadataDatastandardsMapper;

    public JsonResult saveData(MetadataDatastandardsDto dto) {
        MetadataDatastandards metadataDatastandards = BeanUtil.copyProperties(dto, MetadataDatastandards.class);
        if(Objects.isNull(metadataDatastandards.getDatastandardid())){
            //验证名称或显示名称重复数据
            LambdaQueryWrapper<MetadataDatastandards> queryWrapper = new LambdaQueryWrapper<MetadataDatastandards>();
            queryWrapper.and(i -> i.eq(MetadataDatastandards::getName,metadataDatastandards.getName()).or().eq(MetadataDatastandards::getDisplayname,metadataDatastandards.getDisplayname()));
            List<MetadataDatastandards> list = metadataDatastandardsMapper.selectList(queryWrapper);
            if(!list.isEmpty()){
                return JsonResult.error("名称或显示名称重复");
            }
            metadataDatastandardsMapper.insert(metadataDatastandards);
        }else{
            metadataDatastandardsMapper.updateById(metadataDatastandards);
        }
        return JsonResult.success();
    }

    public JsonResult<List<MetadataDatastandards>> queryList(String searchStr) {
        LambdaQueryWrapper<MetadataDatastandards> queryWrapper = new LambdaQueryWrapper<MetadataDatastandards>();
        if(Objects.nonNull(searchStr) && !"".equals(searchStr)){
            queryWrapper.and(i -> i.like(MetadataDatastandards::getName, searchStr).or().like(MetadataDatastandards::getDisplayname, searchStr));
        }
        queryWrapper.orderByDesc(MetadataDatastandards::getDisplayorder);
        return JsonResult.success(metadataDatastandardsMapper.selectList(queryWrapper));

    }

    public JsonResult delData(String datastandardid) {
        int i = this.metadataDatastandardsMapper.deleteById(datastandardid);
        if(i>0){
            return JsonResult.success();
        }else{
            return JsonResult.error("删除失败");
        }
    }
}
