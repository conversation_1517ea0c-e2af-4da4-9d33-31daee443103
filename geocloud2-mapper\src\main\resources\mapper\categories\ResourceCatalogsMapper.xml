<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.naturalresources.categories.ResourceCatalogsMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.naturalresources.categories.ResourceCatalogs">
    <!--@mbg.generated-->
    <!--@Table public.resource_catalogs-->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="displayorder" jdbcType="NUMERIC" property="displayorder" />
    <result column="parentid" jdbcType="CHAR" property="parentid" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="resourcecategory" jdbcType="NUMERIC" property="resourcecategory" />
    <result column="resourcecount" jdbcType="NUMERIC" property="resourcecount" />
    <result column="usabledresourcecount" jdbcType="NUMERIC" property="usabledresourcecount" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="isvisiable" jdbcType="BOOLEAN" property="isvisiable" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", displayorder, parentid, description, resourcecategory, resourcecount, 
    usabledresourcecount, code, isvisiable
  </sql>

  <select id="findSelfAndChildrenById" resultType="com.bjcj.model.po.naturalresources.categories.ResourceCatalogs">
    WITH RECURSIVE cata_data as (SELECT * FROM resource_catalogs WHERE id=#{id} UNION ALL SELECT resource_catalogs.* from resource_catalogs INNER JOIN cata_data ON resource_catalogs.parentid = cata_data.id)  select * from cata_data
  </select>
</mapper>