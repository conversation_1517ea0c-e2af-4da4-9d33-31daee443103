package com.bjcj.model.po.safetyManage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
    * 角色与数据产品关联表
    */
@Schema(description="角色与数据产品关联表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_role_prod")
public class SysRoleProd implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

    /**
     * 数据产品id
     */
    @TableField(value = "product_id")
    @Schema(description="数据产品id")
    private Long productId;

    /**
     * 角色id
     */
    @TableField(value = "role_id")
    @Schema(description="角色id")
    private Long roleId;

    /**
     * 授权角色
     */
    @TableField(value = "from_role_name")
    @Schema(description="授权角色")
    private String fromRoleName;

    private static final long serialVersionUID = 1L;
}