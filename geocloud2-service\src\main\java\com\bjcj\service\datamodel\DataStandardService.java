package com.bjcj.service.datamodel;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.datamodel.DataStandardMapper;
import com.bjcj.model.dto.datamodel.DataStandardDto;
import com.bjcj.model.po.datamodel.DataStandard;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 *@Author：qinyi
 *@Date：2024/1/3  15:20
*/
@Service
public class DataStandardService extends ServiceImpl<DataStandardMapper, DataStandard> {

    @Resource
    DataStandardMapper dataStandardMapper;

    public JsonResult saveData(DataStandardDto dto) {
        DataStandard dataStandard = BeanUtil.copyProperties(dto, DataStandard.class);
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        if(dataStandard.getId() == null){
            //验证名称或显示名称重复数据
            LambdaQueryWrapper<DataStandard> queryWrapper = new LambdaQueryWrapper<DataStandard>();
            queryWrapper.and(i -> i.eq(DataStandard::getName, dataStandard.getName()).or().eq(DataStandard::getShowName, dataStandard.getShowName()));
            List<DataStandard> list = dataStandardMapper.selectList(queryWrapper);
            if(!list.isEmpty()){
                return JsonResult.error("名称或显示名称重复");
            }
            dataStandard.setOperator(username);
            dataStandard.setCreateTime(LocalDateTime.now());
            dataStandardMapper.insert(dataStandard);
        }else{
            dataStandard.setOperator(username);
            dataStandard.setUpdateTime(LocalDateTime.now());
            dataStandardMapper.updateById(dataStandard);
        }
        return JsonResult.success();
    }

    public JsonResult<List<DataStandard>> queryList(String searchStr) {
        LambdaQueryWrapper<DataStandard> queryWrapper = new LambdaQueryWrapper<DataStandard>();
        if(Objects.nonNull(searchStr) && !"".equals(searchStr)){
            queryWrapper.and(i -> i.like(DataStandard::getName, searchStr).or().like(DataStandard::getShowName, searchStr));
        }
        queryWrapper.orderByDesc(DataStandard::getCreateTime);
        return JsonResult.success(dataStandardMapper.selectList(queryWrapper));
    }

    public JsonResult delData(Long id) {
        int i = this.dataStandardMapper.deleteById(id);
        if(i>0){
            return JsonResult.success();
        }else{
            return JsonResult.error("删除失败");
        }
    }
}