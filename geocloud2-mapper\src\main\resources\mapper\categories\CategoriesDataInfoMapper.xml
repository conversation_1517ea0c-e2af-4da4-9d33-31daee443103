<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.naturalresources.categories.CategoriesDataInfoMapper">

  <select id="selectPageList" resultType="com.bjcj.model.po.naturalresources.categories.CategoriesService">
    select cs.* from categories_service cs
    left join categories_data_info cdi on cdi.id = cs.parent_id
    where 1=1
    <if test="searchStr!= null and searchStr!= ''">
      and ( cs.name like concat('%', #{searchStr}, '%') or cs.sname like concat('%', #{searchStr}, '%') )
    </if>
    <if test="categoriesDataId!= null and categoriesDataId!=''">
      and cdi.catalogue = #{categoriesDataId}
    </if>
    and cdi.del = 0 and cdi.status=true
    order by cs.create_time desc
    LIMIT #{size} OFFSET #{current}

  </select>

  <select id="selectListCount" resultType="java.lang.Long">
    select count(*) from (
    select cs.* from categories_service cs
    left join categories_data_info cdi on cdi.id = cs.parent_id
    where 1=1
    <if test="searchStr!= null and searchStr!= ''">
      and ( cs.name like concat('%', #{searchStr}, '%') or cs.sname like concat('%', #{searchStr}, '%') )
    </if>
    <if test="categoriesDataId!= null and categoriesDataId!=''">
      and cdi.catalogue = #{categoriesDataId}
    </if>
    and cdi.del = 0 and cdi.status=true
    ) c
  </select>

  <update id="updateReviewStatus">
    update categories_data_info set review_status = #{status} where id = #{resId}
    </update>
</mapper>