package com.bjcj.mapper.naturalresources.categories;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.model.po.naturalresources.categories.CategoriesService;
import com.bjcj.model.po.naturalresources.categories.CategoriesService2;
import com.bjcj.model.vo.naturalresources.categories.CategoriesServiceVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/28 17:33 周二
 */
public interface CategoriesServiceMapper extends BaseMapper<CategoriesService> {

    IPage<CategoriesServiceVo> lists(@Param("name") String name,@Param("id") String id,@Param("ids") List<Long> ids,@Param("auth") int auth,@Param("roleId") Long roleId, @Param("page") Page<CategoriesServiceVo> page);

    List<CategoriesService> selectServicePage(@Param("current") long current,@Param("size") long size,@Param("searchStr") String searchStr,@Param("categoriesDataId") String categoriesDataId,@Param("cateIds") List<Long> cateIds);

    Long selectServicePageCount(@Param("searchStr") String searchStr,@Param("categoriesDataId") String categoriesDataId,@Param("cateIds") List<Long> cateIds);

    List<CategoriesService2> selectServicePage2(@Param("current") long current, @Param("size") long size, @Param("datainfoids") List<String> datainfoids, @Param("ordersql") String ordersql, @Param("startYear") String startYear, @Param("endYear") String endYear, @Param("scale") String scale, @Param("publishInstitutionName") String publishInstitutionName);

    int selectServicePage2Count(@Param("current") long current,@Param("size") long size,@Param("datainfoids") List<String> datainfoids,@Param("ordersql") String ordersql,@Param("startYear") String startYear,@Param("endYear") String endYear,@Param("scale") String scale,@Param("publishInstitutionName") String publishInstitutionName);

    int selectServiceCountByIdIn(@Param("datainfoids") List<Long> datainfoids);

    List<CategoriesService2> selectServicePage3(@Param("current") long current, @Param("size") long size, @Param("ordersql") String ordersql, @Param("startYear") String startYear, @Param("endYear") String endYear, @Param("scale") String scale, @Param("publishInstitutionName") String publishInstitutionName,@Param("label") String label);

    int selectServicePage3Count(@Param("current") long current,@Param("size") long size,@Param("ordersql") String ordersql,@Param("startYear") String startYear,@Param("endYear") String endYear,@Param("scale") String scale,@Param("publishInstitutionName") String publishInstitutionName,@Param("label") String label);
}
