package com.bjcj.model.dto.fileResManage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/1/15  16:43
*/

/**
    * 文件目录
    */
@Schema(description="文件目录")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FileDirDto implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 文件目录名称
     */
    @TableField(value = "file_dir_name")
    @Schema(description="文件目录名称")
    @Size(max = 100,message = "文件目录名称max length should less than 100")
    @NotBlank(message = "文件目录名称is not blank")
    @RequestKeyParam(name = "fileDirName")
    private String fileDirName;

    /**
     * 编码
     */
    @TableField(value = "code")
    @Schema(description="编码")
    @Size(max = 50,message = "编码max length should less than 50")
    @NotBlank(message = "编码is not blank")
    private String code;

    /**
     * 上级目录名称
     */
    @TableField(value = "parent_dir_name")
    @Schema(description="上级目录名称")
    @Size(max = 100,message = "上级目录名称max length should less than 100")
    private String parentDirName;

    /**
     * 上级目录id
     */
    @TableField(value = "parent_dir_id")
    @Schema(description="上级目录id")
    private Long parentDirId;

    /**
     * 显示顺序
     */
    @TableField(value = "show_sort")
    @Schema(description="显示顺序")
    private Integer showSort;

    /**
     * 描述
     */
    @TableField(value = "remark")
    @Schema(description="描述")
    @Size(max = 255,message = "描述max length should less than 255")
    private String remark;

    @TableField(value = "create_time")
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "update_time")
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField(value = "operater")
    @Schema(description="")
    private String operater;

    private static final long serialVersionUID = 1L;
}