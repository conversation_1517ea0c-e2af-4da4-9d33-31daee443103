package com.bjcj.model.dto.datamodel;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/7/1  14:52
*/

/**
    * 数据转换配置
    */
@Schema(description="数据转换配置")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SpdpDatatransformDto implements Serializable {
    @Schema(description="id")
    private String datatransformid;

    @Schema(description="")
    @Size(max = 60,message = "max length should less than 60")
    @NotBlank(message = "name is not blank")
    private String name;

    @Schema(description="")
    @Size(max = 100,message = "max length should less than 100")
    @NotBlank(message = "displayname is not blank")
    private String displayname;

    @Schema(description="")
    @Size(max = 100,message = "max length should less than 100")
    private String datatransformmodelid;

    @Schema(description="")
    @Size(max = 4000,message = "max length should less than 4000")
    private String params;

    private static final long serialVersionUID = 1L;
}