spring:
    datasource:
        dynamic:
            enabled: true #启用动态数据源，默认true
            primary: master #设置默认的数据源或者数据源组,默认值即为master
            strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
            grace-destroy: false #是否优雅关闭数据源，默认为false，设置为true时，关闭数据源时如果数据源中还存在活跃连接，至多等待10s后强制关闭
            datasource:
                master:
                    url: *********************************************
                    password: cj@888
                    driver-class-name: org.postgresql.Driver
    #                    type: com.zaxxer.hikari.HikariDataSource
    #                    hikari:
    #                        maximum-pool-size: 8
    #                        minimum-idle: 4
    #                        idle-timeout: 30000
    #                        connection-timeout: 30000
    #                        max-lifetime: 45000
    #                slave_1:
    #                    url: *****************************************
    #                    username: GEODATA
    #                    password: GEODATA
    #                    driver-class-name: oracle.jdbc.driver.OracleDriver


    #        driver-class-name: org.postgresql.Driver
    #        url: *****************************************
    #        username: postgres
    #        password: postgre

    #        url: *********************************************
    #        username: postgres
    #        password: cj@888
    #        type: com.zaxxer.hikari.HikariDataSource
    #        hikari:
    #            maximum-pool-size: 8
    #            minimum-idle: 4
    #            idle-timeout: 30000
    #            connection-timeout: 30000
    #            max-lifetime: 45000
    #            auto-commit: true
    #            pool-name: geocloud2
    servlet:
        multipart:
            # 最大置总上传的数据大小 ：默认10M
            max-request-size: 1024MB
            # 最大上传单个文件大小：默认1M
            max-file-size: 1024MB
    jackson:
        time-zone: GMT+8
        date-format: java.text.SimpleDateFormat
    mvc:
        pathmatch:
            ## knife4j推荐的配置，不加或许会报错吧
            matching-strategy: ant_path_matcher
    data:
        redis:
            host: ************
            port: 6379
            database: 1
            timeout: 2000ms
            lettuce:
                pool:
                    # 连接池最大连接数
                    max-active: 20
                    # 连接池中的最小空闲连接
                    max-idle: 10
                    # 连接池最大阻塞等待时间(使用负数表示没有限制,单位ms)
                    max-wait: 3000
mybatis-plus:
    type-aliases-package: com.bjcj.model
    global-config:
        banner: false

# sa-token配置
sa-token:
    # token 有效期（单位：秒） 默认30天，-1 代表永久有效
    timeout: 2592000
    # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
    active-timeout: -1
    # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
    is-share: false
    # 是否允许同一账号并发登录
    isConcurrent: true
    # 同一账号最大登录数量
    maxLoginCount: 1
    # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
    token-style: simple-uuid
    # 是否输出操作日志
    is-log: true
    # 关闭控制台banner
    is-print: off
    # jwt秘钥
    jwt-secret-key: VvJjzPTgO2Ee58iq1dC57oAdhXZKeX
    # SSO-相关配置
    sso-client:
        mode: client2
        # SSO-Server 端主机地址
        server-url: http://************:9000
        client: geocloud2
        # 是否打开单点注销功能
        is-slo: true
    sign:
        # API 接口调用秘钥
        secret-key: HZW1h2tkB23O6vWLNPH2VDkP4w5y6X

    # 配置Sa-Token单独使用的Redis连接 （此处需要和SSO-Server端连接同一个Redis）
    alone-redis:
        # Redis数据库索引
        database: 5
        # Redis服务器地址
        host: ************
        # Redis服务器连接端口
        port: 6379
        # Redis服务器连接密码（默认为空）
        password:
        # 连接超时时间
        timeout: 10s
        lettuce:
            pool:
                # 连接池最大连接数
                max-active: 200
                # 连接池最大阻塞等待时间（使用负值表示没有限制）
                max-wait: -1ms
                # 连接池中的最大空闲连接
                max-idle: 10
                # 连接池中的最小空闲连接
                min-idle: 0

geocloud2:
    file:
        upload: /project/geocloud2/upload/
        log: /project/geocloud2/logs/
    defaultPassword: 123
    serverurl: http://************:8000
    clientId: jcpt
    clientSecret: cj888
    checkCaptcha: false
    translation:
        url: https://openapi.youdao.com/api
        app-key:
        app-secret:

logging:
    level:
        com.bjcj.mapper: debug

# License相关配置
license:
    subject: yangqu_license
    publicAlias: publicCert
    storePass: tAwwOOz0xo28QWWqf4QsK8N1cL692X
    # 改成自己本地的license路径
    licensePath: C:\gitxm\geocloud2\license\license.lic
    publicKeysStorePath: C:\gitxm\geocloud2\license\publicCerts.keystore

