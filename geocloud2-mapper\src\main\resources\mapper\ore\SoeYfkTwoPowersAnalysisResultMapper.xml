<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.ore.SoeYfkTwoPowersAnalysisResultMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.ore.SoeYfkTwoPowersAnalysisResult">
    <!--@mbg.generated-->
    <!--@Table soe_yfk_two_powers_analysis_result-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="input_dk" jdbcType="VARCHAR" property="inputDk" />
    <result column="table_columns" jdbcType="VARCHAR" property="tableColumns" />
    <result column="table_rows" jdbcType="VARCHAR" property="tableRows" />
    <result column="attributes" jdbcType="VARCHAR" property="attributes" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, input_dk, table_columns, table_rows, attributes, create_time, update_time
  </sql>
</mapper>