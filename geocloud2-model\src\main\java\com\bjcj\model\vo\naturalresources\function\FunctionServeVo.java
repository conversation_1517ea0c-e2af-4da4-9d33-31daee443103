package com.bjcj.model.vo.naturalresources.function;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.bjcj.model.po.naturalresources.function.FunctionProvider;
import com.bjcj.model.po.naturalresources.function.RequestParams;
import com.bjcj.model.po.naturalresources.function.RequestResult;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/8 11:21 周五
 */
@Data
public class FunctionServeVo implements Serializable {

    private Long id;

    @Schema(description="服务注册名")
    private String name;

    @Schema(description="服务显示名")
    private String sname;

    @Schema(description="服务目录")
    private String fwCatalogue;

    @Schema(description="权限")
    private int auth;

    @Schema(description="注册地址")
    private String regAddress;

    @Schema(description="服务类型")
    private String fwItype;

    @Schema(description="服务标签")
    private String fwTag;

    @Schema(description="缩略图")
    private String imgurl;

    @Schema(description="描述")
    private String description;

    @Schema(description="注册人")
    private String regName;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Schema(description="注册时间")
    private LocalDateTime createTime;

    @Schema(description="运行状态")
    private Boolean status;

    @Schema(description="删除状态（0未删除，1已删除）")
    private int del;

    private FunctionProvider functionProvider;

    private List<RequestParams> requestParams;

    private List<RequestResult> requestResult;

}
