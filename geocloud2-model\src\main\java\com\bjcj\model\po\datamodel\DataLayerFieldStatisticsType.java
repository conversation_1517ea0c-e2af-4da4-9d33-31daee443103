package com.bjcj.model.po.datamodel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 * 统计分类表
 *@Date：2024/1/3  15:32
*/
@Schema(description="统计分类表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "data_layer_field_statistics_type")
public class DataLayerFieldStatisticsType implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    @NotNull(message = "is not null")
    private Long id;

    /**
     * 统计分类
     */
    @TableField(value = "statistics_type")
    @Schema(description="统计分类")
    @Size(max = 100,message = "统计分类max length should less than 100")
    private String statisticsType;

    private static final long serialVersionUID = 1L;
}