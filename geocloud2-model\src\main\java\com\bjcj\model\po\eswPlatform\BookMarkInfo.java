package com.bjcj.model.po.eswPlatform;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024-8-13  15:02
*/
/**
    * 视点书签表
    */
@Schema(description="视点书签表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "public.book_mark_info")
public class BookMarkInfo implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(description="")
    @Size(max = 64,message = "最大长度要小于 64")
    private String id;

    @TableField(value = "content")
    @Schema(description="")
    private String content;

    @TableField(value = "description")
    @Schema(description="")
    @Size(max = 255,message = "最大长度要小于 255")
    private String description;

    @TableField(value = "displayOrder")
    @Schema(description="")
    @NotNull(message = "不能为null")
    private Integer displayOrder;

    @TableField(value = "name")
    @Schema(description="")
    @Size(max = 255,message = "最大长度要小于 255")
    @NotBlank(message = "不能为空")
    private String name;

    @TableField(value = "subsystemname")
    @Schema(description="")
    @Size(max = 255,message = "最大长度要小于 255")
    private String subsystemName;

    @TableField(value = "userid")
    @Schema(description="")
    @NotNull(message = "不能为null")
    private Long userId;

    @TableField(value = "special_plan_id")
    @Schema(description="")
    @NotNull(message = "不能为null")
    private Long specialPlanId;

    private static final long serialVersionUID = 1L;
}