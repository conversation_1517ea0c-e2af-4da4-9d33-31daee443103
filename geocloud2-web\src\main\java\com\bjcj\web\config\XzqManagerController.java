package com.bjcj.web.config;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.common.utils.support.Create;
import com.bjcj.common.utils.support.Update;
import com.bjcj.model.po.system.SysXzq14;
import com.bjcj.service.system.SysXzq14Service;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.groups.Default;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/11 17:32 周一
 */
@Tag(name = "行政区管理", description = "行政区管理")
@ApiSupport(order = 4)
@RestController
@RequestMapping("/sys/config")
@Validated
public class XzqManagerController {

    @Resource
    SysXzq14Service sysXzq14Service;

    @Operation(summary = "行政区列表-三级查询", description = "行政区列表-三级查询")
    @ApiOperationSupport(order = 1)
    @Parameter(name = "pId", description = "父id")
    @OperaLog(
            operaType = OperaLogConstant.LOOK,
            operaModule = "行政区列表-三级查询",
            operaDesc = "行政区列表-三级查询"
    )
    @SaCheckPermission("sys:read")
    @GetMapping("/xzq")
    public JsonResult<List<SysXzq14>> xzq(Long pId) {
        return sysXzq14Service.xzqList(pId);
    }

    @Operation(summary = "行政区-新增", description = "行政区-新增")
    @ApiOperationSupport(order = 2)
    @Parameter(name = "sysXzq", description = "行政区对象")
    @OperaLog(
            operaType = OperaLogConstant.CREATE,
            operaModule = "行政区-新增",
            operaDesc = "行政区-新增"
    )
    @SaCheckPermission("sys:write")
    @PostMapping("/xzq")
    public JsonResult<Object> xzqCreate(@Validated({Create.class, Default.class}) @RequestBody SysXzq14 sysXzq) {
        return sysXzq14Service.xzqCreate(sysXzq);
    }

    @Operation(summary = "行政区-修改", description = "行政区-修改")
    @ApiOperationSupport(order = 3)
    @Parameter(name = "sysXzq", description = "行政区对象")
    @OperaLog(
            operaType = OperaLogConstant.UPDATE,
            operaModule = "行政区-修改",
            operaDesc = "行政区-修改"
    )
    @SaCheckPermission("sys:write")
    @PutMapping("/xzq")
    public JsonResult<Object> xzqUpdate(@Validated({Update.class, Default.class}) @RequestBody SysXzq14 sysXzq) {
        return sysXzq14Service.xzqUpdate(sysXzq);
    }

    @Operation(summary = "行政区-批量删除", description = "行政区-批量删除")
    @ApiOperationSupport(order = 4)
    @Parameter(name = "ids", description = "行政区id列表")
    @OperaLog(
            operaType = OperaLogConstant.DELETE,
            operaModule = "行政区-批量删除",
            operaDesc = "行政区-批量删除"
    )
    @SaCheckPermission("sys:del")
    @DeleteMapping("/xzq")
    public JsonResult<Object> xzqDelBatch(@NotEmpty @RequestParam List<Long> ids) {
        return sysXzq14Service.xzqDelBatch(ids);
    }

    @Operation(summary = "行政区-信息重复校验", description = "行政区-信息重复校验")
    @ApiOperationSupport(order = 5)
    @Parameter(name = "sysXzq", description = "行政区对象")
    @GetMapping("/xzq/check")
    public JsonResult<Object> xzqNameCheck(SysXzq14 sysXzq) {
        return sysXzq14Service.xzqNameCheck(sysXzq);
    }


    @Operation(summary = "根据行政区id查询祖籍id列表", description = "根据行政区id查询祖籍id列表")
    @ApiOperationSupport(order = 6)
    @SaCheckPermission("sys:read")
    @Parameter(name = "id", description = "id")
    @GetMapping("/xzqIdList")
    public JsonResult<List<Long>> xzqIdList(@RequestParam("id") Long id) {
        List<Long> idList = new ArrayList<Long>();
        idList = sysXzq14Service.getParentIdList(id);
        //List数据反转
        Collections.reverse(idList);
        return JsonResult.success(idList);
    }

}
