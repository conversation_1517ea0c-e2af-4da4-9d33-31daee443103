package com.bjcj.model.po.safetyManage;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
*@Author：qinyi
*@Date：2023/11/24  11:01
*/
@Schema(description="行政区代码表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_xzq_code")
public class SafetyXzqCode implements Serializable {
    /**
     * 行政区名称
     */
    @TableField(value = "xzq_name")
    @Schema(description="行政区名称")
    @Size(max = 50,message = "行政区名称最大长度要小于 50")
    private String xzqName;

    /**
     * 行政区级别
     */
    @TableField(value = "xzq_level")
    @Schema(description="行政区级别")
    @Size(max = 10,message = "行政区级别最大长度要小于 10")
    private String xzqLevel;

    /**
     * 行政区编码
     */
    @TableField(value = "xzq_code")
    @Schema(description="行政区编码")
    @Size(max = 50,message = "行政区编码最大长度要小于 50")
    private String xzqCode;

    /**
     * 父节点行政区编码
     */
    @TableField(value = "parent_code")
    @Schema(description="父节点行政区编码")
    @Size(max = 50,message = "父节点行政区编码最大长度要小于 50")
    private String parentCode;

    private static final long serialVersionUID = 1L;
}