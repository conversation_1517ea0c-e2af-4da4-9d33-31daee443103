package com.bjcj.model.po.datamodel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bjcj.model.dto.datamodel.RuleDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/7/2  9:07
*/
/**
    * 规则目录
    */
@Schema(description="数据检查")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "spdp_rulecatalog")
public class SpdpRulecatalog implements Serializable {
    /**
     * 规则目录ID
     */
    @TableId(value = "rulecatalogid", type = IdType.ASSIGN_UUID)
    @Schema(description="规则目录ID")
    private String rulecatalogid;

    /**
     * 目录名称
     */
    @TableField(value = "name")
    @Schema(description="目录名称")
    @Size(max = 60,message = "目录名称max length should less than 60")
    @NotBlank(message = "目录名称is not blank")
    private String name;

    /**
     * 显示名称或别名
     */
    @TableField(value = "displayname")
    @Schema(description="显示名称或别名")
    @Size(max = 60,message = "显示名称或别名max length should less than 60")
    @NotBlank(message = "显示名称或别名is not blank")
    private String displayname;

    /**
     * 说明或描述
     */
    @TableField(value = "description")
    @Schema(description="说明或描述")
    @Size(max = 500,message = "说明或描述max length should less than 500")
    private String description;

    /**
     * 目录级别
     */
    @TableField(value = "grade")
    @Schema(description="目录级别")
    private Integer grade;

    /**
     * 是否必须
     */
    @TableField(value = "isneed")
    @Schema(description="是否必须")
    private Boolean isneed;

    /**
     * 规则代码
     */
    @TableField(value = "rulecode")
    @Schema(description="规则代码")
    @Size(max = 50,message = "规则代码max length should less than 50")
    private String rulecode;

    /**
     * 父ID
     */
    @TableField(value = "parentid")
    @Schema(description="父ID")
    @Size(max = 36,message = "父IDmax length should less than 36")
    private String parentid;

    /**
     * 内部所验证的对象类型标识符
     */
    @TableField(value = "innervalidateobjecttypekey")
    @Schema(description="内部所验证的对象类型标识符")
    @Size(max = 100,message = "内部所验证的对象类型标识符max length should less than 100")
    private String innervalidateobjecttypekey;

    @TableField(exist = false)
    @Schema(description="规则列表")
    private List<RuleDto> ruleDtos;

    private static final long serialVersionUID = 1L;
}