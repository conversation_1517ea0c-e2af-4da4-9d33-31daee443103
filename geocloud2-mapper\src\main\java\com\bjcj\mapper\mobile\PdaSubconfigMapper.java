package com.bjcj.mapper.mobile;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.mobile.PdaSubconfig;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface PdaSubconfigMapper extends BaseMapper<PdaSubconfig> {

    List<PdaSubconfig> list(Map<String, Object> map);

    List<PdaSubconfig> getWholeOption(PdaSubconfig pdaSubconfig);

}
