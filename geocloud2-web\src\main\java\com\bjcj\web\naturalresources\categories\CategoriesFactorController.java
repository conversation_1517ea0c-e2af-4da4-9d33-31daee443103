package com.bjcj.web.naturalresources.categories;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.naturalresources.categories.CategoriesFactor;
import com.bjcj.service.naturalresources.categories.CategoriesFactorService;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/29 11:35 周三
 */
@Tag(name = "数据服务管理(要素服务)", description = "数据服务管理(要素服务)")
@ApiSupport(order = 54)
@RestController
@Slf4j
@RequestMapping(value = "/categories_factor")
public class CategoriesFactorController {

    @Resource
    private CategoriesFactorService categoriesFactorService;

    /*@Operation(summary = "要素服务列表")
    @GetMapping(value = "/lists")
    public JsonResult lists(int current, int size){

        return categoriesFactorService.lists(current, size);
    }

    @Operation(summary = "注册要素服务")
    @PostMapping(value = "/save")
    public JsonResult save(@RequestBody @Validated CategoriesFactor categoriesFactor){

        return JsonResult.success(categoriesFactorService.save(categoriesFactor));
    }*/

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "修改要素服务",
            operaType = OperaLogConstant.LOOK,
            operaDesc = "修改要素服务"
    )
    @Operation(summary = "修改要素服务")
    @PutMapping(value = "/update")
    public JsonResult update(@RequestBody @Validated CategoriesFactor categoriesFactor){

        return JsonResult.success(categoriesFactorService.saveOrUpdate(categoriesFactor));
    }

    @SaCheckPermission("sys:del")
    @OperaLog(
            operaModule = "删除要素服务",
            operaType = OperaLogConstant.LOOK,
            operaDesc = "删除要素服务"
    )
    @Operation(summary = "删除要素服务")
    @DeleteMapping(value = "/del/{id}")
    public JsonResult del(@PathVariable Long id){

        return categoriesFactorService.del(id);
    }

    @Operation(summary = "根据父id查询")
    @GetMapping(value = "/findById/{id}")
    public JsonResult<List<CategoriesFactor>> findById(@PathVariable Long id){

        return categoriesFactorService.findById(id);
    }

}
