package com.bjcj.web.mobile;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.mobile.PdaSubconfigDto;
import com.bjcj.model.po.mobile.PdaSubconfig;
import com.bjcj.model.po.mobile.PdaSudoconfig;
import com.bjcj.service.mobile.PdaSubconfigService;
import com.bjcj.service.mobile.PdaSudoconfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/PdaSubConfig")
@Tag(name = "App模块菜单配置（子模块）")
public class PdaSubConfigController {

    @Autowired
    PdaSubconfigService pdaSubconfigServices;

    @Autowired
    PdaSudoconfigService pdaSudoconfigServices;

    @SaCheckPermission("sys:read")
    @Operation(
            summary = "获取根模块及其子模块列表",
            description = "获取所有根模块配置项，并递归返回其子模块列表"
    )
    @GetMapping("/getOption")
    public JsonResult<List<PdaSudoconfig>> getOption(){
        return JsonResult.success(pdaSudoconfigServices.getOptionMethod());
    }


    @SaCheckPermission("sys:read")
    @Operation(
            summary = "获取根模块下的所有子模块",
            description = "返回所有模块配置项，递归获取并返回所有子模块，形成完整的树结构"
    )
    @GetMapping("/getWholeOption")
    public JsonResult<List<PdaSudoconfig>> getWholeOption(){
        return JsonResult.success(pdaSudoconfigServices.getWholeOptionMethod());
    }


    @SaCheckPermission("sys:read")
    @Operation(
            summary = "分页获取子模块列表",
            description = "根据传入的根模块PID分页查询子模块，返回数据列表及总记录数"
    )
    @GetMapping("/list")
    public JsonResult getList(@RequestParam("current") Integer currentPage, @RequestParam("size") Integer pageSize,
                                                      String pid){
        return pdaSubconfigServices.selectPage(currentPage, pageSize,pid);
    }

    @SaCheckPermission("sys:write")
    @Operation(
            summary = "新增子模块配置项",
            description = "接收传入的PdaSubconfig对象，进行新增操作，返回操作状态"
    )
    @PostMapping("/add")
    public JsonResult addPdaSubConfig(@RequestBody PdaSubconfig pdaSubconfig){
        return pdaSubconfigServices.add(pdaSubconfig) ? JsonResult.success("success") : JsonResult.success("false");
    }

    @SaCheckPermission("sys:write")
    @Operation(
            summary = "删除子模块配置项",
            description = "根据传入的subid删除对应的配置项，返回删除操作是否成功"
    )
    @DeleteMapping("/delete/{subid}")
    public JsonResult deleteSubConfig(@PathVariable("subid") String subid){
        PdaSubconfig pdaSubconfig = new PdaSubconfig();
        pdaSubconfig.setSubid(subid);

        return pdaSubconfigServices.delete(pdaSubconfig) ? JsonResult.success("success") : JsonResult.success("false");
    }

    @SaCheckPermission("sys:write")
    @Operation(
            summary = "编辑子模块配置项",
            description = "根据传入的PdaSubconfig对象更新配置，调用更新逻辑"
    )
    @PutMapping("/update")
    public JsonResult updateSubConfig(@RequestBody PdaSubconfig pdaSubconfig){

        return pdaSubconfigServices.update(pdaSubconfig) ? JsonResult.success("success") : JsonResult.success("false");
    }

    @SaCheckPermission("sys:read")
    @Operation(
            summary = "校验子模块ID唯一性",
            description = "根据传入的subid查询是否已存在相同的id，返回是否唯一的结果"
    )
    @GetMapping("/checkOnly")
    public JsonResult toCheckOnly(@RequestParam("subid") String subid){
        PdaSubconfig pdaSubconfig = new PdaSubconfig();
        pdaSubconfig.setSubid(subid);
        return pdaSubconfigServices.getOne(pdaSubconfig)==null ? JsonResult.success(true) : JsonResult.success(false);
    }


    @Operation(
            summary = "getParentSubConfig（web端暂时没用）"
    )
    @SaCheckPermission("sys:read")
    @GetMapping("/getParentSubConfig")
    public JsonResult<List<PdaSubconfig>> getParentSubConfig(){
        return JsonResult.success(pdaSubconfigServices.getParentSubconfig());
    }


    //  获取首页
    @Operation(
            summary = "getHome（web端暂时没用）"
    )
    @SaCheckPermission("sys:read")
    @GetMapping("/getHome")
    public JsonResult<PdaSubconfig> getHomePage(){
        return JsonResult.success(pdaSubconfigServices.getOne());
    }


}
