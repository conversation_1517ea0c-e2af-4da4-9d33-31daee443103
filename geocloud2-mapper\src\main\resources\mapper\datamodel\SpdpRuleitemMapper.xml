<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.SpdpRuleitemMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.SpdpRuleitem">
    <!--@mbg.generated-->
    <!--@Table public.spdp_ruleitem-->
    <result column="ruleitemid" jdbcType="CHAR" property="ruleitemid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="displayname" jdbcType="VARCHAR" property="displayname" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="validatorkey" jdbcType="VARCHAR" property="validatorkey" />
    <result column="validatorconfigdatatext" jdbcType="VARCHAR" property="validatorconfigdatatext" />
    <result column="ruleid" jdbcType="CHAR" property="ruleid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ruleitemid, "name", displayname, description, validatorkey, validatorconfigdatatext, 
    ruleid
  </sql>
</mapper>