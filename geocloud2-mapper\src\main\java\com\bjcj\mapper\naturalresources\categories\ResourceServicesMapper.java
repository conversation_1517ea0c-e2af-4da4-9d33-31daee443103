package com.bjcj.mapper.naturalresources.categories;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.model.po.naturalresources.categories.ResourceServices;
import com.bjcj.model.po.naturalresources.categories.ResourceServices2;
import com.bjcj.model.vo.naturalresources.categories.ResourceServicesVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/4/25  9:19
*/
public interface ResourceServicesMapper extends BaseMapper<ResourceServices> {
    IPage<ResourceServicesVo> lists(@Param("name") String name,@Param("id") String id,@Param("ids") List<String> ids,@Param("auth") int auth,@Param("roleId") Long roleId,@Param("page") Page<ResourceServicesVo> page);

    List<ResourceServices> selectListAndGroupName(@Param("itemid") String itemid);

    List<ResourceServices2> selectServicePage2(@Param("current") long current, @Param("size") long size, @Param("datainfoids") List<String> datainfoids, @Param("ordersql") String ordersql, @Param("startYear") Integer startYear, @Param("endYear") Integer endYear, @Param("scale") String scale, @Param("publishInstitutionName") String publishInstitutionName);

    int selectServicePage2Count(@Param("current") long current,@Param("size") long size,@Param("datainfoids") List<String> datainfoids,@Param("ordersql") String ordersql,@Param("startYear") Integer startYear,@Param("endYear") Integer endYear,@Param("scale") String scale,@Param("publishInstitutionName") String publishInstitutionName);

    int selectServiceCountByIdIn(@Param("datainfoids") List<String> datainfoids);

    int selectServiceCountByIdIn2(@Param("resourcecatalogids") List<String> resourcecatalogids);


    List<ResourceServices2> selectServicePage3(@Param("current") long current, @Param("size") long size, @Param("ordersql") String ordersql, @Param("startYear") String startYear, @Param("endYear") String endYear, @Param("scale") String scale, @Param("publishInstitutionName") String publishInstitutionName,@Param("label") String label);

    int selectServicePage3Count(@Param("current") long current,@Param("size") long size,@Param("ordersql") String ordersql,@Param("startYear") String startYear,@Param("endYear") String endYear,@Param("scale") String scale,@Param("publishInstitutionName") String publishInstitutionName,@Param("label") String label);

    List<ResourceServices> selectServicePage(@Param("current") long current,@Param("size") long size,@Param("searchStr") String searchStr,@Param("catalogsId") String catalogsId,@Param("cateIds") List<String> cateIds);

    Long selectServicePageCount(@Param("searchStr") String searchStr,@Param("catalogsId") String catalogsId,@Param("cateIds") List<String> cateIds);

    List<ResourceServices> selectPageList(@Param("current") Long current, @Param("size") Long size, @Param("searchStr") String searchStr, @Param("resourcecatalogid")  String resourcecatalogid);

    Long selectListCount( @Param("searchStr") String searchStr,@Param("resourcecatalogid")  String resourcecatalogid);

    List<ResourceServices2> selectServicePage22(@Param("current") long current, @Param("size") long size, @Param("ids") List<String> ids, @Param("ordersql") String ordersql, @Param("startYear") String startYear, @Param("endYear") String endYear, @Param("scale") String scale, @Param("publishInstitutionName") String publishInstitutionName,@Param("name") String name);

    int selectServicePage22Count(@Param("current") long current,@Param("size") long size,@Param("ids") List<String> ids,@Param("ordersql") String ordersql,@Param("startYear") String startYear,@Param("endYear") String endYear,@Param("scale") String scale,@Param("publishInstitutionName") String publishInstitutionName,@Param("name") String name);

    List<ResourceServices2> serviceListByName(@Param("name") String name);

    List<ResourceServices2> selectList2(@Param("ids") List<String> ids);

    ResourceServices2 selectOnes(@Param("id") String resServiceId);
}