package com.bjcj.mapper.safetyManage;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.safetyManage.SafetyRole;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 *@Author：qinyi
 *@Date：2023/11/29  8:49
*/
public interface SafetyRoleMapper extends BaseMapper<SafetyRole> {
    List<SafetyRole> selectIdInByUserId(@Param("id") Long id);

    List<SafetyRole> getRoleByUserId(@Param("userId") Long userId);


}