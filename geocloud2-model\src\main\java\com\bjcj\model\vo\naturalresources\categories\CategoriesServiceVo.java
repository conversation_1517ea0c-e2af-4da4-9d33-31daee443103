package com.bjcj.model.vo.naturalresources.categories;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/11/29 8:46 周三
 */
@Data
public class CategoriesServiceVo implements Serializable {

    private Long id;

    @Schema(description="父级id")
    private Long parentId;

    @Schema(description="注册名称")
    private String name;

    @Schema(description="服务标识")
    private String fwbs;

    @Schema(description="显示名称")
    private String sname;

    @Schema(description="注册地址")
    private String address;

    @Schema(description="服务类型")
    private String fwitype;

    @Schema(description="服务分组")
    private String fwgroup;

    @Schema(description="是否显示0不1是")
    private int show;

    @Schema(description="状态")
    private Boolean status;

    @Schema(description="服务排序")
    private int fwsort;

    @Schema(description="标签")
    private String tags;

    @Schema(description="缩略图")
    private String imgurl;

    @Schema(description="描述")
    private String remark;

    @Schema(description="服务参数")
    private String fwcs;

    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description="删除状态（0未删除，1已删除）")
    private int del;

    @Schema(description="数据服务id")
    private Long cateId;

    @Schema(description="角色id")
    private Long roleId;

}
