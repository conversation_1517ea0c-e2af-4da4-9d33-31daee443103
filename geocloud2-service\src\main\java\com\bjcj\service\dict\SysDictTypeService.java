package com.bjcj.service.dict;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.dict.SysDictDataMapper;
import com.bjcj.mapper.dict.SysDictTypesMapper;
import com.bjcj.model.po.dict.SysDictData;
import com.bjcj.model.po.dict.SysDictType;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Objects;

/**
 *@Author：qinyi
 *@Date：2024/2/1  10:12
*/
@Service
public class SysDictTypeService extends ServiceImpl<SysDictTypesMapper, SysDictType> {

    @Resource
    SysDictTypesMapper sysDictTypeMapper;

    @Resource
    SysDictDataMapper sysDictDataMapper;

    public JsonResult<Page<SysDictType>> pageDataSort(Page<SysDictType> pager, String dictName, String dictType, String status, String dictDataType, String startTime, String endTime) {
        LambdaQueryWrapper<SysDictType> queryWrapper = new LambdaQueryWrapper<>();
        Timestamp s = null;
        Timestamp e = null;
        if(startTime!=null && !"".equals(startTime) && endTime!=null && !"".equals(endTime)){
            startTime = startTime+" 00:00:00";
            endTime = endTime+" 23:59:59";
            s = Timestamp.valueOf(startTime);
            e = Timestamp.valueOf(endTime);
        }
        queryWrapper.like(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dictName), SysDictType::getDictName, dictName)
                .eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dictType), SysDictType::getDictType, dictType)
                .eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(status),SysDictType::getStatus, status)
                .eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dictDataType),SysDictType::getDictDataType, dictDataType)
                .between(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(startTime) && com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(endTime),SysDictType::getCreateTime,s,e);
        return JsonResult.success(sysDictTypeMapper.selectPage(pager, queryWrapper));
    }

    public JsonResult deleteById(Long id) {
        SysDictType sysDictType = sysDictTypeMapper.selectById(id);
        if(Objects.isNull(sysDictType)) return JsonResult.error("字典不存在");
        this.sysDictDataMapper.delete(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictType,sysDictType.getDictType()));
        this.sysDictTypeMapper.deleteById(id);
        return JsonResult.success();
    }
}
