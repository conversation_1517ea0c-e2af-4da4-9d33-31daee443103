package com.bjcj.service.cloudportal;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.mapper.cloudportal.Dltb140105Mapper;
import com.bjcj.model.po.cloudportal.Dltb140105;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 *@Author：qinyi
 *@Date：2024/7/12  15:29
*/
@Service
@DS("slave_1")
public class Dltb140105Service extends ServiceImpl<Dltb140105Mapper, Dltb140105> {

    @Resource
    Dltb140105Mapper dltb140105Mapper;

    public BigDecimal selectMjSum(String simplecode, List<String> nydCodeList) {
        return this.dltb140105Mapper.selectMjSum(simplecode,nydCodeList);
    }

    public BigDecimal selectMjSum2(String simplecode) {
        return this.dltb140105Mapper.selectMjSum2(simplecode);
    }

    public BigDecimal selectMjSums(String simplecode, String dictValue) {
        return this.dltb140105Mapper.selectMjSums(simplecode,dictValue);
    }
}
