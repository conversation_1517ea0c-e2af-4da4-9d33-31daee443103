<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.naturalresources.function.FunctionServeMapper">

  <select id="selectPageList" resultType="com.bjcj.model.po.naturalresources.function.FunctionServe">
    select fs.* from function_serve fs
    where 1=1
    <if test="searchStr!= null and searchStr!= ''">
      and ( fs.name like concat('%', #{searchStr}, '%') or fs.sname like concat('%', #{searchStr}, '%') )
    </if>
    <if test="functionDataId!= null and functionDataId!=''">
      and fs.fw_catalogue = #{functionDataId}
    </if>
    and fs.del=0 and fs.status=true
    order by fs.create_time desc
    LIMIT #{size} OFFSET #{current}
  </select>

  <select id="selectListCount" resultType="java.lang.Long">
    select count(*) from (
    select fs.* from function_serve fs
    where 1=1
    <if test="searchStr!= null and searchStr!= ''">
      and ( fs.name like concat('%', #{searchStr}, '%') or fs.sname like concat('%', #{searchStr}, '%') )
    </if>
    <if test="functionDataId!= null and functionDataId!=''">
      and fs.fw_catalogue = #{functionDataId}
    </if>
    and fs.del=0 and fs.status=true
    ) c
  </select>
</mapper>