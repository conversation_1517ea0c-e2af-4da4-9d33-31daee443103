<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.mobile.PdaSubconfigMapper">
    <select id="list" resultType="com.bjcj.model.po.mobile.PdaSubconfig">
		select * from sys_mobile_subconfig t
		<where>
		1=1

		<if test="tenantid != null and tenantid != ''">
		 	AND t.tenantid=#{tenantid}
		</if>

		<if test="applyKind != null and applyKind != ''">
			and Applykind = #{applykind}
		</if>

		<if test="parentId != null and parentId != ''and parentId != '0'">
			and parentId = #{parentid}
		</if>

		<if test="parentId != null and parentId != ''and parentId == '0'">
			and ( parentId = #{parentid} or parentid is null )
		</if>

		<if test="name != null and name != ''">
			and applykind = #{name}
		</if>

		<if test="pid != null and pid != ''">
			and pid = #{pid}
		</if>
		  	and t.isdisplay='是'

		<if test="roleid != null and roleid != ''">
			and (subid in (
				select menuid from sys_mobile_modulemenu_power where roleid = #{roleid}
			) or pubcomponent = 1)
		</if>
		<if test="publiccomponent != null">
			and PUBCOMPONENT = #{publiccomponent}
		</if>
		  	order by sortid asc
		</where>
	</select>

	<select id="getWholeOption" parameterType="PdaSubconfig" resultType="PdaSubconfig" >
		select * from sys_mobile_subconfig t
		<where>
			pid = #{pid} and isdisplay = '是'
			and ((builtmodule = '是') OR (builtmodule = '否' AND pubcomponent = 0))
			<if test="subid == null" >
				and parentid is NULL
			</if>
			<if test="subid != null">
				and parentid = #{subid}
			</if>
		</where>

	</select>

</mapper>
