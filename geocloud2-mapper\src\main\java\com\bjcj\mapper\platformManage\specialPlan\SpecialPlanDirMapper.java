package com.bjcj.mapper.platformManage.specialPlan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlanDir;
import org.apache.ibatis.annotations.Param;


/**
 *@Author：qinyi
 *@Date：2024/1/26  10:17
*/
public interface SpecialPlanDirMapper extends BaseMapper<SpecialPlanDir> {
    void delDataById(@Param("id") String id,@Param("specialPlanId") Long specialPlanId);
}