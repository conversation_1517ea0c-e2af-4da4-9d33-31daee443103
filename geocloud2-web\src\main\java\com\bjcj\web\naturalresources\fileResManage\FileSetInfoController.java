package com.bjcj.web.naturalresources.fileResManage;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.fileResManage.FileInfoDto;
import com.bjcj.model.dto.fileResManage.FileSetInfoDto;
import com.bjcj.model.po.naturalresources.fileResManage.FileInfo;
import com.bjcj.model.po.naturalresources.fileResManage.FileSetInfo;
import com.bjcj.service.naturalresources.fileResManage.FileInfoService;
import com.bjcj.service.naturalresources.fileResManage.FileSetInfoService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
* 文件集信息表(public.file_set_info)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/fileSetInfo")
@Tag(name = "文件集信息")
@Validated
public class FileSetInfoController {
    /**
    * 服务对象
    */
    @Resource
    private FileSetInfoService fileSetInfoService;

    @Resource
    private FileInfoService fileInfoService;

    @OperaLog(operaModule = "文件集信息-新增/编辑",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "文件集信息-新增/编辑")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 2)
    @Transactional(rollbackFor = Exception.class)
    @RequestLock(prefix = "addOrEdit")
    public JsonResult addOrEdit(@Validated @RequestBody FileSetInfoDto dto) {
        List<FileInfoDto> fileInfoDtos = dto.getFileInfoDtos();
        return fileSetInfoService.addOrEdit(dto,fileInfoDtos);
    }

    @OperaLog(operaModule = "文件集信息-删除",operaType = OperaLogConstant.DELETE,operaDesc = "文件集信息-删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del")
    @Operation(summary = "删除", description = "根据id删除")
    @Parameters({
            @Parameter(name = "id", description = "id", required = true)
    })
    @ApiOperationSupport(order = 3)
    @Transactional(rollbackFor = Exception.class)
    public JsonResult del(@RequestParam("id") Long id) {
        return this.fileSetInfoService.delData(id);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "列表", description = "分页,搜索")
    @ApiOperationSupport(order = 1)
    @GetMapping("/list")
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "fileDirId", description = "文件资源目录id", required = true),
            @Parameter(name = "searchStr", description = "名称搜索", required = false)
    })
    public JsonResult<Page<FileSetInfo>> list(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam("fileDirId") Long fileDirId,
            @RequestParam(value = "searchStr",required = false) String searchStr) {
        Page<FileSetInfo> pager = new Page<>(page, pageSize);
        return this.fileSetInfoService.pageData(pager, fileDirId,searchStr);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "文件集id查文件", description = "文件集id查文件")
    @ApiOperationSupport(order = 4)
    @GetMapping("/queryFileInfo")
    @Parameters({
            @Parameter(name = "fileSetInfoId", description = "文件集id", required = true)
    })
    public JsonResult<List<FileInfo>> queryFileInfo(@RequestParam("fileSetInfoId") Long fileSetInfoId) {
        return JsonResult.success(this.fileInfoService.list(new LambdaQueryWrapper<FileInfo>().eq(FileInfo::getFileSetInfoId, fileSetInfoId)));
    }


}
