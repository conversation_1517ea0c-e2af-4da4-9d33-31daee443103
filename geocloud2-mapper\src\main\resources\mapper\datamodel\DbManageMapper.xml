<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.DbManageMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.DbManage">
    <!--@mbg.generated-->
    <!--@Table public.db_manage-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="space_type" jdbcType="VARCHAR" property="spaceType" />
    <result column="example_url" jdbcType="VARCHAR" property="exampleUrl" />
    <result column="db_version" jdbcType="VARCHAR" property="dbVersion" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="database" jdbcType="VARCHAR" property="database" />
    <result column="is_only_read" jdbcType="BOOLEAN" property="isOnlyRead" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", space_type, example_url, db_version, username, "password", "database", 
    is_only_read, remark
  </sql>
</mapper>