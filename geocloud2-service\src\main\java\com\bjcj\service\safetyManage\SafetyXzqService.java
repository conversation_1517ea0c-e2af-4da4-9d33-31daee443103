package com.bjcj.service.safetyManage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.safetyManage.SafetyXzqCodeMapper;
import com.bjcj.mapper.system.SysXzq14Mapper;
import com.bjcj.model.po.safetyManage.SafetyXzqCode;
import com.bjcj.model.po.system.SysXzq14;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @Author：qinyi
 * @Package：com.bjcj.service.safe
 * @Project：geocloud2
 * @name：SafeService
 * @Date：2023/11/23 10:45
 * @Filename：SafeService
 */
@Service
public class SafetyXzqService extends ServiceImpl<SafetyXzqCodeMapper, SafetyXzqCode> {
    @Resource
    SysXzq14Mapper sysXzq14Mapper;

    public JsonResult<List<SysXzq14>> queryData(String searchStr, Long pid) {
        LambdaQueryWrapper<SysXzq14> queryWrapper = new LambdaQueryWrapper<SysXzq14>();
        if(StringUtils.isEmpty(searchStr) && Objects.isNull(pid))
            queryWrapper.like(SysXzq14::getLevel,1);
        else if(Objects.nonNull(pid) && StringUtils.isEmpty(searchStr))
            queryWrapper.eq(SysXzq14::getPId,pid);
        else if(StringUtils.isNotEmpty(searchStr))
            queryWrapper.and(wrapper -> wrapper.like(SysXzq14::getName,searchStr)).or().eq(SysXzq14::getCode,searchStr);
        return JsonResult.success(sysXzq14Mapper.selectList(queryWrapper));
    }
}
