package com.bjcj.model.dto.datamodel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/4/19  14:53
*/

/**
    * 表结构
    */
@Schema(description="图层和图层字段集合")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MetadataTablestructuresAllDto implements Serializable {
    @Schema(description="图层集合")
    private List<MetadataTablestructuresDto> metadataTablestructuresList;

    @Schema(description="图层字段集合")
    private List<MetadataTablestructurefieldsDto> metadataTablestructurefieldsDtoList;
}