package com.bjcj.web.datamodel;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.datamodel.RuleDto;
import com.bjcj.model.dto.datamodel.RuleItemDto;
import com.bjcj.model.dto.datamodel.SpatialDataDto;
import com.bjcj.model.po.datamodel.SpdpRule;
import com.bjcj.model.po.datamodel.SpdpRulecatalog;
import com.bjcj.model.po.datamodel.SpdpRuleitem;
import com.bjcj.service.datamodel.SpdpRuleService;
import com.bjcj.service.datamodel.SpdpRulecatalogService;
import com.bjcj.service.datamodel.SpdpRuleitemService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.ModelMapper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
* 规则目录(public.spdp_rulecatalog)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/spdp_rulecatalog")
@Tag(name = "数据检查")
@Validated
public class SpdpRulecatalogController {
    /**
    * 服务对象
    */
    @Resource
    private SpdpRulecatalogService spdpRulecatalogService;

    @Resource
    private SpdpRuleService spdpRuleService;

    @Resource
    private SpdpRuleitemService spdpRuleitemService;

    @SaCheckPermission("sys:read")
    @GetMapping("/list")
    @Operation(summary = "列表", description = "列表")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "searchStr", description = "searchStr", required = false)
    })
    public JsonResult<List<SpdpRulecatalog>> list(@RequestParam(value = "searchStr",required = false) String searchStr){
        if(StringUtils.isNotBlank(searchStr)){
            return JsonResult.success(this.spdpRulecatalogService.list(new LambdaQueryWrapper<SpdpRulecatalog>().and(i -> i.like(SpdpRulecatalog::getName, searchStr).or().like(SpdpRulecatalog::getDisplayname, searchStr))));
        }
        return JsonResult.success(this.spdpRulecatalogService.list());
    }


    @SaCheckPermission("sys:read")
    @GetMapping("/details/{rulecatalogid}")
    @Operation(summary = "详情", description = "根据id查询")
    @ApiOperationSupport(order = 6)
    public JsonResult<SpdpRulecatalog> details(@PathVariable("rulecatalogid") String rulecatalogid){
        SpdpRulecatalog byId = this.spdpRulecatalogService.getById(rulecatalogid);
        List<SpdpRule> list = this.spdpRuleService.list(new LambdaQueryWrapper<SpdpRule>().eq(SpdpRule::getRulecatalogid, rulecatalogid));
        List<RuleDto> ruleDtos = new ArrayList<>();

        list.stream().forEach(item->{
            item.setRuleItemList(this.spdpRuleitemService.list(new LambdaQueryWrapper<SpdpRuleitem>().eq(SpdpRuleitem::getRuleid, item.getRuleid())));
            RuleDto ddto = BeanUtil.copyProperties(item, RuleDto.class);
            ModelMapper modelMapper = new ModelMapper();
            //item.getRuleItemList()批量转换为ruleItemDtos
            ddto.setRuleItemDtos(item.getRuleItemList().stream().map(i->modelMapper.map(i, RuleItemDto.class)).collect(Collectors.toList()));
            ruleDtos.add(ddto);
        });
        byId.setRuleDtos(ruleDtos);
        return JsonResult.success(byId);
    }



    @OperaLog(operaModule = "删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del/{rulecatalogid}")
    @Operation(summary = "删除", description = "根据id删除")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "delsjjc")
    @Transactional(rollbackFor = Exception.class)
    public JsonResult del(@PathVariable("rulecatalogid") String rulecatalogid){
        return this.spdpRulecatalogService.deleteById(rulecatalogid);
    }

    @OperaLog(operaModule = "批量删除",operaType = OperaLogConstant.DELETE,operaDesc = "批量删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delBatch")
    @Operation(summary = "批量删除", description = "根据id批量删除")
    @ApiOperationSupport(order = 3)
    @Parameters({
            @Parameter(name = "rulecatalogids", description = "ids逗号拼接", required = true)
    })
    @RequestLock(prefix = "delBatchsjjc")
    @Transactional(rollbackFor = Exception.class)
    public JsonResult delBatch(@RequestParam("rulecatalogids") String rulecatalogids){
        if(rulecatalogids.contains(",")){
            //ids转list
            List<String> ids = List.of(rulecatalogids.split(","));
            return this.spdpRulecatalogService.deleteByIds(ids);
        }else{
            return JsonResult.error("ids格式错误,必须选择一条以上的数据");
        }
    }

    @SaCheckPermission("sys:write")
    @PostMapping("/edit")
    @Operation(summary = "编辑", description = "编辑")
    @ApiOperationSupport(order = 4)
    @RequestLock(prefix = "editsjjc")
    @Transactional(rollbackFor = Exception.class)
    public JsonResult editsjjc(@Validated @RequestBody SpatialDataDto dto){
        SpdpRulecatalog ruleCatalog = this.serviceItemDtoToRuleCatalog(dto);
        return this.spdpRulecatalogService.editRuleCatalog(ruleCatalog,dto.getRuleDtos());
    }

    @SaCheckPermission("sys:write")
    @PostMapping("/add")
    @Operation(summary = "注册", description = "注册")
    @ApiOperationSupport(order = 5)
    @RequestLock(prefix = "addsjjc")
    @Transactional(rollbackFor = Exception.class)
    public JsonResult add(@Validated @RequestBody SpatialDataDto dto){
        SpdpRulecatalog ruleCatalog = this.serviceItemDtoToRuleCatalog(dto);
        return this.spdpRulecatalogService.addRuleCatalog(ruleCatalog,dto.getRuleDtos());
    }

    private SpdpRulecatalog serviceItemDtoToRuleCatalog(SpatialDataDto spatiaDataDto) {
        SpdpRulecatalog ruleCatalog = new SpdpRulecatalog();
        ruleCatalog.setName(spatiaDataDto.getName());
        ruleCatalog.setRulecatalogid(spatiaDataDto.getRulecatalogid());
        ruleCatalog.setDisplayname(spatiaDataDto.getDisplayname());
        ruleCatalog.setGrade(0);
        ruleCatalog.setIsneed(true);
        return ruleCatalog;
    }

}
