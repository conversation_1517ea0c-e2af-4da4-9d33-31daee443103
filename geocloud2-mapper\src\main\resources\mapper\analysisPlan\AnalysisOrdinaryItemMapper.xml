<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.platformManage.analysisPlan.AnalysisOrdinaryItemMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.platformManage.analysisPlan.AnalysisOrdinaryItem">
    <!--@mbg.generated-->
    <!--@Table public.analysis_ordinary_item-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="analysis_plan_id" jdbcType="BIGINT" property="analysisPlanId" />
    <result column="res_service_id" jdbcType="CHAR" property="resServiceId" />
    <result column="func_service_id" jdbcType="CHAR" property="funcServiceId" />
    <result column="field_plan_id" jdbcType="BIGINT" property="fieldPlanId" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="name" jdbcType="VARCHAR" property="name" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, analysis_plan_id, res_service_id, func_service_id, field_plan_id, "operator", 
    create_time, update_time, "name"
  </sql>
</mapper>