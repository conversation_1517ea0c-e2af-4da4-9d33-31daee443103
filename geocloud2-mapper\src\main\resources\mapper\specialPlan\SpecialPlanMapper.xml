<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.platformManage.specialPlan.SpecialPlanMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.platformManage.specialPlan.SpecialPlan">
    <!--@mbg.generated-->
    <!--@Table public.special_plan-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="plan_name" jdbcType="VARCHAR" property="planName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operater" jdbcType="VARCHAR" property="operater" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, plan_name, remark, create_time, update_time, operater
  </sql>
</mapper>