package com.bjcj.model.po.platformManage.fieldPlan;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/2/19  17:22
*/
/**
    * 字段展示方案图层字段表
    */
@Schema(description="字段展示方案图层字段表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "field_plan_layer_field")
public class FieldPlanLayerField implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 字段名称
     */
    @TableField(value = "field_name")
    @Schema(description="字段名称")
    @Size(max = 50,message = "字段名称max length should less than 50")
    @NotBlank(message = "字段名称is not blank")
    private String fieldName;

    /**
     * 字段显示名称
     */
    @TableField(value = "show_name")
    @Schema(description="字段显示名称")
    @Size(max = 50,message = "字段显示名称max length should less than 50")
    private String showName;

    /**
     * 标准名称
     */
    @TableField(value = "standard_name")
    @Schema(description="标准名称")
    @Size(max = 50,message = "标准名称max length should less than 50")
    private String standardName;

    /**
     * 字段类型
     */
    @TableField(value = "field_type_id")
    @Schema(description="字段类型")
    @NotNull(message = "字段类型is not null")
    private Long fieldTypeId;

    /**
     * 字段长度
     */
    @TableField(value = "field_length")
    @Schema(description="字段长度")
    private Integer fieldLength;

    /**
     * 字段精度
     */
    @TableField(value = "field_accuracy")
    @Schema(description="字段精度")
    private Integer fieldAccuracy;

    /**
     * 小数位数
     */
    @TableField(value = "decimal_places")
    @Schema(description="小数位数")
    private Integer decimalPlaces;

    /**
     * 是否必填
     */
    @TableField(value = "is_must")
    @Schema(description="是否必填")
    private Boolean isMust;

    /**
     * 是否可为空
     */
    @TableField(value = "is_null")
    @Schema(description="是否可为空")
    private Boolean isNull;

    /**
     * 描述
     */
    @TableField(value = "remark")
    @Schema(description="描述")
    @Size(max = 255,message = "描述max length should less than 255")
    private String remark;

    /**
     * 统计分类
     */
    @TableField(value = "statistics_type_id")
    @Schema(description="统计分类")
    private Long statisticsTypeId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time",fill = FieldFill.UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 最后操作人
     */
    @TableField(value = "operator")
    @Schema(description="最后操作人")
    @Size(max = 50,message = "最后操作人max length should less than 50")
    private String operator;

    /**
     * 字段展示方案图层id
     */
    @TableField(value = "data_layer_id")
    @Schema(description="字段展示方案图层id")
    @NotNull(message = "字段展示方案图层idis not null")
    private String dataLayerId;

    /**
     * 字段展示方案id
     */
    @TableField(value = "field_plan_id")
    @Schema(description="字段展示方案id")
    private Long fieldPlanId;

    /**
     * 是否排序
     */
    @TableField(value = "is_sort")
    @Schema(description="是否排序")
    @NotNull(message = "是否排序is not null")
    private Boolean isSort;

    /**
     * 显示顺序
     */
    @TableField(value = "show_sort")
    @Schema(description="显示顺序")
    private Integer showSort;

    /**
     * 是否显示
     */
    @TableField(value = "is_show")
    @Schema(description="是否显示")
    @NotNull(message = "是否显示is not null")
    private Boolean isShow;

    /**
     * 映射方式
     */
    @TableField(value = "mapping_method")
    @Schema(description="映射方式")
    private String mappingMethod;

    /**
     * 参数
     */
    @TableField(value = "mapping_param")
    @Schema(description="参数")
    private String mappingParam;

    /**
     * 是否图层显示字段
     */
    @TableField(value = "is_layer_show")
    @Schema(description="是否图层显示字段")
    @NotNull(message = "是否图层显示字段is not null")
    private Boolean isLayerShow;

    @TableField(value = "is_conf_attr")
    @Schema(description="属性字段(现有读取的),内置字段(预置字典自己编辑的)")
    @NotNull(message = "属性字段is not null")
    private Boolean isConfAttr;


    private static final long serialVersionUID = 1L;
}