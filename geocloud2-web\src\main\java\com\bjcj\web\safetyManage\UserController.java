package com.bjcj.web.safetyManage;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.safetyManage.UserDto;
import com.bjcj.model.po.safetyManage.*;
import com.bjcj.service.safetyManage.SafetyRoleService;
import com.bjcj.service.safetyManage.SafetyUserRoleService;
import com.bjcj.service.safetyManage.UserService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* 用户表(public."sys_user")表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("user")
@Tag(name = "人员管理")
@Validated
public class UserController {
    /**
    * 服务对象
    */
    @Resource
    private UserService userService;

    @Resource
    private SafetyRoleService safetyRoleService;

    @Resource
    private SafetyUserRoleService safetyUserRoleService;

    @OperaLog(operaModule = "人员管理-注册用户",operaType = OperaLogConstant.CREATE,operaDesc = "注册用户")
    @SaCheckPermission("sys:write")
    @PostMapping("/registerUser")
    @Operation(summary = "注册用户", description = "注册(编辑)用户")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "registerUser")
    public JsonResult registerUser(@Valid @RequestBody UserDto dto) throws Exception {
        return this.userService.saveData(dto);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/page")
    @Operation(summary = "人员列表分页", description = "带搜索框模糊查询(可搜姓名和用户名)")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "searchStr", description = "姓名和用户名搜索", required = false),
            @Parameter(name = "isLock", description = "是否锁定", required = false),
            @Parameter(name = "institutionId", description = "所属机构id", required = true),
            @Parameter(name = "deptId", description = "所属部门id", required = false)
    })
    public JsonResult<Page<User>> page(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam(value = "searchStr",required = false) String searchStr,
            @RequestParam(value = "isLock",required = false) String isLock,
            @RequestParam("institutionId") Long institutionId,
            @RequestParam(value = "deptId",required = false) Long deptId) {
        Page<User> pager = new Page<>(page, pageSize);
        return this.userService.pageDataSort(pager,searchStr,institutionId,deptId,isLock);
    }

    @OperaLog(operaModule = "人员管理-删除人员",operaType = OperaLogConstant.DELETE,operaDesc = "删除人员")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del")
    @Operation(summary = "删除人员", description = "根据id删除人员")
    @Parameters({
            @Parameter(name = "id", description = "主键id", required = true)
    })
    @Transactional(rollbackFor = Exception.class)
    @ApiOperationSupport(order = 3)
    public JsonResult del(@RequestParam("id") Long id){
        return this.userService.delData(id);
    }

    @OperaLog(operaModule = "人员管理-人员激活",operaType = OperaLogConstant.UPDATE,operaDesc = "人员激活")
    @SaCheckPermission("sys:write")
    @PatchMapping("/activation")
    @Operation(summary = "人员激活", description = "人员激活")
    @Parameters({
            @Parameter(name = "id", description = "用户id", required = true)
    })
    @ApiOperationSupport(order = 4)
    public JsonResult activation(@RequestParam("id") Long id){
        return this.userService.updateActiveStatus(id);
    }

    @OperaLog(operaModule = "人员管理-密码重置",operaType = OperaLogConstant.UPDATE,operaDesc = "密码重置")
    @SaCheckPermission("sys:write")
    @PatchMapping("/resetPassword")
    @Operation(summary = "密码重置", description = "密码重置后默认(123456)")
    @Parameters({
            @Parameter(name = "id", description = "用户id", required = true)
    })
    @ApiOperationSupport(order = 5)
    public JsonResult resetPassword(@RequestParam("id") Long id) throws Exception {
        return this.userService.resetPassword(id);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/allInstitutionList")
    @Operation(summary = "展示所有机构", description = "展示所有机构")
    @ApiOperationSupport(order = 6)
    public JsonResult<List<SafetyInstitution>> allInstitutionList(){
        return this.safetyRoleService.allInstitutionList();
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/queryDeptByInstitutionOrParentId")
    @Operation(summary = "查询部门", description = "根据机构id查询顶层部门或者根据父id查询子部门")
    @Parameters({
            @Parameter(name = "institutionId", description = "机构id", required = false),
            @Parameter(name = "deptId", description = "父部门id", required = false)
    })
    @ApiOperationSupport(order = 7)
    public JsonResult<List<SafetyDept>> queryDeptByInstitutionOrParentId(
            @RequestParam(value = "institutionId",required = false) Long institutionId,
            @RequestParam(value = "deptId",required = false) Long deptId){
        return this.safetyRoleService.queryTopDeptByInstitution(institutionId,deptId);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("queryAllRoles")
    @Operation(summary = "查询所有角色", description = "查询所有角色")
    @ApiOperationSupport(order = 8)
    public JsonResult<List<SafetyRole>> queryAllRoles(){
        return this.safetyRoleService.selectAllRoles();
    }

    @SaCheckPermission("sys:read")
    @GetMapping("queryExistingRoles")
    @Operation(summary = "查询当前人员现有角色", description = "查询当前人员现有角色")
    @Parameters({
            @Parameter(name = "userId", description = "用户id", required = true)
    })
    @ApiOperationSupport(order = 9)
    public JsonResult<List<SafetyUserRole>> queryExistingRoles(
            @RequestParam("userId") Long userId){
        return this.safetyUserRoleService.queryExistingUserRoles(userId);
    }

    @OperaLog(operaModule = "人员管理-保存人员角色",operaType = OperaLogConstant.UPDATE,operaDesc = "保存人员角色")
    @SaCheckPermission("sys:write")
    @PostMapping("saveUserRoles")
    @Operation(summary = "保存人员角色", description = "保存人员角色")
    @Parameters({
            @Parameter(name = "userId", description = "用户id", required = true),
            @Parameter(name = "roleIds", description = "角色id数组字符串逗号分隔", required = true)
    })
    @Transactional(rollbackFor = Exception.class)
    @ApiOperationSupport(order = 10)
    @RequestLock(prefix = "saveUserRoles")
    public JsonResult saveUserRoles(
            @RequestParam("userId") Long userId,
            @RequestParam("roleIds") String roleIds){
        return this.safetyUserRoleService.saveRoleUsers(userId,roleIds);
    }

    /**
    @SaCheckPermission("sys:write")
    @GetMapping("runXzqData")
    @Operation(summary = "运行行政区数据(前端别调)", description = "运行行政区数据每次传一个级别(2,3,4,市,区,街道)")
    @ApiOperationSupport(order = 11)
    public JsonResult runXzqData(@RequestParam("level") String level){
        return this.userService.runXzqData(level);
    }
    */

    /**
     * 获取当前用户权限集合
     * @return 权限集合
     */
    @SaCheckPermission("sys:read")
    @GetMapping("/queryUserPermissions")
    @Operation(summary = "获取当前用户权限集合", description = "获取当前用户权限集合")
    @ApiOperationSupport(order = 12)
    public JsonResult<List<String>> queryUserPermissions(){
        return this.userService.queryUserPermissions();
    }

    //获取专题平台登录用户权限集合
    @SaCheckPermission("sys:read")
    @GetMapping("/queryUserPermissionsForSpecialPlan")
    @Operation(summary = "获取专题平台登录用户权限集合", description = "获取专题平台登录用户权限集合")
    @ApiOperationSupport(order = 13)
    public JsonResult<List<String>> queryUserPermissionsForSpecialPlan(@RequestParam("specialPlanName")String specialPlanName){
        return this.userService.queryUserPermissionsForSpecialPlan(specialPlanName);
    }

}
