package com.bjcj.model.dto.cloudportal;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author：qinyi
 * @Date：2024/1/15 10:29
 */
@Schema(description="收藏表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CollectDto implements Serializable {

    @Schema(description="")
    private Long id;

    /**
     * 数据服务,应用服务,数据产品
     */
    @TableField(value = "resouce_type")
    @Schema(description="数据服务,应用服务,数据产品")
    @Size(max = 50,message = "数据服务,应用服务,数据产品max length should less than 50")
    @NotBlank(message = "数据服务,应用服务,数据产品is not blank")
    private String resouceType;

    /**
     * 资源id
     */
    @TableField(value = "res_id")
    @Schema(description="资源id")
    @NotNull(message = "资源idis not null")
    private String resId;
}
