package com.bjcj.web.safetyManage.fieldPlan;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.fieldPlan.DataLayerIdsDto;
import com.bjcj.model.dto.fieldPlan.FieldLayerNameDto;
import com.bjcj.model.dto.fieldPlan.FieldPlanDto;
import com.bjcj.model.dto.fieldPlan.FieldPlanLayerFieldDto;
import com.bjcj.model.po.platformManage.fieldPlan.FieldPlan;
import com.bjcj.model.po.platformManage.fieldPlan.FieldPlanLayer;
import com.bjcj.model.po.platformManage.fieldPlan.FieldPlanLayerField;
import com.bjcj.service.platformManage.fieldPlan.FieldPlanLayerFieldService;
import com.bjcj.service.platformManage.fieldPlan.FieldPlanLayerService;
import com.bjcj.service.platformManage.fieldPlan.FieldPlanService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;


/**
* 字段展示方案(public.field_plan)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/field_plan")
@Tag(name = "4字段展示方案")
@Validated
public class FieldPlanController {
    /**
    * 服务对象
    */
    @Resource
    private FieldPlanService fieldPlanService;

    @Resource
    private FieldPlanLayerService fieldPlanLayerService;

    @Resource
    private FieldPlanLayerFieldService fieldPlanLayerFieldService;


    @SaCheckPermission("sys:read")
    @Operation(summary = "方案列表", description = "方案列表")
    @ApiOperationSupport(order = 1)
    @GetMapping("/list")
    public JsonResult<List<FieldPlan>> list() {
        return JsonResult.success(fieldPlanService.list());
    }

    @OperaLog(operaModule = "新增/编辑方案",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "新增/编辑方案")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑方案", description = "新增/编辑方案")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "addOrEdit")
    public JsonResult addOrEdit(@Validated @RequestBody FieldPlanDto dto){
        if(Objects.nonNull(this.fieldPlanService.getOne(new LambdaQueryWrapper<FieldPlan>().eq(FieldPlan::getFieldName, dto.getFieldName())))) return JsonResult.error("方案名称已存在");
        FieldPlan fieldPlan = BeanUtil.copyProperties(dto, FieldPlan.class);
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        fieldPlan.setOperater(username);
        return fieldPlanService.saveOrUpdate(fieldPlan) ? JsonResult.success() : JsonResult.error();
    }

    @OperaLog(operaModule = "删除方案",operaType = OperaLogConstant.DELETE,operaDesc = "删除方案")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除方案", description = "删除方案")
    @ApiOperationSupport(order = 3)
    @Transactional(rollbackFor = Exception.class)
    public JsonResult delete(@PathVariable("id") Long id) {
        return this.fieldPlanService.deleteById(id);
    }


    @OperaLog(operaModule = "新增图层",operaType = OperaLogConstant.CREATE,operaDesc = "新增图层")
    @SaCheckPermission("sys:write")
    @PostMapping("/addLayer")
    @Operation(summary = "新增图层", description = "新增图层")
    @ApiOperationSupport(order = 4)
    @Transactional(rollbackFor = Exception.class)
    @RequestLock(prefix = "addLayer")
    public JsonResult addLayer(@Validated @RequestBody DataLayerIdsDto dto){
        return this.fieldPlanService.addLayer(dto);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/pitchonDataLayers")
    @Operation(summary = "获取已勾选的图层", description = "获取已勾选的图层")
    @ApiOperationSupport(order = 11)
    public JsonResult<List<String>> pitchonDataLayers(@RequestParam("fieldPlanId") Long fieldPlanId){
        return JsonResult.success(fieldPlanLayerService.list(new LambdaQueryWrapper<FieldPlanLayer>().eq(FieldPlanLayer::getFieldPlanId, fieldPlanId)).stream().map(FieldPlanLayer::getId).toList());
    }


    @SaCheckPermission("sys:read")
    @Operation(summary = "图层列表", description = "图层列表")
    @ApiOperationSupport(order = 5)
    @GetMapping("/page")
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "searchStr", description = "搜索", required = false),
            @Parameter(name = "fieldPlanId", description = "方案id", required = true)
    })
    public JsonResult<Page<FieldPlanLayer>> page(@RequestParam("current") Integer page,
                                                 @RequestParam("size") Integer pageSize,
                                                 @RequestParam(value = "searchStr" ,required = false) String searchStr,
                                                 @RequestParam("fieldPlanId") Long fieldPlanId){
        Page<FieldPlanLayer> pager = new Page<>(page, pageSize);
        LambdaQueryWrapper<FieldPlanLayer> wrapper = new LambdaQueryWrapper<FieldPlanLayer>();
        wrapper
                .eq(FieldPlanLayer::getFieldPlanId,fieldPlanId)
                .and(wp -> wp
                        .apply("1=1")
                        .like(Objects.nonNull(searchStr),FieldPlanLayer::getName,searchStr)
                        .or()
                        .like(Objects.nonNull(searchStr),FieldPlanLayer::getShowName,searchStr)
                )
                .orderByAsc(FieldPlanLayer::getCreateTime);
        return JsonResult.success(this.fieldPlanLayerService.page(pager,wrapper));
    }

    @OperaLog(operaModule = "配置字段信息",operaType = OperaLogConstant.UPDATE,operaDesc = "配置字段信息")
    @SaCheckPermission("sys:write")
    @PostMapping("/editFieldPlanLayerField")
    @Operation(summary = "配置字段信息", description = "配置字段信息")
    @ApiOperationSupport(order = 6)
    @RequestLock(prefix = "editFieldPlanLayerField")
    public JsonResult editFieldPlanLayerField(@Validated @RequestBody FieldPlanLayerFieldDto dto){
        return this.fieldPlanLayerFieldService.editFieldPlanLayerField(dto);
    }

    @OperaLog(operaModule = "编辑图层名称",operaType = OperaLogConstant.UPDATE,operaDesc = "编辑图层名称")
    @SaCheckPermission("sys:write")
    @PostMapping("/editFieldLayerName")
    @Operation(summary = "编辑图层名称", description = "编辑图层名称")
    @ApiOperationSupport(order = 7)
    @RequestLock(prefix = "editFieldLayerName")
    public JsonResult editFieldLayerName(@Validated @RequestBody FieldLayerNameDto dto){
        return this.fieldPlanLayerService.editFieldLayerName(dto);
    }

    @OperaLog(operaModule = "删除图层",operaType = OperaLogConstant.DELETE,operaDesc = "删除图层")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/deleteLayer/{id}")
    @Operation(summary = "删除图层", description = "删除图层")
    @ApiOperationSupport(order = 8)
    @Transactional(rollbackFor = Exception.class)
    @Parameters({
            @Parameter(name = "id", description = "图层id", required = true),
            @Parameter(name = "fieldPlanId", description = "字段方案id", required = true),
    })
    public JsonResult deleteLayer(@RequestParam("id") String id,@RequestParam("fieldPlanId") Long fieldPlanId) {
        return this.fieldPlanLayerService.deleteById(id,fieldPlanId);
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "图层字段列表", description = "图层字段列表")
    @ApiOperationSupport(order = 9)
    @GetMapping("/fieldList")
    @Parameters({
            @Parameter(name = "fieldPlanLayerId", description = "图层id", required = true),
            @Parameter(name = "fieldPlanId", description = "字段方案id", required = true)
    })
    public JsonResult<List<FieldPlanLayerField>> fieldList(@RequestParam("fieldPlanLayerId") String fieldPlanLayerId,@RequestParam("fieldPlanId") Long fieldPlanId){
        return JsonResult.success(this.fieldPlanLayerFieldService.list(new LambdaQueryWrapper<FieldPlanLayerField>().eq(FieldPlanLayerField::getDataLayerId,fieldPlanLayerId)
                .eq(FieldPlanLayerField::getFieldPlanId,fieldPlanId)));
    }


    @OperaLog(operaModule = "导出到...",operaType = OperaLogConstant.CREATE,operaDesc = "传平台id")
    @SaCheckPermission("sys:write")
    @PostMapping("/export")
    @Operation(summary = "字段展示方案导出到...", description = "传平台id")
    @Parameters({
            @Parameter(name = "fromPlatformAppConfId", description = "导出的平台id", required = true),
            @Parameter(name = "toPlatformAppConfId", description = "导入的平台id", required = true)
    })
    @ApiOperationSupport(order = 10)
    @Transactional(rollbackFor = Exception.class)
    public JsonResult exportTo(@RequestParam("fromPlatformAppConfId") Long fromPlatformAppConfId,
                               @RequestParam("toPlatformAppConfId") Long toPlatformAppConfId){
        return this.fieldPlanService.exportTo(fromPlatformAppConfId,toPlatformAppConfId);
    }

    @OperaLog(operaModule = "新增字段",operaType = OperaLogConstant.CREATE,operaDesc = "新增字段")
    @SaCheckPermission("sys:write")
    @PostMapping("/addField")
    @Operation(summary = "新增字段", description = "新增字段")
    @ApiOperationSupport(order = 12)
    @RequestLock(prefix = "addField")
    public JsonResult addField(@Validated @RequestBody FieldPlanLayerField field){
        return this.fieldPlanLayerFieldService.addField(field);
    }

    @OperaLog(operaModule = "删除字段",operaType = OperaLogConstant.DELETE,operaDesc = "删除字段")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/deleteField/{id}")
    @Operation(summary = "删除字段", description = "删除字段")
    @ApiOperationSupport(order = 13)
    public JsonResult deleteField(@PathVariable("id") String id){
        return this.fieldPlanLayerFieldService.removeById(Long.valueOf(id)) ? JsonResult.success() : JsonResult.error("删除失败");
    }

}
