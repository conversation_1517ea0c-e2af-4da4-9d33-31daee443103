package com.bjcj.model.dto.eswPlatform;

import com.bjcj.model.po.eswPlatform.Schemes;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024-8-16  11:38
*/
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectsDto implements Serializable {
    private String id;

    /**
     * 名称
     */
    @Schema(description="名称")
    @Size(max = 255,message = "名称最大长度要小于 255")
    private String name;

    /**
     * 位置信息
     */
    @Schema(description="位置信息")
    private String content;

    /**
     * 状态
     */
    @Schema(description="状态")
    private Short status;

    /**
     * 描述
     */
    @Schema(description="描述")
    private String description;

    @Schema(description="")
    private Long specialPlanId;

    @Schema(description="Schema子集")
    private List<Schemes> childrens;


    private static final long serialVersionUID = 1L;
}