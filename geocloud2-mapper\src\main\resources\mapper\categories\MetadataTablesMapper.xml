<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.naturalresources.categories.MetadataTablesMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.naturalresources.categories.MetadataTables">
    <!--@mbg.generated-->
    <!--@Table public.metadata_tables-->
    <id column="tableid" jdbcType="CHAR" property="tableid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="displayname" jdbcType="VARCHAR" property="displayname" />
    <result column="dataitemid" jdbcType="CHAR" property="dataitemid" />
    <result column="tablestructureid" jdbcType="CHAR" property="tablestructureid" />
    <result column="recordcount" jdbcType="NUMERIC" property="recordcount" />
    <result column="updatetime" jdbcType="TIMESTAMP" property="updatetime" />
    <result column="datacategoryid" jdbcType="CHAR" property="datacategoryid" />
    <result column="datastandardid" jdbcType="CHAR" property="datastandardid" />
    <result column="workspaceid" jdbcType="CHAR" property="workspaceid" />
    <result column="districtcode" jdbcType="VARCHAR" property="districtcode" />
    <result column="districtname" jdbcType="VARCHAR" property="districtname" />
    <result column="zone" jdbcType="NUMERIC" property="zone" />
    <result column="serverid" jdbcType="CHAR" property="serverid" />
    <result column="metadata" jdbcType="OTHER" property="metadata" />
    <result column="datacategory" jdbcType="NUMERIC" property="datacategory" />
    <result column="businesscategory" jdbcType="NUMERIC" property="businesscategory" />
    <result column="displayorder" jdbcType="NUMERIC" property="displayorder" />
    <result column="registerman" jdbcType="VARCHAR" property="registerman" />
    <result column="registerdate" jdbcType="TIMESTAMP" property="registerdate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    tableid, "name", displayname, dataitemid, tablestructureid, recordcount, updatetime, 
    datacategoryid, datastandardid, workspaceid, districtcode, districtname, "zone", 
    serverid, metadata, datacategory, businesscategory, displayorder, registerman, registerdate
  </sql>
</mapper>