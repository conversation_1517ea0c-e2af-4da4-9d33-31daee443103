package com.bjcj.service.datamodel;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.datamodel.DataLayerMapper;
import com.bjcj.model.dto.datamodel.DataLayerDto;
import com.bjcj.model.po.datamodel.DataLayer;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 *@Author：qinyi
 *@Date：2024/1/3  15:32
*/
@Service
public class DataLayerService extends ServiceImpl<DataLayerMapper, DataLayer> {

    @Resource
    DataLayerMapper dataLayerMapper;

    @Resource
    DataLayerFieldService dataLayerFieldService;


    public JsonResult saveData(DataLayerDto dto) {
        DataLayer dataLayer = BeanUtil.copyProperties(dto, DataLayer.class);
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        if(Objects.isNull(dataLayer.getId())){
            //验证名称或显示名称重复数据
            LambdaQueryWrapper<DataLayer> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DataLayer::getDataStandardId,dataLayer.getDataStandardId());
            queryWrapper.and(i->i.eq(DataLayer::getName,dataLayer.getName()).or().eq(DataLayer::getShowName,dataLayer.getShowName()));
            List<DataLayer> list = this.dataLayerMapper.selectList(queryWrapper);
            if(!list.isEmpty()){
                return JsonResult.error("名称或显示名称重复");
            }
            dataLayer.setOperator(username);
            dataLayer.setCreateTime(LocalDateTime.now());
            dataLayerMapper.insert(dataLayer);
        }else{
            dataLayer.setOperator(username);
            dataLayer.setUpdateTime(LocalDateTime.now());
            dataLayerMapper.updateById(dataLayer);
        }
        return JsonResult.success();
    }

}
