package com.bjcj.service.safetyManage;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.constant.UserConstants;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.common.utils.domain.Ztree;
import com.bjcj.mapper.platformConf.PlatformAppConfMapper;
import com.bjcj.mapper.platformManage.specialPlan.SpecialPlanMapper;
import com.bjcj.mapper.safetyManage.*;
import com.bjcj.model.dto.safetyManage.MenuDto;
import com.bjcj.model.dto.safetyManage.RoleMenuPermsDto;
import com.bjcj.model.dto.safetyManage.SpecialPlanAndMenuIds;
import com.bjcj.model.po.platformConf.PlatformAppConf;
import com.bjcj.model.po.safetyManage.Menu;
import com.bjcj.model.po.safetyManage.RoleMenu;
import com.bjcj.model.po.safetyManage.SafetyUserRole;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 *@Author：qinyi
 *@Date：2023/12/4  9:19
*/
@Service
public class MenuService extends ServiceImpl<MenuMapper, Menu> {

    @Resource
    MenuMapper menuMapper;

    @Resource
    RoleMenuMapper roleMenuMapper;

    @Resource
    UserMapper userMapper;

    @Resource
    CheckIsAdmin checkIsAdmin;

    @Resource
    SafetyUserRoleMapper safetyUserRoleMapper;

    @Resource
    PlatformAppConfMapper platformAppConfMapper;

    @Resource
    SpecialPlanMapper specialPlanMapper;

    @Resource
    SafetyRoleMapper safetyRoleMapper;


    public boolean checkMenuNameUnique(Long id,Long parentId,String menuName) {
        Long menuId = Objects.isNull(id) ? -1L : id;
        Menu info = menuMapper.checkMenuNameUnique(menuName, parentId);
        if (Objects.nonNull(info) && info.getId().longValue() != menuId.longValue()){
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    public boolean checkMenuNameUnique2(Long id,Long parentId,String menuName,Long appid) {
        Long menuId = Objects.isNull(id) ? -1L : id;
        Menu info = menuMapper.checkMenuNameUnique2(menuName, parentId,appid);
        if (Objects.nonNull(info) && info.getId().longValue() != menuId.longValue()){
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    public JsonResult insertMenu(MenuDto dto) {
        Menu menu = BeanUtil.copyProperties(dto,Menu.class);
        return menuMapper.insert(menu) > 0 ? JsonResult.success() : JsonResult.error();
    }

    public JsonResult updateMenu(MenuDto dto) {
        Menu menu = BeanUtil.copyProperties(dto,Menu.class);
        return menuMapper.updateById(menu) > 0? JsonResult.success() : JsonResult.error();
    }

    public int selectCountMenuByParentId(Long parentId){
        return menuMapper.selectCountMenuByParentId(parentId);
    }

    public int selectCountRoleMenuByMenuId(Long menuId)
    {
        return roleMenuMapper.selectCountRoleMenuByMenuId(menuId);
    }

    public JsonResult deleteMenuById(Long id) {
        return menuMapper.deleteById(id) > 0? JsonResult.success() : JsonResult.error();
    }

    public List<Menu> selectMenuList(String menuName, Long userId,Long specialPlanId) {
        //专题id转化为平台id
        Long platFormAppConfId = 0L;
        if(Objects.nonNull(specialPlanId)) {
            platFormAppConfId = specialPlanMapper.selectById(specialPlanId).getPlatformAppConfId();
        }
        //弃用原列表形式菜单,转为树结构
        List<Menu> menuList = null;
        if(checkIsAdmin.checkIsAdmin(StpUtil.getLoginIdAsLong())){
            LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.isNull(Objects.isNull(specialPlanId), Menu::getPlatformAppConfId);
            queryWrapper.eq(Objects.nonNull(specialPlanId), Menu::getPlatformAppConfId, platFormAppConfId);
            menuList = this.menuMapper.selectList(queryWrapper);
        }else{
            if(Objects.isNull(specialPlanId)) {
                menuList = this.menuMapper.selectMenuListByUserId(null,userId);
            } else {
                menuList = this.menuMapper.selectMenuListByUserIdSpecialPlan(platFormAppConfId,userId);
            }
        }
        List<Menu> finalMenuList = menuList;
        List<Menu> collect = menuList.stream()
                .filter(item -> Objects.equals(item.getParentId(), 0L))
                .map(item -> item.setChildren(getChild(item.getId(), finalMenuList)))
                .sorted(Comparator.comparingInt(menu -> (menu.getShowSort() == null ? 0 : menu.getShowSort())))
                .collect(Collectors.toList());
        //不从sql中根据name like过滤,因为无法加载出完整的树结构,在程序代码中过滤
        boolean exist = false;
        if(Objects.nonNull(menuName) && !menuName.isEmpty()) {
            for (int i = 0; i < finalMenuList.size(); i++) {
                if(finalMenuList.get(i).getMenuName().equals(menuName)) {
                    exist = true;
                    break;
                }
            }
            if(!exist){
                return new ArrayList<>();
            }else{
               /*List<Menu> menuByNameList = getMenuByName(collect, menuName);
               return menuByNameList;*/
                //过滤按钮权限相关信息:zzn
                List<Menu> menuByNameList = getMenuByName(collect, menuName);
                return this.getCollect(menuByNameList);
            }
        }
        //过滤按钮权限相关信息:zzn
//        return collect;
        return  this.getCollect(collect);
    }


    /**
     * 专题用的树
     * @param specialPlanName
     * @param userId
     * @return
     */
    public List<Menu> selectMenuListSpecialPlan(String specialPlanName, Long userId) {
        //根据专题名获取平台id
        // Long specialPlanId = this.specialPlanMapper.selectOne(new LambdaQueryWrapper<SpecialPlan>().eq(SpecialPlan::getPlanName, specialPlanName)).getId();
        Long platFormAppConfId = this.platformAppConfMapper.selectOne(
                new LambdaQueryWrapper<PlatformAppConf>()
                        .eq(PlatformAppConf::getAppName, specialPlanName)
        ).getId();
        //弃用原列表形式菜单,转为树结构
        List<Menu> menuList = null;
        if(checkIsAdmin.checkIsAdmin(userId)){
            LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Menu::getPlatformAppConfId, platFormAppConfId)
                    .eq(Menu::getVisible, "0");
            menuList = this.menuMapper.selectList(queryWrapper);
        }else{
            menuList = this.menuMapper.selectMenuListByUserIdSpecialPlan(platFormAppConfId,userId);
        }
        List<Menu> finalMenuList = menuList;
        List<Menu> collect = menuList.stream()
                .filter(item -> Objects.equals(item.getParentId(), 0L))
                .map(item -> item.setChildren(getChild(item.getId(), finalMenuList)))
                .sorted(Comparator.comparingInt(menu -> (menu.getShowSort() == null ? 0 : menu.getShowSort())))
                .collect(Collectors.toList());

        //过滤按钮权限相关信息:zzn
        return  this.getCollect(collect);
    }


    /**
     * 过滤按钮权限相关信息:zzn
     * @param menuList 菜单集合
     * @return 过滤完的菜单集合
     */
    private List<Menu> getCollect(List<Menu> menuList){
        return Optional.ofNullable(menuList)
                .orElseGet(Collections::emptyList)
                .stream()
                .filter(menu -> menu.getMenuType() !=null && !"F".equals(menu.getMenuType()))
                .collect(Collectors.toList());
    }


    public List<Menu> selectMenuList2(String menuName, Long userId, Long platformAppConfId) {
        //弃用原列表形式菜单,转为树结构
        List<Menu> menuList = null;
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNull(Objects.isNull(platformAppConfId),Menu::getPlatformAppConfId);
        queryWrapper.eq(Objects.nonNull(platformAppConfId),Menu::getPlatformAppConfId,platformAppConfId);
        menuList = this.menuMapper.selectList(queryWrapper);
        List<Menu> finalMenuList = menuList;
        List<Menu> collect = menuList.stream()
                .filter(item -> Objects.equals(item.getParentId(), 0L))
                .map(item -> item.setChildren(getChild(item.getId(), finalMenuList)))
                .sorted(Comparator.comparingInt(menu -> (menu.getShowSort() == null ? 0 : menu.getShowSort())))
                .collect(Collectors.toList());
        //不从sql中根据name like过滤,因为无法加载出完整的树结构,在程序代码中过滤
        boolean exist = false;
        if(Objects.nonNull(menuName) && !menuName.isEmpty()) {
            for (int i = 0; i < finalMenuList.size(); i++) {
                if(finalMenuList.get(i).getMenuName().equals(menuName)) {
                    exist = true;
                    break;
                }
            }
            if(!exist){
                return new ArrayList<>();
            }else{
                List<Menu> menuByNameList = getMenuByName(collect, menuName);
                return menuByNameList;
            }
        }
        return collect;
    }

    public List<Menu> selectMenuList3(String menuName, Long userId,Long platformAppConfId) {
        //弃用原列表形式菜单,转为树结构
        List<Menu> menuList = null;
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Menu::getPlatformAppConfId,platformAppConfId);
        menuList = this.menuMapper.selectList(queryWrapper);
        List<Menu> finalMenuList = menuList;
        List<Menu> collect = menuList.stream()
                .filter(item -> Objects.equals(item.getParentId(), 0L))
                .map(item -> item.setChildren(getChild(item.getId(), finalMenuList)))
                .sorted(Comparator.comparingInt(menu -> (menu.getShowSort() == null ? 0 : menu.getShowSort())))
                .collect(Collectors.toList());
        //不从sql中根据name like过滤,因为无法加载出完整的树结构,在程序代码中过滤
        boolean exist = false;
        if(Objects.nonNull(menuName) && !menuName.isEmpty()) {
            for (int i = 0; i < finalMenuList.size(); i++) {
                if(finalMenuList.get(i).getMenuName().equals(menuName)) {
                    exist = true;
                    break;
                }
            }
            if(!exist){
                return new ArrayList<>();
            }else{
                List<Menu> menuByNameList = getMenuByName(collect, menuName);
                return menuByNameList;
            }
        }
        return collect;
    }

    private List<Menu> getChild(Long id, List<Menu> menuList){
        return menuList.stream()
                .filter(item -> Objects.equals(item.getParentId(), id) && !Objects.equals(item.getMenuType(), "F"))
                .map(item -> item.setChildren(getChild(item.getId(), menuList)))
                .sorted(Comparator.comparingInt(menu -> (menu.getShowSort() == null ? 0 : menu.getShowSort())))
                .collect(Collectors.toList());
    }

    /**
     * 在树结构上做模糊查询(剪枝操作）
     */
    private List<Menu> getMenuByName(List<Menu> menuList,String selectName){
        Iterator<Menu> iterator = menuList.iterator();
        while(iterator.hasNext()){
            Menu menu = iterator.next();
            if(!menu.getMenuName().contains(selectName)){
                List<Menu> childrenList = menu.getChildren();
                if(!CollectionUtils.isEmpty(childrenList)){
                    getMenuByName(childrenList, selectName);
                }
                if(CollectionUtils.isEmpty(childrenList)){
                    iterator.remove();
                }
            }
        }
        return menuList;
    }

    public JsonResult<List<Ztree>> roleMenuTreeData(Long roleId) {
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        List<Ztree> ztrees = new ArrayList<Ztree>();
        List<Menu> menuList = selectMenuAll(userId);
        if (Objects.nonNull(roleId))
        {
            List<String> roleMenuList = menuMapper.selectMenuTree(roleId);
            ztrees = initZtree(menuList, roleMenuList, true);
        }
        else
        {
            ztrees = initZtree(menuList, null, true);
        }
        return JsonResult.success(ztrees);
    }


    /**
     * 查询菜单集合
     *
     * @return 所有菜单信息
     */
    public List<Menu> selectMenuAll(Long userId)
    {
        List<Menu> menuList = null;
        Long loginUserId = (Long) JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId");
//        if(principal.getPermissions().contains("ADMIN")){
        if(checkIsAdmin.checkIsAdmin(loginUserId)){
            menuList = menuMapper.selectMenuAll();
        }else {
            menuList = menuMapper.selectMenuAllByUserId(userId);
        }
        return menuList;
    }

    /**
     * 对象转菜单树
     *
     * @param menuList 菜单列表
     * @param roleMenuList 角色已存在菜单列表
     * @param permsFlag 是否需要显示权限标识
     * @return 树结构列表
     */
    public List<Ztree> initZtree(List<Menu> menuList, List<String> roleMenuList, boolean permsFlag){
        List<Ztree> ztrees = new ArrayList<Ztree>();
        boolean isCheck = Objects.nonNull(roleMenuList);
        for (Menu menu : menuList){
            Ztree ztree = new Ztree();
            ztree.setId(menu.getId());
            ztree.setpId(menu.getParentId());
            ztree.setName(transMenuName(menu, permsFlag));
            ztree.setTitle(menu.getMenuName());
            if (isCheck)
            {
                ztree.setChecked(roleMenuList.contains(menu.getId() + menu.getPerms()));
            }
            ztrees.add(ztree);
        }
        return ztrees;
    }

    public String transMenuName(Menu menu, boolean permsFlag)
    {
        StringBuffer sb = new StringBuffer();
        sb.append(menu.getMenuName());
        if (permsFlag)
        {
            sb.append("<font color=\"#888\">&nbsp;&nbsp;&nbsp;" + menu.getPerms() + "</font>");
        }
        return sb.toString();
    }

    public JsonResult<RoleMenuPermsDto> queryMenuAuthByUserId(Long userId, Long specialPlanId) {
        RoleMenuPermsDto dto = new RoleMenuPermsDto();
        List<SafetyUserRole> safetyUserRoleList = safetyUserRoleMapper.selectList(new LambdaQueryWrapper<SafetyUserRole>().eq(SafetyUserRole::getUserId, userId));
        List<Long> roleIds = safetyUserRoleList.stream().map(SafetyUserRole::getRoleId).toList();
        List<RoleMenu> roleMenuList = roleMenuMapper.selectList(new LambdaQueryWrapper<RoleMenu>()
                        .isNull(Objects.isNull(specialPlanId),RoleMenu::getSpecialPlanId)
                        .eq(Objects.nonNull(specialPlanId),RoleMenu::getSpecialPlanId, specialPlanId)
                        .in(RoleMenu::getRoleId, roleIds));
        List<Long> menuIds = roleMenuList.stream().map(RoleMenu::getMenuId).toList();
        dto.setMenuIds(menuIds.stream().map(Object::toString).collect(Collectors.joining(",")));


        //写入专题菜单权限
        List<Long> roleIdList = this.safetyUserRoleMapper.queryUserRoleId(userId);
        if(Objects.nonNull(specialPlanId)){
            List<RoleMenu> roleMenuList2 = this.roleMenuMapper.selectList(new LambdaQueryWrapper<RoleMenu>().in(RoleMenu::getRoleId, roleIdList)
                    .eq(RoleMenu::getSpecialPlanId, specialPlanId));
            SpecialPlanAndMenuIds s = new SpecialPlanAndMenuIds();
            s.setMenuIds(roleMenuList2.stream().map(roleMenu -> String.valueOf(roleMenu.getMenuId())).collect(Collectors.joining(",")));
            s.setSpecialPlanId(specialPlanId);
            List<SpecialPlanAndMenuIds> sList = new ArrayList<>();
            sList.add(s);
            dto.setSpecialPlansAndMenuIds(sList);
        }

        return JsonResult.success(dto);
    }

    public JsonResult importTo(Long fromPlatformAppConfId, Long toPlatformAppConfId) {
        if(Objects.isNull(this.platformAppConfMapper.selectOne(new LambdaQueryWrapper<PlatformAppConf>().eq(PlatformAppConf::getId,toPlatformAppConfId)))) {
            return JsonResult.error("目标平台应用不存在");
        }
        List<Menu> menuList = menuMapper.selectList(new LambdaQueryWrapper<Menu>().eq(Menu::getPlatformAppConfId,fromPlatformAppConfId));
        if(menuList.isEmpty()) {
            return JsonResult.error("源平台应用菜单为空");
        }
        List<Menu> finalMenuList = menuList;
        List<Menu> collect = menuList.stream()
                .filter(item -> Objects.equals(item.getParentId(), 0L))
                .map(item -> item.setChildren(getChild(item.getId(), finalMenuList)))
                .sorted(Comparator.comparingInt(menu -> (menu.getShowSort() == null ? 0 : menu.getShowSort())))
                .toList();
        Long parent_menuid = 0L;
        this.insertMenuTree(parent_menuid,collect,toPlatformAppConfId);
        return JsonResult.success("导入成功!");
    }

    private void insertMenuTree(Long parent_menuid,List<Menu> collect, Long toPlatformAppConfId){
        collect.forEach(menu -> {
            menu.setId(null);
            menu.setPlatformAppConfId(toPlatformAppConfId);
            menu.setParentId(parent_menuid);
            this.menuMapper.insert(menu);
            if(!menu.getChildren().isEmpty()) {
                this.insertMenuTree(menu.getId(),menu.getChildren(),toPlatformAppConfId);
            }
        });
    }

}
