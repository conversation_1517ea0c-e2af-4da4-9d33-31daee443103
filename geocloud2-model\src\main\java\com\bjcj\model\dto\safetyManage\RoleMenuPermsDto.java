package com.bjcj.model.dto.safetyManage;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author：qinyi
 * @Date：2023/12/12 16:04
 */
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RoleMenuPermsDto implements Serializable {

    @Schema(description="角色id")
    @NotNull(message = "角色id不能为空")
    private Long roleId;

    @Schema(description="菜单ids(逗号拼接)")
    @NotBlank(message = "菜单ids不能为空")
    private String menuIds;

    @Schema(description="permsIds(逗号拼接)")
    @NotBlank(message = "permsIds不能为空")
    private String permsIds;

    // @Schema(description="专题id")
    // private Long specialPlanId;

    @Schema(description="多专题勾选对象")
    List<SpecialPlanAndMenuIds> specialPlansAndMenuIds;


    private static final long serialVersionUID = 1L;

}
