package com.bjcj.web.safetyManage;

import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.personnalCenter.ModifyPwdDto;
import com.bjcj.service.safetyManage.UserService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author：qinyi
 * @Date：2023/12/21 14:34
 */
@RestController
@RequestMapping("personal")
@Tag(name = "个人中心")
@Validated
public class PersonCenterController {
    @Resource
    private UserService userService;

    @OperaLog(operaModule = "个人中心-修改密码",operaType = OperaLogConstant.UPDATE,operaDesc = "修改密码")
    @PostMapping("/modifyPwd")
    @Operation(summary = "修改密码", description = "修改密码")
    @ApiOperationSupport(order = 1)
    @RequestLock(prefix = "modifyPwd")
    @Transactional(rollbackFor = Exception.class)
    public JsonResult modifyPwd(@Valid @RequestBody ModifyPwdDto dto) throws Exception {
        return this.userService.modifyPwd(dto);
    }

}
