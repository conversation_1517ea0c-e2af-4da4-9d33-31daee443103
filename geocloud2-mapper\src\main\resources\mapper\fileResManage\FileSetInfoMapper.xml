<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.naturalresources.fileResManage.FileSetInfoMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.naturalresources.fileResManage.FileSetInfo">
    <!--@mbg.generated-->
    <!--@Table public.file_set_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="file_dir_id" jdbcType="BIGINT" property="fileDirId" />
    <result column="xzq_id" jdbcType="BIGINT" property="xzqId" />
    <result column="open_level" jdbcType="VARCHAR" property="openLevel" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="data_type" jdbcType="VARCHAR" property="dataType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="data_use_level" jdbcType="VARCHAR" property="dataUseLevel" />
    <result column="provider_name" jdbcType="VARCHAR" property="providerName" />
    <result column="provider_code" jdbcType="VARCHAR" property="providerCode" />
    <result column="provider_type" jdbcType="VARCHAR" property="providerType" />
    <result column="contacter" jdbcType="VARCHAR" property="contacter" />
    <result column="contacter_phonenum" jdbcType="VARCHAR" property="contacterPhonenum" />
    <result column="provider_addr" jdbcType="VARCHAR" property="providerAddr" />
    <result column="data_format" jdbcType="VARCHAR" property="dataFormat" />
    <result column="data_types" jdbcType="VARCHAR" property="dataTypes" />
    <result column="network_type" jdbcType="VARCHAR" property="networkType" />
    <result column="data_storage_capacity" jdbcType="VARCHAR" property="dataStorageCapacity" />
    <result column="data_production_time" jdbcType="TIMESTAMP" property="dataProductionTime" />
    <result column="data_res_status" jdbcType="VARCHAR" property="dataResStatus" />
    <result column="data_res_summary" jdbcType="VARCHAR" property="dataResSummary" />
    <result column="data_res_content" jdbcType="VARCHAR" property="dataResContent" />
    <result column="share_type" jdbcType="VARCHAR" property="shareType" />
    <result column="share_condition" jdbcType="VARCHAR" property="shareCondition" />
    <result column="share_way" jdbcType="VARCHAR" property="shareWay" />
    <result column="update_cycle" jdbcType="VARCHAR" property="updateCycle" />
    <result column="publish_date" jdbcType="TIMESTAMP" property="publishDate" />
    <result column="stop_date" jdbcType="TIMESTAMP" property="stopDate" />
    <result column="update_dates" jdbcType="TIMESTAMP" property="updateDates" />
    <result column="data_res_organizer" jdbcType="VARCHAR" property="dataResOrganizer" />
    <result column="data_res_custodian" jdbcType="VARCHAR" property="dataResCustodian" />
    <result column="data_res_manufacturer" jdbcType="VARCHAR" property="dataResManufacturer" />
    <result column="relation_res_name" jdbcType="VARCHAR" property="relationResName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", file_dir_id, xzq_id, open_level, "label", update_date, data_type, remark, 
    create_time, update_time, "operator", data_use_level, provider_name, provider_code, 
    provider_type, contacter, contacter_phonenum, provider_addr, data_format, data_types, 
    network_type, data_storage_capacity, data_production_time, data_res_status, data_res_summary, 
    data_res_content, share_type, share_condition, share_way, update_cycle, publish_date, 
    stop_date, update_dates, data_res_organizer, data_res_custodian, data_res_manufacturer, 
    relation_res_name
  </sql>
</mapper>