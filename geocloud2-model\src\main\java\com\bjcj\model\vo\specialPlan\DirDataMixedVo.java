package com.bjcj.model.vo.specialPlan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/1/26  10:17
*/
@Schema(description="方案与目录混合")
@Data
public class DirDataMixedVo implements Serializable {
    @Schema(description="id")
    private String id;

    @Schema(description="名称")
    private String dirName;

    @Schema(description="显示名称")
    private String showName;


    @Schema(description="显示顺序")
    private Integer showSort;

    @Schema(description="是否显示")
    private Boolean isShow;

    @Schema(description="是否展开")
    private Boolean isExpand;

    @Schema(description="是否初始化加载")
    private Boolean isInitLoad;

    @Schema(description="目录或专题(dir/data)")
    private String dataType;

    @Schema(description="子目录或专题")
    private List<DirDataMixedVo> children;

    @Schema(description="父节点id")
    private String parentId;

    @Schema(description="服务原始地址")
    private String url;

    @Schema(description="资源类型")
    private String resourcetype;
}