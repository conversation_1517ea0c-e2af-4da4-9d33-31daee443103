package com.bjcj.web.ore;

import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.ore.SoeYfkTwoPowersAnalysisResult;
import com.bjcj.service.ore.SoeYfkTwoPowersAnalysisResultService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024-12-04 09:31
 */
@Tag(name = "压覆矿", description = "压覆矿")
@RestController
@RequestMapping("/ore-cover")
public class OreCoverController {

    @Resource
    SoeYfkTwoPowersAnalysisResultService soeYfkTwoPowersAnalysisResultService;

    @Operation(summary = "查询分析后的结果")
    @ApiOperationSupport(order = 1)
    @GetMapping("/{analysisId}")
    public JsonResult list(@PathVariable String analysisId) {
        SoeYfkTwoPowersAnalysisResult analysisResult = soeYfkTwoPowersAnalysisResultService.getById(analysisId);
        return JsonResult.success(analysisResult);
    }


}
