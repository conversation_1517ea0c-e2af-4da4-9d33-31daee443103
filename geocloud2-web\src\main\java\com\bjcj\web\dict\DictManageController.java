package com.bjcj.web.dict;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.dict.SysDictDataDto;
import com.bjcj.model.dto.dict.SysDictTypeDto;
import com.bjcj.model.po.dict.SysDictData;
import com.bjcj.model.po.dict.SysDictType;
import com.bjcj.service.dict.SysDictDataService;
import com.bjcj.service.dict.SysDictTypeService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * @Author：qinyi
 * @Date：2024/2/1 10:26
 */
@RestController
@RequestMapping("/dict")
@Tag(name = "3字典")
@Validated
public class DictManageController {
    @Resource
    private SysDictTypeService sysDictTypeService;

    @Resource
    private SysDictDataService sysDictDataService;

    @SaCheckPermission("sys:read")
    @Operation(summary = "字典列表", description = "字典列表")
    @ApiOperationSupport(order = 1)
    @GetMapping("/page")
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "dictName", description = "字典名称", required = false),
            @Parameter(name = "dictType", description = "字典类型", required = false),
            @Parameter(name = "status", description = "状态（0正常 1停用）", required = false),
            @Parameter(name = "dictDataType", description = "字典类型(1普通,2级联,3分组)", required = false),
            @Parameter(name = "startTime", description = "开始时间", required = false),
            @Parameter(name = "endTime", description = "结束时间", required = false)
    })
    public JsonResult<Page<SysDictType>> page(@RequestParam("current") Integer page,
                                              @RequestParam("size") Integer pageSize,
                                              @RequestParam(value = "dictName",required = false) String dictName,
                                              @RequestParam(value = "dictType",required = false) String dictType,
                                              @RequestParam(value = "status",required = false) String status,
                                              @RequestParam(value = "dictDataType",required = false) String dictDataType,
                                              @RequestParam(value = "startTime",required = false) String startTime,
                                              @RequestParam(value = "endTime",required = false) String endTime){
        Page<SysDictType> pager = new Page<>(page, pageSize);
        return this.sysDictTypeService.pageDataSort(pager,dictName,dictType,status,dictDataType,startTime,endTime);
    }


    @OperaLog(operaModule = "字典-新增/编辑",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "字典-新增/编辑")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑字典", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "addOrEdit")
    public JsonResult addOrEdit(@Validated @RequestBody SysDictTypeDto dto){
        if(Objects.isNull(dto.getDictId()) && Objects.nonNull(this.sysDictTypeService.getOne(new LambdaQueryWrapper<SysDictType>().eq(SysDictType::getDictType,dto.getDictType()))))
            return JsonResult.error("字典类型已存在");
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        dto.setOperater(username);
        SysDictType sysDictType = BeanUtil.copyProperties(dto, SysDictType.class);
        return this.sysDictTypeService.saveOrUpdate(sysDictType) ? JsonResult.success() : JsonResult.error();
    }

    @OperaLog(operaModule = "字典-删除",operaType = OperaLogConstant.DELETE,operaDesc = "字典-删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除字典", description = "删除字典")
    @ApiOperationSupport(order = 3)
    @Transactional(rollbackFor = Exception.class)
    public JsonResult delete(@PathVariable("id") Long id){
        return this.sysDictTypeService.deleteById(id);
    }


    @OperaLog(operaModule = "字典值-新增/编辑",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "字典值-新增/编辑")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEditDictValue")
    @Operation(summary = "新增/编辑字典值", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 4)
    @RequestLock(prefix = "addOrEditDictValue")
    public JsonResult addOrEditDictValue(@Validated @RequestBody SysDictDataDto dto){
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        dto.setOperater(username);
        SysDictType sysDictType = this.sysDictTypeService.getOne(new LambdaQueryWrapper<SysDictType>().eq(SysDictType::getDictType,dto.getDictType()));
        if(sysDictType.getDictDataType().equals(2) && dto.getPid() == null) return JsonResult.error("级联字典值pid必填");
        if(sysDictType.getDictDataType().equals(3) && dto.getGroupName() == null) return JsonResult.error("分组字典值groupName必填");
        //级联字典排序值自增
        if(dto.getDictSort() == 1122){
            Long pid = dto.getPid();
            String dictType = dto.getDictType();
            int max_sort_value = this.sysDictDataService.selectMaxSortValue(pid,dictType);
            dto.setDictSort(max_sort_value);
        }
        return this.sysDictDataService.saveOrUpdate(BeanUtil.copyProperties(dto, SysDictData.class)) ? JsonResult.success() : JsonResult.error();
    }

    @OperaLog(operaModule = "字典值-删除",operaType = OperaLogConstant.DELETE,operaDesc = "字典值-删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/deleteDictValue/{id}")
    @Operation(summary = "删除字典值", description = "删除字典值")
    @ApiOperationSupport(order = 5)
    @Transactional(rollbackFor = Exception.class)
    public JsonResult deleteDictValue(@PathVariable("id") Long id){
        return this.sysDictDataService.removeById(id) ? JsonResult.success() : JsonResult.error();
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/dictDataList")
    @Operation(summary = "字典值-列表", description = "字典值-列表")
    @ApiOperationSupport(order = 6)
    @Parameters({
            @Parameter(name = "dictType", description = "字典类型", required = true),
            @Parameter(name = "dictLabel", description = "字典标签", required = false),
            @Parameter(name = "status", description = "状态（0正常 1停用）", required = false),
            @Parameter(name = "groupName", description = "分组", required = false)
    })
    public JsonResult dictDataList(@RequestParam("dictType") String dictType,
                                   @RequestParam(value = "dictLabel",required = false) String dictLabel,
                                   @RequestParam(value = "status",required = false) String status,
                                   @RequestParam(value = "groupName",required = false) String groupName){
        SysDictType sysDictType = this.sysDictTypeService.getOne(new LambdaQueryWrapper<SysDictType>().eq(SysDictType::getDictType,dictType));
        switch (sysDictType.getDictDataType()){
            case "1":
                return JsonResult.success(this.sysDictDataService.list(new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getDictType,dictType).eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dictLabel), SysDictData::getDictLabel, dictLabel).eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(status), SysDictData::getStatus, status).eq(SysDictData::getStatus,"0")));
            case "2":
                //返回父子级树
                return this.sysDictDataService.queryTreeList(dictType,dictLabel,status,groupName);
            case "3":
                //返回分组结构
                return this.sysDictDataService.queryGroupList(dictType,dictLabel,status);
        }
        return JsonResult.error("请确认字典类型参数是否正确");
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/dictDataListManage")
    @Operation(summary = "字典值-列表(管理,统一树结构)", description = "字典值-列表(管理,统一树结构)")
    @ApiOperationSupport(order = 7)
    @Parameters({
            @Parameter(name = "dictType", description = "字典类型", required = true),
            @Parameter(name = "dictLabel", description = "字典标签", required = false),
            @Parameter(name = "status", description = "状态（0正常 1停用）", required = false),
            @Parameter(name = "groupName", description = "组名", required = false)
    })
    public JsonResult dictDataListManage(@RequestParam("dictType") String dictType,
                                   @RequestParam(value = "dictLabel",required = false) String dictLabel,
                                   @RequestParam(value = "status",required = false) String status,
                                         @RequestParam(value = "groupName",required = false) String groupName){
        return this.sysDictDataService.queryTreeList(dictType,dictLabel,status,groupName);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/dictGroupList")
    @Operation(summary = "字典值-组列表", description = "字典值-组列表")
    @ApiOperationSupport(order = 8)
    @Parameters({
            @Parameter(name = "dictType", description = "字典类型", required = true)
    })
    public JsonResult dictGroupList(@RequestParam("dictType") String dictType){
        return this.sysDictDataService.queryGroups(dictType);
    }


}
