package com.bjcj.service.naturalresources.categories;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.cloudportal.UserCollectedMapper;
import com.bjcj.mapper.naturalresources.categories.*;
import com.bjcj.mapper.naturalresources.resReview.UserServrReviewMapper;
import com.bjcj.mapper.platformManage.specialPlan.SpecialPlanDataMapper;
import com.bjcj.mapper.platformManage.specialPlan.SpecialPlanDirMapper;
import com.bjcj.mapper.platformManage.specialPlan.SpecialPlanMapper;
import com.bjcj.mapper.safetyManage.SafetyUserRoleMapper;
import com.bjcj.mapper.safetyManage.SysRoleCateMapper;
import com.bjcj.model.po.cloudportal.UserCollected2;
import com.bjcj.model.po.naturalresources.categories.*;
import com.bjcj.model.po.naturalresources.resReview.UserServrReview;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlan;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlanData;
import com.bjcj.model.po.platformManage.specialPlan.SpecialPlanDir;
import com.bjcj.model.po.safetyManage.SafetyDept;
import com.bjcj.model.po.safetyManage.SafetyUserRole;
import com.bjcj.model.po.safetyManage.SysRoleCate;
import com.bjcj.model.vo.naturalresources.categories.CategoriesDataVo;
import com.bjcj.service.safetyManage.CheckIsAdmin;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Cleanup;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName dataService
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/11/27 10:02
 */
@Service
public class CategoriesDataService extends ServiceImpl<CategoriesDataMapper, CategoriesData> {

    @Resource
    private CategoriesDataMapper dataServiceMapper;

    @Resource
    private CategoriesDataInfoMapper categoriesDataInfoMapper;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private HttpServletResponse response;

    @Resource
    private CategoriesServiceMapper categoriesServiceMapper;

    @Resource
    private UserCollectedMapper userCollectedMapper;

    @Resource
    private UserServrReviewMapper userServrReviewMapper;

    @Resource
    CheckIsAdmin checkIsAdmin;

    @Resource
    SpecialPlanMapper specialPlanMapper;

    @Resource
    SpecialPlanDirMapper specialPlanDirMapper;

    @Resource
    SpecialPlanDataMapper specialPlanDataMapper;

    @Resource
    SafetyUserRoleMapper safetyUserRoleMapper;

    @Resource
    SysRoleCateMapper sysRoleCateMapper;

    @Resource
    ResourceServicesMapper resourceServicesMapper;

    @Resource
    ResourceCatalogsMapper resourceCatalogsMapper;

    @Resource
    ResourceDataitemsMapper resourceDataitemsMapper;

    @Resource
    ResourceTagsMapper resourceTagsMapper;



    public JsonResult<List<CategoriesDataVo>> treeList() {
        LambdaQueryWrapper<CategoriesData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CategoriesData::getParentId, 0)
                .eq(CategoriesData::getDel, 0)
                .eq(CategoriesData::getStatus, true)
                .orderByAsc(CategoriesData::getDisplayOrder);
        //查询根级
        List<CategoriesData> categoriesData = dataServiceMapper.selectList(wrapper);
        List<CategoriesDataVo> list = categoriesData.stream().map(p -> {
            CategoriesDataVo obj = new CategoriesDataVo();
            BeanUtils.copyProperties(p, obj);
            return obj;
        }).collect(Collectors.toList());

        list.forEach(item->{getChildren(item,1);});

        return JsonResult.success(list);
    }

    private void getChildren(CategoriesDataVo item, int index){
        LambdaQueryWrapper<CategoriesData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CategoriesData::getParentId, item.getId())
                .eq(CategoriesData::getDel, 0)
                .orderByAsc(CategoriesData::getDisplayOrder);
        if(index == 1){
            wrapper.eq(CategoriesData::getStatus, true);
        }

        //根据parentId查询
        List<CategoriesData> list = dataServiceMapper.selectList(wrapper);

        List<CategoriesDataVo> voList = list.stream().map(p -> {
            CategoriesDataVo vo = new CategoriesDataVo();
            BeanUtils.copyProperties(p, vo);
            return vo;
        }).collect(Collectors.toList());
        //写入到children
        item.setChildren(voList);

        //如果children不为空，继续往下找
        if (!CollectionUtils.isEmpty(voList)) {
            voList.forEach(items->{getChildren(items,index);});
        }
    }

    public JsonResult lists(String name,String code) {

        //查询根级
        List<CategoriesDataVo> list = dataServiceMapper.selectList(
                        Wrappers.<CategoriesData>query().lambda()
                                .eq(CategoriesData::getParentId, 0)
                                .eq(CategoriesData::getDel, 0)
                                .eq(StringUtils.isNotBlank(name), CategoriesData::getName, name)
                                .eq(StringUtils.isNotBlank(code), CategoriesData::getCode, code)
                                .orderByAsc(CategoriesData::getDisplayOrder)
                ).stream()
                .map(item -> BeanUtil.copyProperties(item, CategoriesDataVo.class)).collect(Collectors.toList());
        list.forEach(item->{getChildren(item,2);});
        // for(int i=0;i<list.size();i++){
        //     List<CategoriesDataInfo> categoriesDataInfos = this.categoriesDataInfoMapper.selectList(new LambdaQueryWrapper<CategoriesDataInfo>().eq(CategoriesDataInfo::getCatalogue, String.valueOf(list.get(i).getId())));
        //     if(categoriesDataInfos.size() > 0){
        //         list.remove(list.get(i));
        //         i--;
        //     }
        // }
        return JsonResult.success(list);

    }

    public JsonResult<CategoriesData> findById(Long id){
        CategoriesData categoriesData = dataServiceMapper.selectById(id);
        return JsonResult.success(categoriesData);
    }

    public JsonResult del(Long id) {
        if(!this.dataServiceMapper.selectList(new LambdaQueryWrapper<CategoriesData>().eq(CategoriesData::getParentId, id).eq(CategoriesData::getDel,0)).isEmpty()){
            return JsonResult.error("该目录下还有子目录，请先删除子目录");
        }
        LambdaUpdateWrapper<CategoriesData> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(CategoriesData::getDel, 1)
                .eq(CategoriesData::getId, id);
        int result = dataServiceMapper.update(wrapper);

        return JsonResult.success(result);
    }

    public JsonResult uptStatus(Long id, Boolean status) {
        LambdaUpdateWrapper<CategoriesData> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(CategoriesData::getStatus, status)
                .eq(CategoriesData::getId, id);
        int result = dataServiceMapper.update(wrapper);

        return JsonResult.success(result);
    }

    public JsonResult exportJson(){

        LambdaQueryWrapper<CategoriesData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CategoriesData::getDel, 0)
                .eq(CategoriesData::getStatus, true);
        List<CategoriesData> entities = dataServiceMapper.selectList(wrapper);
        try (
            // 获取响应输出流
            OutputStream outputStream = response.getOutputStream();){
            // 设置响应头
            response.setContentType("application/json");
            response.setHeader("Content-Disposition", "attachment; filename=\"dataServiceCategoriesData.json\"");
            // 将查询到的结果序列化为json格式
            String jsonString = JSON.toJSONString(entities);
            // 将序列化的json数据集写入到输出流中
            outputStream.write(jsonString.getBytes(StandardCharsets.UTF_8));
            // 推送输出流结果到浏览器
            outputStream.flush();
        }catch (Exception e){
            log.error("导出文件失败"+e);
        }

        return JsonResult.success();
    }

    public JsonResult importJson(MultipartFile file){
        List<CategoriesData> list = new ArrayList<>();
        try {
            @Cleanup InputStream inputStream = file.getInputStream();
            list = objectMapper.readValue(inputStream, new TypeReference<List<CategoriesData>>() {});
            this.saveBatch(list);
        }catch (Exception e) {
            log.error("导入文件失败"+e);
        }

        return JsonResult.success();
    }

    public List<SpecialPlanDir> cataDataList(String appName) {
        //根据appName查询专题方案id
        Long special_plan_id = this.specialPlanMapper.selectOne(new LambdaQueryWrapper<SpecialPlan>().eq(SpecialPlan::getPlanName, appName)).getId();
        return this.specialPlanDirMapper.selectList(new LambdaQueryWrapper<SpecialPlanDir>().eq(SpecialPlanDir::getParentId, "0").eq(SpecialPlanDir::getIsShow, true).eq(SpecialPlanDir::getSpecialPlanId, special_plan_id).orderByAsc(SpecialPlanDir::getShowSort));
    }

    /**
    public List<CategoriesData> treeListByCataId(Long cataId, Long userId,String searchStr,String appName) {
        //树结构
        List<CategoriesData> cataList = this.dataServiceMapper.selectList(new LambdaQueryWrapper<CategoriesData>()
                // .eq(CategoriesData::getParentId, cataId)
                .eq(CategoriesData::getStatus, true)
                .eq(CategoriesData::getDel, 0)
                .orderByAsc(CategoriesData::getCreateTime));
        List<CategoriesData> finalCataList = cataList;
        List<CategoriesData> collect = cataList.stream()
                .filter(item -> !Objects.equals(item.getParentId(), 0L))
                .map(item -> item.setChildren(getChild(item.getId(), finalCataList)))
                .sorted(Comparator.comparingInt(CategoriesData::getDisplayOrder))
                .collect(Collectors.toList());
        //不从sql中根据name like过滤,因为无法加载出完整的树结构,在程序代码中过滤
        boolean exist = false;
        if(Objects.nonNull(searchStr) && !searchStr.isEmpty()) {
            for (int i = 0; i < finalCataList.size(); i++) {
                if(finalCataList.get(i).getName().contains(searchStr)) {
                    exist = true;
                    break;
                }
            }
            if(!exist){
                return new ArrayList<>();
            }else{
                List<CategoriesData> cataByNameList = getCataByName(collect, searchStr);
                for(int i=0;i<cataByNameList.size();i++) {
                    if (!String.valueOf(cataByNameList.get(i).getParentId()).equals(String.valueOf(cataId))) {
                        cataByNameList.remove(cataByNameList.get(i));
                        i--;
                    }

                }
                return cataByNameList;
            }
        }
        for(int i=0;i<collect.size();i++) {
            if (!String.valueOf(collect.get(i).getParentId()).equals(String.valueOf(cataId))) {
                collect.remove(collect.get(i));
                i--;
            }

        }
        return collect;
    }
*/

    public List<SpecialPlanDir> treeListByCataIdSpecial(String cataId, Long userId,String searchStr,String appName) {
        //根据appName查询专题方案id
        Long special_plan_id = this.specialPlanMapper.selectOne(new LambdaQueryWrapper<SpecialPlan>().eq(SpecialPlan::getPlanName, appName)).getId();
        //树结构
        List<SpecialPlanDir> cataList = this.specialPlanDirMapper.selectList(new LambdaQueryWrapper<SpecialPlanDir>()
                .eq(SpecialPlanDir::getSpecialPlanId, special_plan_id).eq(SpecialPlanDir::getIsShow, true)
                .orderByAsc(SpecialPlanDir::getCreateTime));
        List<SpecialPlanDir> finalCataList = cataList;
        List<SpecialPlanDir> collect = cataList.stream()
                .filter(item -> !Objects.equals(item.getParentId(), "0"))
                .map(item -> item.setChildren(getChild2(item.getId(), finalCataList)))
                .sorted(Comparator.comparingInt(SpecialPlanDir::getShowSort))
                .collect(Collectors.toList());
        //不从sql中根据name like过滤,因为无法加载出完整的树结构,在程序代码中过滤
        boolean exist = false;
        if(Objects.nonNull(searchStr) && !searchStr.isEmpty()) {
            for (int i = 0; i < finalCataList.size(); i++) {
                if(finalCataList.get(i).getDirName().contains(searchStr)) {
                    exist = true;
                    break;
                }
            }
            if(!exist){
                return new ArrayList<>();
            }else{
                List<SpecialPlanDir> cataByNameList = getCataByName2(collect, searchStr);
                for(int i=0;i<cataByNameList.size();i++) {
                    if (!String.valueOf(cataByNameList.get(i).getParentId()).equals(String.valueOf(cataId))) {
                        cataByNameList.remove(cataByNameList.get(i));
                        i--;
                    }

                }
                return cataByNameList;
            }
        }
        for(int i=0;i<collect.size();i++) {
            if (!String.valueOf(collect.get(i).getParentId()).equals(String.valueOf(cataId))) {
                collect.remove(collect.get(i));
                i--;
            }

        }
        return collect;
    }


    // private List<CategoriesData> getChild(String id, List<CategoriesData> cataList){
    //     return cataList.stream()
    //             .filter(item -> Objects.equals(item.getParentId(), id))
    //             .map(item -> item.setChildren(getChild(item.getId(), cataList)))
    //             .sorted(Comparator.comparingInt(CategoriesData::getDisplayOrder))
    //             .collect(Collectors.toList());
    // }

    private List<SpecialPlanDir> getChild2(String id, List<SpecialPlanDir> cataList){
        return cataList.stream()
                .filter(item -> Objects.equals(item.getParentId(), id))
                .map(item -> item.setChildren(getChild2(item.getId(), cataList)))
                .sorted(Comparator.comparingInt(SpecialPlanDir::getShowSort))
                .collect(Collectors.toList());
    }

    /**
     * 在树结构上做模糊查询(剪枝操作）
     */
    private List<CategoriesData> getCataByName(List<CategoriesData> cataList,String selectName){
        Iterator<CategoriesData> iterator = cataList.iterator();
        while(iterator.hasNext()){
            CategoriesData cata = iterator.next();
            if(!cata.getName().contains(selectName)){
                List<CategoriesData> childrenList = cata.getChildren();
                if(!com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(childrenList)){
                    getCataByName(childrenList, selectName);
                }
                if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(childrenList)){
                    iterator.remove();
                }
            }
        }
        return cataList;
    }

    private List<SpecialPlanDir> getCataByName2(List<SpecialPlanDir> cataList,String selectName){
        Iterator<SpecialPlanDir> iterator = cataList.iterator();
        while(iterator.hasNext()){
            SpecialPlanDir cata = iterator.next();
            if(!cata.getDirName().contains(selectName)){
                List<SpecialPlanDir> childrenList = cata.getChildren();
                if(!com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(childrenList)){
                    getCataByName2(childrenList, selectName);
                }
                if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(childrenList)){
                    iterator.remove();
                }
            }
        }
        return cataList;
    }

    public JsonResult<Page<ResourceServices2>> serviceList(String cataDataId, Page<SafetyDept> pager, String sort,
                                                           String order, String startYear, String endYear,
                                                           String scale, String publishInstitutionName) {
        List<String> ids = new ArrayList<>();
        List<String> ids2 = new ArrayList<>();
        List<SpecialPlanDir> dataList = this.dataServiceMapper.findSelfAndChildrenByIdDir(cataDataId);
        ids.addAll(dataList.stream().map(item -> String.valueOf(item.getId())).toList());
        LambdaQueryWrapper<SpecialPlanData> datainfoWrapper = new LambdaQueryWrapper<SpecialPlanData>();
        ids.forEach(id -> {
            ids2.add(id);
        });
        datainfoWrapper.in(SpecialPlanData::getSpecialPlanDirId,ids2);
        List<String> datainfoids = this.specialPlanDataMapper.selectList(datainfoWrapper)
                .stream()
                .map(SpecialPlanData::getCataDataInfoId).toList();
        //todo 处理datainfoids  这里面应该去掉私有的(除了已经被授权的私有数据) 先找到已经授权给我所在的角色下的私有数据idList--这块先不管,私有暂时不处理
        if(datainfoids.isEmpty()) {
            return JsonResult.success(new Page<>());
        }
        String ordersql = "";
        if(Objects.nonNull(sort) && Objects.nonNull(order)){
            ordersql = "order by cs."+sort+" "+order;
        }
        if(StringUtils.isBlank(startYear)){
            startYear="0";
        }
        if(StringUtils.isBlank(endYear)){
            endYear="0";
        }
        List<ResourceServices2> cataServiceList = this.resourceServicesMapper.selectServicePage2(
                (pager.getCurrent() - 1) * pager.getSize(),
                pager.getSize(),
                datainfoids,
                ordersql,
                Integer.parseInt(startYear),
                Integer.parseInt(endYear),
                scale,publishInstitutionName
        );
        int count = this.resourceServicesMapper.selectServicePage2Count(
                (pager.getCurrent() - 1) * pager.getSize(),
                pager.getSize(),
                datainfoids,
                ordersql,
                Integer.parseInt(startYear),
                Integer.parseInt(endYear),
                scale,
                publishInstitutionName
        );
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        //写入是否收藏和当前数据权限信息
        cataServiceList.parallelStream().forEachOrdered(cataservice -> {
            UserCollected2 userCollected = this.userCollectedMapper.selectOneData(userId, cataservice.getId());
            cataservice.setIsCollect(userCollected != null);
            UserServrReview userServrReview = this.userServrReviewMapper.selectByUserIdAndResId(userId, cataservice.getId());
            //查询用户是否超管  //todo 超管查看所有数据的权限也去掉,现在专题里都需要申请
            // Boolean admin = checkIsAdmin.checkIsAdmin(principal.getId());
            // if(admin) cataservice.setHasPermission("2");
            // else{
            if(Objects.isNull(userServrReview)) {
                cataservice.setHasPermission("0");
            } else{
                if(userServrReview.getReviewStatus() == 0) {
                    cataservice.setHasPermission("3");
                }
                if(userServrReview.getReviewStatus() == 1) {
                    cataservice.setHasPermission("1");
                }
                if(userServrReview.getReviewStatus() == 2) {
                    cataservice.setHasPermission("0");
                }
            }

            //如果父级datainfo中的类型是公开  设置为2  无需申请
            //安全0 公开1 私有2
            int authItype = cataservice.getAuthoritytype();
            if(authItype == 1) {
                cataservice.setHasPermission("2");
            }
            // }
        });
        //再查询当前登录用户的角色,去主动授权的表中按角色查询哪些数据是已经被授权的,然后权限标识字段再重新覆盖一下
        List<Long> roleIds = new ArrayList<>();
        List<SafetyUserRole> userRoles = this.safetyUserRoleMapper.selectList(
                new LambdaQueryWrapper<SafetyUserRole>().eq(SafetyUserRole::getUserId, userId)
        );
        if(!userRoles.isEmpty()){
            roleIds.addAll(userRoles.stream().map(item -> item.getRoleId()).toList());
            List<SysRoleCate> sysRoleCateList = this.sysRoleCateMapper.selectList(new LambdaQueryWrapper<SysRoleCate>().in(SysRoleCate::getRoleId, roleIds));
            List<String> cataIds = sysRoleCateList.stream().map(item -> item.getCateId()).toList();
            cataServiceList.parallelStream().forEachOrdered(cataservice -> {
                if(cataIds.contains(cataservice.getId())){
                    cataservice.setHasPermission("1");
                }
            });
        }
        cataServiceList.forEach(item ->{
            item.setUrl("涉密数据,无权查看");
        });
        Page<ResourceServices2> page = new Page<>();
        page.setRecords(cataServiceList);
        page.setTotal(count);
        return JsonResult.success(page);
    }

    public JsonResult<Page<ResourceServices2>> serviceLists(String cataDataId, Page<SafetyDept> pager, String sort, String order, String startYear, String endYear, String scale, String publishInstitutionName) {
        List<String> ids = new ArrayList<>();
        List<ResourceCatalogs> dataList = this.resourceCatalogsMapper.findSelfAndChildrenById(cataDataId);
        ids.addAll(dataList.stream().map(item -> String.valueOf(item.getId())).toList());
        LambdaQueryWrapper<ResourceDataitems> datainfoWrapper = new LambdaQueryWrapper<ResourceDataitems>();
        datainfoWrapper.in(ResourceDataitems::getResourcecatalogid,ids);
        List<String> datainfoids = this.resourceDataitemsMapper.selectList(datainfoWrapper).stream().map(ResourceDataitems::getId).toList();
        //todo 处理datainfoids  这里面应该去掉私有的(除了已经被授权的私有数据) 先找到已经授权给我所在的角色下的私有数据idList--这块先不管,私有暂时不处理
        if(datainfoids.isEmpty())  return JsonResult.success(new Page<>());
        String ordersql = "";
        if(Objects.nonNull(sort) && Objects.nonNull(order)){
            ordersql = "order by cs."+sort+" "+order;
        }
        if(StringUtils.isBlank(startYear)){
            startYear="0";
        }
        if(StringUtils.isBlank(endYear)){
            endYear="0";
        }
        List<ResourceServices2> cataServiceList = this.resourceServicesMapper.selectServicePage2((pager.getCurrent() - 1) * pager.getSize(),pager.getSize(),datainfoids,ordersql,Integer.parseInt(startYear),Integer.parseInt(endYear),scale,publishInstitutionName);
        int count = this.resourceServicesMapper.selectServicePage2Count((pager.getCurrent() - 1) * pager.getSize(),pager.getSize(),datainfoids,ordersql,Integer.parseInt(startYear),Integer.parseInt(endYear),scale,publishInstitutionName);
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        //写入是否收藏和当前数据权限信息
        cataServiceList.parallelStream().forEachOrdered(cataservice -> {
            UserCollected2 userCollected = this.userCollectedMapper.selectOneData(userId, cataservice.getId());
            cataservice.setIsCollect(userCollected != null);
            UserServrReview userServrReview = this.userServrReviewMapper.selectByUserIdAndResId(userId, cataservice.getId());
            //查询用户是否超管  //todo 超管查看所有数据的权限也去掉,现在专题里都需要申请
            // Boolean admin = checkIsAdmin.checkIsAdmin(principal.getId());
            // if(admin) cataservice.setHasPermission("2");
            // else{

            if(Objects.isNull(userServrReview)) cataservice.setHasPermission("0");
            else{
                if(userServrReview.getReviewStatus() == 0) cataservice.setHasPermission("3");
                if(userServrReview.getReviewStatus() == 1) cataservice.setHasPermission("1");
                if(userServrReview.getReviewStatus() == 2) cataservice.setHasPermission("0");
            }

            //如果父级datainfo中的类型是公开  设置为2  无需申请
            //安全0 公开1 私有2
            int authItype = Integer.parseInt(String.valueOf(this.resourceDataitemsMapper.selectById(cataservice.getDataitemid()).getAuthoritytype()));
            // int authItype = cataservice.getAuthoritytype();

            if(authItype == 1) cataservice.setHasPermission("2");
            // }
        });
        //再查询当前登录用户的角色,去主动授权的表中按角色查询哪些数据是已经被授权的,然后权限标识字段再重新覆盖一下
        List<Long> roleIds = new ArrayList<>();
        List<SafetyUserRole> userRoles = this.safetyUserRoleMapper.selectList(
                new LambdaQueryWrapper<SafetyUserRole>().eq(SafetyUserRole::getUserId, userId));
        if(!userRoles.isEmpty()){
            roleIds.addAll(userRoles.stream().map(item -> item.getRoleId()).toList());
            List<SysRoleCate> sysRoleCateList = this.sysRoleCateMapper.selectList(new LambdaQueryWrapper<SysRoleCate>().in(SysRoleCate::getRoleId, roleIds));
            List<String> cataIds = sysRoleCateList.stream().map(item -> item.getCateId()).toList();
            cataServiceList.parallelStream().forEachOrdered(cataservice -> {
                if(cataIds.contains(cataservice.getId())){
                    cataservice.setHasPermission("1");
                }
            });
        }
        List<String> tagList = new ArrayList<>();
        cataServiceList.stream().forEach(cata -> {
            cata.setUrl("涉密数据,无权查看");
            List<ResourceTags> resourceTags = this.resourceTagsMapper.selectList(new LambdaQueryWrapper<ResourceTags>().eq(ResourceTags::getResourceid, cata.getId()));
            if(!resourceTags.isEmpty()) {
                tagList.addAll(resourceTags.stream().map(ResourceTags::getName).toList());
            }
        });
        Page<ResourceServices2> page = new Page<>();
        page.setRecords(cataServiceList);
        page.setTotal(count);
        //tagList转换为逗号拼接的字符串
        page.setCountId(tagList.stream().distinct().collect(Collectors.joining(",")));
        return JsonResult.success(page);
    }


    public JsonResult<Page<ResourceServices2>> serviceLists2(String cataDataId, Page<SafetyDept> pager, String sort, String order, String startYear, String endYear, String scale, String publishInstitutionName, String name) {
        List<String> ids = new ArrayList<>();
        List<ResourceCatalogs> dataList = this.resourceCatalogsMapper.findSelfAndChildrenById(cataDataId);
        ids.addAll(dataList.stream().map(item -> String.valueOf(item.getId())).toList());
        String ordersql = "";
        if(Objects.nonNull(sort) && Objects.nonNull(order)){
            ordersql = "order by cs."+sort+" "+order;
        }
        List<ResourceServices2> cataServiceList = this.resourceServicesMapper.selectServicePage22((pager.getCurrent() - 1) * pager.getSize(),pager.getSize(),ids,ordersql,startYear,endYear,scale,publishInstitutionName,name);
        int count = this.resourceServicesMapper.selectServicePage22Count((pager.getCurrent() - 1) * pager.getSize(),pager.getSize(),ids,ordersql,startYear,endYear,scale,publishInstitutionName,name);
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        //写入是否收藏和当前数据权限信息
        cataServiceList.parallelStream().forEachOrdered(cataservice -> {
            UserCollected2 userCollected = this.userCollectedMapper.selectOneData(userId, cataservice.getId());
            cataservice.setIsCollect(userCollected != null);
            UserServrReview userServrReview = this.userServrReviewMapper.selectByUserIdAndResId(userId, cataservice.getId());
            //查询用户是否超管  //todo 超管查看所有数据的权限也去掉,现在专题里都需要申请
            // Boolean admin = checkIsAdmin.checkIsAdmin(principal.getId());
            // if(admin) cataservice.setHasPermission("2");
            // else{

            if(Objects.isNull(userServrReview)) cataservice.setHasPermission("0");
            else{
                if(userServrReview.getReviewStatus() == 0) cataservice.setHasPermission("3");
                if(userServrReview.getReviewStatus() == 1) cataservice.setHasPermission("1");
                if(userServrReview.getReviewStatus() == 2) cataservice.setHasPermission("0");
            }

            //如果父级datainfo中的类型是公开  设置为2  无需申请
            //安全0 公开1 私有2
            int authItype = cataservice.getAuthoritytype();
            if(authItype == 1) cataservice.setHasPermission("2");
            // }
        });
        //再查询当前登录用户的角色,去主动授权的表中按角色查询哪些数据是已经被授权的,然后权限标识字段再重新覆盖一下
        List<Long> roleIds = new ArrayList<>();
        List<SafetyUserRole> userRoles = this.safetyUserRoleMapper.selectList(new LambdaQueryWrapper<SafetyUserRole>().eq(SafetyUserRole::getUserId, userId));
        if(!userRoles.isEmpty()){
            roleIds.addAll(userRoles.stream().map(item -> item.getRoleId()).toList());
            List<SysRoleCate> sysRoleCateList = this.sysRoleCateMapper.selectList(new LambdaQueryWrapper<SysRoleCate>().in(SysRoleCate::getRoleId, roleIds));
            List<String> cataIds = sysRoleCateList.stream().map(item -> item.getCateId()).toList();
            cataServiceList.parallelStream().forEachOrdered(cataservice -> {
                if(cataIds.contains(cataservice.getId())){
                    cataservice.setHasPermission("1");
                }
            });
        }
        List<String> tagList = new ArrayList<>();
        cataServiceList.stream().forEach(cata -> {
            cata.setUrl("涉密数据,无权查看");
            List<ResourceTags> resourceTags = this.resourceTagsMapper.selectList(new LambdaQueryWrapper<ResourceTags>().eq(ResourceTags::getResourceid, cata.getId()));
            if(!resourceTags.isEmpty()) {
                tagList.addAll(resourceTags.stream().map(ResourceTags::getName).toList());
            }
        });
        Page<ResourceServices2> page = new Page<>();
        page.setRecords(cataServiceList);
        page.setTotal(count);
        //tagList转换为逗号拼接的字符串
        page.setCountId(tagList.stream().distinct().collect(Collectors.joining(",")));
        return JsonResult.success(page);
    }


    // public JsonResult<Integer> queryServiceCountByCataId(Long cataDataId) {
    //     int count = 0;
    //     List<SpecialPlanData> specialPlanData = this.specialPlanDataMapper.selectList(new LambdaQueryWrapper<SpecialPlanData>().eq(SpecialPlanData::getSpecialPlanDirId, cataDataId));
    //     count = specialPlanData.size();
    //     return JsonResult.success(count);
    // }

    public JsonResult<Integer> queryServiceCountByCataId(String cataDataId) {
        //需要先判断这个目录是自建的还是copy过来的目录  分两种情况处理
        ResourceCatalogs cd = this.resourceCatalogsMapper.selectById(cataDataId);
        if(Objects.nonNull(cd)){
            List<String> ids = new ArrayList<>();
            List<String> ids2 = new ArrayList<>();
            List<ResourceCatalogs> dataList = this.resourceCatalogsMapper.findSelfAndChildrenById(cataDataId);
            ids.addAll(dataList.stream().filter(item -> item.getResourcecategory() == 6).map(item -> String.valueOf(item.getId())).toList());
            ids2.addAll(dataList.stream().filter(item -> item.getResourcecategory() == 1).map(item -> String.valueOf(item.getId())).toList());
            if(ids.size() != 0){
                LambdaQueryWrapper<ResourceDataitems> datainfoWrapper = new LambdaQueryWrapper<ResourceDataitems>();
                datainfoWrapper.in(!ids.isEmpty(),ResourceDataitems::getResourcecatalogid,ids)
                        .eq(ResourceDataitems::getStatus,1);

                List<String> datainfoids = this.resourceDataitemsMapper.selectList(datainfoWrapper).stream().map(ResourceDataitems::getId).toList();
                int count = 0;
                if(datainfoids.isEmpty()){
                    return JsonResult.success(0);
                }else {
                    count = this.resourceServicesMapper.selectServiceCountByIdIn(datainfoids);
                    return JsonResult.success(count);
                }
            }
            if(!ids2.isEmpty()){
                int count = 0;
                count = this.resourceServicesMapper.selectServiceCountByIdIn2(ids2);
                return JsonResult.success(count);
            }

        }
        else{
            int count = 0;
            List<SpecialPlanData> specialPlanData = this.specialPlanDataMapper.selectList(new LambdaQueryWrapper<SpecialPlanData>().like(SpecialPlanData::getSpecialPlanDirId, cataDataId));
            count = specialPlanData.size();
            return JsonResult.success(count);
        }
        return null;
    }

    public JsonResult<Integer> queryServiceCountByCataId2(String cataDataId) {
        //需要先判断这个目录是自建的还是copy过来的目录  分两种情况处理
        ResourceCatalogs cd = this.resourceCatalogsMapper.selectById(cataDataId);
        if(Objects.nonNull(cd)){
            List<String> ids = new ArrayList<>();
            List<ResourceCatalogs> dataList = this.resourceCatalogsMapper.findSelfAndChildrenById(cataDataId);
            ids.addAll(dataList.stream().map(item -> String.valueOf(item.getId())).toList());
            // LambdaQueryWrapper<ResourceDataitems> datainfoWrapper = new LambdaQueryWrapper<ResourceDataitems>();
            // datainfoWrapper.in(!ids.isEmpty(),ResourceDataitems::getResourcecatalogid,ids)
            //         .eq(ResourceDataitems::getStatus,1);
            //
            // List<String> datainfoids = this.resourceDataitemsMapper.selectList(datainfoWrapper).stream().map(ResourceDataitems::getId).toList();
            int count = 0;
            if(ids.isEmpty()){
                return JsonResult.success(0);
            }else {
                count = this.resourceServicesMapper.selectList(new LambdaQueryWrapper<ResourceServices>().in(ResourceServices::getResourcecatalogid,ids)).size();
                return JsonResult.success(count);
            }
        }
        else{
            int count = 0;
            List<SpecialPlanData> specialPlanData = this.specialPlanDataMapper.selectList(new LambdaQueryWrapper<SpecialPlanData>().like(SpecialPlanData::getSpecialPlanDirId, cataDataId));
            count = specialPlanData.size();
            return JsonResult.success(count);
        }
    }

    public JsonResult<Page<ResourceServices2>> labelServiceLists(Page<SafetyDept> pager, String sort, String label, String order, String startYear, String endYear, String scale, String publishInstitutionName) {
        String ordersql = "";
        if(Objects.nonNull(sort) && Objects.nonNull(order)){
            ordersql = "order by cs."+sort+" "+order;
        }
        List<ResourceServices2> cataServiceList = this.resourceServicesMapper.selectServicePage3((pager.getCurrent() - 1) * pager.getSize(),pager.getSize(),ordersql,startYear,endYear,scale,publishInstitutionName,label.trim());
        int count = this.resourceServicesMapper.selectServicePage3Count((pager.getCurrent() - 1) * pager.getSize(),pager.getSize(),ordersql,startYear,endYear,scale,publishInstitutionName,label.trim());
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        //写入是否收藏和当前数据权限信息
        cataServiceList.parallelStream().forEachOrdered(cataservice -> {
            UserCollected2 userCollected = this.userCollectedMapper.selectOneData(userId, cataservice.getId());
            cataservice.setIsCollect(userCollected != null);
            UserServrReview userServrReview = this.userServrReviewMapper.selectByUserIdAndResId(userId, cataservice.getId());
            //查询用户是否超管  //todo 超管查看所有数据的权限也去掉,现在专题里都需要申请
            // Boolean admin = checkIsAdmin.checkIsAdmin(principal.getId());
            // if(admin) cataservice.setHasPermission("2");
            // else{

            if(Objects.isNull(userServrReview)) cataservice.setHasPermission("0");
            else{
                if(userServrReview.getReviewStatus() == 0) cataservice.setHasPermission("3");
                if(userServrReview.getReviewStatus() == 1) cataservice.setHasPermission("1");
                if(userServrReview.getReviewStatus() == 2) cataservice.setHasPermission("0");
            }

            //如果父级datainfo中的类型是公开  设置为2  无需申请
            //安全0 公开1 私有2
            int authItype = cataservice.getAuthoritytype();
            if(authItype == 1) cataservice.setHasPermission("2");
            // }
        });
        //再查询当前登录用户的角色,去主动授权的表中按角色查询哪些数据是已经被授权的,然后权限标识字段再重新覆盖一下
        List<Long> roleIds = new ArrayList<>();
        List<SafetyUserRole> userRoles = this.safetyUserRoleMapper.selectList(new LambdaQueryWrapper<SafetyUserRole>().eq(SafetyUserRole::getUserId, userId));
        if(!userRoles.isEmpty()){
            roleIds.addAll(userRoles.stream().map(item -> item.getRoleId()).toList());
            List<SysRoleCate> sysRoleCateList = this.sysRoleCateMapper.selectList(new LambdaQueryWrapper<SysRoleCate>().in(SysRoleCate::getRoleId, roleIds));
            List<String> cataIds = sysRoleCateList.stream().map(SysRoleCate::getCateId).toList();
            cataServiceList.parallelStream().forEachOrdered(cataservice -> {
                if(cataIds.contains(cataservice.getId())){
                    cataservice.setHasPermission("1");
                }
            });
        }
        List<String> tagList = new ArrayList<>();
        cataServiceList.stream().forEach(cata -> {
            cata.setUrl("涉密数据,无权查看");
            List<ResourceTags> resourceTags = this.resourceTagsMapper.selectList(new LambdaQueryWrapper<ResourceTags>().eq(ResourceTags::getResourceid, cata.getId()));
            if(!resourceTags.isEmpty()) {
                tagList.addAll(resourceTags.stream().map(ResourceTags::getName).toList());
            }
        });
        Page<ResourceServices2> page = new Page<>();
        page.setRecords(cataServiceList);
        page.setTotal(count);
        //tagList转换为逗号拼接的字符串
        page.setCountId(tagList.stream().distinct().collect(Collectors.joining(",")));
        return JsonResult.success(page);
    }

    public JsonResult<List<ResourceServices2>> serviceListByName(String name) {
        List<ResourceServices2> list = this.resourceServicesMapper.serviceListByName(name);
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        //写入是否收藏和当前数据权限信息
        list.parallelStream().forEachOrdered(cataservice -> {
            UserCollected2 userCollected = this.userCollectedMapper.selectOneData(userId, cataservice.getId());
            cataservice.setIsCollect(userCollected != null);
            UserServrReview userServrReview = this.userServrReviewMapper.selectByUserIdAndResId(userId, cataservice.getId());
            //查询用户是否超管  //todo 超管查看所有数据的权限也去掉,现在专题里都需要申请
            // Boolean admin = checkIsAdmin.checkIsAdmin(principal.getId());
            // if(admin) cataservice.setHasPermission("2");
            // else{
            if(Objects.isNull(userServrReview)) cataservice.setHasPermission("0");
            else{
                if(userServrReview.getReviewStatus() == 0) cataservice.setHasPermission("3");
                if(userServrReview.getReviewStatus() == 1) cataservice.setHasPermission("1");
                if(userServrReview.getReviewStatus() == 2) cataservice.setHasPermission("0");
            }

            //如果父级datainfo中的类型是公开  设置为2  无需申请
            //安全0 公开1 私有2
            int authItype = cataservice.getAuthoritytype();
            if(authItype == 1) cataservice.setHasPermission("2");
            // }
        });
        //再查询当前登录用户的角色,去主动授权的表中按角色查询哪些数据是已经被授权的,然后权限标识字段再重新覆盖一下
        List<Long> roleIds = new ArrayList<>();
        List<SafetyUserRole> userRoles = this.safetyUserRoleMapper.selectList(new LambdaQueryWrapper<SafetyUserRole>().eq(SafetyUserRole::getUserId, userId));
        if(!userRoles.isEmpty()){
            roleIds.addAll(userRoles.stream().map(item -> item.getRoleId()).toList());
            List<SysRoleCate> sysRoleCateList = this.sysRoleCateMapper.selectList(new LambdaQueryWrapper<SysRoleCate>().in(SysRoleCate::getRoleId, roleIds));
            List<String> cataIds = sysRoleCateList.stream().map(item -> item.getCateId()).toList();
            list.parallelStream().forEachOrdered(cataservice -> {
                if(cataIds.contains(cataservice.getId())){
                    cataservice.setHasPermission("1");
                }
            });
        }
        list.forEach(item ->{
            item.setUrl("涉密数据,无权查看");
        });
        return JsonResult.success(list);
    }

}
