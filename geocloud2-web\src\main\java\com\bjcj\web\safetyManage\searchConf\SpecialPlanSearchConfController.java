package com.bjcj.web.safetyManage.searchConf;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.searchConf.SpecialPlanSearchConfDto;
import com.bjcj.model.po.naturalresources.categories.ResourceServices;
import com.bjcj.model.po.platformManage.searchConf.SpecialPlanSearchConf;
import com.bjcj.service.naturalresources.categories.CategoriesServiceService;
import com.bjcj.service.naturalresources.categories.ResourceServicesService;
import com.bjcj.service.platformManage.searchConf.SpecialPlanSearchConfService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;


/**
* 专题查询配置表(public.special_plan_search_conf)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/special_plan_search_conf")
@Validated
@Tag(name = "10.专题查询配置")
public class SpecialPlanSearchConfController {

    /**
    * 服务对象
    */
    @Resource
    private SpecialPlanSearchConfService specialPlanSearchConfService;

    @Resource
    private CategoriesServiceService cataService;

    @Resource
    private ResourceServicesService resourceServicesService;

    @OperaLog(operaModule = "专题新增查询服务",operaType = OperaLogConstant.CREATE,operaDesc = "专题新增查询服务")
    @SaCheckPermission("sys:write")
    @PostMapping("/addServer")
    @Operation(summary = "专题新增查询服务", description = "专题新增查询服务")
    @ApiOperationSupport(order = 1)
    @RequestLock(prefix = "addServer")
    public JsonResult addServer(@Validated @RequestBody SpecialPlanSearchConfDto dto){
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        if(dto.getResourceServicesIdList().isEmpty()) return JsonResult.error("服务集合不能为空");
        List<String> msgList = new ArrayList<>();
        List<SpecialPlanSearchConf> hasList = this.specialPlanSearchConfService.list(new LambdaQueryWrapper<SpecialPlanSearchConf>()
                .eq(SpecialPlanSearchConf::getSpecialPlanId, dto.getSpecialPlanId()));
        hasList.forEach(item -> {
            if(dto.getResourceServicesIdList().contains(item.getCategoriesServiceId())){
                String msg = "服务id:"+item.getCategoriesServiceId()+";服务名称:"+item.getCataSname()+"已存在";
                msgList.add(msg);
            }
        });
        if(!msgList.isEmpty()){
            return JsonResult.error(msgList.toString());
        }else{
            //通过验证,写入数据
            List<SpecialPlanSearchConf> saveList = new ArrayList<>(dto.getResourceServicesIdList().size());
            dto.getResourceServicesIdList().forEach(item -> {
                ResourceServices cs = this.resourceServicesService.getById(item);
                SpecialPlanSearchConf conf = new SpecialPlanSearchConf();
                conf.setCategoriesServiceId(item);
                conf.setSpecialPlanId(dto.getSpecialPlanId());
                conf.setCataSname(cs.getDisplayname());
                conf.setOperator(username);
                conf.setAddress(cs.getUrl());
                saveList.add(conf);
            });
            this.specialPlanSearchConfService.saveBatch(saveList);
            return JsonResult.success();
        }
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "列表", description = "列表")
    @ApiOperationSupport(order = 2)
    @GetMapping("/list")
    @Parameters({
            @Parameter(name = "specialPlanId", description = "专题id", required = true)
    })
    public JsonResult<List<SpecialPlanSearchConf>> list(@RequestParam("specialPlanId") Long specialPlanId){
        return JsonResult.success(this.specialPlanSearchConfService.list(new LambdaQueryWrapper<SpecialPlanSearchConf>()
                .eq(SpecialPlanSearchConf::getSpecialPlanId, specialPlanId)));
    }

    @OperaLog(operaModule = "删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除", description = "删除")
    @ApiOperationSupport(order = 3)
    public JsonResult delete(@PathVariable("id") Long id){
        this.specialPlanSearchConfService.removeById(id);
        return JsonResult.success();
    }
}
