package com.bjcj.model.po.dict;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/2/1  10:12
*/
/**
    * 字典类型表
    */
@Schema(description="字典类型表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_dict_type")
public class SysDictType implements Serializable {
    /**
     * 字典主键
     */
    @TableId(value = "dict_id", type = IdType.ASSIGN_ID)
    @Schema(description="字典主键")
    private Long dictId;

    /**
     * 字典名称
     */
    @TableField(value = "dict_name")
    @Schema(description="字典名称")
    @Size(max = 100,message = "字典名称max length should less than 100")
    private String dictName;

    /**
     * 字典类型
     */
    @TableField(value = "dict_type")
    @Schema(description="字典类型")
    @Size(max = 100,message = "字典类型max length should less than 100")
    private String dictType;

    /**
     * 状态（0正常 1停用）
     */
    @TableField(value = "status")
    @Schema(description="状态（0正常 1停用）")
    private String status;

    /**
     * 最后操作人
     */
    @TableField(value = "operater")
    @Schema(description="最后操作人")
    @Size(max = 30,message = "最后操作人max length should less than 30")
    private String operater;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.UPDATE)
    @Schema(description="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @Schema(description="备注")
    @Size(max = 500,message = "备注max length should less than 500")
    private String remark;

    /**
     * 字典类型(1普通,2级联,3分组)
     */
    @TableField(value = "dict_data_type")
    @Schema(description="字典类型(1普通,2级联,3分组)")
    private String dictDataType;

    private static final long serialVersionUID = 1L;
}