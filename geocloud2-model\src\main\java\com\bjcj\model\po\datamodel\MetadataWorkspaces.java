package com.bjcj.model.po.datamodel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/4/18  15:29
*/
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "metadata_workspaces")
public class MetadataWorkspaces implements Serializable {
    /**
     * 工作空间ID
     */
    @TableId(value = "workspaceid", type = IdType.ASSIGN_UUID)
    @Schema(description="工作空间ID")
    private String workspaceid;

    /**
     * 名称
     */
    @TableField(value = "name")
    @Schema(description="名称")
    @Size(max = 60,message = "名称max length should less than 60")
    @NotBlank(message = "名称is not blank")
    private String name;

    /**
     * 别名
     */
    @TableField(value = "displayname")
    @Schema(description="别名")
    @Size(max = 60,message = "别名max length should less than 60")
    private String displayname;

    /**
     * 用户名
     */
    @TableField(value = "username")
    @Schema(description="用户名")
    @Size(max = 60,message = "用户名max length should less than 60")
    private String username;

    /**
     * 密码
     */
    @TableField(value = "password")
    @Schema(description="密码")
    @Size(max = 60,message = "密码max length should less than 60")
    private String password;

    /**
     * 版本，例如：sde.DEFAULT
     */
    @TableField(value = "version")
    @Schema(description="版本，例如：sde.DEFAULT")
    @Size(max = 20,message = "版本，例如：sde.DEFAULTmax length should less than 20")
    private String version;

    /**
     * 工作空间类型，包括：SDE、FileGDB、Shapefile、MDB、Oracle、SqlServer等
     */
    @TableField(value = "workspacetype")
    @Schema(description="工作空间类型，包括：SDE、FileGDB、Shapefile、MDB、Oracle、SqlServer等")
    @Size(max = 20,message = "工作空间类型，包括：SDE、FileGDB、Shapefile、MDB、Oracle、SqlServer等max length should less than 20")
    @NotBlank(message = "工作空间类型，包括：SDE、FileGDB、Shapefile、MDB、Oracle、SqlServer等is not blank")
    private String workspacetype;

    /**
     * 文件型工作空间的路径或者ArcSDE Connection File路径
     */
    @TableField(value = "path")
    @Schema(description="文件型工作空间的路径或者ArcSDE Connection File路径")
    @Size(max = 255,message = "文件型工作空间的路径或者ArcSDE Connection File路径max length should less than 255")
    private String path;

    /**
     * SDE Workspace所在的ArcSDE Server的标识
     */
    @TableField(value = "serverid")
    @Schema(description="SDE Workspace所在的ArcSDE Server的标识")
    @Size(max = 36,message = "SDE Workspace所在的ArcSDE Server的标识max length should less than 36")
    private String serverid;

    /**
     * 数据库名称，用于SqlServer、DB2、PostgreSQL等非oracle数据库
     */
    @TableField(value = "database")
    @Schema(description="数据库名称，用于SqlServer、DB2、PostgreSQL等非oracle数据库")
    @Size(max = 50,message = "数据库名称，用于SqlServer、DB2、PostgreSQL等非oracle数据库max length should less than 50")
    private String database;

    /**
     * 描述信息
     */
    @TableField(value = "description")
    @Schema(description="描述信息")
    @Size(max = 150,message = "描述信息max length should less than 150")
    private String description;

    /**
     * 服务器名称
     */
    @TableField(value = "servername")
    @Schema(description="服务器名称")
    @Size(max = 60,message = "服务器名称max length should less than 60")
    private String servername;

    /**
     * 工作空间实例
     */
    @TableField(value = "instance")
    @Schema(description="工作空间实例")
    @Size(max = 300,message = "工作空间实例max length should less than 300")
    private String instance;

    /**
     * 是否只读，默认为0
     */
    @TableField(value = "readonly")
    @Schema(description="是否只读，默认为0")
    private Boolean readonly;

    private static final long serialVersionUID = 1L;
}