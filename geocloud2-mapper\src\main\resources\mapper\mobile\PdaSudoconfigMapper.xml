<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.mobile.PdaSudoconfigMapper">
    <select id="get" resultType="com.bjcj.model.po.mobile.PdaSudoconfig">
		select * from sys_mobile_sudoconfig t
		<where>
			1=1 and t.isdisplay='是'
			<if test="tenantid != null and tenantid != ''">
			and t.tenantid=#{tenantid}
			</if>
			<if test="pid != null and pid != ''">
				and t.pid=#{pid}
			</if>
			<if test="name != null and name != ''">
				and t.name=#{name}
			</if>
<!--			<if test="roleid != null and name != ''">-->
<!--				and pid in (-->
<!--					select menuid from sys_mobile_modulemenu_power where roleid = #{roleid}-->
<!--				)-->
<!--			</if>-->
		</where>

	</select>

</mapper>
