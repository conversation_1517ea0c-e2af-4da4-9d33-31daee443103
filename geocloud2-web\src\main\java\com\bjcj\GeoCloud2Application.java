package com.bjcj;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

import java.net.InetAddress;


/**
 * Hello world!
 * <AUTHOR>
 */
@SpringBootApplication
@MapperScan("com.bjcj.mapper")
public class GeoCloud2Application {

    public static void main(String[] args) throws Exception {
        ConfigurableApplicationContext application = SpringApplication.run(GeoCloud2Application.class, args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String property = env.getProperty("server.servlet.context-path");
        String path = property == null ? "" :  property;
        System.out.println(
                "\n\t" +
                        "----------------------------------------------------------\n\t" +
                        "Application kjgh is running! Access URLs:\n\t" +
                        "Doc: \t\thttp://localhost:" + port + path + "/doc.html" + "\n\t" +
                        "Local: \t\thttp://localhost:" + port + path + "\n\t" +
                        "External: \thttp://" + ip + ":" + port + path + "\n\t" +
                        "------------------------------------------------------------");
    }

}
