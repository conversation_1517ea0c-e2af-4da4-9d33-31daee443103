package com.bjcj.mapper.cloudportal;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.cloudportal.UserCollected;
import com.bjcj.model.po.cloudportal.UserCollected2;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/1/15  9:55
*/
public interface UserCollectedMapper extends BaseMapper<UserCollected> {
    List<UserCollected> selectPageData(@Param("current") Long current, @Param("size") Long size, @Param("searchStr") String searchStr,@Param("resouceType") String resouceType,@Param("userId") Long userId);

    int selectPageDataCount(@Param("current") Long current, @Param("size") Long size, @Param("searchStr") String searchStr,@Param("resouceType") String resouceType,@Param("userId") Long userId);

    int selectCountByUserIdAndResId(@Param("id") Long id,@Param("resId") String resId);

    UserCollected2 selectOneData(@Param("loginuserid") Long loginuserid,@Param("id") String id);
}