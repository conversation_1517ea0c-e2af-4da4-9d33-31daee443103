package com.bjcj.model.dto.statistics;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author：qinyi
 * @Date：2024-8-8 09:34
 */
@Schema(description="数据服务统计")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResourceServiceStatisticsDto implements Serializable {
    @Schema(description="总数量")
    private Long resourceServiceCount;

    @Schema(description="运行中数量")
    private Long resourceServiceRuningCount;

    @Schema(description="待审核数量")
    private Long resourceServiceNoReviewCount;

    @Schema(description="审核未通过数量")
    private Long resourceServiceReviewNotCount;

    @Schema(description="维护中数量")
    private Long resourceServiceWHZCount;
}
