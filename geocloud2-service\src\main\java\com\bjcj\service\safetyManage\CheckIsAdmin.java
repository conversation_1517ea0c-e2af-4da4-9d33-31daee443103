package com.bjcj.service.safetyManage;

import com.bjcj.mapper.safetyManage.UserMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author：qinyi
 * @Date：2023/12/7 10:02
 */
@Component
public class CheckIsAdmin {
    @Resource
    UserMapper userMapper;

    public boolean checkIsAdmin(Long userId){
        List<String> roles = userMapper.selectUserRole(userId);
        return roles.contains("admin");
    }

    public String getUserRoleName(Long userId){
        return userMapper.selectUserRoleName(userId);
    }
}
