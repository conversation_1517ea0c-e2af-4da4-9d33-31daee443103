<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.SpdpDeletedataMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.SpdpDeletedata">
    <!--@mbg.generated-->
    <!--@Table public.spdp_deletedata-->
    <result column="deleteid" jdbcType="CHAR" property="deleteid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="displayname" jdbcType="VARCHAR" property="displayname" />
    <result column="deletemodelid" jdbcType="VARCHAR" property="deletemodelid" />
    <result column="params" jdbcType="VARCHAR" property="params" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    deleteid, "name", displayname, deletemodelid, params
  </sql>
</mapper>