package com.bjcj.model.dto.resReview;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/1/22  9:43
*/

/**
    * 注册审核记录
    */
@Schema(description="注册审核记录")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResRegisterLogDto implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 审核状态
     */
    @TableField(value = "review_status")
    @Schema(description="审核状态")
    @NotNull(message = "审核状态is not null")
    private Boolean reviewStatus;

    /**
     * 资源类型(应用服务,数据产品,数据资源)
     */
    @TableField(value = "res_type")
    @Schema(description="资源类型(应用服务,数据产品,数据资源)现在就传数据资源就行了")
    @Size(max = 100,message = "资源类型(应用服务,数据产品,数据资源)max length should less than 100")
    @NotBlank(message = "资源类型is not blank")
    private String resType;

    /**
     * 资源名称
     */
    @TableField(value = "res_name")
    @Schema(description="资源名称")
    @Size(max = 100,message = "资源名称max length should less than 100")
    @NotBlank(message = "资源名称is not blank")
    @RequestKeyParam(name = "resName")
    private String resName;

    /**
     * 发布注册时间
     */
    @TableField(value = "publish_time")
    @Schema(description="发布注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "publish_time is not null")
    private LocalDateTime publishTime;

    /**
     * 审核时间
     */
    @TableField(value = "review_time")
    @Schema(description="审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewTime;

    /**
     * 发布者nickname
     */
    @TableField(value = "publish_user_name")
    @Schema(description="发布者username")
    @Size(max = 50,message = "发布者username max length should less than 50")
    @NotBlank(message = "发布者usernameis not blank")
    private String publishUserName;

    /**
     * 审核意见
     */
    @TableField(value = "review_remark")
    @Schema(description="审核意见")
    @Size(max = 255,message = "审核意见max length should less than 255")
    private String reviewRemark;

    /**
     * 资源id
     */
    @TableField(value = "res_id")
    @Schema(description="资源id")
    @NotNull(message = "资源idis not null")
    private String resId;


    /**
     * 审核人
     */
    @TableField(value = "operater")
    @Schema(description="审核人")
    private String operater;

    private static final long serialVersionUID = 1L;
}