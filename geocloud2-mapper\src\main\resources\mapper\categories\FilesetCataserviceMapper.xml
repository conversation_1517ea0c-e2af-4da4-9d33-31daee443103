<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.naturalresources.categories.FilesetCataserviceMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.naturalresources.categories.FilesetCataservice">
    <!--@mbg.generated-->
    <!--@Table public.fileset_cataservice-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cata_service_id" jdbcType="BIGINT" property="cataServiceId" />
    <result column="file_set_info_id" jdbcType="BIGINT" property="fileSetInfoId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, cata_service_id, file_set_info_id
  </sql>

  <select id="selectByCataServiceId" resultMap="BaseResultMap">
    select * from fileset_cataservice where cata_service_id=#{cataServiceId}
  </select>

  <delete id="deleteBatch">
    delete from fileset_cataservice where file_set_info_id in
    <foreach collection="idList" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and cata_service_id=#{resourceServicesId}
    </delete>
</mapper>