package com.bjcj.model.dto.platformConf;

import com.bjcj.common.utils.annotation.RequestKeyParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/2/28  14:39
*/

/**
    * 服务引擎配置表
    */
@Schema(description="服务引擎配置表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ServiceEngineConfDto implements Serializable {

    @Schema(description="")
    private Long id;

    /**
     * 服务器集群名称
     */
    @Schema(description="服务器集群名称")
    @Size(max = 100,message = "服务器集群名称max length should less than 100")
    @NotBlank(message = "服务器集群名称is not blank")
    @RequestKeyParam(name = "serverColonyName")
    private String serverColonyName;

    /**
     * 集群类型
     */
    @Schema(description="集群类型")
    @Size(max = 50,message = "集群类型max length should less than 50")
    private String serverColonyType;

    /**
     * 服务用户名
     */
    @Schema(description="服务用户名")
    @Size(max = 50,message = "服务用户名max length should less than 50")
    private String serverUserName;

    /**
     * 服务密码
     */
    @Schema(description="服务密码")
    @Size(max = 50,message = "服务密码max length should less than 50")
    private String serverPassword;

    /**
     * token请求url
     */
    @Schema(description="token请求url")
    @Size(max = 255,message = "token请求urlmax length should less than 255")
    private String tokenUrl;

    /**
     * 基地址url
     */
    @Schema(description="基地址url")
    @Size(max = 255,message = "基地址urlmax length should less than 255")
    @NotBlank(message = "基地址urlis not blank")
    private String baseUrl;

    /**
     * 是否启动0否1是
     */
    @Schema(description="是否启动0否1是")
    private String isRun;

    /**
     * 服务器集群描述
     */
    @Schema(description="服务器集群描述")
    @Size(max = 255,message = "服务器集群描述max length should less than 255")
    private String remark;

    private static final long serialVersionUID = 1L;
}