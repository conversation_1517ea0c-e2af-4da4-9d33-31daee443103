package com.bjcj.service.datamodel;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.mapper.datamodel.DataLayerFieldTypeMapper;
import com.bjcj.model.po.datamodel.DataLayerFieldType;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 *@Author：qinyi
 *@Date：2024/1/3  15:31
*/
@Service
public class DataLayerFieldTypeService extends ServiceImpl<DataLayerFieldTypeMapper, DataLayerFieldType> {

    @Resource
    DataLayerFieldTypeMapper dataLayerFieldTypeMapper;

    public Long selectByName(String fieldtypeStr) {
        return this.dataLayerFieldTypeMapper.selectByName(fieldtypeStr);
    }
}
