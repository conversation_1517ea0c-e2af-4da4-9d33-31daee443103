package com.bjcj.model.dto.fileResManage;

import com.baomidou.mybatisplus.annotation.TableField;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/1/16  11:04
*/

/**
    * 文件信息表
    */
@Schema(description="文件信息表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FileInfoDto implements Serializable {
    @Schema(description="")
    private Long id;

    /**
     * 所属文件集id
     */
    @Schema(description="所属文件集id")
    private Long fileSetInfoId;

    /**
     * 文件名称
     */
    @Schema(description="文件名称")
    @RequestKeyParam(name = "fileName")
    private String fileName;

    /**
     * 地址
     */
    @Schema(description="地址")
    private String fileUrl;

    /**
     * 文件大小
     */
    @Schema(description="文件大小")
    private String fileSize;

    /**
     * 文件类型
     */
    @Schema(description="文件类型")
    private String fileType;

    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField(value = "operator")
    @Schema(description="")
    private String operator;

    private static final long serialVersionUID = 1L;
}