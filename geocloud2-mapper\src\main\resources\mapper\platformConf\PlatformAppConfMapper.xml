<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.platformConf.PlatformAppConfMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.platformConf.PlatformAppConf">
    <!--@mbg.generated-->
    <!--@Table public.platform_app_conf-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
    <result column="system_title" jdbcType="VARCHAR" property="systemTitle" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="login_theme" jdbcType="VARCHAR" property="loginTheme" />
    <result column="platform_menu_id" jdbcType="BIGINT" property="platformMenuId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operater" jdbcType="VARCHAR" property="operater" />
    <result column="system_config_json" jdbcType="VARCHAR" property="systemConfigJson" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, app_name, show_name, system_title, url, login_theme, platform_menu_id, remark, 
    create_time, update_time, operater, system_config_json
  </sql>
</mapper>