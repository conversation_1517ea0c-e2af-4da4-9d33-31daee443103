package com.bjcj.model.dto.modulePlan;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/2/27  15:21
*/
@Schema(description="组件配置方案表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "module_plan")
public class ModulePlanDto implements Serializable {
    @Schema(description="")
    private Long id;

    /**
     * 组件名称
     */
    @Schema(description="组件名称")
    @Size(max = 100,message = "组件名称max length should less than 100")
    @NotBlank(message = "组件名称is not blank")
    @RequestKeyParam(name = "moduleName")
    private String moduleName;

    /**
     * 显示名称
     */
    @Schema(description="显示名称")
    @Size(max = 100,message = "显示名称max length should less than 100")
    @NotBlank(message = "显示名称is not blank")
    private String showName;

    /**
     * 是否延迟0否,1是默认0
     */
    @Schema(description="是否延迟0否,1是默认0")
    @Size(max = 1,message = "是否延迟0否,1是默认0max length should less than 1")
    @NotBlank(message = "是否延迟0否,1是默认0is not blank")
    private String isLag;

    /**
     * 显示顺序
     */
    @Schema(description="显示顺序")
    @NotNull(message = "显示顺序不能为空")
    private Short showSort;

    /**
     * 组件类型
     */
    @Schema(description="组件类型")
    @Size(max = 50,message = "组件类型max length should less than 50")
    private String moduleType;

    /**
     * 组件路径
     */
    @Schema(description="组件路径")
    @Size(max = 255,message = "组件路径max length should less than 255")
    private String moduleSrc;

    /**
     * 所属上级id
     */
    @Schema(description="所属上级id")
    @NotNull(message = "所属上级id不能为空")
    private Long parentId;

    /**
     * 元数据json
     */
    @Schema(description="元数据json")
    private String confJson;

    /**
     * 元数据解释json
     */
    @Schema(description="元数据解释json")
    private String confJsonSchema;

    @Schema(description="平台应用配置id")
    @NotNull(message = "平台应用配置id不能为空")
    private Long platformAppConfId;

    private static final long serialVersionUID = 1L;
}