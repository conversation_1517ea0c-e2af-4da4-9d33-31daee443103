package com.bjcj.model.po.datamodel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 * 字段类型表
 *@Date：2024/1/3  15:31
*/
@Schema(description="字段类型表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "data_layer_field_type")
public class DataLayerFieldType implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    @NotNull(message = "is not null")
    private Long id;

    /**
     * 字段类型
     */
    @TableField(value = "name")
    @Schema(description="字段类型")
    @Size(max = 50,message = "字段类型max length should less than 50")
    private String name;

    private static final long serialVersionUID = 1L;
}