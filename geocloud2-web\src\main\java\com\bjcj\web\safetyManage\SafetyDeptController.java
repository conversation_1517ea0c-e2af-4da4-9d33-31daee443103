package com.bjcj.web.safetyManage;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.safetyManage.SafetyDeptDto;
import com.bjcj.model.po.safetyManage.SafetyDept;
import com.bjcj.model.po.safetyManage.SafetyInstitution;
import com.bjcj.service.safetyManage.SafetyDeptService;
import com.bjcj.service.safetyManage.SafetyRoleService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* 部门表(sys_dept)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/sysDept")
@Tag(name = "部门管理")
@Validated
public class SafetyDeptController {
    /**
    * 服务对象
    */
    @Resource
    private SafetyDeptService safetyDeptService;

    @Resource
    private SafetyRoleService safetyRoleService;

    @OperaLog(operaModule = "部门管理-新增/编辑部门",operaType = OperaLogConstant.CREATE_OR_UPDATE,operaDesc = "新增/编辑部门")
    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑部门", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "addOrEdit")
    public JsonResult add(@Validated @RequestBody SafetyDeptDto dto){
        return this.safetyDeptService.saveData(dto);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/page")
    @Operation(summary = "部门列表分页", description = "带搜索框模糊查询(可搜名称)")
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "searchStr", description = "名称搜索", required = false),
            @Parameter(name = "institutionId", description = "所属机构id", required = true),
            @Parameter(name = "deptId", description = "父部门id(不传默认为根部门)", required = false)
    })
    @ApiOperationSupport(order = 1)
    public JsonResult<Page<SafetyDept>> page(
            @RequestParam("current") Integer page,
            @RequestParam("size") Integer pageSize,
            @RequestParam(value = "searchStr",required = false) String searchStr,
            @RequestParam("institutionId") Long institutionId,
            @RequestParam(value = "deptId",required = false) Long deptId) {
        Page<SafetyDept> pager = new Page<>(page, pageSize);
        return this.safetyDeptService.pageDataSort(pager,searchStr,institutionId,deptId);
    }

    @OperaLog(operaModule = "部门管理-删除部门",operaType = OperaLogConstant.DELETE,operaDesc = "删除部门")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del")
    @Operation(summary = "删除部门", description = "根据id删除部门及下所有子部门")
    @Parameters({
            @Parameter(name = "id", description = "部门id", required = true)
    })
    @ApiOperationSupport(order = 3)
    public JsonResult del(@RequestParam("id") Long id){
        return this.safetyDeptService.delData(id);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/allInstitutionList")
    @Operation(summary = "展示所有机构", description = "展示所有机构")
    @ApiOperationSupport(order = 4)
    public JsonResult<List<SafetyInstitution>> allInstitutionList(){
        return this.safetyRoleService.allInstitutionList();
    }


    @SaCheckPermission("sys:read")
    @GetMapping("/deptUserCount")
    @Operation(summary = "查询部门下人员数", description = "查询部门下人员数")
    @ApiOperationSupport(order = 5)
    public Long deptUserCount(@RequestParam("deptId") Long deptId){
        return this.safetyDeptService.deptUserCount(deptId);
    }

    @OperaLog(operaModule = "部门管理-清空部门下人员",operaType = OperaLogConstant.DELETE,operaDesc = "清空部门下人员")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delDeptUser")
    @Operation(summary = "清空部门下人员(仅限最子部门)", description = "根据部门id删除部门下人员")
    @Parameters({
            @Parameter(name = "deptId", description = "部门id", required = true)
    })
    @ApiOperationSupport(order = 6)
    public JsonResult delDeptUser(@RequestParam("deptId") Long deptId){
        return this.safetyDeptService.delDeptUser(deptId);
    }
}
