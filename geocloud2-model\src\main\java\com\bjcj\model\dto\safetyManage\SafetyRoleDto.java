package com.bjcj.model.dto.safetyManage;

import com.baomidou.mybatisplus.annotation.TableField;
import com.bjcj.common.utils.annotation.RequestKeyParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2023/11/29  8:49
*/

@Schema(description="角色表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SafetyRoleDto implements Serializable {

    @Schema(description="")
    private Long id;

    /**
     * 角色名称
     */
    @Schema(description="角色名称")
    @Size(max = 50,message = "角色名称最大长度要小于 50")
    @NotBlank(message = "角色名称不能为空")
    @RequestKeyParam(name = "roleName")
    private String roleName;

    /**
     * 角色类型
     */
    @Schema(description="角色类型")
    @Size(max = 50,message = "角色类型最大长度要小于 50")
    private String roleType;

    /**
     * 显示顺序
     */
    @Schema(description="显示顺序")
    @NotNull(message = "显示顺序不能为null")
    private Integer showSort;

    /**
     * 备注
     */
    @Schema(description="备注")
    @Size(max = 255,message = "备注最大长度要小于 255")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description="创建时间")
    @Size(max = 30,message = "创建时间最大长度要小于 30")
    private String createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @Schema(description="修改时间")
    @Size(max = 30,message = "修改时间最大长度要小于 30")
    private String updateTime;

    /**
     * 最后操作人
     */
    @TableField(value = "operater")
    @Schema(description="最后操作人")
    @Size(max = 30,message = "最后操作人最大长度要小于 30")
    private String operater;

    private static final long serialVersionUID = 1L;
}