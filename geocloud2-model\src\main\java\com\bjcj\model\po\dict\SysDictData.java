package com.bjcj.model.po.dict;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/2/1  10:13
*/
/**
    * 字典数据表
    */
@Schema(description="字典数据表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_dict_data")
@Accessors(chain = true)
public class SysDictData implements Serializable {
    /**
     * 字典编码
     */
    @TableId(value = "dict_code", type = IdType.ASSIGN_ID)
    @Schema(description="字典编码")
    private Long dictCode;

    /**
     * 字典排序
     */
    @TableField(value = "dict_sort")
    @Schema(description="字典排序")
    private Integer dictSort;

    /**
     * 字典标签
     */
    @TableField(value = "dict_label")
    @Schema(description="字典标签")
    @Size(max = 100,message = "字典标签max length should less than 100")
    private String dictLabel;

    /**
     * 字典键值
     */
    @TableField(value = "dict_value")
    @Schema(description="字典键值")
    @Size(max = 100,message = "字典键值max length should less than 100")
    private String dictValue;

    /**
     * 字典类型
     */
    @TableField(value = "dict_type")
    @Schema(description="字典类型")
    @Size(max = 100,message = "字典类型max length should less than 100")
    private String dictType;

    /**
     * 样式属性（其他样式扩展）
     */
    @TableField(value = "css_class")
    @Schema(description="样式属性（其他样式扩展）")
    @Size(max = 100,message = "样式属性（其他样式扩展）max length should less than 100")
    private String cssClass;

    /**
     * 表格回显样式
     */
    @TableField(value = "list_class")
    @Schema(description="表格回显样式")
    @Size(max = 100,message = "表格回显样式max length should less than 100")
    private String listClass;

    /**
     * 是否默认（Y是 N否）
     */
    @TableField(value = "is_default")
    @Schema(description="是否默认（Y是 N否）")
    private String isDefault;

    /**
     * 状态（0正常 1停用）
     */
    @TableField(value = "\"status\"")
    @Schema(description="状态（0正常 1停用）")
    private String status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 最后操作人
     */
    @TableField(value = "operater")
    @Schema(description="最后操作人")
    @Size(max = 30,message = "最后操作人max length should less than 30")
    private String operater;

    /**
     * 父级id
     */
    @TableField(value = "pid")
    @Schema(description="父级id")
    private Long pid;

    /**
     * 组id
     */
    @TableField(value = "group_name")
    @Schema(description="组名称")
    private String groupName;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @Schema(description="备注")
    @Size(max = 500,message = "备注max length should less than 500")
    private String remark;

    @TableField(exist = false)
    @Schema(description="子集")
    private List<SysDictData> children;

    private static final long serialVersionUID = 1L;
}