<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.eswPlatform.SchemesMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.eswPlatform.Schemes">
    <!--@mbg.generated-->
    <!--@Table public.schemes-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="projectid" jdbcType="VARCHAR" property="projectid" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="description" jdbcType="VARCHAR" property="description" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", projectid, "type", description
  </sql>
</mapper>