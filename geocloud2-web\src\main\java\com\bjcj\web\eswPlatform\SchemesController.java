package com.bjcj.web.eswPlatform;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.eswPlatform.Schemes;
import com.bjcj.service.eswPlatform.SchemesService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* (public.schemes)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/schemes")
@Validated
@Tag(name = "规划项目_待选方案")
public class SchemesController {
    /**
    * 服务对象
    */
    @Resource
    private SchemesService schemesService;

    @SaCheckPermission("sys:read")
    @GetMapping("/list")
    @Operation(summary = "列表", description = "列表")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "projectid", description = "项目Id", required = true)
    })
    public JsonResult<List<Schemes>> list(@RequestParam(value = "projectid",required = true) String projectid) {
        return JsonResult.success(this.schemesService.list(new LambdaQueryWrapper<Schemes>().eq(Schemes::getProjectid, projectid)));
    }

    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑", description = "新增/编辑")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "addoreditschemes")
    public JsonResult addOrEdit(@Validated @RequestBody Schemes po){
        return this.schemesService.saveOrUpdate(po) ? JsonResult.success() : JsonResult.error();
    }

    @OperaLog(operaModule = "删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del/{id}")
    @Operation(summary = "删除", description = "根据id删除")
    @ApiOperationSupport(order = 3)
    @RequestLock(prefix = "delschemes")
    @Transactional(rollbackFor = Exception.class)
    public JsonResult del(@PathVariable("id") String id){
//        return this.schemesService.removeById(id) ? JsonResult.success() : JsonResult.error();
        return this.schemesService.removeAllById(id);
    }

    @OperaLog(operaModule = "批量删除",operaType = OperaLogConstant.DELETE,operaDesc = "批量删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delBatch")
    @Operation(summary = "批量删除", description = "根据id批量删除")
    @ApiOperationSupport(order = 4)
    @Parameters({
        @Parameter(name = "ids", description = "ids逗号拼接", required = true)
    })
    @RequestLock(prefix = "delBatchschemes")
    @Transactional(rollbackFor = Exception.class)
    public JsonResult delBatch(@RequestParam("ids") String ids){
        if(ids.contains(",")){
            //ids转list
            List<String> idss = List.of(ids.split(","));
//            return JsonResult.success(this.schemesService.removeByIds(idss));
            return this.schemesService.removeAllByIds(idss);
        }else{
            return JsonResult.error("ids格式错误,必须选择一条以上的数据");
        }
    }

}
