package com.bjcj.service.naturalresources.function;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.naturalresources.function.RequestResultMapper;
import com.bjcj.model.po.naturalresources.function.RequestResult;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/4 15:26 周一
 */
@Service
public class RequestResultService extends ServiceImpl<RequestResultMapper, RequestResult> {

    @Resource
    private RequestResultMapper requestResultMapper;

    public JsonResult del(Long id){
        LambdaUpdateWrapper<RequestResult> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(RequestResult::getDel, 1)
                .eq(RequestResult::getId, id);
        int result = requestResultMapper.update(wrapper);
        return JsonResult.success(result);
    }

    public JsonResult<RequestResult> findById(Long id){
        RequestResult requestResult = requestResultMapper.selectById(id);
        return JsonResult.success(requestResult);
    }

    public JsonResult<List<RequestResult>> findByParentId(Long parentid){
        LambdaQueryWrapper<RequestResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RequestResult::getParentId, parentid)
                .eq(RequestResult::getDel, 0);
        List<RequestResult> list =  requestResultMapper.selectList(wrapper);

        return JsonResult.success(list);
    }

}
