package com.bjcj.service.platformManage.analysisPlan;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.platformManage.analysisPlan.AnalysisOrdinaryItemMapper;
import com.bjcj.mapper.platformManage.analysisPlan.AnalysisPlanFieldDetailMapper;
import com.bjcj.mapper.platformManage.analysisPlan.AnalysisPlanFieldStatisticsMapper;
import com.bjcj.mapper.platformManage.analysisPlan.AnalysisPlanMapper;
import com.bjcj.model.dto.analysisPlan.AnalysisPlanDto;
import com.bjcj.model.po.platformManage.analysisPlan.AnalysisOrdinaryItem;
import com.bjcj.model.po.platformManage.analysisPlan.AnalysisPlan;
import com.bjcj.model.po.platformManage.analysisPlan.AnalysisPlanFieldDetail;
import com.bjcj.model.po.platformManage.analysisPlan.AnalysisPlanFieldStatistics;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 *@Author：qinyi
 *@Date：2024/2/21  17:36
*/
@Service
public class AnalysisPlanService extends ServiceImpl<AnalysisPlanMapper, AnalysisPlan> {

    @Resource
    private AnalysisPlanMapper analysisPlanMapper;

    @Resource
    private AnalysisPlanFieldDetailService analysisPlanFieldDetailService;

    @Resource
    private AnalysisPlanFieldStatisticsService analysisPlanFieldStatisticsService;

    @Resource
    private AnalysisPlanFieldDetailMapper analysisPlanFieldDetailMapper;

    @Resource
    private AnalysisPlanFieldStatisticsMapper analysisPlanFieldStatisticsMapper;

    @Resource
    private AnalysisOrdinaryItemMapper analysisOrdinaryItemMapper;

    public JsonResult addOrEdit(AnalysisPlanDto dto) {
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        AnalysisPlan analysisPlan = BeanUtil.copyProperties(dto, AnalysisPlan.class);
        analysisPlan.setOperater(username);
        switch (dto.getPlanType()){
            case "0":
                this.saveOrUpdate(analysisPlan);
                break;
            case "1":
                this.saveOrUpdate(analysisPlan);
                List<AnalysisPlanFieldDetail> analyzeFieldDetails = new ArrayList<AnalysisPlanFieldDetail>();
                dto.getAnalysisPlanFieldDetailDtos().forEach(item -> {
                    AnalysisPlanFieldDetail analysisPlanFieldDetail = BeanUtil.copyProperties(item, AnalysisPlanFieldDetail.class);
                    analysisPlanFieldDetail.setAnalysisPlanId(analysisPlan.getId());
                    analyzeFieldDetails.add(analysisPlanFieldDetail);
                });
                this.analysisPlanFieldDetailService.saveOrUpdateBatch(analyzeFieldDetails);
                break;
            case "2":
                this.saveOrUpdate(analysisPlan);
                List<AnalysisPlanFieldDetail> analyzeFieldDetails2 = new ArrayList<AnalysisPlanFieldDetail>();
                dto.getAnalysisPlanFieldDetailDtos().forEach(item -> {
                    AnalysisPlanFieldDetail analysisPlanFieldDetail = BeanUtil.copyProperties(item, AnalysisPlanFieldDetail.class);
                    analysisPlanFieldDetail.setAnalysisPlanId(analysisPlan.getId());
                    analyzeFieldDetails2.add(analysisPlanFieldDetail);
                });
                this.analysisPlanFieldDetailService.saveOrUpdateBatch(analyzeFieldDetails2);
                List<AnalysisPlanFieldStatistics> analyzeFieldStatisticsList = new ArrayList<AnalysisPlanFieldStatistics>();
                dto.getAnalysisPlanFieldStatisticsDtos().forEach(item -> {
                    AnalysisPlanFieldStatistics analysisPlanFieldStatistics = BeanUtil.copyProperties(item, AnalysisPlanFieldStatistics.class);
                    analysisPlanFieldStatistics.setAnalysisPlanId(analysisPlan.getId());
                    analyzeFieldStatisticsList.add(analysisPlanFieldStatistics);
                });
                this.analysisPlanFieldStatisticsService.saveOrUpdateBatch(analyzeFieldStatisticsList);
                break;
            case "3":
                this.saveOrUpdate(analysisPlan);
                List<AnalysisPlanFieldDetail> analyzeFieldDetails3 = new ArrayList<AnalysisPlanFieldDetail>();
                dto.getAnalysisPlanFieldDetailDtos().forEach(item -> {
                    AnalysisPlanFieldDetail analysisPlanFieldDetail = BeanUtil.copyProperties(item, AnalysisPlanFieldDetail.class);
                    analysisPlanFieldDetail.setAnalysisPlanId(analysisPlan.getId());
                    analyzeFieldDetails3.add(analysisPlanFieldDetail);
                });
                this.analysisPlanFieldDetailService.saveOrUpdateBatch(analyzeFieldDetails3);
                List<AnalysisPlanFieldStatistics> analyzeFieldStatisticsList2 = new ArrayList<AnalysisPlanFieldStatistics>();
                dto.getAnalysisPlanFieldStatisticsDtos().forEach(item -> {
                    AnalysisPlanFieldStatistics analysisPlanFieldStatistics = BeanUtil.copyProperties(item, AnalysisPlanFieldStatistics.class);
                    analysisPlanFieldStatistics.setAnalysisPlanId(analysisPlan.getId());
                    analyzeFieldStatisticsList2.add(analysisPlanFieldStatistics);
                });
                this.analysisPlanFieldStatisticsService.saveOrUpdateBatch(analyzeFieldStatisticsList2);
                break;
        }
        return JsonResult.success();
    }

    public JsonResult deleteById(Long id) {
        this.analysisPlanFieldDetailMapper.delete(new LambdaQueryWrapper<AnalysisPlanFieldDetail>().eq(AnalysisPlanFieldDetail::getAnalysisPlanId,id));
        this.analysisPlanFieldStatisticsMapper.delete(new LambdaQueryWrapper<AnalysisPlanFieldStatistics>().eq(AnalysisPlanFieldStatistics::getAnalysisPlanId,id));
        this.analysisPlanMapper.deleteById(id);
        return JsonResult.success();
    }

    public JsonResult exportTo(Long fromPlatformAppConfId, Long toPlatformAppConfId) {
        List<AnalysisPlan> oldAnalysisPlanList = this.analysisPlanMapper.selectList(new LambdaQueryWrapper<AnalysisPlan>().eq(AnalysisPlan::getPlatformAppConfId,fromPlatformAppConfId));
        List<Long> old_analysisPlanIds = oldAnalysisPlanList.stream().map(AnalysisPlan::getId).toList();

        //先删除原数据
        List<AnalysisPlan> newAnalysisPlanList = this.analysisPlanMapper.selectList(new LambdaQueryWrapper<AnalysisPlan>().eq(AnalysisPlan::getPlatformAppConfId,toPlatformAppConfId));
        if(!newAnalysisPlanList.isEmpty()){
            List<Long> new_analysisPlanIds = newAnalysisPlanList.stream().map(AnalysisPlan::getId).toList();
            List<AnalysisPlan> plans = this.analysisPlanMapper.selectList(new LambdaQueryWrapper<AnalysisPlan>().eq(AnalysisPlan::getPlatformAppConfId,toPlatformAppConfId));
            if(!plans.isEmpty()) this.analysisPlanMapper.deleteBatchIds(plans.stream().map(AnalysisPlan::getId).toList());
            List<AnalysisPlanFieldDetail> details = this.analysisPlanFieldDetailMapper.selectList((new LambdaQueryWrapper<AnalysisPlanFieldDetail>().in(AnalysisPlanFieldDetail::getAnalysisPlanId,new_analysisPlanIds)));
            if(!details.isEmpty()) this.analysisPlanFieldDetailMapper.deleteBatchIds(details.stream().map(AnalysisPlanFieldDetail::getId).toList());
            List<AnalysisPlanFieldStatistics> statistics = this.analysisPlanFieldStatisticsMapper.selectList(new LambdaQueryWrapper<AnalysisPlanFieldStatistics>().in(AnalysisPlanFieldStatistics::getAnalysisPlanId,new_analysisPlanIds));
            if(!statistics.isEmpty()) this.analysisPlanFieldStatisticsMapper.deleteBatchIds(statistics.stream().map(AnalysisPlanFieldStatistics::getId).toList());
            List<AnalysisOrdinaryItem> ordinaryItems = this.analysisOrdinaryItemMapper.selectList(new LambdaQueryWrapper<AnalysisOrdinaryItem>().in(AnalysisOrdinaryItem::getAnalysisPlanId,new_analysisPlanIds));
            if(!ordinaryItems.isEmpty()) this.analysisOrdinaryItemMapper.deleteBatchIds(ordinaryItems.stream().map(AnalysisOrdinaryItem::getId).toList());
        }
        //导入数据
        oldAnalysisPlanList.forEach(item -> {
            Long oldid = item.getId();
            item.setId(null);
            item.setPlatformAppConfId(toPlatformAppConfId);
            item.setOperater((String) JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username"));
            this.analysisPlanMapper.insert(item);
            Long newid = item.getId();

            //导入analysis_plan_field_detail
            List<AnalysisPlanFieldDetail> detailList = this.analysisPlanFieldDetailMapper.selectList(new LambdaQueryWrapper<AnalysisPlanFieldDetail>()
                    .eq(AnalysisPlanFieldDetail::getAnalysisPlanId,oldid));
            detailList.forEach(item2 -> {
                item2.setId(null);
                item2.setAnalysisPlanId(newid);
                this.analysisPlanFieldDetailMapper.insert(item2);
            });
            //导入analysis_plan_field_statistics
            List<AnalysisPlanFieldStatistics> statisticsList = this.analysisPlanFieldStatisticsMapper.selectList(new LambdaQueryWrapper<AnalysisPlanFieldStatistics>()
                    .eq(AnalysisPlanFieldStatistics::getAnalysisPlanId,oldid));
            statisticsList.forEach(item3 -> {
                item3.setId(null);
                item3.setAnalysisPlanId(newid);
                this.analysisPlanFieldStatisticsMapper.insert(item3);
            });
            //导入analysis_ordinary_item
            List<AnalysisOrdinaryItem> ordinaryItemList = this.analysisOrdinaryItemMapper.selectList(new LambdaQueryWrapper<AnalysisOrdinaryItem>()
                    .eq(AnalysisOrdinaryItem::getAnalysisPlanId,oldid));
            ordinaryItemList.forEach(item4 -> {
                item4.setId(null);
                item4.setAnalysisPlanId(newid);
                this.analysisOrdinaryItemMapper.insert(item4);
            });
        });
        return JsonResult.success("导入成功!");
    }
}
