package com.bjcj.model.po.safetyManage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
*@Author：qinyi
*@Date：2023/11/24  15:46
*/
@Schema(description="部门表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_dept")
public class SafetyDept implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    @NotNull(message = "主键不能为null")
    private Long id;

    /**
     * 部门名称
     */
    @TableField(value = "dept_name")
    @Schema(description="部门名称")
    @Size(max = 50,message = "部门名称最大长度要小于 50")
    @NotBlank(message = "部门名称不能为空")
    private String deptName;

    /**
     * 显示顺序
     */
    @TableField(value = "show_sort")
    @Schema(description="显示顺序")
    @NotNull(message = "显示顺序不能为null")
    private Integer showSort;

    /**
     * 所属机构id
     */
    @TableField(value = "parent_institution_id")
    @Schema(description="所属机构id")
    @NotNull(message = "所属机构id不能为null")
    private Long parentInstitutionId;

    /**
     * 上级部门id
     */
    @TableField(value = "parent_dept_id")
    @Schema(description="上级部门id")
    private Long parentDeptId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description="创建时间")
    @Size(max = 30,message = "创建时间最大长度要小于 30")
    private String createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @Schema(description="修改时间")
    @Size(max = 30,message = "修改时间最大长度要小于 30")
    private String updateTime;

    /**
     * 最后操作人
     */
    @TableField(value = "operater")
    @Schema(description="最后操作人")
    @Size(max = 30,message = "最后操作人最大长度要小于 30")
    private String operater;

    /**
     * 是否含有子部门
     */
    @TableField(exist = false)
    @Schema(description="是否含有子部门")
    private Boolean isHasNode = false;

    /**
     * 上级部门名称
     */
    @TableField(exist = false)
    @Schema(description="上级部门名称")
    private String parentDeptName;


    private static final long serialVersionUID = 1L;
}