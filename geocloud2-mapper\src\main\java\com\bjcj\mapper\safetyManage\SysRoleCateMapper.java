package com.bjcj.mapper.safetyManage;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.safetyManage.SysRoleCate;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 *@Author：qinyi
 *@Date：2023/12/13  15:09
*/
public interface SysRoleCateMapper extends BaseMapper<SysRoleCate> {
    Integer insertBatch(@Param("list")  List<SysRoleCate> list);

    void deleteBatch(@Param("list") List<String> list,@Param("roleId") Long roleId);

    String selectRoleNameByCateId(@Param("id") String id);
}