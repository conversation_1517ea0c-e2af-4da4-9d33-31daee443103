<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.naturalresources.categories.CategoriesServiceGroupMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.naturalresources.categories.CategoriesServiceGroup">
    <!--@mbg.generated-->
    <!--@Table public.categories_service_group-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
    <result column="show_sort" jdbcType="INTEGER" property="showSort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, group_name, show_name, show_sort, create_time, update_time, "operator"
  </sql>
</mapper>