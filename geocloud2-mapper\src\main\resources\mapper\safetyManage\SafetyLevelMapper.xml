<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.safetyManage.SafetyLevelMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.safetyManage.SafetyLevel">
    <!--@mbg.generated-->
    <!--@Table public.sys_level-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="level_name" jdbcType="VARCHAR" property="levelName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, level_name
  </sql>
</mapper>