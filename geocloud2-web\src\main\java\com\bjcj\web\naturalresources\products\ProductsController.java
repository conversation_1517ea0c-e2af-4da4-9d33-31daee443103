package com.bjcj.web.naturalresources.products;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.naturalresources.products.Products;
import com.bjcj.service.naturalresources.products.ProductsService;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2023/12/5 9:24 周二
 */
@Tag(name = "数据产品管理（数据产品分类）",description = "数据产品管理（数据产品分类）")
@ApiSupport(order = 58)
@RestController
@Slf4j
@RequestMapping(value = "/products")
public class ProductsController {

    @Resource
    private ProductsService productsService;

    @SaCheckPermission("sys:read")
    @Operation(summary = "数据产品分类树形")
    @GetMapping(value = "/treeList")
    public JsonResult treeList(){

        return productsService.treeList();
    }

    @SaCheckPermission("sys:read")
    @Operation(summary = "数据产品分类列表")
    @GetMapping(value = "/lists")
    public JsonResult lists(){

        return productsService.lists();
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "数据产品分类注册",
            operaType = OperaLogConstant.CREATE,
            operaDesc = "数据产品分类注册"
    )
    @Operation(summary = "数据产品分类注册")
    @PostMapping(value = "/save")
    public JsonResult save(@RequestBody @Validated Products products){
        productsService.save(products);
        Long id = products.getId();
        return JsonResult.success(id);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "数据产品分类修改",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "数据产品分类修改"
    )
    @Operation(summary = "数据产品分类修改")
    @PutMapping(value = "/update")
    public JsonResult update(@RequestBody @Validated Products products){
        boolean result = productsService.saveOrUpdate(products);
        if(!result){
            return JsonResult.error();
        }
        return JsonResult.success();
    }

    @SaCheckPermission("sys:del")
    @OperaLog(
            operaModule = "数据产品分类删除",
            operaType = OperaLogConstant.DELETE,
            operaDesc = "数据产品分类删除"
    )
    @Operation(summary = "数据产品分类删除")
    @DeleteMapping(value = "/del/{id}")
    public JsonResult del(@PathVariable Long id){

        return productsService.del(id);
    }

    @SaCheckPermission("sys:write")
    @OperaLog(
            operaModule = "数据产品分类显示",
            operaType = OperaLogConstant.UPDATE,
            operaDesc = "数据产品分类显示"
    )
    @Operation(summary = "数据产品分类显示")
    @PostMapping(value = "/ishow")
    public JsonResult ishow(@RequestParam Long id, Boolean show){

        return productsService.ishow(id, show);
    }

    @Operation(summary = "导出")
    @GetMapping(value = "/export")
    public JsonResult exportJson(){

        return productsService.exportJson();
    }

    @Operation(summary = "导入")
    @PostMapping(value = "/import")
    public JsonResult importJson(@RequestParam("file") MultipartFile file){

        return productsService.importJson(file);
    }

}
