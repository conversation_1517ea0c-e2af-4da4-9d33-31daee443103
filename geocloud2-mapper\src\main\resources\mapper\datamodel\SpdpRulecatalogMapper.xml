<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.SpdpRulecatalogMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.SpdpRulecatalog">
    <!--@mbg.generated-->
    <!--@Table public.spdp_rulecatalog-->
    <result column="rulecatalogid" jdbcType="CHAR" property="rulecatalogid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="displayname" jdbcType="VARCHAR" property="displayname" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="grade" jdbcType="NUMERIC" property="grade" />
    <result column="isneed" jdbcType="BOOLEAN" property="isneed" />
    <result column="rulecode" jdbcType="VARCHAR" property="rulecode" />
    <result column="parentid" jdbcType="CHAR" property="parentid" />
    <result column="innervalidateobjecttypekey" jdbcType="VARCHAR" property="innervalidateobjecttypekey" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    rulecatalogid, "name", displayname, description, grade, isneed, rulecode, parentid, 
    innervalidateobjecttypekey
  </sql>
</mapper>