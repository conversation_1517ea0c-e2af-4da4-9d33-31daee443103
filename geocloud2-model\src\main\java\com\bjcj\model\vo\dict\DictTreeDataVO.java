package com.bjcj.model.vo.dict;

import com.bjcj.model.po.dict.SysDictData;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author：qinyi
 * @Date：2024/2/2 10:01
 */
@Data
@Schema(description="字典数据树列表")
public class DictTreeDataVO {
    @Schema(description="字典主键")
    private Long dictId;

    /**
     * 字典名称
     */
    @Schema(description="字典名称")
    private String dictName;

    /**
     * 字典类型
     */
    @Schema(description="字典类型")
    private String dictType;

    /**
     * 状态（0正常 1停用）
     */
    @Schema(description="状态（0正常 1停用）")
    private String status;

    /**
     * 最后操作人
     */
    @Schema(description="最后操作人")
    private String operater;

    /**
     * 创建时间
     */
    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description="备注")
    private String remark;

    /**
     * 字典类型(1普通,2级联,3分组)
     */
    @Schema(description="字典类型(1普通,2级联,3分组)")
    private String dictDataType;

    @Schema(description="子集")
    private List<SysDictData> children;
}
