package com.bjcj.common.utils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/10/11 14:40 周三
 */
@Slf4j
public class Geocloud2Util {

    /**
     * <h2>去掉code后面的0 便于查询</h2>
     * 141026000000 => 141026
     * 141026101000 => 141026101
     * @param code:
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/9/20 11:20
     */
    public static String cutCode(String code) {
        // 取后6位
        String suf = StrUtil.subSuf(code, 6);

        if (StrUtil.equals(suf, "000000")) {
            return StrUtil.subPre(code, 6);
        }
        if (StrUtil.equals(StrUtil.subSuf(suf, 3), "000")) {
            return StrUtil.subPre(code, 9);
        }
        return code;
    }

    /**
     * <h2>strCompareTo</h2>
     * @param str1:
     * @param str2:
     * @return str1 < str2 true
     *  str1 > str2 false
     * <AUTHOR>
     * @date 2024/1/21 16:53
     */
    public static boolean strCompareTo(String str1, String str2) {
        // 提取出连续的数字
        String num1 = str1.replaceAll("\\D+", "");
        String num2 = str2.replaceAll("\\D+", "");

        BigDecimal bg1 = new BigDecimal(num1);
        BigDecimal bg2 = new BigDecimal(num2);

        return bg1.compareTo(bg2) <= 0;
    }

}
