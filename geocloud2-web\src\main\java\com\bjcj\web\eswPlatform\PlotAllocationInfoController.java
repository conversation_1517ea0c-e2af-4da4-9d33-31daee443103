package com.bjcj.web.eswPlatform;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.eswPlatform.PlotAllocationInfo;
import com.bjcj.service.eswPlatform.PlotAllocationInfoService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* (plot_allocation_info)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/plot_allocation_info")
@Validated
@Tag(name = "模型地块")
public class PlotAllocationInfoController {
    /**
    * 服务对象
    */
    @Resource
    private PlotAllocationInfoService plotAllocationInfoService;

    @SaCheckPermission("sys:read")
    @GetMapping("/page")
    @Operation(summary = "列表", description = "列表")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "current", description = "页码", required = true),
            @Parameter(name = "size", description = "每页条数", required = true),
            @Parameter(name = "type", description = "类型", required = true)
    })
    public JsonResult<Page<PlotAllocationInfo>> list(@RequestParam("current") Integer page,
                                                     @RequestParam("size") Integer pageSize,
                                                     @RequestParam("type") String type) {
        Page<PlotAllocationInfo> pager = new Page<>(page, pageSize);
        return this.plotAllocationInfoService.selectpage(pager, type);
    }

    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑", description = "新增/编辑")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "addoreditpublic.plot_allocation_info")
    public JsonResult addOrEdit(@Validated @RequestBody PlotAllocationInfo po){
        return this.plotAllocationInfoService.saveOrUpdate(po) ? JsonResult.success() : JsonResult.error();
    }

    @OperaLog(operaModule = "删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del/{id}")
    @Operation(summary = "删除", description = "根据id删除")
    @ApiOperationSupport(order = 3)
    @RequestLock(prefix = "delpublic.plot_allocation_info")
    public JsonResult del(@PathVariable("id") String id){
        return this.plotAllocationInfoService.removeById(id) ? JsonResult.success() : JsonResult.error();
    }

    @OperaLog(operaModule = "批量删除",operaType = OperaLogConstant.DELETE,operaDesc = "批量删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/delBatch")
    @Operation(summary = "批量删除", description = "根据id批量删除")
    @ApiOperationSupport(order = 4)
    @Parameters({
        @Parameter(name = "ids", description = "ids逗号拼接", required = true)
    })
    @RequestLock(prefix = "delBatchpublic.plot_allocation_info")
    public JsonResult delBatch(@RequestParam("ids") String ids){
        if(ids.contains(",")){
            //ids转list
            List<String> idss = List.of(ids.split(","));
            return JsonResult.success(this.plotAllocationInfoService.removeByIds(idss));
        }else{
            return JsonResult.error("ids格式错误,必须选择一条以上的数据");
        }
    }

}
