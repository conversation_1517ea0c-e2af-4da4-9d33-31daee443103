package com.bjcj.service.mobile;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.mobile.PdaSudoconfigMapper;
import com.bjcj.model.dto.mobile.PdaSudoconfigDto;
import com.bjcj.model.po.mobile.PdaSubconfig;
import com.bjcj.model.po.mobile.PdaSudoconfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Transactional
public class PdaSudoconfigService extends ServiceImpl<PdaSudoconfigMapper,PdaSudoconfig> {

    @Autowired
    private PdaSudoconfigMapper sudoconfigMapper;

    @Autowired
    private PdaSubconfigService pdaSubconfigServices;

    public PdaSudoconfig get(String pid, String name, String type, String tenantId,String roleid) {
        Map<String, Object> map=new HashMap<>();
        map.put("tenantId",tenantId);
        map.put("pid",pid);
        map.put("name",name);
        map.put("type",type);
        map.put("roleid",roleid);
        PdaSudoconfig softList= this.sudoconfigMapper.get(map);
        return softList;
    }

    public List<PdaSudoconfig> getOption() {
        QueryWrapper<PdaSudoconfig> queryWrapper = new QueryWrapper();
        queryWrapper.eq("issystem","是");
        queryWrapper.eq("isdisplay","是");
        return list(queryWrapper);
    }

    public List<PdaSudoconfig> listByPage(PdaSudoconfigDto dto) {
        LambdaQueryWrapper<PdaSudoconfig> queryWrapper = new LambdaQueryWrapper<>();

        if (dto.getCurrentPage() != null && dto.getPageSize() != null) {
            IPage<PdaSudoconfig> ipage = new Page<>(dto.getCurrentPage(), dto.getPageSize());
            IPage<PdaSudoconfig> pageResult = page(ipage, queryWrapper);
            return pageResult.getRecords();
        }
        return new ArrayList<>();
    }
    public List<PdaSubconfig> getSubOption(PdaSubconfig pdaSubconfig){

        List<PdaSubconfig> list = pdaSubconfigServices.getOption(pdaSubconfig);
        for (int i=0;i<list.size();i++){
            pdaSubconfig.setSubid(list.get(i).getSubid());
            List<PdaSubconfig> temp = getSubOption(pdaSubconfig);
            if (temp.size()!=0){
                list.get(i).setChildren(temp);
            }
        }
        return list;
    }

    public List<PdaSubconfig> getWholeSubConfig(PdaSubconfig pdaSubconfig){

        List<PdaSubconfig> list = pdaSubconfigServices.getWholeOption(pdaSubconfig);
        for (int i=0;i<list.size();i++){
            pdaSubconfig.setSubid(list.get(i).getSubid());
            if ("是".equals(list.get(i).getBuiltmodule())){
                List<PdaSubconfig> temp = getWholeSubConfig(pdaSubconfig);
                if (temp.size()!=0){
                    list.get(i).setChildren(temp);
                }
            }
        }
        return list;
    }

    public Long count(PdaSudoconfigDto PdaSudoconfigDto) {
        QueryWrapper queryWrapper = new QueryWrapper();
        return count(queryWrapper);
    }

    public Boolean add(PdaSudoconfig pdaSudoconfig) {
        return save(pdaSudoconfig);
    }

    public Boolean delete(PdaSudoconfig pdaSudoconfig) {
        return removeById(pdaSudoconfig);
    }

    public Boolean update(PdaSudoconfig pdaSudoconfig) {
        return updateById(pdaSudoconfig);
    }

    public PdaSudoconfig getOne(PdaSudoconfig pdaSudoconfig) {
        QueryWrapper<PdaSudoconfig> queryWrapper = new QueryWrapper();
        if (pdaSudoconfig.getPid()!=null){
            queryWrapper.eq("pid",pdaSudoconfig.getPid());
        }
        return getOne(queryWrapper);
    }

    public List<PdaSudoconfig> getOptionMethod() {
        List<PdaSudoconfig> list = this.getOption();

        for (int i=0;i<list.size();i++){
            PdaSubconfig temp = new PdaSubconfig();
            temp.setPid(list.get(i).getPid());
            list.get(i).setChildren(this.getSubOption(temp));
        }
        return list;
    }

    public List<PdaSudoconfig> getWholeOptionMethod() {
        List<PdaSudoconfig> list = this.getOption();

        for (int i=0;i<list.size();i++){
            list.get(i).setSubid(list.get(i).getPid());
            PdaSubconfig temp = new PdaSubconfig();
            temp.setPid(list.get(i).getPid());
            list.get(i).setChildren(this.getWholeSubConfig(temp));
        }
        return list;
    }


    public JsonResult selectPage(Integer page, Integer pageSize) {
        Page<PdaSudoconfig> pager = new Page<>(page, pageSize);
        LambdaQueryWrapper<PdaSudoconfig> wrapper = new LambdaQueryWrapper<>();
        Page<PdaSudoconfig> pages = sudoconfigMapper.selectPage(pager, wrapper);
        return JsonResult.success(pages);

    }
}
