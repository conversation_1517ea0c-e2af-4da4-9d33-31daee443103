package com.bjcj.service.system;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSONObject;
import com.bjcj.common.utils.SnowflakeUtil;
import com.bjcj.common.utils.properties.GeoCloud2Properties;
import com.bjcj.model.bo.YdReq;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;

/**
 * 翻译服务
 *
 * <AUTHOR>
 * @date 2023/12/27 14:21 周三
 */
@Service
@Slf4j
public class TranslationService {

    @Resource
    GeoCloud2Properties geoCloud2Properties;


    // public static void main(String[] args) throws IOException {
    //
    //     Map<String, String> params = new HashMap<String, String>();
    //     String q = "待输入的文字";
    //     String salt = String.valueOf(System.currentTimeMillis());
    //     params.put("from", "源语言");
    //     params.put("to", "目标语言");
    //     params.put("signType", "v3");
    //     String curtime = String.valueOf(System.currentTimeMillis() / 1000);
    //     params.put("curtime", curtime);
    //     String signStr = APP_KEY + truncate(q) + salt + curtime + APP_SECRET;
    //     String sign = getDigest(signStr);
    //     params.put("appKey", APP_KEY);
    //     params.put("q", q);
    //     params.put("salt", salt);
    //     params.put("sign", sign);
    //     params.put("vocabId", "您的用户词表ID");
    //     /** 处理结果 */
    //     requestForHttp(YOUDAO_URL, params);
    // }

    public void requestForHttp(YdReq ydReq) {

        String salt = SnowflakeUtil.snowflakeId();
        String curtime = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().getEpochSecond() + "";

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("q", ydReq.getQ());
        jsonObject.put("from", ydReq.getFrom());
        jsonObject.put("to", ydReq.getTo());
        jsonObject.put("appKey", geoCloud2Properties.getTranslation().getAppKey());
        jsonObject.put("salt", salt);
        jsonObject.put(
                "sign",
                getDigest(
                        geoCloud2Properties.getTranslation().getAppKey()
                                + truncate(ydReq.getQ())
                                + salt + curtime
                                + geoCloud2Properties.getTranslation().getAppSecret()
                )
        );
        jsonObject.put("signType", ydReq.getSignType());
        jsonObject.put("curtime", curtime);
        jsonObject.put("domain", ydReq.getDomain());
        jsonObject.put("rejectFallback", ydReq.getRejectFallback());

        String result2 = HttpRequest.post(geoCloud2Properties.getTranslation().getUrl())
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .body(jsonObject.toJSONString())
                // .header(Header.CONTENT_TYPE, "multipart/form-data;charset=UTF-8")
                // .form(jsonObject)
                .timeout(20000)
                .execute()
                .body();

        log.info("result2:{}",result2);

        // try {
        //     Header[] contentType = httpResponse.getHeaders("Content-Type");
        //     logger.info("Content-Type:" + contentType[0].getValue());
        //     if ("audio/mp3".equals(contentType[0].getValue())) {
        //         // 如果响应是wav
        //         HttpEntity httpEntity = httpResponse.getEntity();
        //         ByteArrayOutputStream baos = new ByteArrayOutputStream();
        //         httpResponse.getEntity().writeTo(baos);
        //         byte[] result = baos.toByteArray();
        //         EntityUtils.consume(httpEntity);
        //         if (result != null) {// 合成成功
        //             String file = "合成的音频存储路径" + System.currentTimeMillis() + ".mp3";
        //             byte2File(result, file);
        //         }
        //     } else {
        //         /** 响应不是音频流，直接显示结果 */
        //         HttpEntity httpEntity = httpResponse.getEntity();
        //         String json = EntityUtils.toString(httpEntity, "UTF-8");
        //         EntityUtils.consume(httpEntity);
        //         logger.info(json);
        //         System.out.println(json);
        //     }
        // } finally {
        //     try {
        //         if (httpResponse != null) {
        //             httpResponse.close();
        //         }
        //     } catch (IOException e) {
        //         logger.info("## release resouce error ##" + e);
        //     }
        // }
    }

    /**
     * 构建请求参数
     */
    private HashMap<String, Object> buildParam(YdReq ydReq) {

        String salt = SnowflakeUtil.snowflakeId();
        String curtime = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().getEpochSecond() + "";

        // StringBuilder sb = new StringBuilder();
        // sb.append("q").append(ydReq.getQ());
        // sb.append("from").append(ydReq.getFrom());
        // sb.append("to").append(ydReq.getTo());
        // sb.append("appKey").append(geoCloud2Properties.getTranslation().getAppKey());
        // sb.append("salt").append(salt);
        // sb.append("sign").append(getDigest(
        //                 geoCloud2Properties.getTranslation().getAppKey()
        //                         + truncate(ydReq.getQ())
        //                         + salt + curtime
        //                         + geoCloud2Properties.getTranslation().getAppSecret()
        //         )
        // );
        // sb.append("curtime").append(curtime);
        // sb.append("domain").append(ydReq.getDomain());
        // sb.append("rejectFallback").append(ydReq.getRejectFallback());
        //
        // return sb.toString();

        HashMap<String, Object> param = new HashMap<>();
        param.put("q", ydReq.getQ());
        param.put("from", ydReq.getFrom());
        param.put("to", ydReq.getTo());
        param.put("appKey", geoCloud2Properties.getTranslation().getAppKey());
        param.put("salt", salt);
        param.put(
                "sign",
                getDigest(
                        geoCloud2Properties.getTranslation().getAppKey()
                                + truncate(ydReq.getQ())
                                + salt + curtime
                                + geoCloud2Properties.getTranslation().getAppSecret()
                )
        );
        param.put("signType", ydReq.getSignType());
        param.put("curtime", curtime);
        param.put("domain", ydReq.getDomain());
        param.put("rejectFallback", ydReq.getRejectFallback());

        return param;
    }



    /**
     * 生成加密字段
     */
    public String getDigest(String string) {
        if (string == null) {
            return null;
        }
        char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        byte[] btInput = string.getBytes(StandardCharsets.UTF_8);
        try {
            MessageDigest mdInst = MessageDigest.getInstance("SHA-256");
            mdInst.update(btInput);
            byte[] md = mdInst.digest();
            int j = md.length;
            char str[] = new char[j * 2];
            int k = 0;
            for (byte byte0 : md) {
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(str);
        } catch (NoSuchAlgorithmException e) {
            return null;
        }
    }

    /**
     * @param result 音频字节流
     * @param file   存储路径
     */
    private static void byte2File(byte[] result, String file) {
        File audioFile = new File(file);
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(audioFile);
            fos.write(result);

        } catch (Exception e) {
            log.info(e.toString());
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }

    /**
     * 截断字符串
     */
    public static String truncate(String q) {
        if (q == null) {
            return null;
        }
        int len = q.length();
        return len <= 20 ? q : (q.substring(0, 10) + len + q.substring(len - 10, len));
    }


}
