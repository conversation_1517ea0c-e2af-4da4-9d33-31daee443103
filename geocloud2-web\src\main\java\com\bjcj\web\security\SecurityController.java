package com.bjcj.web.security;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import cn.hutool.core.map.MapUtil;
import com.bjcj.common.core.redis.RedisHandler;
import com.bjcj.common.core.redis.RedisKeyConstant;
import com.bjcj.common.utils.SnowflakeUtil;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.common.utils.properties.GeoCloud2Properties;
import com.bjcj.mapper.safetyManage.SysUserLoginPointMapper;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/11/24 15:51 周五
 */
@Tag(name = "0登录", description = "登录接口")
@ApiSupport(order = 2, author = "guow")
@RestController
@RequestMapping("/security")
public class SecurityController {

    @Resource
    SysUserLoginPointMapper sysUserLoginPointMapper;

    @Resource
    RedisHandler redisHandler;

    @Resource
    GeoCloud2Properties geoCloud2Properties;

    // @Operation(summary = "token刷新")
    // @ApiOperationSupport(order = 4)
    // @Parameter(name = "userId", description = "用户id", required = true)
    // @GetMapping("/token/refresh")
    // public JsonResult<HashMap<String, String>> tokenRefresh(long userId) {
    //     SysUserLoginPoint sysUserLoginPoint = sysUserLoginPointMapper.selectOne(
    //             Wrappers.<SysUserLoginPoint>query().lambda().eq(SysUserLoginPoint::getUserId, userId)
    //     );
    //     if (null == sysUserLoginPoint) {
    //         return JsonResult.build(Code.USER_NOT_LOGIN, Message.USER_NOT_LOGIN);
    //     }
    //     if (sysUserLoginPoint.getInvalidTime().isBefore(LocalDateTime.now())) {
    //         return JsonResult.build(Code.TOKEN_EXPIRED, Message.TOKEN_EXPIRED);
    //     }
    //     HashMap<String, String> result = new HashMap<>(4);
    //     result.put(
    //             geoCloud2Properties.getJwt().getHeader(),
    //             JwtTokenUtils.generateToken(JSON.toJSONString(
    //                     myUserDetailService.loadUserByUsername(sysUserLoginPoint.getUsername())
    //             ))
    //     );
    //     return JsonResult.success(result);
    // }

    @Operation(summary = "获取验证码")
    @ApiOperationSupport(order = 3)
    @GetMapping("/captcha")
    public JsonResult<Map<String, String>> getCaptcha() {

        LineCaptcha captcha = CaptchaUtil.createLineCaptcha(120, 40, 4, 50);

        //将验证码和对应的随机key值写入缓存数据库
        String key = SnowflakeUtil.snowflakeId();
        redisHandler.set(RedisKeyConstant.CAPTCHA + key, captcha.getCode(), 90, TimeUnit.SECONDS);

        return JsonResult.success(
                MapUtil.<String, String>builder()
                        .put("key", key)
                        .put("base64Image", captcha.getImageBase64Data())
                        .build()
        );
    }

}
