package com.bjcj.mapper.cloudportal;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.cloudportal.Dltb140105;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/7/12  15:29
*/
public interface Dltb140105Mapper extends BaseMapper<Dltb140105> {
    BigDecimal selectMjSum(@Param("simplecode") String simplecode,@Param("nydCodeList") List<String> nydCodeList);

    BigDecimal selectMjSum2(@Param("simplecode") String simplecode);

    BigDecimal selectMjSums(@Param("simplecode") String simplecode,@Param("dictValue") String dictValue);
}