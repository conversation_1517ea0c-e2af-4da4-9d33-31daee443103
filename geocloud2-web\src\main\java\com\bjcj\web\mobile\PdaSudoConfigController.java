package com.bjcj.web.mobile;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.mobile.PdaSudoconfigDto;
import com.bjcj.model.po.mobile.PdaSudoconfig;
import com.bjcj.service.mobile.PdaSudoconfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/pdaSudoConfig")
@Tag(name = "App模块菜单配置（根模块）")
public class PdaSudoConfigController {

    @Autowired
    PdaSudoconfigService pdaSudoconfigService;

    @SaCheckPermission("sys:read")
    @Operation(
            summary = "查询所有根模块",
            description = "查询所有 'issystem' 为 '是' 且 'isdisplay' 为 '是' 的根模块。"
    )
    @GetMapping("/getOption")
    public JsonResult<List<PdaSudoconfig>> getOption(){
        return JsonResult.success(pdaSudoconfigService.getOption());
    }

    @SaCheckPermission("sys:read")
    @Operation(
            summary = "获取配置项分页列表",
            description = "根据请求的过滤条件，分页查询根模块配置项列表，并返回数据列表和总记录数"
    )
    @GetMapping("/list")
    public JsonResult getList(@RequestParam("current") Integer currentPage, @RequestParam("size") Integer pageSize){
        return pdaSudoconfigService.selectPage(currentPage,pageSize);
    }

    @SaCheckPermission("sys:write")
    @Operation(
            summary = "新增根模块配置项",
            description = "接收前端传入的PdaSudoconfig对象，调用服务层新增配置，返回操作结果状态"
    )
    @PostMapping("/add")
    public JsonResult addPdaSudoConfig(@RequestBody PdaSudoconfig pdaSudoconfig){
        return JsonResult.success(pdaSudoconfigService.add(pdaSudoconfig));
    }

    @SaCheckPermission("sys:write")
    @Operation(
            summary = "删除根模块配置项",
            description = "根据传入的PID删除对应的配置项，返回删除操作是否成功"
    )
    @DeleteMapping("/delete/{pid}")
    public JsonResult deletePdaSudoConfig(@PathVariable("pid") String pid){

        PdaSudoconfig pdaSudoconfig = new PdaSudoconfig();
        pdaSudoconfig.setPid(pid);

        return JsonResult.success(pdaSudoconfigService.delete(pdaSudoconfig));
    }

    @SaCheckPermission("sys:write")
    @Operation(
            summary = "更新根模块配置项",
            description = "根据传入的PdaSudoconfig对象更新对应的配置项，返回更新操作是否成功"
    )
    @PutMapping("/update")
    public JsonResult updatePdaSudoConfig(@RequestBody PdaSudoconfig pdaSudoconfig){
        return JsonResult.success(pdaSudoconfigService.update(pdaSudoconfig));
    }

    @SaCheckPermission("sys:read")
    @Operation(
            summary = "检查根模块ID是否唯一",
            description = "根据传入的PID查询对象，检查是否存在相同的PID。如果存在则返回 'false'，否则返回 'true'。"
    )
    @GetMapping("/checkOnly")
    public JsonResult toCheckOnly(@RequestParam("pid") String pid){
        PdaSudoconfig pdaSudoconfig = new PdaSudoconfig();
        pdaSudoconfig.setPid(pid);

        return pdaSudoconfigService.getOne(pdaSudoconfig)==null ? JsonResult.success(true) : JsonResult.success(false);
    }

}
