package com.bjcj.mapper.safetyManage;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.safetyManage.User;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
*@Author：qinyi
*@Package：com.bjcj.mapper.safetyManage
*@Project：geocloud2
*@name：UserMapper
*@Date：2023/11/27  10:31
*@Filename：UserMapper
*/
public interface UserMapper extends BaseMapper<User> {
    List<String> selectUserRole(Long id);

    void modifyPassword(@Param("userId") Long userId,@Param("encryption") String encryption);

    String selectUserRoleName(Long userId);

    /**
     * 根据用户ID查询用户权限
     * @param loginId 登录用户ID
     * @return 权限集合
     */
    List<String> queryUserPermissions(@Param("userId") Long loginId);
}