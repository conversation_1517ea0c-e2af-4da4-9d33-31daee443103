package com.bjcj.model.po.datamodel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2024/7/2  10:07
*/
/**
    * 规则项
    */
@Schema(description="规则项")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "spdp_ruleitem")
public class SpdpRuleitem implements Serializable {
    /**
     * 规则项ID
     */
    @TableId(value = "ruleitemid", type = IdType.ASSIGN_UUID)
    @Schema(description="规则项ID")
    @Size(max = 36,message = "规则项IDmax length should less than 36")
    private String ruleitemid;

    /**
     * 规则项名称
     */
    @TableField(value = "name")
    @Schema(description="规则项名称")
    @Size(max = 60,message = "规则项名称max length should less than 60")
    private String name;

    /**
     * 显示名称或别名
     */
    @TableField(value = "displayname")
    @Schema(description="显示名称或别名")
    @Size(max = 60,message = "显示名称或别名max length should less than 60")
    private String displayname;

    /**
     * 说明或描述
     */
    @TableField(value = "description")
    @Schema(description="说明或描述")
    @Size(max = 500,message = "说明或描述max length should less than 500")
    private String description;

    /**
     * 检查器的Key名称
     */
    @TableField(value = "validatorkey")
    @Schema(description="检查器的Key名称")
    @Size(max = 200,message = "检查器的Key名称max length should less than 200")
    @NotBlank(message = "检查器的Key名称is not blank")
    private String validatorkey;

    /**
     * 检查器配置信息
     */
    @TableField(value = "validatorconfigdatatext")
    @Schema(description="检查器配置信息")
    @Size(max = 4000,message = "检查器配置信息max length should less than 4000")
    private String validatorconfigdatatext;

    /**
     * 规则ID
     */
    @TableField(value = "ruleid")
    @Schema(description="规则ID")
    @Size(max = 36,message = "规则IDmax length should less than 36")
    @NotBlank(message = "规则IDis not blank")
    private String ruleid;

    private static final long serialVersionUID = 1L;
}