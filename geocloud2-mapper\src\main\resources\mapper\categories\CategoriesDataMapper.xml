<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.naturalresources.categories.CategoriesDataMapper">

  <select id="findSelfAndChildrenById" resultType="com.bjcj.model.po.naturalresources.categories.CategoriesData">
    WITH RECURSIVE cata_data as (SELECT * FROM categories_data WHERE id=#{cataDataId} UNION ALL SELECT categories_data.* from categories_data INNER JOIN cata_data ON categories_data.parent_id = cata_data.id)  select * from cata_data
  </select>

  <select id="findSelfAndChildrenByIdDir" resultType="com.bjcj.model.po.platformManage.specialPlan.SpecialPlanDir">
    WITH RECURSIVE cata_data as (SELECT * FROM special_plan_dir WHERE id=#{cataDataId} UNION ALL SELECT special_plan_dir.* from special_plan_dir INNER JOIN cata_data ON special_plan_dir.parent_id = cata_data.id)  select * from cata_data
  </select>
</mapper>