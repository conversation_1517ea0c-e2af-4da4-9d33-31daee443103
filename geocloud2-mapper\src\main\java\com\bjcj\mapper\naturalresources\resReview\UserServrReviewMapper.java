package com.bjcj.mapper.naturalresources.resReview;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.model.po.naturalresources.resReview.UserServrReview;
import com.bjcj.model.po.naturalresources.resReview.UserServrReview2;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;


/**
 *@Author：qinyi
 *@Date：2024/1/17  16:00
*/
public interface UserServrReviewMapper extends BaseMapper<UserServrReview> {
    List<UserServrReview> selectPageData(@Param("current") Long current, @Param("size") Long size, @Param("searchStr") String searchStr, @Param("reviewStatus") Integer reviewStatus, @Param("serviceType") String serviceType, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime, @Param("orders") String orders);

    int selectPageDataCount(@Param("current") Long current, @Param("size") Long size, @Param("searchStr") String searchStr, @Param("reviewStatus") Integer reviewStatus, @Param("serviceType") String serviceType, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime, @Param("orders") String orders);

    UserServrReview selectByUserIdAndResId(@Param("loginuserid") Long loginuserid,@Param("id") String id);

    List<UserServrReview2> selectByServiceId(@Param("id") Long id);

    List<UserServrReview2> selectListMap(@Param("userId") Long userId,@Param("serverid") String serverid);
}