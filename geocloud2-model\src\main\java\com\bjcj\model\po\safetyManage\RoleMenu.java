package com.bjcj.model.po.safetyManage;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 *@Author：qinyi
 *@Date：2023/12/4  10:48
*/
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_role_menu")
public class RoleMenu implements Serializable {
    @Schema(description="")
    private Long roleId;

    @Schema(description="")
    private Long menuId;

    @Schema(description="")
    private Long specialPlanId;

    private static final long serialVersionUID = 1L;
}