package com.bjcj.service.safetyManage;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.safetyManage.SafetyDeptMapper;
import com.bjcj.mapper.safetyManage.UserMapper;
import com.bjcj.model.dto.safetyManage.SafetyDeptDto;
import com.bjcj.model.po.safetyManage.SafetyDept;
import com.bjcj.model.po.safetyManage.User;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Author：qinyi
 * @Date：2023/11/24 15:48
 */
@Service
public class SafetyDeptService extends ServiceImpl<SafetyDeptMapper, SafetyDept> {

    @Resource
    SafetyDeptMapper safetyDeptMapper;

    @Resource
    UserMapper userMapper;

    public JsonResult saveData(SafetyDeptDto dto) {
        SafetyDept safetyDept = BeanUtil.copyProperties(dto, SafetyDept.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        safetyDept.setOperater(username);
        //验证重复数据
        LambdaQueryWrapper<SafetyDept> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(dto.getDeptName()),SafetyDept::getDeptName,dto.getDeptName());
        queryWrapper.eq(!Objects.isNull(dto.getParentDeptId()),SafetyDept::getParentDeptId,dto.getParentDeptId())
                .eq(SafetyDept::getParentInstitutionId,dto.getParentInstitutionId());
        List<SafetyDept> sdList = this.safetyDeptMapper.selectList(queryWrapper);
//        if(!sdList.isEmpty())
//            if(!!Objects.isNull(safetyDept.getId()) && Long.compare(safetyDept.getId(),sdList.get(0).getId()) != 0)
//                return JsonResult.build(400,"同级不能有名字相同的部门,请换一个名称!");
        if(Objects.isNull(safetyDept.getId())){
            if(!sdList.isEmpty())
                return JsonResult.build(400,"同级不能有名字相同的部门,请换一个名称!");
            safetyDept.setCreateTime(sdf.format(new Date()));
            if (Objects.isNull(dto.getParentDeptId()))
                safetyDept.setParentDeptId(Long.parseLong("0"));
            return safetyDeptMapper.insert(safetyDept) > 0 ? JsonResult.success() : JsonResult.error();
        }else{
            if(!sdList.isEmpty())
                if(!Objects.isNull(safetyDept.getId()) && Long.compare(safetyDept.getId(),sdList.get(0).getId()) != 0)
                    return JsonResult.build(400,"同级不能有名字相同的部门,请换一个名称!");
            //子部门不允许直接换到其他机构下
            LambdaQueryWrapper<SafetyDept> qw = new LambdaQueryWrapper<>();
            qw.eq(SafetyDept::getId,dto.getId());
            SafetyDept sd = this.safetyDeptMapper.selectOne(qw);
            if(dto.getParentDeptId() != 0 && Long.compare(dto.getParentInstitutionId(),sd.getParentInstitutionId()) != 0)
                return JsonResult.build(400,"子部门不可直接更换机构!");
            safetyDept.setUpdateTime(sdf.format(new Date()));
            return safetyDeptMapper.updateById(safetyDept) > 0 ? JsonResult.success() : JsonResult.error();
        }
    }

    public JsonResult<Page<SafetyDept>> pageDataSort(Page<SafetyDept> pager, String searchStr, Long institutionId,Long deptId) {
        LambdaQueryWrapper<SafetyDept> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotEmpty(searchStr),SafetyDept::getDeptName,searchStr);
        queryWrapper.eq(SafetyDept::getParentInstitutionId,institutionId);
        if(Objects.isNull(deptId) && StringUtils.isEmpty(searchStr))
            deptId = 0L;
        queryWrapper.eq(!Objects.isNull(deptId),SafetyDept::getParentDeptId,deptId);
        queryWrapper.orderByAsc(SafetyDept::getShowSort);
        Page<SafetyDept> safetyDeptPage = safetyDeptMapper.selectPage(pager, queryWrapper);
        //查询部门下是否存在子部门
        safetyDeptPage.getRecords().parallelStream().forEachOrdered(dept -> {
            LambdaQueryWrapper<SafetyDept> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.eq(SafetyDept::getParentDeptId,dept.getId());
            List<SafetyDept> nodes = this.safetyDeptMapper.selectList(queryWrapper1);
            if(!nodes.isEmpty()) {
                dept.setIsHasNode(true);
            }
            //写入上级部门名称
            LambdaQueryWrapper<SafetyDept> queryWrapper2 = new LambdaQueryWrapper<>();
            queryWrapper2.eq(SafetyDept::getId,dept.getParentDeptId());
            SafetyDept safetyDept = this.safetyDeptMapper.selectOne(queryWrapper2);
            if(Objects.nonNull(safetyDept)){
                dept.setParentDeptName(safetyDept.getDeptName());
            }else{
                dept.setParentDeptName("无");
            }

        });
        return JsonResult.success(safetyDeptPage);
    }

    public JsonResult delData(Long id) {
        /**
         * 原递归删除逻辑
        List<Long> idList = new ArrayList<>();
        List<Long> allIds = getAllIds(id, idList);
        //删除所有子节点 及本节点
        allIds.add(id);
        return this.safetyDeptMapper.deleteBatchIds(allIds) > 0 ? JsonResult.success() : JsonResult.error();
        */

        //有子节点不可删除,最子节点有人不可删除
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getDeptId,id);
        List<User> userList = userMapper.selectList(queryWrapper);
        if(Objects.nonNull(queryDeptByParentId(id)) && queryDeptByParentId(id).size() != 0) {
            return JsonResult.build(400,"有子部门不可删除!");
        } else if(Objects.nonNull(userList) && userList.size() != 0) {
            return JsonResult.build(400,"部门下存在人员,不可删除!");
        } else {
            return this.safetyDeptMapper.deleteById(id) > 0? JsonResult.success() : JsonResult.error();
        }

    }

    /**
     * 递归获取所有节点
     */
    private List<Long> getAllIds(Long id,List<Long> idList){
        //递归查询其下子节点
        List<SafetyDept> safetyDeptList = queryDeptByParentId(id);
        if(safetyDeptList.size() != 0){
            for(SafetyDept dept : safetyDeptList) {
                idList.add(dept.getId());
                getAllIds(dept.getId(),idList);
            }
        }
        return idList;
    }

    /**
     * 根据部门id查询其下子节点
     */
    private List<SafetyDept> queryDeptByParentId(Long id){
        LambdaQueryWrapper<SafetyDept> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SafetyDept::getParentDeptId,id);
        return this.safetyDeptMapper.selectList(queryWrapper);
    }

    public Long deptUserCount(Long deptId) {
        return this.userMapper.selectCount(new LambdaQueryWrapper<User>().eq(User::getDeptId,deptId));
    }

    public JsonResult delDeptUser(Long deptId) {
        if(Objects.nonNull(queryDeptByParentId(deptId)) && queryDeptByParentId(deptId).size()!= 0) {
            return JsonResult.build(400,"有子部门不可删除!");
        }
        return this.userMapper.delete(new LambdaQueryWrapper<User>().eq(User::getDeptId,deptId)) > 0? JsonResult.success() : JsonResult.error();
    }
}
