<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.eswPlatform.BookMarkInfoMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.eswPlatform.BookMarkInfo">
    <!--@mbg.generated-->
    <!--@Table public.book_mark_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="displayOrder" jdbcType="INTEGER" property="displayorder" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="subsystemname" jdbcType="VARCHAR" property="subsystemname" />
    <result column="userid" jdbcType="BIGINT" property="userid" />
    <result column="special_plan_id" jdbcType="BIGINT" property="specialPlanId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, content, description, displayOrder, "name", subsystemname, userid, special_plan_id
  </sql>
</mapper>