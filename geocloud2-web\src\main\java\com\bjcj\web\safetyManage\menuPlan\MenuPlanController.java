package com.bjcj.web.safetyManage.menuPlan;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.jwt.JWTUtil;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.Date2String;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.dto.safetyManage.MenuDto;
import com.bjcj.model.po.safetyManage.Menu;
import com.bjcj.service.safetyManage.MenuService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
* (sys_menu)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/menuPlan")
@Tag(name = "6平台菜单方案管理")
@Validated
public class MenuPlanController {
    /**
    * 服务对象
    */
    @Resource
    private MenuService menuService;

    @Resource
    Date2String date2String;

    @SaCheckPermission("sys:read")
    @GetMapping("/list")
    @Operation(summary = "菜单树", description = "菜单树")
    @Parameters({
            @Parameter(name = "menuName", description = "菜单名称", required = false)
    })
    @ApiOperationSupport(order = 1)
    public JsonResult<List<Menu>> list(@RequestParam(value = "menuName",required = false) String menuName){
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        List<Menu> menuList = this.menuService.selectMenuList(menuName,userId,null);
        return JsonResult.success(menuList);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/listRight")
    @Operation(summary = "菜单列表", description = "菜单列表")
    @Parameters({
            @Parameter(name = "menuName", description = "菜单名称", required = false),
            @Parameter(name = "platformAppConfId", description = "平台应用配置id", required = true)
    })
    @ApiOperationSupport(order = 6)
    public JsonResult<List<Menu>> listRight(@RequestParam(value = "menuName",required = false) String menuName,
                                            @RequestParam(value = "platformAppConfId",required = true) Long platformAppConfId){
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        List<Menu> menuList = this.menuService.selectMenuList3(menuName,userId,platformAppConfId);
        return JsonResult.success(menuList);
    }



    @OperaLog(operaModule = "菜单管理-添加菜单",operaType = OperaLogConstant.CREATE,operaDesc = "添加菜单")
    @SaCheckPermission("sys:write")
    @PostMapping("/add")
    @Operation(summary = "添加菜单", description = "添加菜单")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "add")
    public JsonResult add(@Validated @RequestBody MenuDto dto) {
        if (!menuService.checkMenuNameUnique2(null, dto.getParentId(), dto.getMenuName(),dto.getPlatformAppConfId())){
            return JsonResult.error("新增菜单'" + dto.getMenuName() + "'失败，菜单名称已存在");
        }
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        dto.setOperater(username);
        dto.setCreateTime(date2String.nowDateStr());
        return menuService.insertMenu(dto);
    }

    @SaCheckPermission("sys:read")
    @GetMapping("/checkMenuNameUnique")
    @Operation(summary = "检测菜单名称唯一性", description = "检测菜单名称唯一性")
    @Parameters({
            @Parameter(name = "id", description = "菜单id", required = false),
            @Parameter(name = "parentId", description = "父菜单id", required = true),
            @Parameter(name = "menuName", description = "菜单名称", required = true),
            @Parameter(name = "platformAppConfId", description = "平台应用配置id", required = true)
    })
    @ApiOperationSupport(order = 5)
    public boolean checkMenuNameUnique(@RequestParam(value = "id",required = false) Long id,@RequestParam("parentId") Long parentId, @RequestParam("menuName") String menuName,
                                        @RequestParam("platformAppConfId") Long platformAppConfId)
    {
        return menuService.checkMenuNameUnique2(id,parentId,menuName,platformAppConfId);
    }


    @OperaLog(operaModule = "菜单管理-编辑菜单",operaType = OperaLogConstant.UPDATE,operaDesc = "编辑菜单")
    @SaCheckPermission("sys:write")
    @PutMapping("/edit")
    @Operation(summary = "编辑菜单", description = "编辑菜单")
    @ApiOperationSupport(order = 3)
    @RequestLock(prefix = "edit")
    public JsonResult edit(@Validated @RequestBody MenuDto dto) {
        if (!menuService.checkMenuNameUnique2(dto.getId(),dto.getParentId(),dto.getMenuName(),dto.getPlatformAppConfId())){
            return JsonResult.error("修改菜单'" + dto.getMenuName() + "'失败，菜单名称已存在");
        }
        String username = JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("username").toString();
        dto.setOperater(username);
        return menuService.updateMenu(dto);
    }

    @OperaLog(operaModule = "菜单管理-删除菜单",operaType = OperaLogConstant.DELETE,operaDesc = "删除菜单")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del")
    @Operation(summary = "删除菜单", description = "根据id删除菜单")
    @Parameters({
            @Parameter(name = "id", description = "菜单id", required = true)
    })
    @ApiOperationSupport(order = 4)
    public JsonResult remove(@RequestParam("id") Long id) {
        if (menuService.selectCountMenuByParentId(id) > 0){
            return JsonResult.error("存在子菜单,不允许删除");
        }
        if (menuService.selectCountRoleMenuByMenuId(id) > 0){
            return JsonResult.error("菜单已分配,不允许删除");
        }
        return menuService.deleteMenuById(id);
    }

    @OperaLog(operaModule = "导出此菜单到...",operaType = OperaLogConstant.CREATE,operaDesc = "导出此菜单到...")
    @SaCheckPermission("sys:write")
    @PostMapping("/importTo")
    @Operation(summary = "导出此菜单到...", description = "导出此菜单到...")
    @ApiOperationSupport(order = 7)
    @Parameters({
            @Parameter(name = "fromPlatformAppConfId", description = "导出的平台id", required = true),
            @Parameter(name = "toPlatformAppConfId", description = "导入的平台id", required = true)
    })
    public JsonResult importTo(@RequestParam("fromPlatformAppConfId") Long fromPlatformAppConfId,
                              @RequestParam("toPlatformAppConfId") Long toPlatformAppConfId) {
        return menuService.importTo(fromPlatformAppConfId,toPlatformAppConfId);
    }

}
