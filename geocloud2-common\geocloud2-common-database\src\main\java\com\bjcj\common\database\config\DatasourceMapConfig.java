package com.bjcj.common.database.config;


import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Configuration
@ConfigurationProperties(prefix = "spring.datasource.dynamic")
public class DatasourceMapConfig {
    Map<String,Map<String,String>> datasource;

    public Map<String, Map<String,String>> getDatasource() {
        return datasource;
    }

    public void setDatasource(Map<String, Map<String,String>> datasource) {
        this.datasource = datasource;
    }
}
