package com.bjcj.service.safetyManage;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.mapper.safetyManage.SysRoleFuncMapper;
import com.bjcj.model.dto.safetyManage.RoleFunctionsDto;
import com.bjcj.model.po.safetyManage.SysRoleFunc;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 *@Author：qinyi
 *@Date：2023/12/13  15:09
*/
@Service
public class SysRoleFuncService extends ServiceImpl<SysRoleFuncMapper, SysRoleFunc> {

    @Resource
    SysRoleFuncMapper sysRoleFuncMapper;

    @Resource
    CheckIsAdmin checkIsAdmin;

    public JsonResult saveRoleFunctions(RoleFunctionsDto dto) {
        //获取当前登录用户角色
        Long userId = Long.parseLong(JWTUtil.parseToken(StpUtil.getTokenValue()).getPayload("userId").toString());
        String fromRoleName = checkIsAdmin.getUserRoleName(userId);
        Long roleId = dto.getRoleId();
        if(!dto.getAddIdList().isEmpty()){
            List<SysRoleFunc> list = new ArrayList<SysRoleFunc>();
            dto.getAddIdList().forEach(funcId -> {
                SysRoleFunc sysRoleFunc =  new SysRoleFunc(){{setFuncId(funcId);setRoleId(roleId);setFromRoleName(fromRoleName);}};
                list.add(sysRoleFunc);
            });
            sysRoleFuncMapper.insertBatch(list);
        }
        if(!dto.getDelIdList().isEmpty()){
            sysRoleFuncMapper.deleteBatch(dto.getDelIdList(),roleId);
        }
        return JsonResult.success();
    }
}
