package com.bjcj.model.po.platformManage.specialPlan;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/1/19  9:09
*/
/**
    * 专题展示方案表
    */
@Schema(description="专题展示方案表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "special_plan")
public class SpecialPlan implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    /**
     * 方案名称
     */
    @TableField(value = "plan_name")
    @Schema(description="方案名称")
    @Size(max = 100,message = "方案名称max length should less than 100")
    @NotBlank(message = "方案名称is not blank")
    private String planName;

    /**
     * 描述
     */
    @TableField(value = "remark")
    @Schema(description="描述")
    @Size(max = 255,message = "描述max length should less than 255")
    private String remark;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "update_time",fill = FieldFill.UPDATE)
    @Schema(description="")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField(value = "operater")
    @Schema(description="")
    @Size(max = 30,message = "max length should less than 30")
    @NotBlank(message = "is not blank")
    private String operater;


    @TableField(value = "platform_app_conf_id")
    @Schema(description="平台应用配置id")
    @NotNull(message = "平台应用配置不能为空")
    private Long platformAppConfId;

    @TableField(exist = false)
    @Schema(description="平台应用配置showname")
    private String platformAppConfShowName;

    private static final long serialVersionUID = 1L;
}