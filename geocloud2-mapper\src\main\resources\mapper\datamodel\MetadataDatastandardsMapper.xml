<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.mapper.datamodel.MetadataDatastandardsMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.model.po.datamodel.MetadataDatastandards">
    <!--@mbg.generated-->
    <!--@Table public.metadata_datastandards-->
    <id column="datastandardid" jdbcType="CHAR" property="datastandardid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="displayname" jdbcType="VARCHAR" property="displayname" />
    <result column="datacategoryid" jdbcType="CHAR" property="datacategoryid" />
    <result column="defaultdisplayschemaid" jdbcType="CHAR" property="defaultdisplayschemaid" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="createtime" jdbcType="TIMESTAMP" property="createtime" />
    <result column="displayorder" jdbcType="NUMERIC" property="displayorder" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    datastandardid, "name", description, code, displayname, datacategoryid, defaultdisplayschemaid, 
    version, createtime, displayorder
  </sql>
</mapper>