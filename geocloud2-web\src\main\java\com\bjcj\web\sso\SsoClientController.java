package com.bjcj.web.sso;

import cn.dev33.satoken.SaManager;
import cn.dev33.satoken.sso.config.SaSsoClientConfig;
import cn.dev33.satoken.sso.error.SaSsoErrorCode;
import cn.dev33.satoken.sso.exception.SaSsoException;
import cn.dev33.satoken.sso.processor.SaSsoClientProcessor;
import cn.dev33.satoken.sso.processor.SaSsoServerProcessor;
import cn.dev33.satoken.sso.template.SaSsoUtil;
import cn.dev33.satoken.stp.SaLoginConfig;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaFoxUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.bjcj.common.utils.domain.Code;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.common.utils.domain.Message;
import com.bjcj.service.safetyManage.UserService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Sa-Token-SSO Client端 Controller 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sso")
@Tag(name = "01 SSO登录")
@Slf4j
public class SsoClientController {

	@Resource
	UserService userService;

	/*
	 * SSO-Client端：处理所有SSO相关请求 
	 * 		http://{host}:{port}/sso/login			-- Client端登录地址，接受参数：back=登录后的跳转地址 
	 * 		http://{host}:{port}/sso/logout			-- Client端单点注销地址（isSlo=true时打开），接受参数：back=注销后的跳转地址 
	 * 		http://{host}:{port}/sso/logoutCall		-- Client端单点注销回调地址（isSlo=true时打开），此接口为框架回调，开发者无需关心
	 */
	@GetMapping("/*")
	@Operation(
			summary = "所有sso请求都走这个接口",
			description = """
					SSO-Client端：处理所有SSO相关请求 \n\t
							http://{host}:{port}/sso/login			-- Client端登录地址，接受参数：back=登录后的跳转地址 \n\t
							http://{host}:{port}/sso/logout			-- Client端单点注销地址（isSlo=true时打开），接受参数：back=注销后的跳转地址 \n\t
							http://{host}:{port}/sso/logoutCall		-- Client端单点注销回调地址（isSlo=true时打开），此接口为框架回调，开发者无需关心 \n\t
					"""
	)
	@ApiOperationSupport(order = 1)
	public Object ssoRequest() {
		return SaSsoClientProcessor.instance.dister();
	}

	// 配置SSO相关参数
	@Autowired
	private void configSso(SaSsoClientConfig ssoClient) {
		// 配置Http请求处理器
		ssoClient.sendHttp = url -> {
			System.out.println("------ 发起请求：" + url);
			String resStr = HttpUtil.get(url);
			System.out.println("------ 请求结果：" + resStr);
			return resStr;
		};
	}

	@PostMapping("/isLogin")
	@Operation(summary = "当前是否登录", description = "当前是否登录")
	@ApiOperationSupport(order = 2)
	public JsonResult isLogin() {
		return JsonResult.success(StpUtil.isLogin());
	}

	@PostMapping("/getSsoAuthUrl")
	@Operation(summary = "返回SSO认证中心登录地址", description = "返回SSO认证中心登录地址")
	@ApiOperationSupport(order = 3)
	public JsonResult getSsoAuthUrl(String clientLoginUrl) {
		String serverAuthUrl = SaSsoUtil.buildServerAuthUrl(clientLoginUrl, "");
		return JsonResult.build(Code.SUCCESS, Message.SUCCESS, serverAuthUrl);
	}

	@PostMapping("/doLoginByTicket")
	@Operation(summary = "根据ticket进行登录", description = "根据ticket进行登录")
	@ApiOperationSupport(order = 4)
	public JsonResult doLoginByTicket(String ticket) {

		// 客户端
		String client = "geocloud2";

		// 读取 loginId
		String loginId = SaManager.getSaTokenDao().get(
				SaSsoServerProcessor.instance.ssoServerTemplate.splicingTicketSaveKey(ticket)
		);

		// 读取session
		String s = SaManager.getSaTokenDao().get(
				SaSsoServerProcessor.instance.ssoServerTemplate.getStpLogic().splicingKeySession(loginId)
		);
		log.info("token字符串:{}", s);
		String tokenValue = JSONUtil.parseObj(s)
				.getJSONArray("tokenSignList")
				.getJSONArray(1).getJSONObject(0).getStr("value");

		if(SaFoxUtil.isEmpty(loginId)) {
			throw new SaSsoException("无效ticket：" + ticket).setCode(SaSsoErrorCode.CODE_30004);
		}

		// 解析出这个 ticket 关联的 Client
		String ticketClient = SaSsoServerProcessor.instance.ssoServerTemplate.getTicketToClient(ticket);

		// 校验 client 参数是否正确，即：创建 ticket 的 client 和当前校验 ticket 的 client 是否一致
		if(SaFoxUtil.notEquals(client, ticketClient)) {
			throw new SaSsoException("该 ticket 不属于 client=" + client + ", ticket 值: " + ticket)
					.setCode(SaSsoErrorCode.CODE_30011);
		}

		// 删除 ticket 信息，使其只有一次性有效
		SaSsoServerProcessor.instance.ssoServerTemplate.deleteTicket(ticket);
		SaSsoServerProcessor.instance.ssoServerTemplate.deleteTicketIndex(loginId);
		SaSsoServerProcessor.instance.ssoServerTemplate.deleteTicketToClient(ticket);

		// 登录
		StpUtil.login(loginId, SaLoginConfig.setToken(tokenValue));

		return JsonResult.success(StpUtil.getTokenInfo());
	}


}
