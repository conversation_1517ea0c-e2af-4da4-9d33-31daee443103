package com.bjcj.model.po.naturalresources.categories;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 *@Author：qinyi
 *@Date：2024/4/25  9:19
*/

/**
    * 资源项
    */
@Schema(description="注册功能服务用")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResourceServicesZcgnfw implements Serializable {
    /**
     * 标识
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(description="标识")
    @Size(max = 36,message = "标识max length should less than 36")
    private String id;

    /**
     * 名称
     */
    @TableField(value = "name")
    @Schema(description="注册名称")
    @NotBlank(message = "注册名称is not blank")
    private String name;

    /**
     * 别名
     */
    @TableField(value = "displayname")
    @Schema(description="显示名称")
    @NotBlank(message = "显示名称is not blank")
    private String displayname;

    /**
     * 资源目录id，resource_catalogs的id
     */
    @TableField(value = "resourcecatalogid")
    @Schema(description="服务目录id")
    @NotBlank(message = "服务目录idis not blank")
    private String resourcecatalogid;


    /**
     * 权限类型
     */
    @TableField(value = "authoritytype")
    @Schema(description="权限类型")
    @NotNull(message = "权限类型is not null")
    private Short authoritytype;


    /**
     * 原始地址
     */
    @TableField(value = "url")
    @Schema(description="注册地址")
    @NotBlank(message = "注册地址is not blank")
    private String url;

    /**
     * 服务类型
     */
    @TableField(value = "resourcetype")
    @Schema(description="服务类型")
    @NotBlank(message = "服务类型is not blank")
    private String resourcetype;

    /**
     * 服务标签
     */
    @TableField(value = "servicetag")
    @Schema(description="服务标签")
    @Size(max = 60,message = "服务标签max length should less than 60")
    private String servicetag;


    /**
     * 缩略图url
     */
    @TableField(value = "thumbnailurl")
    @Schema(description="缩略图url")
    @Size(max = 500,message = "缩略图urlmax length should less than 500")
    private String thumbnailurl;

    /**
     * 描述
     */
    @TableField(value = "description")
    @Schema(description="描述")
    @Size(max = 2000,message = "描述max length should less than 2000")
    private String description;


    /**
     * 显示顺序
     */
    @TableField(value = "displayorder")
    @Schema(description="显示顺序")
    @NotNull(message = "显示顺序is not null")
    private Short displayorder;

    /**
     * 元数据字段
     */
    @TableField(value = "metadata")
    @Schema(description="元数据字段")
    private Object metadata;

    /**
     * 是否显示
     */
    @TableField(value = "isvisiable")
    @Schema(description="是否显示")
    private Boolean isvisiable;


    /**
     * 服务访问量
     */
    @TableField(value = "accesscount")
    @Schema(description="服务访问量")
    private BigDecimal accesscount;

    /**
     * 资源类别。1应用服务；2应用；3文档；4数据；5数据产品；6数据服务；7数据库；8服务器
     */
    @TableField(value = "resourcecategory")
    @Schema(description="资源类别。1应用服务；2应用；3文档；4数据；5数据产品；6数据服务；7数据库；8服务器")
    private Short resourcecategory;


    /**
     * 注册人登录名
     */
    @TableField(value = "registerman")
    @Schema(description="注册人登录名")
    @Size(max = 100,message = "注册人登录名max length should less than 100")
    private String registerman;

    /**
     * 注册日期
     */
    @TableField(value = "registerdate", fill = FieldFill.INSERT)
    @Schema(description="注册日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registerdate;








    private static final long serialVersionUID = 1L;
}