package com.bjcj.web.eswPlatform;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.eswPlatform.SchemeModels;
import com.bjcj.service.eswPlatform.SchemeModelsService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* (public.scheme_models)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/scheme_models")
@Validated
@Tag(name = "方案模型")
public class SchemeModelsController {
    /**
    * 服务对象
    */
    @Resource
    private SchemeModelsService schemeModelsService;

    @SaCheckPermission("sys:read")
    @GetMapping("/list")
    @Operation(summary = "列表", description = "根据方案查模型列表")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "schemeid", description = "方案id", required = true)
    })
    public JsonResult<List<SchemeModels>> list(@RequestParam("schemeid") String schemeid) {
        return JsonResult.success(this.schemeModelsService.list(new LambdaQueryWrapper<SchemeModels>().eq(SchemeModels::getSchemeid,schemeid)));
    }

    @SaCheckPermission("sys:write")
    @PostMapping("/add")
    @Operation(summary = "新增", description = "新增")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "add_scheme_models")
    public JsonResult addOrEdit(@Validated @RequestBody SchemeModels po){
        return this.schemeModelsService.save(po) ? JsonResult.success() : JsonResult.error();
    }
}
