package com.bjcj.web.eswPlatform;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bjcj.common.core.constant.OperaLogConstant;
import com.bjcj.common.utils.annotation.OperaLog;
import com.bjcj.common.utils.annotation.RequestLock;
import com.bjcj.common.utils.domain.JsonResult;
import com.bjcj.model.po.eswPlatform.Projects;
import com.bjcj.model.po.eswPlatform.Schemes;
import com.bjcj.service.eswPlatform.ProjectsService;
import com.bjcj.service.eswPlatform.SchemesService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* (public.projects)表控制层
* <AUTHOR>
*/
@RestController
@RequestMapping("/projects")
@Validated
@Tag(name = "规划项目")
public class ProjectsController {
    /**
    * 服务对象
    */
    @Resource
    private ProjectsService projectsService;

    @Resource
    private SchemesService schemesService;

    @SaCheckPermission("sys:read")
    @GetMapping("/list")
    @Operation(summary = "列表", description = "列表")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "special_plan_id", description = "专题id", required = true)
    })
    public JsonResult<List<Projects>> list(@RequestParam(value = "special_plan_id",required = true) Long special_plan_id) {
        List<Projects> list = this.projectsService.list(new LambdaQueryWrapper<Projects>().eq(Projects::getSpecialPlanId, special_plan_id));
        list.forEach(p -> {
            p.setChildrens(this.schemesService.list(new LambdaQueryWrapper<Schemes>().eq(Schemes::getProjectid, p.getId())));
        });
        return JsonResult.success(list);
    }

    @SaCheckPermission("sys:write")
    @PostMapping("/addOrEdit")
    @Operation(summary = "新增/编辑", description = "新增/编辑")
    @ApiOperationSupport(order = 2)
    @RequestLock(prefix = "addOrEditghxm")
    public JsonResult addOrEdit(@Validated @RequestBody Projects po){
        return this.projectsService.saveOrUpdate(po) ? JsonResult.success(po.getId()) : JsonResult.error();
    }

    @OperaLog(operaModule = "删除",operaType = OperaLogConstant.DELETE,operaDesc = "删除")
    @SaCheckPermission("sys:del")
    @DeleteMapping("/del/{id}")
    @Operation(summary = "删除", description = "根据id删除")
    @ApiOperationSupport(order = 3)
    @RequestLock(prefix = "delghxm")
    public JsonResult del(@PathVariable("id") String id){
        return this.projectsService.removeById(id) ? JsonResult.success() : JsonResult.error();
    }

}
